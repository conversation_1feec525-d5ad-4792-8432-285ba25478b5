import{I as F,l as p,r,q as P,o as b,w as d,d as _,e as o,f as c,K as $,u as h,a as v,F as y}from"./app-DvIo72ZO.js";const B={class:"grid grid-cols-3 gap-6"},S={class:"col-span-3 sm:col-span-1"},q={name:"AcademicSubjectForm"},O=Object.assign(q,{setup(f){const t={name:"",alias:"",code:"",shortcode:"",type:"",position:"",description:""},e="academic/subject/",i=F(e),m=p({types:[]}),s=p({...t}),l=n=>{Object.assign(m,n)},j=n=>{var a;Object.assign(t,{...n,type:((a=n.type)==null?void 0:a.uuid)||""}),Object.assign(s,$(t))};return(n,a)=>{const g=r("BaseInput"),A=r("FormAction");return b(),P(A,{"has-setup-wizard":!0,"pre-requisites":!0,onSetPreRequisites:l,"init-url":e,"init-form":t,form:s,"set-form":j,redirect:"AcademicSubject"},{default:d(()=>[_("div",B,[_("div",S,[o(g,{type:"text",modelValue:s.name,"onUpdate:modelValue":a[0]||(a[0]=u=>s.name=u),name:"name",label:n.$trans("academic.subject.props.name"),error:c(i).name,"onUpdate:error":a[1]||(a[1]=u=>c(i).name=u),placeholder:"Mathematics",autofocus:""},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),V={name:"AcademicSubjectAction"},R=Object.assign(V,{setup(f){const t=h();return(e,i)=>{const m=r("PageHeaderAction"),s=r("PageHeader"),l=r("ParentTransition");return b(),v(y,null,[o(s,{title:e.$trans(c(t).meta.trans,{attribute:e.$trans(c(t).meta.label)}),navs:[{label:e.$trans("academic.academic"),path:"Academic"},{label:e.$trans("academic.subject.subject"),path:"AcademicSubjectList"}]},{default:d(()=>[o(m,{name:"AcademicSubject",title:e.$trans("academic.subject.subject"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),o(l,{appear:"",visibility:!0},{default:d(()=>[o(O)]),_:1})],64)}}});export{R as default};
