import{l as T,r as a,q as f,o as u,w as e,d as C,e as o,u as j,h as H,j as P,m as L,f as m,a as A,F as M,v as N,s as g,t as _,b as h}from"./app-DvIo72ZO.js";const O={class:"grid grid-cols-3 gap-6"},S={class:"col-span-3 sm:col-span-1"},U={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(d,{emit:p}){const v=p,r={search:""},c=T({...r});return(i,l)=>{const b=a("BaseInput"),n=a("FilterForm");return u(),f(n,{"init-form":r,form:c,onHide:l[1]||(l[1]=t=>v("hide"))},{default:e(()=>[C("div",O,[C("div",S,[o(b,{type:"text",modelValue:c.search,"onUpdate:modelValue":l[0]||(l[0]=t=>c.search=t),name:"search",label:i.$trans("general.search")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},q={name:"TeamConfigRoleList"},G=Object.assign(q,{props:{team:{type:Object,default(){return{name:""}}}},setup(d){const p=j(),v=H(),r=P("emitter"),c="team/role/",i=L(!1),l=T({}),b=n=>{Object.assign(l,n)};return(n,t)=>{const B=a("PageHeaderAction"),I=a("PageHeader"),F=a("ParentTransition"),$=a("DataCell"),y=a("FloatingMenuItem"),R=a("FloatingMenu"),k=a("DataRow"),D=a("BaseButton"),w=a("DataTable"),V=a("ListItem");return u(),f(V,{"init-url":c,uuid:m(p).params.uuid,onSetItems:b},{header:e(()=>[d.team.uuid?(u(),f(I,{key:0,title:n.$trans("team.config.role.role"),navs:[{label:n.$trans("team.team"),path:"TeamList"},{label:d.team.name,path:{name:"TeamShow",params:{uuid:d.team.uuid}}},{label:n.$trans("team.config.config"),path:"TeamConfig"}]},{default:e(()=>[o(B,{url:`teams/${d.team.uuid}/roles/`,name:"TeamConfigRole",title:n.$trans("team.config.role.role"),actions:["create","filter"],"dropdown-actions":["print","pdf","excel"],onToggleFilter:t[0]||(t[0]=s=>i.value=!i.value)},null,8,["url","title"])]),_:1},8,["title","navs"])):h("",!0)]),filter:e(()=>[o(F,{appear:"",visibility:i.value},{default:e(()=>[o(U,{onRefresh:t[1]||(t[1]=s=>m(r).emit("listItems")),onHide:t[2]||(t[2]=s=>i.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[o(F,{appear:"",visibility:!0},{default:e(()=>[o(w,{header:l.headers,meta:l.meta,module:"team.config.role",onRefresh:t[4]||(t[4]=s=>m(r).emit("listItems"))},{actionButton:e(()=>[o(D,{onClick:t[3]||(t[3]=s=>m(v).push({name:"TeamConfigRoleCreate"}))},{default:e(()=>[g(_(n.$trans("global.add",{attribute:n.$trans("team.config.role.role")})),1)]),_:1})]),default:e(()=>[(u(!0),A(M,null,N(l.data,s=>(u(),f(k,{key:s.uuid},{default:e(()=>[o($,{name:"name"},{default:e(()=>[g(_(s.label),1)]),_:2},1024),o($,{name:"createdAt"},{default:e(()=>[g(_(s.createdAt.formatted),1)]),_:2},1024),o($,{name:"action"},{default:e(()=>[s.isDefault?h("",!0):(u(),f(R,{key:0},{default:e(()=>[o(y,{icon:"fas fa-trash",onClick:E=>m(r).emit("deleteItem",{uuid:m(p).params.uuid,moduleUuid:s.uuid})},{default:e(()=>[g(_(n.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024))]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1},8,["uuid"])}}});export{G as default};
