import{i as H,u as N,h as $,j as F,l as M,r as n,a as b,o as l,e as o,w as e,f as t,q as u,b as m,d as C,F as v,v as O,s as i,t as s}from"./app-DvIo72ZO.js";const q={class:"space-y-2"},E={key:0},U={class:"grid grid-cols-1 gap-x-4 gap-y-8 px-4 pt-4 sm:grid-cols-2"},W={name:"AcademicCourseShow"},J=Object.assign(W,{setup(z){H();const _=N(),k=$(),a=F("$trans"),w={},V="academic/course/",A=[{key:"batch",label:a("academic.batch.batch"),visibility:!0},{key:"action",label:"",visibility:!0}],c=M({...w}),S=f=>{Object.assign(c,f)};return(f,d)=>{const D=n("PageHeaderAction"),x=n("PageHeader"),g=n("TextMuted"),r=n("ListItemView"),B=n("ListContainerVertical"),h=n("BaseCard"),y=n("DataCell"),L=n("DataRow"),T=n("SimpleTable"),j=n("BaseDataView"),I=n("DetailLayoutVertical"),P=n("ShowItem"),R=n("ParentTransition");return l(),b(v,null,[o(x,{title:t(a)(t(_).meta.trans,{attribute:t(a)(t(_).meta.label)}),navs:[{label:t(a)("academic.academic"),path:"Academic"},{label:t(a)("academic.course.course"),path:"AcademicCourseList"}]},{default:e(()=>[o(D,{name:"AcademicCourse",title:t(a)("academic.course.course"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),o(R,{appear:"",visibility:!0},{default:e(()=>[o(P,{"init-url":V,uuid:t(_).params.uuid,onSetItem:S,onRedirectTo:d[0]||(d[0]=p=>t(k).push({name:"AcademicCourse"}))},{default:e(()=>[c.uuid?(l(),u(I,{key:0},{detail:e(()=>[C("div",q,[o(h,{"no-padding":"","no-content-padding":""},{title:e(()=>[i(s(t(a)("academic.course.course")),1)]),action:e(()=>d[1]||(d[1]=[])),default:e(()=>[o(B,null,{default:e(()=>[o(r,{label:t(a)("academic.course.props.name")},{default:e(()=>[i(s(c.name)+" ",1),c.term?(l(),b("span",E,"("+s(c.term)+")",1)):m("",!0),c.pgAccount?(l(),u(g,{key:1,block:""},{default:e(()=>[i(s(c.pgAccount),1)]),_:1})):m("",!0)]),_:1},8,["label"]),o(r,{label:t(a)("academic.division.division")},{default:e(()=>[i(s(c.division.name),1)]),_:1},8,["label"]),o(r,{label:t(a)("academic.course.props.code")},{default:e(()=>[i(s(c.code)+" ",1),o(g,{block:""},{default:e(()=>[i(s(c.shortcode),1)]),_:1})]),_:1},8,["label"]),o(r,{label:t(a)("academic.course.props.batch_with_same_subject")},{default:e(()=>[i(s(c.batchWithSameSubject?t(a)("general.yes"):t(a)("general.no")),1)]),_:1},8,["label"]),o(r,{label:t(a)("general.created_at")},{default:e(()=>[i(s(c.createdAt.formatted),1)]),_:1},8,["label"]),o(r,{label:t(a)("general.updated_at")},{default:e(()=>[i(s(c.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})])]),default:e(()=>[o(h,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:e(()=>[i(s(t(a)("global.detail",{attribute:t(a)("academic.course.course")})),1)]),default:e(()=>[c.batches.length>0?(l(),u(T,{key:0,header:A},{default:e(()=>[(l(!0),b(v,null,O(c.batches,p=>(l(),u(L,{key:p.uuid},{default:e(()=>[o(y,{name:"batch"},{default:e(()=>[i(s(p.name),1)]),_:2},1024),o(y,{name:"action"})]),_:2},1024))),128))]),_:1})):m("",!0),C("dl",U,[o(j,{class:"col-span-1 sm:col-span-2",label:t(a)("academic.course.props.description")},{default:e(()=>[i(s(c.description),1)]),_:1},8,["label"])])]),_:1})]),_:1})):m("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{J as default};
