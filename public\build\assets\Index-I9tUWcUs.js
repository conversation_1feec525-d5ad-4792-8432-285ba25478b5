import{u as R,j as A,l as B,I as j,n as S,r as u,q as v,o as _,w as l,d as c,e as a,b as C,s as m,t as s,i as O,y as I,m as k,a as w,f as N,F as q,v as M}from"./app-DvIo72ZO.js";const E={class:"grid grid-cols-3 gap-6"},W={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={__name:"Filter",props:{initUrl:{type:String,default:""}},emits:["hide"],setup(T,{emit:h}){const f=R();A("moment");const V=h,y=T,g={codeNumber:"",name:"",batches:[],minTotal:"",minPaid:"",minConcession:"",minBalance:"",groups:[],address:""},t=B({...g});j(y.initUrl);const d=B({isLoaded:!(f.query.batches||f.query.groups)});return S(async()=>{d.batches=f.query.batches?f.query.batches.split(","):[],d.groups=f.query.groups?f.query.groups.split(","):[],d.isLoaded=!0}),(o,n)=>{const r=u("BaseInput"),b=u("BaseSelectSearch"),F=u("FilterForm");return _(),v(F,{"init-form":g,multiple:["batches","groups"],form:t,onHide:n[9]||(n[9]=e=>V("hide"))},{default:l(()=>[c("div",E,[c("div",W,[a(r,{type:"text",modelValue:t.codeNumber,"onUpdate:modelValue":n[0]||(n[0]=e=>t.codeNumber=e),name:"codeNumber",label:o.$trans("student.admission.props.code_number")},null,8,["modelValue","label"])]),c("div",z,[a(r,{type:"text",modelValue:t.name,"onUpdate:modelValue":n[1]||(n[1]=e=>t.name=e),name:"name",label:o.$trans("contact.props.name")},null,8,["modelValue","label"])]),c("div",G,[d.isLoaded?(_(),v(b,{key:0,multiple:"",name:"batches",label:o.$trans("global.select",{attribute:o.$trans("academic.batch.batch")}),modelValue:t.batches,"onUpdate:modelValue":n[2]||(n[2]=e=>t.batches=e),"value-prop":"uuid","init-search":d.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:l(e=>[m(s(e.value.course.name)+" "+s(e.value.name),1)]),listOption:l(e=>[m(s(e.option.course.nameWithTerm)+" "+s(e.option.name),1)]),_:1},8,["label","modelValue","init-search"])):C("",!0)]),c("div",J,[a(r,{type:"text",modelValue:t.minTotal,"onUpdate:modelValue":n[3]||(n[3]=e=>t.minTotal=e),name:"minTotal",label:o.$trans("global.min_item",{attribute:o.$trans("finance.fee.total")})},null,8,["modelValue","label"])]),c("div",K,[a(r,{type:"text",modelValue:t.minPaid,"onUpdate:modelValue":n[4]||(n[4]=e=>t.minPaid=e),name:"minPaid",label:o.$trans("global.min_item",{attribute:o.$trans("finance.fee.paid")})},null,8,["modelValue","label"])]),c("div",Q,[a(r,{type:"text",modelValue:t.minConcession,"onUpdate:modelValue":n[5]||(n[5]=e=>t.minConcession=e),name:"minConcession",label:o.$trans("global.min_item",{attribute:o.$trans("finance.fee.concession")})},null,8,["modelValue","label"])]),c("div",X,[a(r,{type:"text",modelValue:t.minBalance,"onUpdate:modelValue":n[6]||(n[6]=e=>t.minBalance=e),name:"minBalance",label:o.$trans("global.min_item",{attribute:o.$trans("finance.fee.balance")})},null,8,["modelValue","label"])]),c("div",Y,[d.isLoaded?(_(),v(b,{key:0,multiple:"",modelValue:t.groups,"onUpdate:modelValue":n[7]||(n[7]=e=>t.groups=e),name:"groups","label-prop":"name","value-prop":"uuid",label:o.$trans("student.group.group"),"init-search":d.groups,"search-action":"option/list","additional-search-query":{type:"student_group"}},null,8,["modelValue","label","init-search"])):C("",!0)]),c("div",Z,[a(r,{type:"text",modelValue:t.address,"onUpdate:modelValue":n[8]||(n[8]=e=>t.address=e),name:"address",label:o.$trans("contact.props.address.address")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},ee={name:"FinanceReportFeeSummary"},te=Object.assign(ee,{setup(T){const h=R(),f=O();let V=["filter"],y=[];I("finance:export")&&(y=["print","pdf","excel"]);const g="finance/report/",t=k(!1),d=k(!1),o=B({headers:[],data:[],meta:{total:0}}),n=async()=>{d.value=!0,await f.dispatch(g+"fetchReport",{name:"fee-summary",params:h.query}).then(r=>{d.value=!1,Object.assign(o,r)}).catch(r=>{d.value=!1})};return S(async()=>{await n()}),(r,b)=>{const F=u("PageHeaderAction"),e=u("PageHeader"),U=u("ParentTransition"),p=u("DataCell"),$=u("TextMuted"),D=u("DataRow"),P=u("DataTable"),H=u("BaseCard");return _(),w(q,null,[a(e,{title:r.$trans(N(h).meta.label),navs:[{label:r.$trans("finance.finance"),path:"Finance"},{label:r.$trans("finance.report.report"),path:"FinanceReport"}]},{default:l(()=>[a(F,{url:"finance/reports/fee-summary/",name:"FinanceReportFeeSummary",title:r.$trans("finance.report.fee_summary.fee_summary"),actions:N(V),"dropdown-actions":N(y),headers:o.headers,onToggleFilter:b[0]||(b[0]=i=>t.value=!t.value)},null,8,["title","actions","dropdown-actions","headers"])]),_:1},8,["title","navs"]),a(U,{appear:"",visibility:t.value},{default:l(()=>[a(x,{onAfterFilter:n,"init-url":g,onHide:b[1]||(b[1]=i=>t.value=!1)})]),_:1},8,["visibility"]),a(U,{appear:"",visibility:!0},{default:l(()=>[a(H,{"no-padding":"","no-content-padding":"","is-loading":d.value},{default:l(()=>[a(P,{header:o.headers,footer:o.footers,meta:o.meta,module:"finance.report.fee_summary",onRefresh:n},{default:l(()=>[(_(!0),w(q,null,M(o.data,(i,L)=>(_(),v(D,{key:i.uuid},{default:l(()=>[a(p,{name:"sno"},{default:l(()=>[m(s(o.meta.sno+L),1)]),_:2},1024),a(p,{name:"codeNumber"},{default:l(()=>[m(s(i.codeNumber),1)]),_:2},1024),a(p,{name:"name"},{default:l(()=>[m(s(i.name)+" ",1),a($,{block:""},{default:l(()=>[m(s(i.rollNumber),1)]),_:2},1024)]),_:2},1024),a(p,{name:"fatherName"},{default:l(()=>[m(s(i.fatherName)+" ",1),a($,{block:""},{default:l(()=>[m(s(i.contactNumber),1)]),_:2},1024)]),_:2},1024),a(p,{name:"course"},{default:l(()=>[m(s(i.courseName)+" ",1),a($,{block:""},{default:l(()=>[m(s(i.batchName),1)]),_:2},1024)]),_:2},1024),a(p,{name:"totalFee"},{default:l(()=>[m(s(i.total.formatted),1)]),_:2},1024),a(p,{name:"concessionFee"},{default:l(()=>[m(s(i.concession.formatted),1)]),_:2},1024),a(p,{name:"paidFee"},{default:l(()=>[m(s(i.paid.formatted),1)]),_:2},1024),a(p,{name:"balanceFee"},{default:l(()=>[m(s(i.balance.formatted),1)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","footer","meta"])]),_:1},8,["is-loading"])]),_:1})],64)}}});export{te as default};
