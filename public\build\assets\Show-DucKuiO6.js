import{i as j,u as $,h as F,j as g,l as O,r as l,a as p,o as d,e as t,w as e,f as o,q as v,b as m,d as _,F as q,v as z,s as r,t as s}from"./app-DvIo72ZO.js";const E={class:"space-y-2"},U={key:0},G={key:0},J=["innerHTML"],K=["innerHTML"],Q={class:"space-y-4"},W={key:1,class:"px-4 py-2"},X={class:"px-4 py-2"},Y={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},Z={name:"InventoryStockRequisitionShow"},ne=Object.assign(Z,{setup(ee){j();const y=$(),h=F(),a=g("$trans");g("emitter");const L={},S="inventory/stockRequisition/",T=[{key:"name",label:a("inventory.stock_item.props.name"),visibility:!0},{key:"quantity",label:a("inventory.stock_requisition.props.quantity"),visibility:!0}],n=O({...L}),V=b=>{Object.assign(n,b)};return(b,u)=>{const R=l("PageHeaderAction"),w=l("PageHeader"),c=l("ListItemView"),I=l("ListContainerVertical"),f=l("BaseCard"),x=l("TextMuted"),k=l("DataCell"),B=l("DataRow"),C=l("SimpleTable"),D=l("BaseAlert"),H=l("ListMedia"),M=l("BaseDataView"),N=l("DetailLayoutVertical"),A=l("ShowItem"),P=l("ParentTransition");return d(),p(q,null,[t(w,{title:o(a)(o(y).meta.trans,{attribute:o(a)(o(y).meta.label)}),navs:[{label:o(a)("inventory.inventory"),path:"Inventory"},{label:o(a)("inventory.stock_requisition.stock_requisition"),path:"InventoryStockRequisitionList"}]},{default:e(()=>[t(R,{name:"InventoryStockRequisition",title:o(a)("inventory.stock_requisition.stock_requisition"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(P,{appear:"",visibility:!0},{default:e(()=>[t(A,{"init-url":S,uuid:o(y).params.uuid,onSetItem:V,onRedirectTo:u[0]||(u[0]=i=>o(h).push({name:"InventoryStockRequisition"}))},{default:e(()=>[n.uuid?(d(),v(N,{key:0},{detail:e(()=>[_("div",E,[t(f,{"no-padding":"","no-content-padding":""},{title:e(()=>[r(s(o(a)("inventory.stock_requisition.props.code_number"))+" "+s(n.codeNumber),1)]),action:e(()=>u[1]||(u[1]=[])),default:e(()=>[t(I,null,{default:e(()=>[t(c,{label:o(a)("inventory.inventory")},{default:e(()=>{var i;return[r(s((i=n.inventory)==null?void 0:i.name),1)]}),_:1},8,["label"]),t(c,{label:o(a)("inventory.stock_requisition.props.voucher_number")},{default:e(()=>[r(s(n.voucherNumber),1)]),_:1},8,["label"]),t(c,{label:o(a)("inventory.vendor")},{default:e(()=>{var i;return[r(s(((i=n.vendor)==null?void 0:i.name)||"-")+" ",1),n.vendor?(d(),p("span",U,"("+s(n.vendor.type.name)+")",1)):m("",!0)]}),_:1},8,["label"]),t(c,{label:o(a)("inventory.place")},{default:e(()=>{var i;return[r(s(((i=n.place)==null?void 0:i.fullName)||"-"),1)]}),_:1},8,["label"]),t(c,{label:o(a)("employee.employee")},{default:e(()=>{var i;return[r(s(((i=n.employee)==null?void 0:i.name)||"-")+" ",1),n.employee?(d(),p("span",G,"("+s(n.employee.designation)+")",1)):m("",!0)]}),_:1},8,["label"]),t(c,{label:o(a)("inventory.stock_requisition.props.date")},{default:e(()=>[r(s(n.date.formatted),1)]),_:1},8,["label"]),t(c,{label:o(a)("inventory.stock_requisition.props.message_to_vendor")},{default:e(()=>[_("div",{innerHTML:n.messageToVendor},null,8,J)]),_:1},8,["label"]),t(c,{label:o(a)("inventory.stock_requisition.props.description")},{default:e(()=>[_("div",{innerHTML:n.description},null,8,K)]),_:1},8,["label"]),t(c,{label:o(a)("general.created_at")},{default:e(()=>[r(s(n.createdAt.formatted),1)]),_:1},8,["label"]),t(c,{label:o(a)("general.updated_at")},{default:e(()=>[r(s(n.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})])]),default:e(()=>[_("div",Q,[t(f,{"no-padding":"","no-content-padding":""},{title:e(()=>[r(s(o(a)("inventory.stock_requisition.props.items")),1)]),footer:e(()=>u[2]||(u[2]=[])),default:e(()=>[n.items.length>0?(d(),v(C,{key:0,header:T},{default:e(()=>[(d(!0),p(q,null,z(n.items,i=>(d(),v(B,{key:i.uuid},{default:e(()=>[t(k,{name:"name"},{default:e(()=>[r(s(i.item.name)+" ",1),t(x,{block:""},{default:e(()=>[r(s(i.description),1)]),_:2},1024)]),_:2},1024),t(k,{name:"quantity"},{default:e(()=>[r(s(i.quantity),1)]),_:2},1024)]),_:2},1024))),128))]),_:1})):m("",!0),n.items.length===0?(d(),p("div",W,[t(D,{design:"info",size:"xs"},{default:e(()=>[r(s(o(a)("general.errors.record_not_found")),1)]),_:1})])):m("",!0),_("div",X,[_("dl",Y,[t(M,{class:"col-span-1 sm:col-span-2"},{default:e(()=>[t(H,{media:n.media,url:`/app/inventory/stock-requisitions/${n.uuid}/`},null,8,["media","url"])]),_:1})])])]),_:1})])]),_:1})):m("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{ne as default};
