import{u as B,h as j,i as S,j as C,m as L,l as g,L as N,n as R,p as V,r as s,q as y,o as l,w as i,b as _,e as q,a as M,d as o,t as n,s as U}from"./app-DvIo72ZO.js";const D={key:0,class:"mb-4"},O={class:"-mt-12 flex w-full justify-center sm:-mt-16"},A=["src"],I={class:"mt-4 text-center text-white"},Q={class:"font-bold"},T={name:"EmployeeShow"},F=Object.assign(T,{setup(W){const r=B(),v=j(),b=S(),m=C("emitter"),w=[{name:"EmployeeShowBasic",icon:"fas fa-chevron-right",label:"general.basic"},{name:"EmployeeShowContact",icon:"fas fa-chevron-right",label:"contact.contact"},{name:"EmployeeRecord",icon:"fas fa-chevron-right",label:"employee.record.record"},{name:"EmployeeIncharge",icon:"fas fa-chevron-right",label:"employee.incharge.incharge"},{name:"EmployeeWorkShift",icon:"fas fa-chevron-right",label:"employee.attendance.work_shift.work_shift"},{name:"EmployeeShowLogin",icon:"fas fa-chevron-right",label:"contact.login.login"},{name:"EmployeeQualification",icon:"fas fa-chevron-right",label:"employee.qualification.qualification"},{name:"EmployeeDocument",icon:"fas fa-chevron-right",label:"employee.document.document"},{name:"EmployeeAccount",icon:"fas fa-chevron-right",label:"finance.account.account"},{name:"EmployeeExperience",icon:"fas fa-chevron-right",label:"employee.experience.experience"}],c=L(!1),e=g({contact:{}});g([]);const u=async()=>{c.value=!0,await b.dispatch("employee/get",{uuid:r.params.uuid}).then(a=>{Object.assign(e,a),c.value=!1}).catch(a=>{c.value=!1,v.push({name:"EmployeeList"})})};return N(()=>r.params.uuid,(a,t)=>{a!=null&&a!=t&&u()},{deep:!0}),R(async()=>{m.on("employeeUpdated",()=>{location.reload()}),await u()}),V(()=>{m.all.delete("employeeUpdated")}),(a,t)=>{const E=s("BaseLoader"),x=s("router-view"),k=s("ModuleConfig");return l(),y(k,{navigations:w,"use-uuid":""},{header:i(()=>[q(E,null,{default:i(()=>{var d,p,f,h;return[e.uuid?(l(),M("div",D,[t[1]||(t[1]=o("img",{class:"h-32 w-full object-cover lg:h-48",src:"/images/user-cover.jpeg",alt:""},null,-1)),o("div",O,[o("img",{class:"h-24 w-24 rounded-full ring-4 ring-white sm:h-32 sm:w-32",src:e.contact.photo,alt:""},null,8,A)]),o("div",I,[o("p",null,n(e.codeNumber),1),o("h1",Q,n(e.contact.name),1),o("p",null,n((p=(d=e.lastRecord)==null?void 0:d.department)==null?void 0:p.name)+" "+n((h=(f=e.lastRecord)==null?void 0:f.designation)==null?void 0:h.name),1),o("p",null,[t[0]||(t[0]=o("i",{class:"fas fa-mobile-alt"},null,-1)),U(" "+n(e.contact.contactNumber),1)])])])):_("",!0)]}),_:1})]),default:i(()=>[e.uuid?(l(),y(x,{key:0,employee:e},null,8,["employee"])):_("",!0)]),_:1})}}});export{F as default};
