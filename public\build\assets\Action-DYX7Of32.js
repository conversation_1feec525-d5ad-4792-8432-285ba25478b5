import{u as b,h as $,j as F,I as T,l as S,r as l,q as j,o as _,w as g,d as p,e as r,f as o,K as B,a as E,F as A}from"./app-DvIo72ZO.js";const k={class:"grid grid-cols-3 gap-4"},y={class:"col-span-3"},H={class:"col-span-3"},O={class:"col-span-3"},I={class:"col-span-3"},w={name:"SitePageForm"},x=Object.assign(w,{setup(f){b();const u=$(),n=F("$trans"),i={name:"",title:"",subTitle:"",content:""},m="site/page/",s=T(m),a=S({...i}),V=d=>{Object.assign(i,{...d}),Object.assign(a,B(i))},P=d=>{u.push({name:"SitePageEdit",params:{uuid:d.page.uuid}})};return(d,e)=>{const c=l("BaseInput"),v=l("MdEditor"),U=l("FormAction");return _(),j(U,{"keep-adding":!1,"init-url":m,"init-form":i,form:a,"set-form":V,"after-submit":P},{default:g(()=>[p("div",k,[p("div",y,[r(c,{type:"text",modelValue:a.name,"onUpdate:modelValue":e[0]||(e[0]=t=>a.name=t),name:"name",label:o(n)("site.page.props.name"),error:o(s).name,"onUpdate:error":e[1]||(e[1]=t=>o(s).name=t),autofocus:""},null,8,["modelValue","label","error"])]),p("div",H,[r(c,{type:"text",modelValue:a.title,"onUpdate:modelValue":e[2]||(e[2]=t=>a.title=t),name:"title",label:o(n)("site.page.props.title"),error:o(s).title,"onUpdate:error":e[3]||(e[3]=t=>o(s).title=t)},null,8,["modelValue","label","error"])]),p("div",O,[r(c,{type:"text",modelValue:a.subTitle,"onUpdate:modelValue":e[4]||(e[4]=t=>a.subTitle=t),name:"subTitle",label:o(n)("site.page.props.sub_title"),error:o(s).subTitle,"onUpdate:error":e[5]||(e[5]=t=>o(s).subTitle=t)},null,8,["modelValue","label","error"])]),p("div",I,[r(v,{label:o(n)("site.page.props.content"),modelValue:a.content,"onUpdate:modelValue":e[6]||(e[6]=t=>a.content=t),error:o(s).content,"onUpdate:error":e[7]||(e[7]=t=>o(s).content=t)},null,8,["label","modelValue","error"])])])]),_:1},8,["form"])}}}),C={name:"SitePageAction"},N=Object.assign(C,{setup(f){const u=b();return(n,i)=>{const m=l("PageHeaderAction"),s=l("PageHeader"),a=l("ParentTransition");return _(),E(A,null,[r(s,{title:n.$trans(o(u).meta.trans,{attribute:n.$trans(o(u).meta.label)}),navs:[{label:n.$trans("site.site"),path:"Site"},{label:n.$trans("site.page.page"),path:"SitePageList"}]},{default:g(()=>[r(m,{name:"SitePage",title:n.$trans("site.page.page"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),r(a,{appear:"",visibility:!0},{default:g(()=>[r(x)]),_:1})],64)}}});export{N as default};
