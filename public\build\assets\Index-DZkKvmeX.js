import{u as T,i as E,m as B,I as H,l as k,n as O,ad as f,r as u,q as R,o as w,w as m,d as n,e as l,f as s,s as U,a as D,b as $,t as p,F as P}from"./app-DvIo72ZO.js";import"./lodash-BPUmB9Gy.js";const L={class:"grid grid-cols-3 gap-6"},A={class:"col-span-3 sm:col-span-1"},N={key:0,class:"ml-1"},I={key:0,class:"ml-1"},M={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},G={class:"flex items-center gap-2"},J={class:"grid grid-cols-3 gap-6"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},ee={class:"mt-4 grid grid-cols-4 gap-6"},te={class:"col-span-3 sm:col-span-1"},ae={class:"mt-4 grid grid-cols-4 gap-6"},oe={class:"col-span-4 sm:col-span-1"},se={class:"col-span-4 sm:col-span-1"},re={class:"col-span-4 sm:col-span-1"},le={class:"col-span-4 sm:col-span-1"},ne={__name:"Filter",props:{initUrl:{type:String,default:""},preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(S,{emit:q}){const i=T();E();const W=q,x=S,v={exam:"",attempt:"first",batch:"",showCourseWise:!1,showSubjectDetail:!1,showWatermark:!1,showPrintDateTime:!1,showSno:!1,title:"",signatory1:"",signatory2:"",signatory3:"",signatory4:""};B(!1);const b=B(!1),o=H(x.initUrl),a=k({...v});k({exams:x.preRequisites.exams});const d=k({exam:"",batch:"",isLoaded:!(i.query.exam&&i.query.batch)});return O(async()=>{if(_.isEmpty(i.query)){d.isLoaded=!0;return}d.exam=i.query.exam,a.exam=i.query.exam,a.attempt=i.query.attempt,d.batch=i.query.batch,a.batch=i.query.batch,a.showCourseWise=f(i.query.showCourseWise||""),a.showSubjectDetail=f(i.query.showSubjectDetail||""),a.showWatermark=f(i.query.showWatermark||""),a.showPrintDateTime=f(i.query.showPrintDateTime||""),a.showSno=f(i.query.showSno||""),d.isLoaded=!0}),(r,t)=>{const c=u("BaseSelect"),F=u("BaseSelectSearch"),y=u("BaseSwitch"),g=u("BaseInput"),j=u("BaseFieldset"),C=u("FilterForm");return w(),R(C,{"init-form":v,multiple:[],form:a,onHide:t[27]||(t[27]=e=>W("hide"))},{default:m(()=>[n("div",L,[n("div",A,[l(c,{modelValue:a.exam,"onUpdate:modelValue":t[0]||(t[0]=e=>a.exam=e),name:"exam",label:r.$trans("exam.exam"),"value-prop":"uuid",options:S.preRequisites.exams,error:s(o).exam,"onUpdate:error":t[1]||(t[1]=e=>s(o).exam=e)},{selectedOption:m(e=>{var h,V;return[U(p(e.value.name)+" ",1),e.value.term?(w(),D("span",N,"("+p(((V=(h=e.value.term)==null?void 0:h.division)==null?void 0:V.name)||r.$trans("general.all"))+")",1)):$("",!0)]}),listOption:m(e=>{var h,V;return[U(p(e.option.name)+" ",1),e.option.term?(w(),D("span",I,"("+p(((V=(h=e.option.term)==null?void 0:h.division)==null?void 0:V.name)||r.$trans("general.all"))+")",1)):$("",!0)]}),_:1},8,["modelValue","label","options","error"])]),n("div",M,[d.isLoaded?(w(),R(c,{key:0,modelValue:a.attempt,"onUpdate:modelValue":t[2]||(t[2]=e=>a.attempt=e),name:"attempt",label:r.$trans("exam.schedule.props.attempt"),options:S.preRequisites.attempts,error:s(o).attempt,"onUpdate:error":t[3]||(t[3]=e=>s(o).attempt=e)},null,8,["modelValue","label","options","error"])):$("",!0)]),n("div",z,[d.isLoaded?(w(),R(F,{key:0,name:"batch",label:r.$trans("global.select",{attribute:r.$trans("academic.batch.batch")}),modelValue:a.batch,"onUpdate:modelValue":t[4]||(t[4]=e=>a.batch=e),error:s(o).batch,"onUpdate:error":t[5]||(t[5]=e=>s(o).batch=e),"value-prop":"uuid","init-search":d.batch,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:m(e=>[U(p(e.value.course.name)+" "+p(e.value.name),1)]),listOption:m(e=>[U(p(e.option.course.nameWithTerm)+" "+p(e.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):$("",!0)])]),l(j,{class:"mt-4"},{legend:m(()=>[n("div",G,[U(p(r.$trans("global.show",{attribute:r.$trans("general.options")}))+" ",1),l(y,{reverse:"",modelValue:b.value,"onUpdate:modelValue":t[6]||(t[6]=e=>b.value=e),name:"showOptions"},null,8,["modelValue"])])]),default:m(()=>[b.value?(w(),D(P,{key:0},[n("div",J,[n("div",K,[l(y,{vertical:"",modelValue:a.showCourseWise,"onUpdate:modelValue":t[7]||(t[7]=e=>a.showCourseWise=e),name:"showCourseWise",label:r.$trans("exam.report.course_wise"),error:s(o).showCourseWise,"onUpdate:error":t[8]||(t[8]=e=>s(o).showCourseWise=e)},null,8,["modelValue","label","error"])]),n("div",Q,[l(y,{vertical:"",modelValue:a.showSubjectDetail,"onUpdate:modelValue":t[9]||(t[9]=e=>a.showSubjectDetail=e),name:"showSubjectDetail",label:r.$trans("global.show",{attribute:r.$trans("academic.subject.subject")}),error:s(o).showSubjectDetail,"onUpdate:error":t[10]||(t[10]=e=>s(o).showSubjectDetail=e)},null,8,["modelValue","label","error"])]),n("div",X,[l(y,{vertical:"",modelValue:a.showWatermark,"onUpdate:modelValue":t[11]||(t[11]=e=>a.showWatermark=e),name:"showWatermark",label:r.$trans("global.show",{attribute:r.$trans("print.watermark")}),error:s(o).showWatermark,"onUpdate:error":t[12]||(t[12]=e=>s(o).showWatermark=e)},null,8,["modelValue","label","error"])]),n("div",Y,[l(y,{vertical:"",modelValue:a.showSno,"onUpdate:modelValue":t[13]||(t[13]=e=>a.showSno=e),name:"showSno",label:r.$trans("global.show",{attribute:r.$trans("general.sno")}),error:s(o).showSno,"onUpdate:error":t[14]||(t[14]=e=>s(o).showSno=e)},null,8,["modelValue","label","error"])]),n("div",Z,[l(y,{vertical:"",modelValue:a.showPrintDateTime,"onUpdate:modelValue":t[15]||(t[15]=e=>a.showPrintDateTime=e),name:"showPrintDateTime",label:r.$trans("global.show",{attribute:r.$trans("general.print_date_time")}),error:s(o).showPrintDateTime,"onUpdate:error":t[16]||(t[16]=e=>s(o).showPrintDateTime=e)},null,8,["modelValue","label","error"])])]),n("div",ee,[n("div",te,[l(g,{type:"text",modelValue:a.title,"onUpdate:modelValue":t[17]||(t[17]=e=>a.title=e),name:"title",label:r.$trans("print.title"),error:s(o).title,"onUpdate:error":t[18]||(t[18]=e=>s(o).title=e)},null,8,["modelValue","label","error"])])]),n("div",ae,[n("div",oe,[l(g,{type:"text",modelValue:a.signatory1,"onUpdate:modelValue":t[19]||(t[19]=e=>a.signatory1=e),name:"signatory1",label:r.$trans("print.signatory1"),error:s(o).signatory1,"onUpdate:error":t[20]||(t[20]=e=>s(o).signatory1=e)},null,8,["modelValue","label","error"])]),n("div",se,[l(g,{type:"text",modelValue:a.signatory2,"onUpdate:modelValue":t[21]||(t[21]=e=>a.signatory2=e),name:"signatory2",label:r.$trans("print.signatory2"),error:s(o).signatory2,"onUpdate:error":t[22]||(t[22]=e=>s(o).signatory2=e)},null,8,["modelValue","label","error"])]),n("div",re,[l(g,{type:"text",modelValue:a.signatory3,"onUpdate:modelValue":t[23]||(t[23]=e=>a.signatory3=e),name:"signatory3",label:r.$trans("print.signatory3"),error:s(o).signatory3,"onUpdate:error":t[24]||(t[24]=e=>s(o).signatory3=e)},null,8,["modelValue","label","error"])]),n("div",le,[l(g,{type:"text",modelValue:a.signatory4,"onUpdate:modelValue":t[25]||(t[25]=e=>a.signatory4=e),name:"signatory4",label:r.$trans("print.signatory4"),error:s(o).signatory4,"onUpdate:error":t[26]||(t[26]=e=>s(o).signatory4=e)},null,8,["modelValue","label","error"])])])],64)):$("",!0)]),_:1})]),_:1},8,["form"])}}},ie={name:"ExamReportExamSummary"},de=Object.assign(ie,{setup(S){const q=T(),i=E();let W=["filter"],x=[];const v="exam/report/",b=B(!0),o=B(!1),a=k({exams:[],attempts:[]}),d=async()=>{o.value=!0,await i.dispatch(v+"preRequisite",{name:"exam-summary"}).then(t=>{o.value=!1,Object.assign(a,t)}).catch(t=>{o.value=!1})},r=async()=>{o.value=!0,await i.dispatch(v+"fetchReport",{name:"exam-summary",params:q.query}).then(t=>{o.value=!1,window.open("/print").document.write(t)}).catch(t=>{o.value=!1})};return O(async()=>{await d()}),(t,c)=>{const F=u("PageHeaderAction"),y=u("PageHeader"),g=u("ParentTransition"),j=u("BaseCard");return w(),D(P,null,[l(y,{title:t.$trans(s(q).meta.label),navs:[{label:t.$trans("exam.exam"),path:"Exam"},{label:t.$trans("exam.report.report"),path:"ExamReport"}]},{default:m(()=>[l(F,{url:"exam/reports/exam-summary/",name:"ExamReportExamSummary",title:t.$trans("exam.report.exam_summary.exam_summary"),actions:s(W),"dropdown-actions":s(x),onToggleFilter:c[0]||(c[0]=C=>b.value=!b.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"]),l(g,{appear:"",visibility:b.value},{default:m(()=>[l(ne,{onAfterFilter:r,"init-url":v,"pre-requisites":a,onHide:c[1]||(c[1]=C=>b.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"]),l(g,{appear:"",visibility:!0},{default:m(()=>[l(j,{"no-padding":"","no-content-padding":"","is-loading":o.value},null,8,["is-loading"])]),_:1})],64)}}});export{de as default};
