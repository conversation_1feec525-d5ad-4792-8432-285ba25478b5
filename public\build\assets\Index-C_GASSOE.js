import{u as J,l as E,n as K,r as s,q as f,o,w as t,d as i,e as n,b as g,h as Q,j as W,y as h,m as X,f as u,B as Y,a as w,F as T,v as S,t as a,am as Z,s as r}from"./app-DvIo72ZO.js";const ee={class:"grid grid-cols-3 gap-6"},te={class:"col-span-3 sm:col-span-1"},ae={class:"col-span-3 sm:col-span-1"},ne={class:"col-span-3 sm:col-span-1"},oe={class:"col-span-3 sm:col-span-1"},se={class:"col-span-3 sm:col-span-1"},le={__name:"Filter",emits:["hide"],setup(x,{emit:$}){const k=J(),C=$,D={codeNumber:"",title:"",types:[],for:"",startDate:"",endDate:""},p=E({...D}),v=E({types:[],isLoaded:!k.query.types});return K(async()=>{v.types=k.query.types?k.query.types.split(","):[],v.isLoaded=!0}),(d,c)=>{const m=s("BaseInput"),_=s("BaseSelectSearch"),F=s("BaseSelect"),I=s("DatePicker"),B=s("FilterForm");return o(),f(B,{"init-form":D,form:p,multiple:["types"],onHide:c[6]||(c[6]=l=>C("hide"))},{default:t(()=>[i("div",ee,[i("div",te,[n(m,{type:"text",modelValue:p.codeNumber,"onUpdate:modelValue":c[0]||(c[0]=l=>p.codeNumber=l),name:"codeNumber",label:d.$trans("calendar.event.props.code_number")},null,8,["modelValue","label"])]),i("div",ae,[n(m,{type:"text",modelValue:p.title,"onUpdate:modelValue":c[1]||(c[1]=l=>p.title=l),name:"title",label:d.$trans("calendar.event.props.title")},null,8,["modelValue","label"])]),i("div",ne,[v.isLoaded?(o(),f(_,{key:0,multiple:"",name:"types",label:d.$trans("global.select",{attribute:d.$trans("calendar.event.type.type")}),modelValue:p.types,"onUpdate:modelValue":c[2]||(c[2]=l=>p.types=l),"value-prop":"uuid","init-search":v.types,"search-action":"option/list","additional-search-query":{type:"event_type"}},null,8,["label","modelValue","init-search"])):g("",!0)]),i("div",oe,[n(F,{modelValue:p.for,"onUpdate:modelValue":c[3]||(c[3]=l=>p.for=l),name:"for",label:d.$trans("calendar.event.props.for"),options:[{label:d.$trans("calendar.event.props.for_alumni"),value:"for_alumni"},{label:d.$trans("calendar.event.props.is_public"),value:"is_public"}]},null,8,["modelValue","label","options"])]),i("div",se,[n(I,{start:p.startDate,"onUpdate:start":c[4]||(c[4]=l=>p.startDate=l),end:p.endDate,"onUpdate:end":c[5]||(c[5]=l=>p.endDate=l),name:"dateBetween",as:"range",label:d.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},re={class:"grid grid-cols-1 gap-4 px-4 pt-4 md:grid-cols-2 lg:grid-cols-3"},ie=["onClick"],de=["src"],ue={class:"px-2 py-2 text-gray-800 dark:text-gray-400"},ce={class:"flex items-center justify-between"},me={class:"font-medium"},pe={class:"text-sm"},fe={class:"mt-2 flex items-center"},_e={class:"mt-2 text-xs"},ye={class:"text-xl font-semibold"},ge={name:"CalendarEventList"},be=Object.assign(ge,{setup(x){const $=Q(),k=W("emitter");let C=["filter"];h("calendar:config")&&C.push("config"),h("event:create")&&C.unshift("create");let D=[];h("event:export")&&(D=["print","pdf","excel"]);const p="calendar/event/",v=X(!1),d=E({}),c=m=>{Object.assign(d,m)};return(m,_)=>{const F=s("PageHeaderAction"),I=s("PageHeader"),B=s("ParentTransition"),l=s("TextMuted"),M=s("CardView"),R=s("Pagination"),U=s("CardList"),b=s("DataCell"),H=s("BaseBadge"),V=s("FloatingMenuItem"),j=s("FloatingMenu"),q=s("DataRow"),O=s("BaseButton"),z=s("DataTable"),G=s("ListItem");return o(),f(G,{"init-url":p,onSetItems:c},{header:t(()=>[n(I,{title:m.$trans("calendar.event.event"),navs:[{label:m.$trans("calendar.calendar"),path:"Calendar"}]},{default:t(()=>[n(F,{url:"calendar/events/",name:"CalendarEvent",title:m.$trans("calendar.event.event"),actions:u(C),"dropdown-actions":u(D),"config-path":"CalendarConfig",onToggleFilter:_[0]||(_[0]=e=>v.value=!v.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:t(()=>[n(B,{appear:"",visibility:v.value},{default:t(()=>[n(le,{onRefresh:_[1]||(_[1]=e=>u(k).emit("listItems")),onHide:_[2]||(_[2]=e=>v.value=!1)})]),_:1},8,["visibility"])]),default:t(()=>[u(Y)(["student","guardian"],"any")?(o(),f(B,{key:0,appear:"",visibility:!0},{default:t(()=>[n(U,{header:d.headers,meta:d.meta},{content:t(()=>[i("div",ye,a(m.$trans("dashboard.nothing_to_show")),1)]),default:t(()=>[i("div",re,[(o(!0),w(T,null,S(d.data,e=>(o(),w("div",{key:e.uuid,class:"cursor-pointer",onClick:y=>u($).push({name:"CalendarEventShow",params:{uuid:e.uuid}})},[i("div",null,[i("img",{class:"rounded-tl-lg rounded-tr-lg",src:e.coverImage},null,8,de)]),n(M,{"no-padding":"","rounded-bottom":""},{default:t(()=>[i("div",ue,[i("div",ce,[i("span",me,a(e.title),1),i("span",pe,a(e.duration),1)]),i("div",fe,[i("span",{class:"px-2 py-1 text-gray-50 text-xs rounded-lg mr-2",style:Z(`background-color: ${e.type.color}`)},a(e.type.name),5),e.venue?(o(),f(l,{key:0,block:""},{default:t(()=>[r(" @ "+a(e.venue),1)]),_:2},1024)):g("",!0)]),i("p",_e,a(e.durationInDetail),1)])]),_:2},1024)],8,ie))),128))]),i("div",null,[n(R,{"card-view":"",meta:d.meta,onRefresh:_[3]||(_[3]=e=>u(k).emit("listItems"))},null,8,["meta"])])]),_:1},8,["header","meta"])]),_:1})):(o(),f(B,{key:1,appear:"",visibility:!0},{default:t(()=>[n(z,{header:d.headers,meta:d.meta,module:"calendar.event",onRefresh:_[5]||(_[5]=e=>u(k).emit("listItems"))},{actionButton:t(()=>[u(h)("event:create")?(o(),f(O,{key:0,onClick:_[4]||(_[4]=e=>u($).push({name:"CalendarEventCreate"}))},{default:t(()=>[r(a(m.$trans("global.add",{attribute:m.$trans("calendar.event.event")})),1)]),_:1})):g("",!0)]),default:t(()=>[(o(!0),w(T,null,S(d.data,e=>(o(),f(q,{key:e.uuid,onDoubleClick:y=>u($).push({name:"CalendarEventShow",params:{uuid:e.uuid}})},{default:t(()=>[n(b,{name:"codeNumber"},{default:t(()=>[r(a(e.codeNumber),1)]),_:2},1024),n(b,{name:"type"},{default:t(()=>[r(a(e.type.name),1)]),_:2},1024),n(b,{name:"title"},{default:t(()=>[r(a(e.title)+" ",1),n(l,{block:""},{default:t(()=>[r(a(e.venue),1)]),_:2},1024)]),_:2},1024),n(b,{name:"startDate"},{default:t(()=>[r(a(e.startDate.formatted)+" ",1),e.startTime.value?(o(),f(l,{key:0,block:""},{default:t(()=>[r(a(e.startTime.formatted),1)]),_:2},1024)):g("",!0)]),_:2},1024),n(b,{name:"endDate"},{default:t(()=>{var y;return[r(a(((y=e.endDate)==null?void 0:y.formatted)||"-")+" ",1),e.endTime.value?(o(),f(l,{key:0,block:""},{default:t(()=>[r(a(e.endTime.formatted),1)]),_:2},1024)):g("",!0)]}),_:2},1024),n(b,{name:"audience"},{default:t(()=>[e.forAlumni?(o(),f(H,{key:0},{default:t(()=>[r(a(m.$trans("calendar.event.props.for_alumni")),1)]),_:1})):g("",!0),(o(!0),w(T,null,S(e.audienceTypes,y=>(o(),w("div",null,a(y),1))),256))]),_:2},1024),n(b,{name:"incharge"},{default:t(()=>{var y,N,P;return[r(a(((N=(y=e.incharge)==null?void 0:y.employee)==null?void 0:N.name)||"-")+" ",1),(P=e.incharge)!=null&&P.employee?(o(),f(l,{key:0,block:""},{default:t(()=>{var L,A;return[r(a((A=(L=e.incharge)==null?void 0:L.employee)==null?void 0:A.designation),1)]}),_:2},1024)):g("",!0)]}),_:2},1024),n(b,{name:"createdAt"},{default:t(()=>[r(a(e.createdAt.formatted),1)]),_:2},1024),n(b,{name:"action"},{default:t(()=>[n(j,null,{default:t(()=>[n(V,{icon:"fas fa-arrow-circle-right",onClick:y=>u($).push({name:"CalendarEventShow",params:{uuid:e.uuid}})},{default:t(()=>[r(a(m.$trans("general.show")),1)]),_:2},1032,["onClick"]),u(h)("event:edit")?(o(),f(V,{key:0,icon:"fas fa-edit",onClick:y=>u($).push({name:"CalendarEventEdit",params:{uuid:e.uuid}})},{default:t(()=>[r(a(m.$trans("general.edit")),1)]),_:2},1032,["onClick"])):g("",!0),u(h)("event:create")?(o(),f(V,{key:1,icon:"fas fa-copy",onClick:y=>u($).push({name:"CalendarEventDuplicate",params:{uuid:e.uuid}})},{default:t(()=>[r(a(m.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):g("",!0),u(h)("event:delete")?(o(),f(V,{key:2,icon:"fas fa-trash",onClick:y=>u(k).emit("deleteItem",{uuid:e.uuid})},{default:t(()=>[r(a(m.$trans("general.delete")),1)]),_:2},1032,["onClick"])):g("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1}))]),_:1})}}});export{be as default};
