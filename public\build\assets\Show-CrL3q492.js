import{j as E,I as Q,l as L,r as u,q as _,o as d,w as e,d as c,e as o,f as t,i as W,u as X,h as Y,z as Z,a as U,b as y,F,v as N,s as r,t as s,A as ee,y as $}from"./app-DvIo72ZO.js";const te={class:"grid grid-cols-3 gap-6"},oe={class:"col-span-3 sm:col-span-1"},ne={class:"col-span-3 sm:col-span-1"},ae={class:"col-span-3 sm:col-span-1"},le={class:"col-span-3"},ie={name:"EnquiryFollowUpForm"},se=Object.assign(ie,{props:{uuid:{type:String,default:""}},emits:["completed"],setup(P,{emit:q}){const v=q,n=E("emitter"),k={followUpDate:null,nextFollowUpDate:null,status:"",remarks:""},V="reception/enquiryFollowUp/",b=Q(V),x=L({statuses:[]}),f=L({...k}),a=w=>{Object.assign(x,w)},R=()=>{v("completed"),n.emit("listItems")};return(w,l)=>{const B=u("DatePicker"),S=u("BaseSelect"),h=u("BaseTextarea"),m=u("FormAction");return d(),_(m,{"pre-requisites":!0,onSetPreRequisites:a,"init-url":V,uuid:P.uuid,"no-data-fetch":"",action:"create","init-form":k,form:f,"keep-adding":!1,"after-submit":R},{default:e(()=>[c("div",te,[c("div",oe,[o(B,{modelValue:f.followUpDate,"onUpdate:modelValue":l[0]||(l[0]=p=>f.followUpDate=p),name:"followUpDate",label:w.$trans("reception.enquiry.follow_up.props.follow_up_date"),"no-clear":"",error:t(b).followUpDate,"onUpdate:error":l[1]||(l[1]=p=>t(b).followUpDate=p)},null,8,["modelValue","label","error"])]),c("div",ne,[o(B,{modelValue:f.nextFollowUpDate,"onUpdate:modelValue":l[2]||(l[2]=p=>f.nextFollowUpDate=p),name:"nextFollowUpDate",label:w.$trans("reception.enquiry.follow_up.props.next_follow_up_date"),error:t(b).nextFollowUpDate,"onUpdate:error":l[3]||(l[3]=p=>t(b).nextFollowUpDate=p)},null,8,["modelValue","label","error"])]),c("div",ae,[o(S,{name:"status",label:w.$trans("reception.enquiry.follow_up.props.status"),modelValue:f.status,"onUpdate:modelValue":l[4]||(l[4]=p=>f.status=p),"label-prop":"label","value-prop":"value",options:x.statuses,error:t(b).status,"onUpdate:error":l[5]||(l[5]=p=>t(b).status=p)},null,8,["label","modelValue","options","error"])]),c("div",le,[o(h,{rows:1,modelValue:f.remarks,"onUpdate:modelValue":l[6]||(l[6]=p=>f.remarks=p),name:"remarks",label:w.$trans("reception.enquiry.follow_up.props.remarks"),error:t(b).remarks,"onUpdate:error":l[7]||(l[7]=p=>t(b).remarks=p)},null,8,["modelValue","label","error"])])])]),_:1},8,["uuid","form"])}}}),re={class:"space-y-4"},ue={class:"flex items-center gap-2"},pe={key:0},de=["onClick"],ce={class:"p-4"},me={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},_e={class:"text-xs"},fe={name:"ReceptionEnquiryShow"},be=Object.assign(fe,{setup(P){W();const q=X(),v=Y(),n=E("$trans"),k=E("emitter"),V={},b="reception/enquiry/",x=[{key:"studentName",label:n("reception.enquiry.props.student_name"),visibility:!0},{key:"birthDate",label:n("contact.props.birth_date"),visibility:!0},{key:"gender",label:n("contact.props.gender"),visibility:!0},{key:"course",label:n("academic.course.course"),visibility:!0},{key:"action",label:"",visibility:!0}],f=[{key:"followUpDate",label:n("reception.enquiry.follow_up.props.follow_up_date"),visibility:!0},{key:"nextFollowUpDate",label:n("reception.enquiry.follow_up.props.next_follow_up_date"),visibility:!0},{key:"status",label:n("reception.enquiry.follow_up.props.status"),visibility:!0},{key:"action",label:"",visibility:!0}],a=L({...V}),R=w=>{Object.assign(a,w)};return(w,l)=>{const B=u("PageHeaderAction"),S=u("PageHeader"),h=u("BaseBadge"),m=u("ListItemView"),p=u("TextMuted"),j=u("ListContainerVertical"),C=u("BaseCard"),g=u("DataCell"),I=u("BaseButton"),T=u("DataRow"),A=u("SimpleTable"),H=u("ListMedia"),M=u("BaseDataView"),O=u("ShowButton"),z=u("DetailLayoutVertical"),G=u("ShowItem"),J=u("ParentTransition"),K=Z("tooltip");return d(),U(F,null,[o(S,{title:t(n)(t(q).meta.trans,{attribute:t(n)(t(q).meta.label)}),navs:[{label:t(n)("reception.reception"),path:"Reception"},{label:t(n)("reception.enquiry.enquiry"),path:"ReceptionEnquiryList"}]},{default:e(()=>[o(B,{name:"ReceptionEnquiry",title:t(n)("reception.enquiry.enquiry"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),o(J,{appear:"",visibility:!0},{default:e(()=>[o(G,{"init-url":b,uuid:t(q).params.uuid,onSetItem:R,onRedirectTo:l[2]||(l[2]=i=>t(v).push({name:"ReceptionEnquiry"}))},{default:e(()=>[a.uuid?(d(),_(z,{key:0},{detail:e(()=>[o(C,{"no-padding":"","no-content-padding":""},{title:e(()=>[r(s(t(n)("global.detail",{attribute:t(n)("reception.enquiry.enquiry")}))+" ",1),o(h,{design:a.status.color},{default:e(()=>[r(s(a.status.label),1)]),_:1},8,["design"])]),default:e(()=>[o(j,null,{default:e(()=>[o(m,{label:t(n)("reception.enquiry.props.code_number")},{default:e(()=>[r(s(a.codeNumber),1)]),_:1},8,["label"]),o(m,{label:t(n)("reception.enquiry.props.date")},{default:e(()=>[r(s(a.date.formatted),1)]),_:1},8,["label"]),o(m,{label:t(n)("reception.enquiry.type.type")},{default:e(()=>{var i;return[r(s(((i=a.type)==null?void 0:i.name)||"-"),1)]}),_:1},8,["label"]),o(m,{label:t(n)("reception.enquiry.source.source")},{default:e(()=>{var i;return[r(s(((i=a.source)==null?void 0:i.name)||"-"),1)]}),_:1},8,["label"]),o(m,{label:t(n)("reception.enquiry.props.name")},{default:e(()=>[r(s(a.name),1)]),_:1},8,["label"]),o(m,{label:t(n)("reception.enquiry.props.email")},{default:e(()=>[r(s(a.email),1)]),_:1},8,["label"]),o(m,{label:t(n)("reception.enquiry.props.contact_number")},{default:e(()=>[r(s(a.contactNumber),1)]),_:1},8,["label"]),o(m,{label:t(n)("reception.enquiry.props.assigned_to")},{default:e(()=>{var i;return[r(s(((i=a.employee)==null?void 0:i.name)||"-")+" ",1),a.employee?(d(),_(p,{key:0,block:""},{default:e(()=>{var D;return[r(s(((D=a.employee)==null?void 0:D.designation)||"-"),1)]}),_:1})):y("",!0)]}),_:1},8,["label"]),o(m,{label:t(n)("reception.enquiry.props.remarks")},{default:e(()=>[r(s(a.remarks),1)]),_:1},8,["label"]),o(m,{label:t(n)("general.created_at")},{default:e(()=>[r(s(a.createdAt.formatted),1)]),_:1},8,["label"]),o(m,{label:t(n)("general.updated_at")},{default:e(()=>[r(s(a.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})]),default:e(()=>[c("div",re,[o(C,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:e(()=>[r(s(t(n)("reception.enquiry.enquiry")),1)]),footer:e(()=>[o(O,null,{default:e(()=>[t($)("enquiry:edit")?(d(),_(I,{key:0,design:"primary",onClick:l[0]||(l[0]=i=>t(v).push({name:"ReceptionEnquiryEdit",params:{uuid:a.uuid}}))},{default:e(()=>[r(s(t(n)("general.edit")),1)]),_:1})):y("",!0)]),_:1})]),default:e(()=>[a.records.length>0?(d(),_(A,{key:0,header:x},{default:e(()=>[(d(!0),U(F,null,N(a.records,i=>(d(),_(T,{key:i.uuid},{default:e(()=>[o(g,{name:"studentName"},{default:e(()=>[c("div",ue,[r(s(i.studentName)+" ",1),i.isConverted?(d(),U("span",pe,l[3]||(l[3]=[c("i",{class:"far fa-check-circle fa-lg text-success"},null,-1)]))):y("",!0)]),i.isConverted?(d(),_(p,{key:0,block:""},{default:e(()=>[c("span",{class:"cursor-pointer",onClick:D=>t(v).push({name:"StudentRegistrationShow",params:{uuid:i.registrationUuid}})},s(t(n)("reception.enquiry.converted_to_registration")),9,de)]),_:2},1024)):y("",!0)]),_:2},1024),o(g,{name:"birthDate"},{default:e(()=>[r(s(i.birthDate.formatted),1)]),_:2},1024),o(g,{name:"gender"},{default:e(()=>[r(s(i.gender.label),1)]),_:2},1024),o(g,{name:"course"},{default:e(()=>[r(s(i.course.name),1)]),_:2},1024),o(g,{name:"action"},{default:e(()=>[t($)("enquiry:action")&&!i.isConverted?ee((d(),_(I,{key:0,size:"xs",onClick:D=>t(k).emit("showActionItem",{uuid:a==null?void 0:a.uuid,moduleUuid:i.uuid,action:"convertToRegistration",confirmation:!0})},{default:e(()=>l[4]||(l[4]=[c("i",{class:"fas fa-share"},null,-1)])),_:2},1032,["onClick"])),[[K,t(n)("reception.enquiry.convert_to_registration")]]):y("",!0)]),_:2},1024)]),_:2},1024))),128))]),_:1})):y("",!0),c("div",ce,[c("dl",me,[o(M,{class:"col-span-1 sm:col-span-2"},{default:e(()=>[o(H,{media:a.media,url:`/app/reception/enquiries/${a.uuid}/`},null,8,["media","url"])]),_:1})])])]),_:1}),a.followUps.length>0?(d(),_(C,{key:0,"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:e(()=>[r(s(t(n)("reception.enquiry.follow_up.follow_up")),1)]),default:e(()=>[a.followUps.length>0?(d(),_(A,{key:0,header:f},{default:e(()=>[(d(!0),U(F,null,N(a.followUps,i=>(d(),U(F,{key:i.uuid},[o(T,null,{default:e(()=>[o(g,{name:"followUpDate"},{default:e(()=>[r(s(i.followUpDate.formatted),1)]),_:2},1024),o(g,{name:"nextFollowUpDate"},{default:e(()=>[r(s(i.nextFollowUpDate.formatted),1)]),_:2},1024),o(g,{name:"status"},{default:e(()=>[r(s(i.status.label),1)]),_:2},1024),o(g,{name:"action"},{default:e(()=>[t($)("enquiry:follow-up")?(d(),_(I,{key:0,size:"xs",design:"danger",onClick:D=>t(k).emit("showActionItem",{uuid:a==null?void 0:a.uuid,moduleUuid:i.uuid,action:"removeFollowUp",confirmation:!0})},{default:e(()=>l[5]||(l[5]=[c("i",{class:"fas fa-trash"},null,-1)])),_:2},1032,["onClick"])):y("",!0)]),_:2},1024)]),_:2},1024),o(T,null,{default:e(()=>[o(g,{colspan:100},{default:e(()=>[c("span",_e,s(i.remarks),1)]),_:2},1024)]),_:2},1024)],64))),128))]),_:1})):y("",!0)]),_:1})):y("",!0)]),t($)("enquiry:follow-up")?(d(),_(C,{key:0,"no-padding":"","no-content-padding":""},{title:e(()=>[r(s(t(n)("reception.enquiry.follow_up.follow_up")),1)]),default:e(()=>[o(se,{onCompleted:l[1]||(l[1]=i=>t(k).emit("refreshItem"))})]),_:1})):y("",!0)]),_:1})):y("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{be as default};
