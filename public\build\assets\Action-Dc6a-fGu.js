import{I as U,l as g,r as i,q as P,o as b,w as m,d,e as s,f as n,a as S,b as R,s as v,t as f,K as k,u as w,F as W}from"./app-DvIo72ZO.js";const q={class:"grid grid-cols-3 gap-6"},y={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},T={class:"col-span-3"},H={class:"grid grid-cols-3 gap-6"},N={class:"col-span-2 sm:col-span-1"},E={key:0,class:"col-span-2 sm:col-span-1"},I={class:"col-span-3 sm:col-span-1"},D={name:"AcademicCourseForm"},M=Object.assign(D,{setup(h){const c={name:"",term:"",division:"",code:"",shortcode:"",position:"",pgAccount:"",enableRegistration:!0,registrationFee:"0",batchWithSameSubject:!1,description:""},l="academic/course/",r=U(l),u=g({divisions:[]}),o=g({...c}),p=a=>{Object.assign(u,a)},F=a=>{Object.assign(c,{...a,registrationFee:a.registrationFee.value,division:a.division.uuid}),Object.assign(o,k(c))};return(a,e)=>{const _=i("BaseInput"),V=i("BaseSelect"),B=i("BaseCheckbox"),$=i("BaseFieldset"),j=i("BaseSwitch"),A=i("TextMuted"),C=i("FormAction");return b(),P(C,{"has-setup-wizard":!0,"pre-requisites":!0,onSetPreRequisites:p,"init-url":l,"init-form":c,form:o,setForm:F,redirect:"AcademicCourse"},{default:m(()=>[d("div",q,[d("div",y,[s(_,{type:"text",modelValue:o.name,"onUpdate:modelValue":e[0]||(e[0]=t=>o.name=t),name:"name",label:a.$trans("academic.course.props.name"),error:n(r).name,"onUpdate:error":e[1]||(e[1]=t=>n(r).name=t),placeholder:"JSS 1",autofocus:""},null,8,["modelValue","label","error"])]),d("div",O,[s(V,{modelValue:o.division,"onUpdate:modelValue":e[2]||(e[2]=t=>o.division=t),name:"division",label:a.$trans("academic.division.division"),"label-prop":"nameWithProgram","value-prop":"uuid",options:u.divisions,error:n(r).division,"onUpdate:error":e[3]||(e[3]=t=>n(r).division=t)},null,8,["modelValue","label","options","error"])]),d("div",T,[s($,null,{legend:m(()=>[v(f(a.$trans("academic.course.registration")),1)]),default:m(()=>[d("div",H,[d("div",N,[s(B,{modelValue:o.enableRegistration,"onUpdate:modelValue":e[4]||(e[4]=t=>o.enableRegistration=t),name:"enableRegistration",label:a.$trans("academic.course.props.enable_registration")},null,8,["modelValue","label"])]),o.enableRegistration?(b(),S("div",E,[s(_,{currency:"",modelValue:o.registrationFee,"onUpdate:modelValue":e[5]||(e[5]=t=>o.registrationFee=t),name:"registrationFee",label:a.$trans("academic.course.props.registration_fee"),error:n(r).registrationFee,"onUpdate:error":e[6]||(e[6]=t=>n(r).registrationFee=t)},null,8,["modelValue","label","error"])])):R("",!0)])]),_:1})]),d("div",I,[s(j,{modelValue:o.batchWithSameSubject,"onUpdate:modelValue":e[7]||(e[7]=t=>o.batchWithSameSubject=t),name:"batchWithSameSubject",label:a.$trans("academic.course.props.batch_with_same_subject"),error:n(r).batchWithSameSubject,"onUpdate:error":e[8]||(e[8]=t=>n(r).batchWithSameSubject=t)},null,8,["modelValue","label","error"]),s(A,{block:""},{default:m(()=>[v(f(a.$trans("academic.course.batch_with_same_subject_info")),1)]),_:1})])])]),_:1},8,["form"])}}}),z={name:"AcademicCourseAction"},K=Object.assign(z,{setup(h){const c=w();return(l,r)=>{const u=i("PageHeaderAction"),o=i("PageHeader"),p=i("ParentTransition");return b(),S(W,null,[s(o,{title:l.$trans(n(c).meta.trans,{attribute:l.$trans(n(c).meta.label)}),navs:[{label:l.$trans("academic.academic"),path:"Academic"},{label:l.$trans("academic.course.course"),path:"AcademicCourseList"}]},{default:m(()=>[s(u,{name:"AcademicCourse",title:l.$trans("academic.course.course"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),s(p,{appear:"",visibility:!0},{default:m(()=>[s(M)]),_:1})],64)}}});export{K as default};
