import{u as k,h,I as P,l as D,r as l,q as v,o as f,w as d,d as m,e as i,f as r,b as S,s as g,t as _,K as N,a as O,F as j}from"./app-DvIo72ZO.js";const q={class:"grid grid-cols-3 gap-6"},y={class:"col-span-3 sm:col-span-1"},T={class:"col-span-2 sm:col-span-1"},L={class:"col-span-2 sm:col-span-1"},C={class:"col-span-2 sm:col-span-1"},E={class:"col-span-3"},w={name:"HostelRoomAllocationForm"},I=Object.assign(w,{setup($){const u=k();h();const n={room:"",student:"",startDate:"",endDate:"",remarks:""},b="hostel/roomAllocation/",s=P(b),c=D({rooms:[]}),a=D({...n}),p=D({student:"",isLoaded:!u.params.uuid}),A=t=>{Object.assign(c,t)},U=t=>{Object.assign(n,{...t,startDate:t.startDate.value,endDate:t.endDate.value,room:t.room.uuid,student:t.student.uuid}),Object.assign(a,N(n)),p.student=t.student.uuid,p.isLoaded=!0};return(t,o)=>{const B=l("BaseSelect"),H=l("BaseSelectSearch"),V=l("DatePicker"),R=l("BaseTextarea"),F=l("FormAction");return f(),v(F,{"pre-requisites":!0,onSetPreRequisites:A,"init-url":b,"init-form":n,form:a,setForm:U,redirect:"HostelRoomAllocation"},{default:d(()=>[m("div",q,[m("div",y,[i(B,{name:"room",label:t.$trans("hostel.room.room"),modelValue:a.room,"onUpdate:modelValue":o[0]||(o[0]=e=>a.room=e),error:r(s).room,"onUpdate:error":o[1]||(o[1]=e=>r(s).room=e),"label-prop":"fullName","value-prop":"uuid",options:c.rooms},null,8,["label","modelValue","error","options"])]),m("div",T,[p.isLoaded?(f(),v(H,{key:0,name:"student",label:t.$trans("global.select",{attribute:t.$trans("student.student")}),modelValue:a.student,"onUpdate:modelValue":o[2]||(o[2]=e=>a.student=e),error:r(s).student,"onUpdate:error":o[3]||(o[3]=e=>r(s).student=e),"value-prop":"uuid","init-search":p.student,"search-key":"name","search-action":"student/summary"},{selectedOption:d(e=>[g(_(e.value.name)+" ("+_(e.value.codeNumber)+") ",1)]),listOption:d(e=>[g(_(e.option.name)+" ("+_(e.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","error","init-search"])):S("",!0)]),m("div",L,[i(V,{modelValue:a.startDate,"onUpdate:modelValue":o[4]||(o[4]=e=>a.startDate=e),name:"startDate",label:t.$trans("hostel.room_allocation.props.start_date"),"no-clear":"",error:r(s).startDate,"onUpdate:error":o[5]||(o[5]=e=>r(s).startDate=e)},null,8,["modelValue","label","error"])]),m("div",C,[i(V,{modelValue:a.endDate,"onUpdate:modelValue":o[6]||(o[6]=e=>a.endDate=e),name:"endDate",label:t.$trans("hostel.room_allocation.props.end_date"),"no-clear":"",error:r(s).endDate,"onUpdate:error":o[7]||(o[7]=e=>r(s).endDate=e)},null,8,["modelValue","label","error"])]),m("div",E,[i(R,{modelValue:a.remarks,"onUpdate:modelValue":o[8]||(o[8]=e=>a.remarks=e),name:"remarks",label:t.$trans("hostel.room_allocation.props.remarks"),error:r(s).remarks,"onUpdate:error":o[9]||(o[9]=e=>r(s).remarks=e)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),K={name:"HostelRoomAllocationAction"},G=Object.assign(K,{setup($){const u=k();return(n,b)=>{const s=l("PageHeaderAction"),c=l("PageHeader"),a=l("ParentTransition");return f(),O(j,null,[i(c,{title:n.$trans(r(u).meta.trans,{attribute:n.$trans(r(u).meta.label)}),navs:[{label:n.$trans("hostel.hostel"),path:"Hostel"},{label:n.$trans("hostel.room_allocation.room_allocation"),path:"HostelRoomAllocationList"}]},{default:d(()=>[i(s,{name:"HostelRoomAllocation",title:n.$trans("hostel.room_allocation.room_allocation"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),i(a,{appear:"",visibility:!0},{default:d(()=>[i(I)]),_:1})],64)}}});export{G as default};
