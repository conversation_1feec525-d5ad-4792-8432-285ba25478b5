import{u as O,l as B,n as U,r as i,q as k,o as c,w as e,d as q,e as n,b as g,s as d,t as l,h as N,j as E,y,m as W,f as t,a as F,F as R,v as T}from"./app-DvIo72ZO.js";const z={class:"grid grid-cols-4 gap-6"},G={class:"col-span-4 sm:col-span-1"},J={class:"col-span-4 sm:col-span-1"},K={class:"col-span-4 sm:col-span-1"},X={class:"col-span-4 sm:col-span-1"},Y={__name:"Filter",emits:["hide"],setup(L,{emit:h}){const v=O(),p=h,$={title:"",subject:"",type:"",batches:[]},r=B({...$}),b=B({subjects:[],types:[]}),_=B({batches:[],isLoaded:!v.query.batches}),w=f=>{Object.assign(b,f)};return U(async()=>{v.query.batches&&(_.batches=v.query.batches.split(",")),_.isLoaded=!0}),(f,u)=>{const m=i("BaseInput"),C=i("BaseSelect"),I=i("BaseSelectSearch"),V=i("FilterForm");return c(),k(V,{"init-form":$,form:r,multiple:["batches"],onHide:u[4]||(u[4]=s=>p("hide")),"pre-requisites":!0,onSetPreRequisites:w,"init-url":"exam/questionBank/"},{default:e(()=>[q("div",z,[q("div",G,[n(m,{type:"text",modelValue:r.title,"onUpdate:modelValue":u[0]||(u[0]=s=>r.title=s),name:"title",label:f.$trans("exam.question_bank.props.title")},null,8,["modelValue","label"])]),q("div",J,[n(C,{modelValue:r.subject,"onUpdate:modelValue":u[1]||(u[1]=s=>r.subject=s),name:"subject",label:f.$trans("exam.question_bank.props.subject"),"label-prop":"name","value-prop":"uuid",options:b.subjects},null,8,["modelValue","label","options"])]),q("div",K,[n(C,{modelValue:r.type,"onUpdate:modelValue":u[2]||(u[2]=s=>r.type=s),name:"type",label:f.$trans("exam.question_bank.props.type"),options:b.types,loading:!_.isLoaded},null,8,["modelValue","label","options","loading"])]),q("div",X,[_.isLoaded?(c(),k(I,{key:0,multiple:"",name:"batches",label:f.$trans("exam.question_bank.props.batches"),modelValue:r.batches,"onUpdate:modelValue":u[3]||(u[3]=s=>r.batches=s),"value-prop":"uuid","init-search":_.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:e(s=>[d(l(s.value.course.name)+" - "+l(s.value.name),1)]),listOption:e(s=>[d(l(s.option.course.nameWithTerm)+" - "+l(s.option.name),1)]),_:1},8,["label","modelValue","init-search"])):g("",!0)])])]),_:1},8,["form"])}}},Z={key:0},ee={name:"ExamQuestionBankIndex"},ae=Object.assign(ee,{setup(L){const h=N(),v=E("emitter"),p=E("$trans");let $=["filter"];y("question-bank:create")&&$.unshift("create");let r=[];y("question-bank:export")&&(r=["print","pdf","excel"]);const b=W(!1),_=B({data:[],headers:[],meta:{}}),w="exam/questionBank/",f=u=>{Object.assign(_,u)};return(u,m)=>{const C=i("PageHeaderAction"),I=i("PageHeader"),V=i("ParentTransition"),s=i("TextMuted"),x=i("DataCell"),j=i("FloatingMenuItem"),M=i("FloatingMenu"),Q=i("DataRow"),A=i("BaseButton"),H=i("DataTable"),P=i("ListItem");return c(),k(P,{"init-url":w,onSetItems:f},{header:e(()=>[n(I,{title:t(p)("exam.question_bank.question_bank"),navs:[{label:t(p)("exam.exam"),path:"Exam"}]},{default:e(()=>[n(C,{url:"exam/question-banks/",name:"ExamQuestionBank",title:t(p)("exam.question_bank.question_bank"),actions:t($),"dropdown-actions":t(r),onToggleFilter:m[0]||(m[0]=a=>b.value=!b.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[n(V,{appear:"",visibility:b.value},{default:e(()=>[n(Y,{onRefresh:m[1]||(m[1]=a=>t(v).emit("listItems")),onHide:m[2]||(m[2]=a=>b.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[n(V,{appear:"",visibility:!0},{default:e(()=>[n(H,{header:_.headers,meta:_.meta,module:"exam.question_bank",onRefresh:m[4]||(m[4]=a=>t(v).emit("listItems"))},{actionButton:e(()=>[t(y)("question-bank:create")?(c(),k(A,{key:0,onClick:m[3]||(m[3]=a=>t(h).push({name:"ExamQuestionBankCreate"}))},{default:e(()=>[d(l(t(p)("global.add",{attribute:t(p)("exam.question_bank.question_bank")})),1)]),_:1})):g("",!0)]),default:e(()=>[(c(!0),F(R,null,T(_.data,a=>(c(),k(Q,{key:a.uuid,onDoubleClick:o=>t(h).push({name:"ExamQuestionBankShow",params:{uuid:a.uuid}})},{default:e(()=>[n(x,{name:"title"},{default:e(()=>[d(l(a.title)+" ",1),n(s,{block:""},{default:e(()=>{var o;return[d(l((o=a.type)==null?void 0:o.label),1)]}),_:2},1024)]),_:2},1024),n(x,{name:"subject"},{default:e(()=>{var o;return[d(l(((o=a.subject)==null?void 0:o.name)||"-"),1)]}),_:2},1024),n(x,{name:"batches"},{default:e(()=>{var o;return[(c(!0),F(R,null,T(a.batches,D=>{var S;return c(),F("div",{key:D.uuid},l((S=D.course)==null?void 0:S.name)+" - "+l(D.name),1)}),128)),(o=a.batches)!=null&&o.length?g("",!0):(c(),F("span",Z,"-"))]}),_:2},1024),n(x,{name:"mark"},{default:e(()=>[d(l(a.mark),1)]),_:2},1024),n(x,{name:"createdAt"},{default:e(()=>{var o;return[d(l((o=a.createdAt)==null?void 0:o.formatted),1)]}),_:2},1024),n(x,{name:"action"},{default:e(()=>[n(M,null,{default:e(()=>[n(j,{icon:"fas fa-arrow-circle-right",onClick:o=>t(h).push({name:"ExamQuestionBankShow",params:{uuid:a.uuid}})},{default:e(()=>[d(l(t(p)("general.show")),1)]),_:2},1032,["onClick"]),t(y)("question-bank:edit")?(c(),k(j,{key:0,icon:"fas fa-edit",onClick:o=>t(h).push({name:"ExamQuestionBankEdit",params:{uuid:a.uuid}})},{default:e(()=>[d(l(t(p)("general.edit")),1)]),_:2},1032,["onClick"])):g("",!0),t(y)("question-bank:create")?(c(),k(j,{key:1,icon:"fas fa-copy",onClick:o=>t(h).push({name:"ExamQuestionBankDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[d(l(t(p)("general.duplicate")),1)]),_:2},1032,["onClick"])):g("",!0),t(y)("question-bank:delete")?(c(),k(j,{key:2,icon:"fas fa-trash",onClick:o=>t(v).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[d(l(t(p)("general.delete")),1)]),_:2},1032,["onClick"])):g("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{ae as default};
