import{u as j,j as B,I as E,l as _,n as $,K as k,r as o,a as C,o as n,q as c,b as m,e as p,f as i,w as d,d as u,F as P}from"./app-DvIo72ZO.js";const F={class:"grid grid-cols-1"},O={class:"col-span-1"},w={name:"EmployeeEditPhoto"},H=Object.assign(w,{props:{employee:{type:Object,default(){return{}}}},setup(e){const s=j(),l=B("emitter"),y=e,a={photo:""};E("employee/");const r=_({...a}),h=async()=>{l.emit("employeeUpdated")},b=async()=>{l.emit("employeeUpdated")};return $(async()=>{Object.assign(a,{photo:y.employee.contact.photo}),Object.assign(r,k(a))}),(t,N)=>{const g=o("PageHeader"),v=o("ImageUpload"),f=o("BaseCard"),U=o("ParentTransition");return n(),C(P,null,[e.employee.uuid?(n(),c(g,{key:0,title:t.$trans(i(s).meta.trans,{attribute:t.$trans(i(s).meta.label)}),navs:[{label:t.$trans("employee.employee"),path:"Employee"},{label:e.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:e.employee.uuid}}}]},null,8,["title","navs"])):m("",!0),p(U,{appear:"",visibility:!0},{default:d(()=>[e.employee.uuid?(n(),c(f,{key:0},{default:d(()=>[u("div",F,[u("div",O,[p(v,{label:t.$trans("contact.props.photo"),src:r.photo,"upload-path":`employees/${e.employee.uuid}/photo`,"remove-path":`employees/${e.employee.uuid}/photo`,onUploaded:h,onRemoved:b},null,8,["label","src","upload-path","remove-path"])])])]),_:1})):m("",!0)]),_:1})],64)}}});export{H as default};
