import{l as B,r as o,q as d,o as l,w as e,d as w,e as i,h as j,j as L,y as _,m as N,f as a,a as S,F as E,v as O,s as u,b as v,t as c}from"./app-DvIo72ZO.js";const U={class:"grid grid-cols-3 gap-6"},q={class:"col-span-3 sm:col-span-1"},x={__name:"Filter",emits:["hide"],setup(I,{emit:m}){const g=m,F={name:""},p=B({...F});return(C,r)=>{const f=o("BaseInput"),b=o("FilterForm");return l(),d(b,{"init-form":F,form:p,onHide:r[1]||(r[1]=n=>g("hide"))},{default:e(()=>[w("div",U,[w("div",q,[i(f,{type:"text",modelValue:p.name,"onUpdate:modelValue":r[0]||(r[0]=n=>p.name=n),name:"name",label:C.$trans("finance.fee_group.props.name")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},z={name:"FinanceFeeGroupList"},J=Object.assign(z,{setup(I){const m=j(),g=L("emitter");let F=["filter"];_("fee-group:create")&&F.unshift("create");let p=[];_("fee-group:export")&&(p=["print","pdf","excel"]);const C="finance/feeGroup/",r=N(!1),f=B({}),b=n=>{Object.assign(f,n)};return(n,s)=>{const D=o("PageHeaderAction"),A=o("PageHeader"),y=o("ParentTransition"),T=o("TextMuted"),h=o("DataCell"),$=o("FloatingMenuItem"),V=o("FloatingMenu"),H=o("DataRow"),M=o("BaseButton"),P=o("DataTable"),R=o("ListItem");return l(),d(R,{"init-url":C,onSetItems:b},{header:e(()=>[i(A,{title:n.$trans("finance.fee_group.fee_group"),navs:[{label:n.$trans("finance.finance"),path:"Finance"}]},{default:e(()=>[i(D,{url:"finance/fee-groups/",name:"FinanceFeeGroup",title:n.$trans("finance.fee_group.fee_group"),actions:a(F),"dropdown-actions":a(p),onToggleFilter:s[0]||(s[0]=t=>r.value=!r.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[i(y,{appear:"",visibility:r.value},{default:e(()=>[i(x,{onRefresh:s[1]||(s[1]=t=>a(g).emit("listItems")),onHide:s[2]||(s[2]=t=>r.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[i(y,{appear:"",visibility:!0},{default:e(()=>[i(P,{header:f.headers,meta:f.meta,module:"finance.fee_group",onRefresh:s[4]||(s[4]=t=>a(g).emit("listItems"))},{actionButton:e(()=>[a(_)("fee-group:create")?(l(),d(M,{key:0,onClick:s[3]||(s[3]=t=>a(m).push({name:"FinanceFeeGroupCreate"}))},{default:e(()=>[u(c(n.$trans("global.add",{attribute:n.$trans("finance.fee_group.fee_group")})),1)]),_:1})):v("",!0)]),default:e(()=>[(l(!0),S(E,null,O(f.data,t=>(l(),d(H,{key:t.uuid,onDoubleClick:k=>a(m).push({name:"FinanceFeeGroupShow",params:{uuid:t.uuid}})},{default:e(()=>[i(h,{name:"name"},{default:e(()=>[u(c(t.name)+" ",1),t.pgAccount?(l(),d(T,{key:0,block:""},{default:e(()=>[u(c(t.pgAccount),1)]),_:2},1024)):v("",!0)]),_:2},1024),i(h,{name:"createdAt"},{default:e(()=>[u(c(t.createdAt.formatted),1)]),_:2},1024),i(h,{name:"action"},{default:e(()=>[i(V,null,{default:e(()=>[i($,{icon:"fas fa-arrow-circle-right",onClick:k=>a(m).push({name:"FinanceFeeGroupShow",params:{uuid:t.uuid}})},{default:e(()=>[u(c(n.$trans("general.show")),1)]),_:2},1032,["onClick"]),a(_)("fee-group:edit")?(l(),d($,{key:0,icon:"fas fa-edit",onClick:k=>a(m).push({name:"FinanceFeeGroupEdit",params:{uuid:t.uuid}})},{default:e(()=>[u(c(n.$trans("general.edit")),1)]),_:2},1032,["onClick"])):v("",!0),a(_)("fee-group:create")?(l(),d($,{key:1,icon:"fas fa-copy",onClick:k=>a(m).push({name:"FinanceFeeGroupDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[u(c(n.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):v("",!0),a(_)("fee-group:delete")?(l(),d($,{key:2,icon:"fas fa-trash",onClick:k=>a(g).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[u(c(n.$trans("general.delete")),1)]),_:2},1032,["onClick"])):v("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{J as default};
