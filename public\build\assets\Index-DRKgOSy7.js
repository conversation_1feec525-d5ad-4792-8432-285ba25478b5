import{u as z,l as w,n as G,r as i,q as h,o,w as e,d as f,b as $,s as m,t as a,e as n,h as J,j as K,y as k,m as Q,f as u,B as X,a as j,F as S,v as I}from"./app-DvIo72ZO.js";const Y={class:"grid grid-cols-3 gap-6"},Z={class:"col-span-3 sm:col-span-1"},M={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3 sm:col-span-1"},te={class:"col-span-3 sm:col-span-1"},se={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(T,{emit:y}){const p=z(),C=y,D=T,q={employees:[],batches:[],subjects:[],startDate:"",endDate:""},c=w({...q}),L=w({subjects:D.preRequisites.subjects}),l=w({employees:[],batches:[],subjects:[],isLoaded:!(p.query.employees||p.query.batches||p.query.subjects)});return G(async()=>{l.employees=p.query.employees?p.query.employees.split(","):[],l.batches=p.query.batches?p.query.batches.split(","):[],l.subjects=p.query.subjects?p.query.subjects.split(","):[],l.isLoaded=!0}),(g,b)=>{const r=i("BaseSelectSearch"),_=i("BaseSelect"),V=i("DatePicker"),F=i("FilterForm");return o(),h(F,{"init-form":q,form:c,multiple:["employees","batches","subjects"],onHide:b[5]||(b[5]=s=>C("hide"))},{default:e(()=>[f("div",Y,[f("div",Z,[l.isLoaded?(o(),h(r,{key:0,multiple:"",name:"employees",label:g.$trans("global.select",{attribute:g.$trans("employee.employee")}),modelValue:c.employees,"onUpdate:modelValue":b[0]||(b[0]=s=>c.employees=s),"value-prop":"uuid","init-search":l.employees,"search-key":"name","search-action":"employee/list"},{selectedOption:e(s=>[m(a(s.value.name)+" ("+a(s.value.codeNumber)+") ",1)]),listOption:e(s=>[m(a(s.option.name)+" ("+a(s.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):$("",!0)]),f("div",M,[l.isLoaded?(o(),h(r,{key:0,multiple:"",name:"batches",label:g.$trans("global.select",{attribute:g.$trans("academic.batch.batch")}),modelValue:c.batches,"onUpdate:modelValue":b[1]||(b[1]=s=>c.batches=s),"value-prop":"uuid","init-search":l.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:e(s=>[m(a(s.value.course.name)+" "+a(s.value.name),1)]),listOption:e(s=>[m(a(s.option.course.nameWithTerm)+" "+a(s.option.name),1)]),_:1},8,["label","modelValue","init-search"])):$("",!0)]),f("div",ee,[n(_,{multiple:"",modelValue:c.subjects,"onUpdate:modelValue":b[2]||(b[2]=s=>c.subjects=s),name:"subjects",label:g.$trans("academic.subject.subject"),"label-prop":"name","value-prop":"uuid",options:L.subjects},null,8,["modelValue","label","options"])]),f("div",te,[n(V,{start:c.startDate,"onUpdate:start":b[3]||(b[3]=s=>c.startDate=s),end:c.endDate,"onUpdate:end":b[4]||(b[4]=s=>c.endDate=s),name:"dateBetween",as:"range",label:g.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},ae={class:"grid grid-cols-1 gap-4 px-4 pt-4 md:grid-cols-2 lg:grid-cols-3"},ne=["onClick"],oe={class:"px-2 py-2 text-gray-800 dark:text-gray-400"},ie={class:"flex items-center justify-between"},le={class:"font-medium"},re={class:"mt-2 flex justify-between"},ue={class:"text-sm"},ce={key:0,class:"text-xs"},de={class:"mt-2 text-xs"},me={class:"text-xl font-semibold"},pe={name:"ResourceLearningMaterialList"},_e=Object.assign(pe,{setup(T){const y=J(),p=K("emitter");let C=["filter"];k("resource:config")&&C.push("config"),k("learning-material:create")&&C.unshift("create");let D=[];k("learning-material:export")&&(D=["print","pdf","excel"]);const q="resource/learningMaterial/",c=Q(!1),L=w({subjects:[]}),l=w({}),g=r=>{Object.assign(L,r)},b=r=>{Object.assign(l,r)};return(r,_)=>{const V=i("PageHeaderAction"),F=i("PageHeader"),s=i("ParentTransition"),O=i("CardView"),P=i("Pagination"),x=i("CardList"),R=i("DataCell"),A=i("TextMuted"),B=i("FloatingMenuItem"),N=i("FloatingMenu"),H=i("DataRow"),U=i("BaseButton"),E=i("DataTable"),W=i("ListItem");return o(),h(W,{"init-url":q,"pre-requisites":!0,onSetPreRequisites:g,"additional-query":{},onSetItems:b},{header:e(()=>[n(F,{title:r.$trans("resource.learning_material.learning_material"),navs:[{label:r.$trans("resource.resource"),path:"Resource"}]},{default:e(()=>[n(V,{url:"resource/learning-materials/",name:"ResourceLearningMaterial",title:r.$trans("resource.learning_material.learning_material"),actions:u(C),"dropdown-actions":u(D),"config-path":"ResourceConfig",onToggleFilter:_[0]||(_[0]=t=>c.value=!c.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[n(s,{appear:"",visibility:c.value},{default:e(()=>[n(se,{onRefresh:_[1]||(_[1]=t=>u(p).emit("listItems")),"pre-requisites":L,onHide:_[2]||(_[2]=t=>c.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:e(()=>[u(X)(["student","guardian"],"any")?(o(),h(s,{key:0,appear:"",visibility:!0},{default:e(()=>[n(x,{header:l.headers,meta:l.meta},{content:e(()=>[f("div",me,a(r.$trans("dashboard.nothing_to_show")),1)]),default:e(()=>[f("div",ae,[(o(!0),j(S,null,I(l.data,t=>(o(),j("div",{key:t.uuid,class:"cursor-pointer",onClick:d=>u(y).push({name:"ResourceLearningMaterialShow",params:{uuid:t.uuid}})},[n(O,{"no-padding":""},{default:e(()=>{var d,v;return[f("div",oe,[f("div",ie,[f("span",le,a(t.title),1)]),f("div",re,[f("span",ue,a(((d=t.employee)==null?void 0:d.name)||"-"),1),t.employee?(o(),j("span",ce,a((v=t.employee)==null?void 0:v.designation),1)):$("",!0)]),f("p",de,a(t.publishedAt.formatted),1)])]}),_:2},1024)],8,ne))),128))]),f("div",null,[n(P,{"card-view":"",meta:l.meta,onRefresh:_[3]||(_[3]=t=>u(p).emit("listItems"))},null,8,["meta"])])]),_:1},8,["header","meta"])]),_:1})):(o(),h(s,{key:1,appear:"",visibility:!0},{default:e(()=>[n(E,{header:l.headers,meta:l.meta,module:"resource.learning_material",onRefresh:_[5]||(_[5]=t=>u(p).emit("listItems"))},{actionButton:e(()=>[u(k)("learning-material:create")?(o(),h(U,{key:0,onClick:_[4]||(_[4]=t=>u(y).push({name:"ResourceLearningMaterialCreate"}))},{default:e(()=>[m(a(r.$trans("global.add",{attribute:r.$trans("resource.learning_material.learning_material")})),1)]),_:1})):$("",!0)]),default:e(()=>[(o(!0),j(S,null,I(l.data,t=>(o(),h(H,{key:t.uuid,onDoubleClick:d=>u(y).push({name:"ResourceLearningMaterialShow",params:{uuid:t.uuid}})},{default:e(()=>[n(R,{name:"title"},{default:e(()=>[m(a(t.titleExcerpt),1)]),_:2},1024),n(R,{name:"records"},{default:e(()=>[(o(!0),j(S,null,I(t.records,d=>{var v;return o(),j("div",null,[m(a(((v=d.batch.course)==null?void 0:v.name)+" "+d.batch.name)+" ",1),d.subject?(o(),h(A,{key:0},{default:e(()=>[m(a(d.subject.name),1)]),_:2},1024)):$("",!0)])}),256))]),_:2},1024),n(R,{name:"employee"},{default:e(()=>{var d;return[m(a(((d=t.employee)==null?void 0:d.name)||"-")+" ",1),n(A,{block:""},{default:e(()=>{var v;return[m(a((v=t.employee)==null?void 0:v.codeNumber),1)]}),_:2},1024)]}),_:2},1024),n(R,{name:"publishedAt"},{default:e(()=>[m(a(t.publishedAt.formatted),1)]),_:2},1024),n(R,{name:"action"},{default:e(()=>[n(N,null,{default:e(()=>[n(B,{icon:"fas fa-arrow-circle-right",onClick:d=>u(y).push({name:"ResourceLearningMaterialShow",params:{uuid:t.uuid}})},{default:e(()=>[m(a(r.$trans("general.show")),1)]),_:2},1032,["onClick"]),u(k)("learning-material:edit")&&t.isEditable?(o(),h(B,{key:0,icon:"fas fa-edit",onClick:d=>u(y).push({name:"ResourceLearningMaterialEdit",params:{uuid:t.uuid}})},{default:e(()=>[m(a(r.$trans("general.edit")),1)]),_:2},1032,["onClick"])):$("",!0),u(k)("learning-material:create")?(o(),h(B,{key:1,icon:"fas fa-copy",onClick:d=>u(y).push({name:"ResourceLearningMaterialDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[m(a(r.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):$("",!0),u(k)("learning-material:delete")&&t.isDeletable?(o(),h(B,{key:2,icon:"fas fa-trash",onClick:d=>u(p).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[m(a(r.$trans("general.delete")),1)]),_:2},1032,["onClick"])):$("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1}))]),_:1})}}});export{_e as default};
