import{u as G,l as q,n as J,r as l,q as h,o as n,w as t,d as u,b as y,s as r,t as s,e as o,h as K,j as Q,y as w,m as X,f as d,B as Y,a as g,F as I,v as L}from"./app-DvIo72ZO.js";const Z={class:"grid grid-cols-3 gap-6"},C={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3 sm:col-span-1"},te={class:"col-span-3 sm:col-span-1"},se={class:"col-span-3 sm:col-span-1"},ae={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(x,{emit:v}){const _=G(),j=v,B=x,O={employees:[],batches:[],subjects:[],startDate:"",endDate:""},m=q({...O}),V=q({subjects:B.preRequisites.subjects}),c=q({employees:[],batches:[],subjects:[],isLoaded:!(_.query.employees||_.query.batches||_.query.subjects)});return J(async()=>{c.employees=_.query.employees?_.query.employees.split(","):[],c.batches=_.query.batches?_.query.batches.split(","):[],c.subjects=_.query.subjects?_.query.subjects.split(","):[],c.isLoaded=!0}),(k,b)=>{const i=l("BaseSelectSearch"),f=l("BaseSelect"),S=l("DatePicker"),U=l("FilterForm");return n(),h(U,{"init-form":O,form:m,multiple:["employees","batches","subjects"],onHide:b[5]||(b[5]=a=>j("hide"))},{default:t(()=>[u("div",Z,[u("div",C,[c.isLoaded?(n(),h(i,{key:0,multiple:"",name:"employees",label:k.$trans("global.select",{attribute:k.$trans("employee.employee")}),modelValue:m.employees,"onUpdate:modelValue":b[0]||(b[0]=a=>m.employees=a),"value-prop":"uuid","init-search":c.employees,"search-key":"name","search-action":"employee/list"},{selectedOption:t(a=>[r(s(a.value.name)+" ("+s(a.value.codeNumber)+") ",1)]),listOption:t(a=>[r(s(a.option.name)+" ("+s(a.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):y("",!0)]),u("div",ee,[c.isLoaded?(n(),h(i,{key:0,multiple:"",name:"batches",label:k.$trans("global.select",{attribute:k.$trans("academic.batch.batch")}),modelValue:m.batches,"onUpdate:modelValue":b[1]||(b[1]=a=>m.batches=a),"value-prop":"uuid","init-search":c.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:t(a=>[r(s(a.value.course.name)+" "+s(a.value.name),1)]),listOption:t(a=>[r(s(a.option.course.nameWithTerm)+" "+s(a.option.name),1)]),_:1},8,["label","modelValue","init-search"])):y("",!0)]),u("div",te,[o(f,{multiple:"",modelValue:m.subjects,"onUpdate:modelValue":b[2]||(b[2]=a=>m.subjects=a),name:"subjects",label:k.$trans("academic.subject.subject"),"label-prop":"name","value-prop":"uuid",options:V.subjects},null,8,["modelValue","label","options"])]),u("div",se,[o(S,{start:m.startDate,"onUpdate:start":b[3]||(b[3]=a=>m.startDate=a),end:m.endDate,"onUpdate:end":b[4]||(b[4]=a=>m.endDate=a),name:"dateBetween",as:"range",label:k.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},oe={class:"grid grid-cols-1 gap-4 px-4 pt-4 md:grid-cols-2 lg:grid-cols-3"},ne=["onClick"],re={class:"px-2 py-2 text-gray-800 dark:text-gray-400"},ie={class:"flex items-center justify-between"},le={class:"font-medium"},ue={class:"mt-2 flex justify-between"},ce={class:"text-sm"},de={key:0,class:"text-xs"},me={class:"flex justify-between"},pe={class:"text-sm"},_e={key:0,class:"text-xs"},be=["href"],fe={key:0,class:"ml-1"},he={class:"mt-2 text-xs"},ye={class:"text-xl font-semibold"},ge=["href"],ve={key:0,class:"ml-1"},ke={name:"ResourceOnlineClassList"},we=Object.assign(ke,{setup(x){const v=K(),_=Q("emitter");let j=["filter"];w("resource:config")&&j.push("config"),w("online-class:create")&&j.unshift("create");let B=[];w("online-class:export")&&(B=["print","pdf","excel"]);const O="resource/onlineClass/",m=X(!1),V=q({subjects:[]}),c=q({}),k=i=>{Object.assign(V,i)},b=i=>{Object.assign(c,i)};return(i,f)=>{const S=l("PageHeaderAction"),U=l("PageHeader"),a=l("ParentTransition"),T=l("BaseBadge"),R=l("TextMuted"),A=l("CardView"),P=l("Pagination"),M=l("CardList"),D=l("DataCell"),F=l("FloatingMenuItem"),N=l("FloatingMenu"),H=l("DataRow"),E=l("BaseButton"),W=l("DataTable"),z=l("ListItem");return n(),h(z,{"init-url":O,"pre-requisites":!0,onSetPreRequisites:k,"additional-query":{},onSetItems:b},{header:t(()=>[o(U,{title:i.$trans("resource.online_class.online_class"),navs:[{label:i.$trans("resource.resource"),path:"Resource"}]},{default:t(()=>[o(S,{url:"resource/online-classes/",name:"ResourceOnlineClass",title:i.$trans("resource.online_class.online_class"),actions:d(j),"dropdown-actions":d(B),"config-path":"ResourceConfig",onToggleFilter:f[0]||(f[0]=e=>m.value=!m.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:t(()=>[o(a,{appear:"",visibility:m.value},{default:t(()=>[o(ae,{onRefresh:f[1]||(f[1]=e=>d(_).emit("listItems")),"pre-requisites":V,onHide:f[2]||(f[2]=e=>m.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:t(()=>[d(Y)(["student","guardian"],"any")?(n(),h(a,{key:0,appear:"",visibility:!0},{default:t(()=>[o(M,{header:c.headers,meta:c.meta},{content:t(()=>[u("div",ye,s(i.$trans("dashboard.nothing_to_show")),1)]),default:t(()=>[u("div",oe,[(n(!0),g(I,null,L(c.data,e=>(n(),g("div",{key:e.uuid,class:"cursor-pointer",onClick:p=>d(v).push({name:"ResourceOnlineClassShow",params:{uuid:e.uuid}})},[o(A,{"no-padding":""},{default:t(()=>{var p,$;return[u("div",re,[u("div",ie,[u("span",le,s(e.topic),1)]),u("div",ue,[u("span",ce,s(((p=e.employee)==null?void 0:p.name)||"-"),1),e.employee?(n(),g("span",de,s(($=e.employee)==null?void 0:$.designation),1)):y("",!0)]),u("div",me,[u("p",pe,s(e.platform.label),1),o(T,{design:e.status.color},{default:t(()=>[r(s(e.status.label),1)]),_:2},1032,["design"])]),e.showUrl?(n(),g("div",_e,[u("a",{href:e.meetingUrl,target:"_blank"},[r(s(e.meetingUrl)+" ",1),e.password?(n(),g("span",fe,"("+s(e.password)+")",1)):y("",!0)],8,be)])):y("",!0),u("p",he,[r(s(e.startAt.formatted)+" ",1),o(R,null,{default:t(()=>[r("("+s(e.duration)+" "+s(i.$trans("list.durations.m"))+")",1)]),_:2},1024)])])]}),_:2},1024)],8,ne))),128))]),u("div",null,[o(P,{"card-view":"",meta:c.meta,onRefresh:f[3]||(f[3]=e=>d(_).emit("listItems"))},null,8,["meta"])])]),_:1},8,["header","meta"])]),_:1})):(n(),h(a,{key:1,appear:"",visibility:!0},{default:t(()=>[o(W,{header:c.headers,meta:c.meta,module:"resource.online_class",onRefresh:f[5]||(f[5]=e=>d(_).emit("listItems"))},{actionButton:t(()=>[d(w)("online-class:create")?(n(),h(E,{key:0,onClick:f[4]||(f[4]=e=>d(v).push({name:"ResourceOnlineClassCreate"}))},{default:t(()=>[r(s(i.$trans("global.add",{attribute:i.$trans("resource.online_class.online_class")})),1)]),_:1})):y("",!0)]),default:t(()=>[(n(!0),g(I,null,L(c.data,e=>(n(),h(H,{key:e.uuid,onDoubleClick:p=>d(v).push({name:"ResourceOnlineClassShow",params:{uuid:e.uuid}})},{default:t(()=>[o(D,{name:"topic"},{default:t(()=>[r(s(e.topicExcerpt)+" ",1),o(T,{design:e.status.color},{default:t(()=>[r(s(e.status.label),1)]),_:2},1032,["design"])]),_:2},1024),o(D,{name:"records"},{default:t(()=>[(n(!0),g(I,null,L(e.records,p=>{var $;return n(),g("div",null,[r(s((($=p.batch.course)==null?void 0:$.name)+" "+p.batch.name)+" ",1),p.subject?(n(),h(R,{key:0},{default:t(()=>[r(s(p.subject.name),1)]),_:2},1024)):y("",!0)])}),256))]),_:2},1024),o(D,{name:"employee"},{default:t(()=>{var p;return[r(s(((p=e.employee)==null?void 0:p.name)||"-")+" ",1),o(R,{block:""},{default:t(()=>{var $;return[r(s(($=e.employee)==null?void 0:$.codeNumber),1)]}),_:2},1024)]}),_:2},1024),o(D,{name:"startAt"},{default:t(()=>[r(s(e.startAt.formatted)+" ("+s(e.duration)+" "+s(i.$trans("list.durations.m"))+") ",1),o(R,{block:""},{default:t(()=>[r(s(e.platform.label),1)]),_:2},1024),e.showUrl?(n(),h(R,{key:0,block:""},{default:t(()=>[u("a",{href:e.meetingUrl,target:"_blank"},s(e.meetingUrl),9,ge),e.password?(n(),g("span",ve,"("+s(e.password)+")",1)):y("",!0)]),_:2},1024)):y("",!0)]),_:2},1024),o(D,{name:"action"},{default:t(()=>[o(N,null,{default:t(()=>[o(F,{icon:"fas fa-arrow-circle-right",onClick:p=>d(v).push({name:"ResourceOnlineClassShow",params:{uuid:e.uuid}})},{default:t(()=>[r(s(i.$trans("general.show")),1)]),_:2},1032,["onClick"]),d(w)("online-class:edit")&&e.isEditable?(n(),h(F,{key:0,icon:"fas fa-edit",onClick:p=>d(v).push({name:"ResourceOnlineClassEdit",params:{uuid:e.uuid}})},{default:t(()=>[r(s(i.$trans("general.edit")),1)]),_:2},1032,["onClick"])):y("",!0),d(w)("online-class:create")?(n(),h(F,{key:1,icon:"fas fa-copy",onClick:p=>d(v).push({name:"ResourceOnlineClassDuplicate",params:{uuid:e.uuid}})},{default:t(()=>[r(s(i.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):y("",!0),d(w)("online-class:delete")&&e.isDeletable?(n(),h(F,{key:2,icon:"fas fa-trash",onClick:p=>d(_).emit("deleteItem",{uuid:e.uuid})},{default:t(()=>[r(s(i.$trans("general.delete")),1)]),_:2},1032,["onClick"])):y("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1}))]),_:1})}}});export{we as default};
