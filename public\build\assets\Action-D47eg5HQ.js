import{i as Y,u as I,h as Z,I as x,l as F,n as ee,r as m,q as _,o as c,w as i,e as l,b as $,d as u,f as s,s as b,t as d,a as v,F as T,J as j,v as te,aT as ae,K as q}from"./app-DvIo72ZO.js";import{u as oe}from"./useCustomFields-XeNAQP8g.js";const re={class:"grid grid-cols-3 gap-6"},se={class:"col-span-3 sm:col-span-1"},ne={class:"col-span-3 sm:col-span-1"},le={key:0},de={key:1,class:"text-danger"},ie={class:"col-span-3 sm:col-span-1"},ue={class:"grid grid-cols-3 gap-6"},me={class:"col-span-3"},pe={key:0,class:"col-span-3"},ce={class:"col-span-3 sm:col-span-2"},ge={class:"flex"},be={class:"col-span-3 sm:col-span-1"},fe={class:"col-span-3 sm:col-span-1"},Ve={class:"col-span-3 sm:col-span-1"},ve={class:"mt-4 grid grid-cols-3 gap-6"},Ne={class:"col-span-3"},$e={key:0,class:"col-span-3 sm:col-span-2"},ye={class:"col-span-3 sm:col-span-1"},Ue=["onClick"],_e={class:"col-span-3 sm:col-span-1"},Fe={class:"col-span-3 sm:col-span-1"},Te={class:"mt-2 flex justify-end"},Be={name:"StudentRegistrationForm"},Ce=Object.assign(Be,{setup(L){Y();const N=I();Z();const g={period:"",course:"",date:"",studentType:"new",student:"",firstName:"",middleName:"",thirdName:"",lastName:"",birthDate:"",gender:"",contactNumber:"",guardians:[],customFields:[]},B="student/registration/",r=x(B),f=F({genders:[],studentTypes:[],guardianTypes:[],relations:[],periods:[]}),V=F({selectedCourse:null}),o=F({...g}),y=F({course:"",isLoaded:!N.params.uuid}),{customFields:O,setCustomFields:h}=oe(),A=a=>{Object.assign(f,a),h(f.customFields),g.customFields=O.value,Object.assign(o,q(g))},P=a=>{var t,U;Object.assign(g,{period:a.period.uuid,course:(t=a.course)==null?void 0:t.uuid,date:a.date.value}),h(f.customFields,a.customFields),g.customFields=O.value,Object.assign(o,q(g)),V.selectedCourse=a.course,y.period=a.period.name,y.course=(U=a.course)==null?void 0:U.name,y.isLoaded=!0},H=a=>{o.course=a?a.uuid:""},z=()=>{N.params.uuid||(V.selectedCourse=null,o.course="")},w=()=>{o.guardians.push(J())},G=async a=>{await ae()&&o.guardians.splice(a,1)},J=()=>({guardianType:"new",guardian:"",name:"",relation:"",contactNumber:""});return ee(()=>{N.params.uuid||w()}),(a,t)=>{const U=m("BaseSelect"),C=m("BaseSelectSearch"),K=m("FormInfo"),E=m("DatePicker"),k=m("BaseFieldset"),S=m("BaseRadioGroup"),R=m("BaseLabel"),M=m("NameInput"),D=m("BaseInput"),Q=m("BaseButton"),W=m("CustomField"),X=m("FormAction");return c(),_(X,{"pre-requisites":!0,onSetPreRequisites:A,"init-url":B,"init-form":g,form:o,"set-form":P,redirect:"StudentRegistration"},{default:i(()=>[l(k,null,{legend:i(()=>[b(d(a.$trans("student.registration.registration")),1)]),default:i(()=>[u("div",re,[u("div",se,[l(U,{modelValue:o.period,"onUpdate:modelValue":t[0]||(t[0]=e=>o.period=e),name:"period",label:a.$trans("global.select",{attribute:a.$trans("academic.period.period")}),options:f.periods,"label-prop":"name","value-prop":"uuid",error:s(r).period,"onUpdate:error":t[1]||(t[1]=e=>s(r).period=e),onChange:z},null,8,["modelValue","label","options","error"])]),u("div",ne,[y.isLoaded?(c(),_(C,{key:0,name:"course",label:a.$trans("global.select",{attribute:a.$trans("academic.course.course")}),modelValue:V.selectedCourse,"onUpdate:modelValue":t[2]||(t[2]=e=>V.selectedCourse=e),error:s(r).course,"onUpdate:error":t[3]||(t[3]=e=>s(r).course=e),"value-prop":"uuid","object-prop":!0,"init-search":y.course,"init-search-key":"name",onChange:H,"additional-search-query":{period:o.period},"search-action":"academic/course/list"},{selectedOption:i(e=>[b(d(e.value.division.name)+" - "+d(e.value.name),1)]),listOption:i(e=>[b(d(e.option.division.name)+" - "+d(e.option.name),1)]),_:1},8,["label","modelValue","error","init-search","additional-search-query"])):$("",!0),y.isLoaded&&V.selectedCourse?(c(),_(K,{key:1},{default:i(()=>[V.selectedCourse.enableRegistration?(c(),v("span",le,d(a.$trans("academic.course.registration_fee_info",{attribute:V.selectedCourse.registrationFee.formatted})),1)):(c(),v("span",de,d(a.$trans("academic.course.registration_disabled_info")),1))]),_:1})):$("",!0)]),u("div",ie,[l(E,{modelValue:o.date,"onUpdate:modelValue":t[4]||(t[4]=e=>o.date=e),name:"date",label:a.$trans("student.registration.props.date"),"no-clear":"",error:s(r).date,"onUpdate:error":t[5]||(t[5]=e=>s(r).date=e)},null,8,["modelValue","label","error"])])])]),_:1}),s(N).params.uuid?$("",!0):(c(),_(k,{key:0,class:"mt-4"},{legend:i(()=>[b(d(a.$trans("student.student")),1)]),default:i(()=>[u("div",ue,[u("div",me,[l(S,{"top-margin":"",options:f.studentTypes,name:"studentType",modelValue:o.studentType,"onUpdate:modelValue":t[6]||(t[6]=e=>o.studentType=e),error:s(r).studentType,"onUpdate:error":t[7]||(t[7]=e=>s(r).studentType=e),horizontal:""},null,8,["options","modelValue","error"])]),o.studentType=="existing"?(c(),v("div",pe,[l(C,{name:"student",label:a.$trans("global.select",{attribute:a.$trans("student.student")}),modelValue:o.student,"onUpdate:modelValue":t[8]||(t[8]=e=>o.student=e),error:s(r).student,"onUpdate:error":t[9]||(t[9]=e=>s(r).student=e),"value-prop":"uuid","search-action":"student/summary","additional-search-query":{allPeriods:!0,status:"all"}},{selectedOption:i(e=>[b(d(e.value.name)+" ("+d(e.value.contactNumber)+") ",1)]),listOption:i(e=>[b(d(e.option.name)+" ("+d(e.option.contactNumber)+") ",1)]),_:1},8,["label","modelValue","error"])])):$("",!0),o.studentType=="new"?(c(),v(T,{key:1},[u("div",ce,[l(R,null,{default:i(()=>[b(d(a.$trans("student.props.name")),1)]),_:1}),u("div",ge,[l(M,{firstName:o.firstName,"onUpdate:firstName":t[10]||(t[10]=e=>o.firstName=e),middleName:o.middleName,"onUpdate:middleName":t[11]||(t[11]=e=>o.middleName=e),thirdName:o.thirdName,"onUpdate:thirdName":t[12]||(t[12]=e=>o.thirdName=e),lastName:o.lastName,"onUpdate:lastName":t[13]||(t[13]=e=>o.lastName=e),formErrors:s(r),"onUpdate:formErrors":t[14]||(t[14]=e=>j(r)?r.value=e:null)},null,8,["firstName","middleName","thirdName","lastName","formErrors"])])]),u("div",be,[l(R,null,{default:i(()=>[b(d(a.$trans("contact.props.gender")),1)]),_:1}),l(S,{"top-margin":"",options:f.genders,name:"gender",modelValue:o.gender,"onUpdate:modelValue":t[15]||(t[15]=e=>o.gender=e),error:s(r).gender,"onUpdate:error":t[16]||(t[16]=e=>s(r).gender=e),horizontal:""},null,8,["options","modelValue","error"])]),u("div",fe,[l(E,{modelValue:o.birthDate,"onUpdate:modelValue":t[17]||(t[17]=e=>o.birthDate=e),name:"birthDate",label:a.$trans("contact.props.birth_date"),"no-clear":"",error:s(r).birthDate,"onUpdate:error":t[18]||(t[18]=e=>s(r).birthDate=e)},null,8,["modelValue","label","error"])]),u("div",Ve,[l(D,{type:"text",modelValue:o.contactNumber,"onUpdate:modelValue":t[19]||(t[19]=e=>o.contactNumber=e),name:"contactNumber",label:a.$trans("contact.props.contact_number"),error:s(r).contactNumber,"onUpdate:error":t[20]||(t[20]=e=>s(r).contactNumber=e)},null,8,["modelValue","label","error"])])],64)):$("",!0)])]),_:1})),o.studentType=="new"&&!s(N).params.uuid?(c(),_(k,{key:1,class:"mt-4"},{legend:i(()=>[b(d(a.$trans("guardian.guardian")),1)]),default:i(()=>[(c(!0),v(T,null,te(o.guardians,(e,p)=>(c(),v("div",ve,[u("div",Ne,[l(S,{"top-margin":"",options:f.guardianTypes,name:`guardians.${p}.guardianType`,modelValue:e.guardianType,"onUpdate:modelValue":n=>e.guardianType=n,error:s(r)[`guardians.${p}.guardianType`],"onUpdate:error":n=>s(r)[`guardians.${p}.guardianType`]=n,horizontal:""},null,8,["options","name","modelValue","onUpdate:modelValue","error","onUpdate:error"])]),e.guardianType=="existing"?(c(),v("div",$e,[l(C,{name:`guardians.${p}.guardian`,label:a.$trans("global.select",{attribute:a.$trans("guardian.guardian")}),modelValue:e.guardian,"onUpdate:modelValue":n=>e.guardian=n,error:s(r)[`guardians.${p}.guardian`],"onUpdate:error":n=>s(r)[`guardians.${p}.guardian`]=n,"value-prop":"uuid","search-action":"guardian/list"},{selectedOption:i(n=>[b(d(n.value.name)+" ("+d(n.value.contactNumber)+") ",1)]),listOption:i(n=>[b(d(n.option.name)+" ("+d(n.option.contactNumber)+") ",1)]),_:2},1032,["name","label","modelValue","onUpdate:modelValue","error","onUpdate:error"])])):$("",!0),e.guardianType=="new"?(c(),v(T,{key:1},[u("div",ye,[l(R,null,{default:i(()=>[b(d(a.$trans("guardian.props.name"))+" ",1),u("span",{class:"text-danger cursor-pointer",onClick:n=>G(p)},t[24]||(t[24]=[u("i",{class:"fas fa-times-circle"},null,-1)]),8,Ue)]),_:2},1024),l(D,{type:"text",modelValue:e.name,"onUpdate:modelValue":n=>e.name=n,name:"name",placeholder:a.$trans("guardian.props.name"),error:s(r)[`guardians.${p}.name`],"onUpdate:error":n=>s(r)[`guardians.${p}.name`]=n},null,8,["modelValue","onUpdate:modelValue","placeholder","error","onUpdate:error"])]),u("div",_e,[l(D,{type:"text",modelValue:e.contactNumber,"onUpdate:modelValue":n=>e.contactNumber=n,name:"contactNumber",label:a.$trans("contact.props.contact_number"),error:s(r)[`guardians.${p}.contactNumber`],"onUpdate:error":n=>s(r)[`guardians.${p}.contactNumber`]=n},null,8,["modelValue","onUpdate:modelValue","label","error","onUpdate:error"])])],64)):$("",!0),u("div",Fe,[l(U,{modelValue:e.relation,"onUpdate:modelValue":n=>e.relation=n,name:`guardians.${p}.relation`,label:a.$trans("contact.props.relation"),options:f.relations,"label-prop":"label","value-prop":"value",error:s(r)[`guardians.${p}.relation`],"onUpdate:error":n=>s(r)[`guardians.${p}.relation`]=n},null,8,["modelValue","onUpdate:modelValue","name","label","options","error","onUpdate:error"])])]))),256)),u("div",Te,[l(Q,{design:"primary",onClick:t[21]||(t[21]=e=>w())},{default:i(()=>[b(d(a.$trans("global.add",{attribute:a.$trans("guardian.guardian")})),1)]),_:1})])]),_:1})):$("",!0),l(W,{customFields:o.customFields,"onUpdate:customFields":t[22]||(t[22]=e=>o.customFields=e),formErrors:s(r),"onUpdate:formErrors":t[23]||(t[23]=e=>j(r)?r.value=e:null)},null,8,["customFields","formErrors"])]),_:1},8,["form"])}}}),ke={name:"StudentRegistrationAction"},De=Object.assign(ke,{setup(L){const N=I();return(g,B)=>{const r=m("PageHeaderAction"),f=m("PageHeader"),V=m("ParentTransition");return c(),v(T,null,[l(f,{title:g.$trans(s(N).meta.trans,{attribute:g.$trans(s(N).meta.label)}),navs:[{label:g.$trans("student.student"),path:"Student"},{label:g.$trans("student.registration.registration"),path:"StudentRegistration"}]},{default:i(()=>[l(r,{name:"StudentRegistration",title:g.$trans("student.registration.registration"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(V,{appear:"",visibility:!0},{default:i(()=>[l(Ce)]),_:1})],64)}}});export{De as default};
