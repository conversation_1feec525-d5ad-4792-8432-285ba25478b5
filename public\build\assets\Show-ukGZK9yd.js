import{i as B,u as C,h as L,j as I,l as S,r as n,a as T,o as _,e as o,w as e,f as a,q as D,b as j,d as g,s as i,t as c,F as H}from"./app-DvIo72ZO.js";const N={class:"space-y-2"},R={class:"grid grid-cols-1 gap-x-4 gap-y-8 px-4 pt-4 sm:grid-cols-2"},$={name:"AcademicProgramShow"},q=Object.assign($,{setup(F){B();const d=C(),b=L(),t=I("$trans"),f={},y="academic/program/";t("academic.period.period");const r=S({...f}),V=m=>{Object.assign(r,m)};return(m,s)=>{const P=n("PageHeaderAction"),v=n("PageHeader"),p=n("TextMuted"),l=n("ListItemView"),w=n("ListContainerVertical"),u=n("BaseCard"),A=n("BaseDataView"),h=n("DetailLayoutVertical"),k=n("ShowItem"),x=n("ParentTransition");return _(),T(H,null,[o(v,{title:a(t)(a(d).meta.trans,{attribute:a(t)(a(d).meta.label)}),navs:[{label:a(t)("academic.academic"),path:"Academic"},{label:a(t)("academic.program.program"),path:"AcademicProgramList"}]},{default:e(()=>[o(P,{name:"AcademicProgram",title:a(t)("academic.program.program"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),o(x,{appear:"",visibility:!0},{default:e(()=>[o(k,{"init-url":y,uuid:a(d).params.uuid,onSetItem:V,onRedirectTo:s[0]||(s[0]=M=>a(b).push({name:"AcademicProgram"}))},{default:e(()=>[r.uuid?(_(),D(h,{key:0},{detail:e(()=>[g("div",N,[o(u,{"no-padding":"","no-content-padding":""},{title:e(()=>[i(c(a(t)("academic.program.program")),1)]),action:e(()=>s[1]||(s[1]=[])),default:e(()=>[o(w,null,{default:e(()=>[o(l,{label:a(t)("academic.program.props.name")},{default:e(()=>[i(c(r.name)+" ",1),o(p,{block:""},{default:e(()=>[i(c(r.type.label),1)]),_:1})]),_:1},8,["label"]),o(l,{label:a(t)("academic.program.props.code")},{default:e(()=>[i(c(r.code)+" ",1),o(p,{block:""},{default:e(()=>[i(c(r.shortcode),1)]),_:1})]),_:1},8,["label"]),o(l,{label:a(t)("academic.program.props.alias")},{default:e(()=>[i(c(r.alias),1)]),_:1},8,["label"]),o(l,{label:a(t)("general.created_at")},{default:e(()=>[i(c(r.createdAt.formatted),1)]),_:1},8,["label"]),o(l,{label:a(t)("general.updated_at")},{default:e(()=>[i(c(r.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})])]),default:e(()=>[o(u,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:e(()=>[i(c(a(t)("global.detail",{attribute:a(t)("academic.program.program")})),1)]),default:e(()=>[g("dl",R,[o(A,{class:"col-span-1 sm:col-span-2",label:a(t)("academic.program.props.description")},{default:e(()=>[i(c(r.description),1)]),_:1},8,["label"])])]),_:1})]),_:1})):j("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{q as default};
