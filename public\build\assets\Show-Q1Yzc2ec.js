import{i as S,u as C,h as D,l as I,r,a as N,o as i,e as n,w as a,f as l,q as d,b as m,d as h,s,t as o,F as V}from"./app-DvIo72ZO.js";const z={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},A={class:"flex flex-wrap gap-2"},H={name:"FinancePaymentMethodShow"},j=Object.assign(H,{setup(R){S();const c=C(),f=D(),y={},b="finance/paymentMethod/",t=I({...y}),g=e=>{Object.assign(t,e)};return(e,_)=>{const B=r("PageHeaderAction"),$=r("PageHeader"),u=r("BaseDataView"),p=r("BaseBadge"),k=r("BaseButton"),P=r("ShowButton"),w=r("BaseCard"),v=r("ShowItem"),F=r("ParentTransition");return i(),N(V,null,[n($,{title:e.$trans(l(c).meta.trans,{attribute:e.$trans(l(c).meta.label)}),navs:[{label:e.$trans("finance.finance"),path:"Finance"},{label:e.$trans("finance.payment_method.payment_method"),path:"FinancePaymentMethod"}]},{default:a(()=>[n(B,{name:"FinancePaymentMethod",title:e.$trans("finance.payment_method.payment_method"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(F,{appear:"",visibility:!0},{default:a(()=>[n(v,{"init-url":b,uuid:l(c).params.uuid,"module-uuid":l(c).params.muuid,onSetItem:g,onRedirectTo:_[1]||(_[1]=M=>l(f).push({name:"FinancePaymentMethod",params:{uuid:t.uuid}}))},{default:a(()=>[t.uuid?(i(),d(w,{key:0},{title:a(()=>[s(o(t.name),1)]),footer:a(()=>[n(P,null,{default:a(()=>[n(k,{design:"primary",onClick:_[0]||(_[0]=M=>l(f).push({name:"FinancePaymentMethodEdit",params:{uuid:t.uuid}}))},{default:a(()=>[s(o(e.$trans("general.edit")),1)]),_:1})]),_:1})]),default:a(()=>[h("dl",z,[n(u,{class:"col-span-1 sm:col-span-2",label:e.$trans("finance.payment_method.props.description")},{default:a(()=>[s(o(t.code),1)]),_:1},8,["label"]),n(u,{class:"col-span-1 sm:col-span-2",label:e.$trans("finance.payment_method.props.description")},{default:a(()=>[h("div",A,[t.hasInstrumentNumber?(i(),d(p,{key:0,size:"sm"},{default:a(()=>[s(o(e.$trans("finance.payment_method.props.has_instrument_number")),1)]),_:1})):m("",!0),t.hasInstrumentDate?(i(),d(p,{key:1,size:"sm"},{default:a(()=>[s(o(e.$trans("finance.payment_method.props.has_instrument_date")),1)]),_:1})):m("",!0),t.hasClearingDate?(i(),d(p,{key:2,size:"sm"},{default:a(()=>[s(o(e.$trans("finance.payment_method.props.has_clearing_date")),1)]),_:1})):m("",!0),t.hasBankDetail?(i(),d(p,{key:3,size:"sm"},{default:a(()=>[s(o(e.$trans("finance.payment_method.props.has_bank_detail")),1)]),_:1})):m("",!0),t.hasReferenceNumber?(i(),d(p,{key:4,size:"sm"},{default:a(()=>[s(o(e.$trans("finance.payment_method.props.has_reference_number")),1)]),_:1})):m("",!0)])]),_:1},8,["label"]),n(u,{class:"col-span-1 sm:col-span-2",label:e.$trans("finance.payment_method.props.description")},{default:a(()=>[s(o(t.description),1)]),_:1},8,["label"]),n(u,{label:e.$trans("general.created_at")},{default:a(()=>[s(o(t.createdAt.formatted),1)]),_:1},8,["label"]),n(u,{label:e.$trans("general.updated_at")},{default:a(()=>[s(o(t.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):m("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{j as default};
