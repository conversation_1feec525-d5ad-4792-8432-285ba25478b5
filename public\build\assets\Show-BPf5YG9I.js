import{i as F,u as O,h as q,j as U,m as z,l as G,r as m,a as b,o as p,e as t,w as e,f as o,q as f,b as E,d,s as i,t as a,F as g,v as x,y as J}from"./app-DvIo72ZO.js";const K={class:"grid grid-cols-1 gap-x-4 gap-y-8 px-4 py-2 sm:grid-cols-4"},M={class:"font-semibold"},Q={class:"font-semibold"},W={class:"mt-4 grid grid-cols-2 border-t border-gray-200 dark:border-gray-700"},X={class:"col-span-2 border-r border-gray-200 dark:border-gray-700 sm:col-span-1"},Y={class:"text-success"},Z={class:"text-success"},ee={class:"text-success text-md font-semibold"},te={class:"text-success text-md font-semibold"},ae={class:"text-success text-xl font-semibold"},le={class:"text-success text-xl font-semibold"},oe={class:"text-success"},ne={class:"text-success"},se={class:"text-success text-xl font-semibold"},re={class:"text-success text-xl font-semibold"},de={class:"col-span-2 sm:col-span-1"},ue={class:"text-danger"},ie={class:"text-danger"},pe={class:"text-danger"},me={class:"text-danger"},ce={class:"text-danger text-md font-semibold"},_e={class:"text-danger text-md font-semibold"},ye={name:"EmployeePayrollShow"},ge=Object.assign(ye,{setup(fe){F();const k=O(),w=q(),n=U("$trans"),j={records:[]},L="employee/payroll/",D=[{key:"payHead",label:n("employee.payroll.pay_head.pay_head"),visibility:!0},{key:"amount",label:n("employee.payroll.props.amount"),visibility:!0}],P=v=>{let r=s.records.filter(_=>_.payHead.category.value=="earning").length,h=s.records.filter(_=>_.payHead.category.value=="deduction").length,C=s.records.filter(_=>_.payHead.category.value=="employee_contribution").length,H=s.records.filter(_=>_.payHead.category.value=="employer_contribution").length;return v=="earning"&&r<h+C?h+C-r:v=="deduction"&&h+H<r?r-(h+H):0},S=z(!1),s=G({...j}),I=v=>{Object.assign(s,v)};return(v,r)=>{const h=m("PageHeaderAction"),C=m("PageHeader"),H=m("ListItemView"),_=m("ListContainerVertical"),B=m("BaseCard"),y=m("BaseDataView"),u=m("DataCell"),c=m("DataRow"),V=m("SimpleTable"),N=m("BaseButton"),R=m("ShowButton"),T=m("DetailLayoutVertical"),$=m("ShowItem"),A=m("ParentTransition");return p(),b(g,null,[t(C,{title:o(n)(o(k).meta.trans,{attribute:o(n)(o(k).meta.label)}),navs:[{label:o(n)("employee.employee"),path:"Employee"},{label:o(n)("employee.payroll.payroll"),path:"EmployeePayrollList"}]},{default:e(()=>[t(h,{name:"EmployeePayroll",title:o(n)("employee.payroll.payroll"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(A,{appear:"",visibility:!0},{default:e(()=>[t($,{"init-url":L,uuid:o(k).params.uuid,onSetItem:I,onRedirectTo:r[1]||(r[1]=l=>o(w).push({name:"EmployeePayroll"})),refresh:S.value,onRefreshed:r[2]||(r[2]=l=>S.value=!1)},{default:e(()=>[s.uuid?(p(),f(T,{key:0},{detail:e(()=>[t(B,{"no-padding":"","no-content-padding":""},{title:e(()=>[i(a(o(n)("employee.payroll.props.code_number"))+" "+a(s.codeNumber),1)]),default:e(()=>[t(_,null,{default:e(()=>[t(H,{label:o(n)("general.created_at")},{default:e(()=>[i(a(s.createdAt.formatted),1)]),_:1},8,["label"]),t(H,{label:o(n)("general.updated_at")},{default:e(()=>[i(a(s.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1}),t(B,{class:"mt-4","no-padding":"","no-content-padding":""},{title:e(()=>[i(a(o(n)("global.summary",{attribute:o(n)("attendance.attendance")})),1)]),default:e(()=>[t(_,null,{default:e(()=>[(p(!0),b(g,null,x(s.attendanceSummary,l=>(p(),f(H,{align:"right",label:l.name+" ("+l.code+")"},{default:e(()=>[i(a(l.count)+" "+a(o(n)("list.durations."+l.unit)),1)]),_:2},1032,["label"]))),256))]),_:1})]),_:1})]),default:e(()=>[t(B,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:e(()=>[i(a(o(n)("employee.payroll.payroll")),1)]),footer:e(()=>[t(R,null,{default:e(()=>[o(J)("payroll:edit")?(p(),f(N,{key:0,design:"primary",onClick:r[0]||(r[0]=l=>o(w).push({name:"EmployeePayrollEdit",params:{uuid:s.uuid}}))},{default:e(()=>[i(a(o(n)("general.edit")),1)]),_:1})):E("",!0)]),_:1})]),default:e(()=>[d("dl",K,[t(y,{label:o(n)("employee.employee")},{default:e(()=>[i(a(s.employee.name),1)]),_:1},8,["label"]),t(y,{label:o(n)("employee.props.code_number")},{default:e(()=>[i(a(s.employee.codeNumber),1)]),_:1},8,["label"]),t(y,{label:o(n)("employee.props.joining_date")},{default:e(()=>[i(a(s.employee.joiningDate.formatted),1)]),_:1},8,["label"]),t(y,{label:o(n)("employee.props.joining_date")},{default:e(()=>[i(a(s.employee.joiningDate.formatted),1)]),_:1},8,["label"]),t(y,{label:o(n)("employee.department.department")},{default:e(()=>[i(a(s.employee.department),1)]),_:1},8,["label"]),t(y,{label:o(n)("employee.designation.designation")},{default:e(()=>[i(a(s.employee.designation),1)]),_:1},8,["label"]),t(y,{label:o(n)("employee.employment_status.employment_status")},{default:e(()=>[i(a(s.employee.employmentStatus),1)]),_:1},8,["label"]),t(y,{class:"col-span-1 sm:col-span-2",label:o(n)("employee.payroll.props.period")},{default:e(()=>[d("span",M,a(s.period),1)]),_:1},8,["label"]),t(y,{class:"col-span-1 sm:col-span-2",label:o(n)("employee.payroll.props.duration")},{default:e(()=>[d("span",Q,a(s.duration),1)]),_:1},8,["label"])]),d("div",W,[d("div",X,[t(V,{corner:"sharp",header:D},{default:e(()=>[(p(!0),b(g,null,x(s.records.filter(l=>l.payHead.category.value=="earning"),l=>(p(),f(c,{key:l.uuid},{default:e(()=>[t(u,{name:"payHead"},{default:e(()=>[d("span",Y,a(l.payHead.name),1)]),_:2},1024),t(u,{name:"amount"},{default:e(()=>[d("span",Z,a(l.amount.formatted),1)]),_:2},1024)]),_:2},1024))),128)),(p(!0),b(g,null,x(P("earning"),l=>(p(),f(c,null,{default:e(()=>[t(u,{name:"payHead"},{default:e(()=>r[3]||(r[3]=[i(" ")])),_:1}),t(u,{name:"amount"},{default:e(()=>r[4]||(r[4]=[i(" ")])),_:1})]),_:1}))),256)),t(c,null,{default:e(()=>[t(u,{name:"payHead"},{default:e(()=>[d("span",ee,a(o(n)("employee.payroll.salary_structure.props.net_earning")),1)]),_:1}),t(u,{name:"amount"},{default:e(()=>[d("span",te,a(s.netEarning.formatted),1)]),_:1})]),_:1}),t(c,null,{default:e(()=>[t(u,{name:"payHead"},{default:e(()=>[d("span",ae,a(o(n)("employee.payroll.salary_structure.props.net_salary")),1)]),_:1}),t(u,{name:"amount"},{default:e(()=>[d("span",le,a(s.total.formatted),1)]),_:1})]),_:1}),(p(!0),b(g,null,x(s.records.filter(l=>l.payHead.category.value=="employer_contribution"),l=>(p(),f(c,{key:l.uuid},{default:e(()=>[t(u,{name:"payHead"},{default:e(()=>[d("span",oe,a(l.payHead.name),1)]),_:2},1024),t(u,{name:"amount"},{default:e(()=>[d("span",ne,a(l.amount.formatted),1)]),_:2},1024)]),_:2},1024))),128)),t(c,null,{default:e(()=>[t(u,{name:"payHead"},{default:e(()=>[d("span",se,a(o(n)("employee.payroll.salary_structure.props.net_employer_contribution")),1)]),_:1}),t(u,{name:"amount"},{default:e(()=>[d("span",re,a(s.employerContribution.formatted),1)]),_:1})]),_:1})]),_:1})]),d("div",de,[t(V,{corner:"sharp",header:D},{default:e(()=>[(p(!0),b(g,null,x(s.records.filter(l=>l.payHead.category.value=="deduction"),l=>(p(),f(c,{key:l.uuid},{default:e(()=>[t(u,{name:"payHead"},{default:e(()=>[d("span",ue,a(l.payHead.name),1)]),_:2},1024),t(u,{name:"amount"},{default:e(()=>[d("span",ie,a(l.amount.formatted),1)]),_:2},1024)]),_:2},1024))),128)),(p(!0),b(g,null,x(s.records.filter(l=>l.payHead.category.value=="employee_contribution"),l=>(p(),f(c,{key:l.uuid},{default:e(()=>[t(u,{name:"payHead"},{default:e(()=>[d("span",pe,a(l.payHead.name),1)]),_:2},1024),t(u,{name:"amount"},{default:e(()=>[d("span",me,a(l.amount.formatted),1)]),_:2},1024)]),_:2},1024))),128)),(p(!0),b(g,null,x(P("deduction"),l=>(p(),f(c,null,{default:e(()=>[t(u,{name:"payHead"},{default:e(()=>r[5]||(r[5]=[i(" ")])),_:1}),t(u,{name:"amount"},{default:e(()=>r[6]||(r[6]=[i(" ")])),_:1})]),_:1}))),256)),t(c,null,{default:e(()=>[t(u,{name:"payHead"},{default:e(()=>[d("span",ce,a(o(n)("employee.payroll.salary_structure.props.net_deduction")),1)]),_:1}),t(u,{name:"amount"},{default:e(()=>[d("span",_e,a(s.netDeduction.formatted),1)]),_:1})]),_:1}),t(c,null,{default:e(()=>[t(u,{name:"payHead"},{default:e(()=>r[7]||(r[7]=[d("span",{class:"text-success text-xl font-semibold"}," ",-1)])),_:1}),t(u,{name:"amount"},{default:e(()=>r[8]||(r[8]=[i(" ")])),_:1})]),_:1})]),_:1})])])]),_:1})]),_:1})):E("",!0)]),_:1},8,["uuid","refresh"])]),_:1})],64)}}});export{ge as default};
