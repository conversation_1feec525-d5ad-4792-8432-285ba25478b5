import{u as T,l as L,I as D,r as u,q as v,o as i,w as t,d as m,e as a,f as e,s as l,t as n,h as Y,j as q,m as M,a as k,b as g,F as U,v as E,y as O,x as Z,B as P}from"./app-DvIo72ZO.js";const ee={class:"grid grid-cols-3 gap-6"},te={class:"col-span-3 sm:col-span-1"},oe={class:"col-span-3"},ne={class:"col-span-3"},ae={class:"col-span-3"},se={name:"ReceptionComplaintActionForm"},le=Object.assign(se,{props:{complaint:{type:Object,required:!0}},emits:["completed"],setup(F,{emit:B}){const w=B;T();const V={status:"",action:"",comment:"",remarks:""},s="reception/complaint/",h=L({statuses:[]}),b=D(s),_=L({...V}),o=y=>{Object.assign(h,y)},C=()=>{w("completed")};return(y,c)=>{const f=u("BaseSelect"),d=u("BaseTextarea"),N=u("FormAction");return i(),v(N,{"no-card":"","pre-requisites":!0,onSetPreRequisites:o,"keep-adding":!1,"init-url":s,uuid:F.complaint.uuid,"no-data-fetch":!0,action:"addLog","init-form":V,form:_,"after-submit":C},{default:t(()=>[m("div",ee,[m("div",te,[a(f,{name:"status",label:y.$trans("global.select",{attribute:y.$trans("reception.complaint.props.status")}),modelValue:_.status,"onUpdate:modelValue":c[0]||(c[0]=p=>_.status=p),options:h.statuses,error:e(b).status,"onUpdate:error":c[1]||(c[1]=p=>e(b).status=p)},null,8,["label","modelValue","options","error"])]),m("div",oe,[a(d,{rows:1,modelValue:_.action,"onUpdate:modelValue":c[2]||(c[2]=p=>_.action=p),name:"action",label:y.$trans("reception.complaint.props.action"),error:e(b).action,"onUpdate:error":c[3]||(c[3]=p=>e(b).action=p)},null,8,["modelValue","label","error"])]),m("div",ne,[a(d,{rows:1,modelValue:_.comment,"onUpdate:modelValue":c[4]||(c[4]=p=>_.comment=p),name:"comment",label:y.$trans("reception.complaint.props.comment"),error:e(b).comment,"onUpdate:error":c[5]||(c[5]=p=>e(b).comment=p)},null,8,["modelValue","label","error"])]),m("div",ae,[a(d,{rows:1,modelValue:_.remarks,"onUpdate:modelValue":c[6]||(c[6]=p=>_.remarks=p),name:"remarks",label:y.$trans("reception.complaint.props.remarks"),error:e(b).remarks,"onUpdate:error":c[7]||(c[7]=p=>e(b).remarks=p)},null,8,["modelValue","label","error"])])])]),_:1},8,["uuid","form"])}}}),ie={class:"grid grid-cols-1 gap-6"},re={class:"col-span-1"},ce={name:"ReceptionComplaintAssignForm"},ue=Object.assign(ce,{props:{complaint:{type:Object,required:!0}},emits:["completed"],setup(F,{emit:B}){const w=B;T();const V={employee:""},s="reception/complaint/",h=D(s),b=L({...V}),_=()=>{w("completed")};return(o,C)=>{const y=u("BaseSelectSearch"),c=u("FormAction");return i(),v(c,{"no-card":"","keep-adding":!1,"init-url":s,uuid:F.complaint.uuid,"no-data-fetch":!0,action:"assign","init-form":V,form:b,"after-submit":_},{default:t(()=>[m("div",ie,[m("div",re,[a(y,{name:"employee",label:o.$trans("employee.employee"),placeholder:o.$trans("global.assign",{attribute:o.$trans("employee.employee")}),modelValue:b.employee,"onUpdate:modelValue":C[0]||(C[0]=f=>b.employee=f),error:e(h).employee,"onUpdate:error":C[1]||(C[1]=f=>e(h).employee=f),"value-prop":"uuid","init-search":b.employee,"search-key":"name","search-action":"employee/summary"},{selectedOption:t(f=>[l(n(f.value.name)+" ("+n(f.value.designation)+") ",1)]),listOption:t(f=>[l(n(f.option.name)+" ("+n(f.option.designation)+") ",1)]),_:1},8,["label","placeholder","modelValue","error","init-search"])])])]),_:1},8,["uuid","form"])}}}),me={class:"space-y-4"},de={class:"px-4 py-2 grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},pe={class:"font-semibold"},_e=["onMouseover"],fe=["onClick"],be={class:"p-4"},ge={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},ye={key:0,class:"text-xs"},ve={key:1,class:"text-xs"},ke={key:2,class:"text-xs"},$e={class:"mt-4 flex items-center justify-between text-xs"},Be=["onClick"],Ve={name:"ReceptionComplaintShow"},he=Object.assign(Ve,{setup(F){const B=T(),w=Y(),V=q("emitter"),s=q("$trans"),h={},b="reception/complaint/",_=M(!1),o=L({...h}),C=f=>{Object.assign(o,f)},y=M(null),c=async f=>{V.emit("showActionItem",{uuid:o.uuid,moduleUuid:f,action:"unassign",confirmation:!0})};return(f,d)=>{const N=u("PageHeaderAction"),p=u("PageHeader"),j=u("BaseBadge"),$=u("ListItemView"),x=u("TextMuted"),H=u("ListContainerVertical"),S=u("BaseCard"),R=u("BaseDataView"),z=u("ListMedia"),G=u("BaseButton"),J=u("ShowButton"),K=u("BaseFieldset"),Q=u("DetailLayoutVertical"),W=u("ShowItem"),X=u("ParentTransition");return i(),k(U,null,[a(p,{title:e(s)(e(B).meta.trans,{attribute:e(s)(e(B).meta.label)}),navs:[{label:e(s)("reception.reception"),path:"Reception"},{label:e(s)("reception.complaint.complaint"),path:"ReceptionComplaint"}]},{default:t(()=>[a(N,{name:"ReceptionComplaint",title:e(s)("reception.complaint.complaint"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(X,{appear:"",visibility:!0},{default:t(()=>[a(W,{"init-url":b,uuid:e(B).params.uuid,"module-uuid":e(B).params.muuid,onSetItem:C,onRedirectTo:d[4]||(d[4]=r=>e(w).push({name:"ReceptionComplaint",params:{uuid:o.uuid}})),refresh:_.value,onRefreshed:d[5]||(d[5]=r=>_.value=!1)},{default:t(()=>[o.uuid?(i(),v(Q,{key:0},{detail:t(()=>[a(S,{"no-padding":"","no-content-padding":""},{title:t(()=>[l(n(e(s)("global.detail",{attribute:e(s)("reception.complaint.complaint")}))+" ",1),a(j,{design:o.status.color},{default:t(()=>[l(n(o.status.label),1)]),_:1},8,["design"])]),default:t(()=>[a(H,null,{default:t(()=>[a($,{label:e(s)("reception.complaint.props.code_number")},{default:t(()=>[l(n(o.codeNumber),1)]),_:1},8,["label"]),o.student?(i(),k(U,{key:0},[a($,{label:e(s)("student.student")},{default:t(()=>[l(n(o.student.name)+" ",1),a(x,{block:""},{default:t(()=>[l(n(o.student.codeNumber),1)]),_:1})]),_:1},8,["label"]),a($,{label:e(s)("academic.course.course")},{default:t(()=>[l(n(o.student.courseName)+" ",1),a(x,{block:""},{default:t(()=>[l(n(o.student.batchName),1)]),_:1})]),_:1},8,["label"])],64)):g("",!0),a($,{label:e(s)("reception.complaint.type.type")},{default:t(()=>[l(n(o.type.name)+" ",1),o.isOnline?(i(),v(j,{key:0,design:"info"},{default:t(()=>[l(n(e(s)("reception.complaint.online")),1)]),_:1})):g("",!0)]),_:1},8,["label"]),a($,{label:e(s)("reception.complaint.props.complainant")},{default:t(()=>[l(n(o.complainantName)+" ",1),a(x,{block:""},{default:t(()=>[l(n(o.complainantContactNumber),1)]),_:1}),a(x,{block:""},{default:t(()=>[l(n(o.complainantAddress),1)]),_:1})]),_:1},8,["label"]),a($,{label:e(s)("reception.complaint.props.date")},{default:t(()=>[l(n(o.date.formatted)+" ",1),a(j,{design:o.status.color},{default:t(()=>[l(n(o.status.label),1)]),_:1},8,["design"])]),_:1},8,["label"]),o.resolvedAt.value?(i(),v($,{key:1,label:e(s)("reception.complaint.props.resolved_at")},{default:t(()=>[l(n(o.resolvedAt.formatted),1)]),_:1},8,["label"])):g("",!0),e(P)(["student","guardian"],"any")?g("",!0):(i(),k(U,{key:2},[a($,{label:e(s)("general.created_at")},{default:t(()=>[l(n(o.createdAt.formatted),1)]),_:1},8,["label"]),a($,{label:e(s)("general.updated_at")},{default:t(()=>[l(n(o.updatedAt.formatted),1)]),_:1},8,["label"])],64))]),_:1})]),_:1}),e(O)("complaint:edit")?(i(),v(S,{key:0},{title:t(()=>[l(n(e(s)("reception.complaint.assign")),1)]),default:t(()=>[a(ue,{complaint:o,onCompleted:d[0]||(d[0]=r=>_.value=!0)},null,8,["complaint"])]),_:1})):g("",!0)]),default:t(()=>[m("div",me,[a(S,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:t(()=>[l(n(e(s)("reception.complaint.complaint")),1)]),footer:t(()=>[a(J,null,{default:t(()=>[e(O)("complaint:edit")&&o.isEditable?(i(),v(G,{key:0,design:"primary",onClick:d[2]||(d[2]=r=>e(w).push({name:"ReceptionComplaintEdit",params:{uuid:o.uuid}}))},{default:t(()=>[l(n(e(s)("general.edit")),1)]),_:1})):g("",!0)]),_:1})]),default:t(()=>[m("dl",de,[a(R,{class:"col-span-1 sm:col-span-2"},{default:t(()=>[m("span",pe,n(e(s)("reception.complaint.props.subject"))+": "+n(o.subject),1)]),_:1}),a(R,{class:"col-span-1 sm:col-span-2"},{default:t(()=>[l(n(o.description),1)]),_:1}),o.incharges.length?(i(),v(R,{key:0,class:"col-span-1 sm:col-span-2",label:e(s)("reception.complaint.props.assigned_to")},{default:t(()=>[(i(!0),k(U,null,E(o.incharges,(r,I)=>(i(),k("div",{class:"font-semibold",onMouseover:A=>y.value=I,onMouseleave:d[1]||(d[1]=A=>y.value=null)},[l(n(r.employee.name)+" ",1),a(x,null,{default:t(()=>[l(n(r.employee.designation),1)]),_:2},1024),y.value===I?(i(),k("i",{key:0,class:"fa fa-trash text-danger cursor-pointer ml-2",onClick:A=>c(r.employee.uuid)},null,8,fe)):g("",!0)],40,_e))),256))]),_:1},8,["label"])):g("",!0),o.action?(i(),v(R,{key:1,class:"col-span-1 sm:col-span-2",label:e(s)("reception.complaint.props.action")},{default:t(()=>[l(n(o.action),1)]),_:1},8,["label"])):g("",!0)]),m("div",be,[m("dl",ge,[a(R,{class:"col-span-1 sm:col-span-2"},{default:t(()=>[a(z,{media:o.media,url:`/app/reception/complaints/${o.uuid}/`},null,8,["media","url"])]),_:1})])])]),_:1}),o.logs.length>0?(i(),v(S,{key:0},{title:t(()=>[l(n(e(s)("reception.complaint.logs")),1)]),default:t(()=>[(i(!0),k(U,null,E(o.logs,(r,I)=>(i(),v(K,{class:Z({"mt-4":I>0}),key:r.uuid},{legend:t(()=>{var A;return[l(n((A=r.employee)==null?void 0:A.name)+" ",1),a(j,{class:"ml-1",design:r.status.color},{default:t(()=>[l(n(r.status.label),1)]),_:2},1032,["design"])]}),default:t(()=>[r.action?(i(),k("div",ye,[m("span",null,n(e(s)("reception.complaint.props.action"))+":",1),l(" "+n(r.action),1)])):g("",!0),r.comment?(i(),k("div",ve,[m("span",null,n(e(s)("reception.complaint.props.comment"))+":",1),l(" "+n(r.comment),1)])):g("",!0),r.remarks&&!e(P)(["student","guardian"],"any")?(i(),k("div",ke,[m("span",null,n(e(s)("reception.complaint.props.remarks"))+":",1),l(" "+n(r.remarks),1)])):g("",!0),m("div",$e,[m("span",null,[m("i",{class:"fas fa-trash cursor-pointer text-danger",onClick:A=>e(V).emit("showActionItem",{uuid:o==null?void 0:o.uuid,moduleUuid:r.uuid,action:"removeLog",confirmation:!0})},null,8,Be)]),m("span",null,[d[6]||(d[6]=m("i",{class:"fas fa-calendar mr-2"},null,-1)),l(" "+n(r.createdAt.formatted),1)])])]),_:2},1032,["class"]))),128))]),_:1})):g("",!0),e(O)("complaint:action")?(i(),v(S,{key:1},{title:t(()=>[l(n(e(s)("reception.complaint.props.action")),1)]),default:t(()=>[a(le,{complaint:o,onCompleted:d[3]||(d[3]=r=>_.value=!0)},null,8,["complaint"])]),_:1})):g("",!0)])]),_:1})):g("",!0)]),_:1},8,["uuid","module-uuid","refresh"])]),_:1})],64)}}});export{he as default};
