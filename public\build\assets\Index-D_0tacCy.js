import{u as F,h as j,i as x,y as w,m as W,c as z,ad as U,l as G,n as I,r as o,a as y,o as a,e as s,w as e,f as c,q as l,b as g,s as u,t as i,F as v,v as B,d as L,x as J}from"./app-DvIo72ZO.js";import{_ as K}from"./Filter-LNpepmHw.js";import{_ as Q}from"./ModuleDropdown-DaP494Fu.js";const X={name:"EmployeeAttendanceList"},te=Object.assign(X,{setup(Y){const m=F(),T=j(),P=x();let D=[];w("attendance:config")&&D.unshift("config");let $=[];w("attendance:export")&&($=["print","pdf","excel"]);const A="employee/attendance/",p=W(!1),V=z(()=>U(m.query.dayWise)),_=G({headers:[],data:[],meta:{total:0}}),h=async()=>{p.value=!0,await P.dispatch(A+"listAttendance",{params:m.query}).then(t=>{p.value=!1,Object.assign(_,t)}).catch(t=>{p.value=!1})};return I(async()=>{m.query.date&&await h()}),(t,C)=>{const N=o("BaseButton"),O=o("PageHeaderAction"),R=o("PageHeader"),E=o("ParentTransition"),S=o("BaseDataView"),k=o("DataCell"),b=o("TextMuted"),q=o("DataRow"),H=o("DataTable"),M=o("BaseCard");return a(),y(v,null,[s(R,{title:t.$trans(c(m).meta.label),navs:[{label:t.$trans("employee.employee"),path:"Employee"}]},{default:e(()=>[s(O,{url:"employee/attendance/",name:"EmployeeAttendance",title:t.$trans("employee.attendance.attendance"),actions:c(D),"dropdown-actions":c($)},{moduleOption:e(()=>[s(Q)]),default:e(()=>[c(w)("attendance:mark")?(a(),l(N,{key:0,design:"white",onClick:C[0]||(C[0]=n=>c(T).push({name:"EmployeeAttendanceMark"}))},{default:e(()=>[u(i(t.$trans("employee.attendance.mark")),1)]),_:1})):g("",!0)]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"]),s(E,{appear:"",visibility:!0},{default:e(()=>[s(K,{onAfterFilter:h,"init-url":A,"date-as":"month","day-wise-filter":""})]),_:1}),s(E,{appear:"",visibility:!0},{default:e(()=>[s(M,{"no-padding":"","no-content-padding":"","is-loading":p.value},{default:e(()=>[s(H,{header:_.headers,meta:_.meta,module:"employee.attendance",onRefresh:h},{default:e(()=>[(a(!0),y(v,null,B(_.data,n=>(a(),l(q,{key:n.uuid},{default:e(()=>[s(k,{name:"employee"},{default:e(()=>[s(S,{label:n.name+" ("+n.codeNumber+")",revert:""},{default:e(()=>[u(i(n.designation),1)]),_:2},1032,["label"])]),_:2},1024),V.value?(a(!0),y(v,{key:0},B(n.attendances,(f,d)=>(a(),l(k,{name:`day${d}`},{default:e(()=>[L("span",{class:J(["font-semibold","text-xs","text-"+f.color])},i(f.code),3)]),_:2},1032,["name"]))),256)):(a(!0),y(v,{key:1},B(n.summary,(f,d)=>(a(),l(k,{align:"center",name:`type_${d}`},{default:e(()=>[L("span",null,i(f),1),d=="Late"?(a(),l(b,{key:0,class:"ml-1"},{default:e(()=>{var r;return[u(" ("+i(((r=n.additionalSummary)==null?void 0:r.totalLateDuration)+t.$trans("list.durations.m_short"))+") ",1)]}),_:2},1024)):g("",!0),d=="EarlyLeaving"?(a(),l(b,{key:1,class:"ml-1"},{default:e(()=>{var r;return[u(" ("+i(((r=n.additionalSummary)==null?void 0:r.totalEarlyLeavingDuration)+t.$trans("list.durations.m_short"))+") ",1)]}),_:2},1024)):g("",!0),d=="Overtime"?(a(),l(b,{key:2,class:"ml-1"},{default:e(()=>{var r;return[u(" ("+i(((r=n.additionalSummary)==null?void 0:r.totalOvertimeDuration)+t.$trans("list.durations.m_short"))+") ",1)]}),_:2},1024)):g("",!0)]),_:2},1032,["name"]))),256))]),_:2},1024))),128))]),_:1},8,["header","meta"])]),_:1},8,["is-loading"])]),_:1})],64)}}});export{te as default};
