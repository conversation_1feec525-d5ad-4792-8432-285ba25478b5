import{l as F,r as u,q as V,o as v,w as e,d as $,e as t,s as i,t as s,h as M,j as O,m as U,f as c,a as C,F as N,v as W,b as A}from"./app-DvIo72ZO.js";const E={class:"grid grid-cols-3 gap-6"},z={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},X={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(y,{emit:m}){const k=m,h={type:"",courses:[],subjects:[],title:"",author:""},r=F({...h});return(p,o)=>{const g=u("BaseSelect"),_=u("BaseInput"),j=u("FilterForm");return v(),V(j,{"init-form":h,form:r,multiple:["courses","subjects"],onHide:o[5]||(o[5]=n=>k("hide"))},{default:e(()=>[$("div",E,[$("div",z,[t(g,{modelValue:r.type,"onUpdate:modelValue":o[0]||(o[0]=n=>r.type=n),name:"type",label:p.$trans("academic.book_list.props.type"),options:y.preRequisites.types},null,8,["modelValue","label","options"])]),$("div",G,[t(g,{multiple:"",modelValue:r.courses,"onUpdate:modelValue":o[1]||(o[1]=n=>r.courses=n),name:"courses","value-prop":"uuid",label:p.$trans("academic.course.course"),options:y.preRequisites.courses},{selectedOption:e(n=>[i(s(n.value.nameWithTerm),1)]),listOption:e(n=>[i(s(n.option.nameWithTerm),1)]),_:1},8,["modelValue","label","options"])]),$("div",J,[t(g,{multiple:"",modelValue:r.subjects,"onUpdate:modelValue":o[2]||(o[2]=n=>r.subjects=n),name:"subjects","label-prop":"name","value-prop":"uuid",label:p.$trans("academic.subject.subject"),options:y.preRequisites.subjects},null,8,["modelValue","label","options"])]),$("div",K,[t(_,{type:"text",modelValue:r.title,"onUpdate:modelValue":o[3]||(o[3]=n=>r.title=n),name:"title",label:p.$trans("academic.book_list.props.title")},null,8,["modelValue","label"])]),$("div",Q,[t(_,{type:"text",modelValue:r.author,"onUpdate:modelValue":o[4]||(o[4]=n=>r.author=n),name:"author",label:p.$trans("academic.book_list.props.author")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},Y={class:"space-x-2"},Z={key:0},L={key:1},x={name:"AcademicBookListList"},te=Object.assign(x,{setup(y){const m=M(),k=O("emitter");let h=["create","filter"],r=["print","pdf","excel"];const p="academic/bookList/",o=U(!1),g=F({types:[],courses:[],subjects:[]}),_=F({}),j=l=>{Object.assign(_,l)},n=l=>{Object.assign(g,l)};return(l,d)=>{const R=u("PageHeaderAction"),I=u("PageHeader"),q=u("ParentTransition"),f=u("DataCell"),w=u("TextMuted"),B=u("FloatingMenuItem"),T=u("FloatingMenu"),D=u("DataRow"),P=u("BaseButton"),S=u("DataTable"),H=u("ListItem");return v(),V(H,{"init-url":p,"pre-requisites":!0,onSetPreRequisites:n,onSetItems:j},{header:e(()=>[t(I,{title:l.$trans("academic.book_list.book_list"),navs:[{label:l.$trans("academic.academic"),path:"Academic"}]},{default:e(()=>[t(R,{url:"academic/book-lists/",name:"AcademicBookList",title:l.$trans("academic.book_list.book_list"),actions:c(h),"dropdown-actions":c(r),onToggleFilter:d[0]||(d[0]=a=>o.value=!o.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(q,{appear:"",visibility:o.value},{default:e(()=>[t(X,{onRefresh:d[1]||(d[1]=a=>c(k).emit("listItems")),"pre-requisites":g,onHide:d[2]||(d[2]=a=>o.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:e(()=>[t(q,{appear:"",visibility:!0},{default:e(()=>[t(S,{header:_.headers,meta:_.meta,module:"academic.book_list",onRefresh:d[4]||(d[4]=a=>c(k).emit("listItems"))},{actionButton:e(()=>[t(P,{onClick:d[3]||(d[3]=a=>c(m).push({name:"AcademicBookListCreate"}))},{default:e(()=>[i(s(l.$trans("global.add",{attribute:l.$trans("academic.book_list.book_list")})),1)]),_:1})]),default:e(()=>[(v(!0),C(N,null,W(_.data,a=>(v(),V(D,{key:a.uuid,onDoubleClick:b=>c(m).push({name:"AcademicBookListShow",params:{uuid:a.uuid}})},{default:e(()=>[t(f,{name:"type"},{default:e(()=>[i(s(a.type.label),1)]),_:2},1024),t(f,{name:"course"},{default:e(()=>[i(s(a.course.nameWithTerm),1)]),_:2},1024),t(f,{name:"subject"},{default:e(()=>{var b;return[i(s(((b=a.subject)==null?void 0:b.name)||"-"),1)]}),_:2},1024),t(f,{name:"title"},{default:e(()=>[i(s(a.title)+" ",1),a.type.value=="notebook"?(v(),V(w,{key:0},{default:e(()=>[$("div",Y,[a.quantity?(v(),C("span",Z,"("+s(a.quantity)+" "+s(l.$trans("list.unit.pcs"))+")",1)):A("",!0),a.pages?(v(),C("span",L,"("+s(a.pages)+" "+s(l.$trans("academic.book_list.props.pages"))+")",1)):A("",!0)])]),_:2},1024)):A("",!0)]),_:2},1024),t(f,{name:"publisher"},{default:e(()=>[i(s(a.publisher)+" ",1),t(w,{block:""},{default:e(()=>[i(s(a.author),1)]),_:2},1024)]),_:2},1024),t(f,{name:"createdAt"},{default:e(()=>[i(s(a.createdAt.formatted),1)]),_:2},1024),t(f,{name:"action"},{default:e(()=>[t(T,null,{default:e(()=>[t(B,{icon:"fas fa-arrow-circle-right",onClick:b=>c(m).push({name:"AcademicBookListShow",params:{uuid:a.uuid}})},{default:e(()=>[i(s(l.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(B,{icon:"fas fa-edit",onClick:b=>c(m).push({name:"AcademicBookListEdit",params:{uuid:a.uuid}})},{default:e(()=>[i(s(l.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t(B,{icon:"fas fa-copy",onClick:b=>c(m).push({name:"AcademicBookListDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[i(s(l.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t(B,{icon:"fas fa-trash",onClick:b=>c(k).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[i(s(l.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{te as default};
