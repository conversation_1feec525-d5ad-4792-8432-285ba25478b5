import{i as R,u as j,h as x,j as N,l as $,r as o,a as v,o as u,e as n,w as e,f as t,q as m,b as f,d as E,F as A,v as O,s as r,t as s,y as q}from"./app-DvIo72ZO.js";const U={class:"space-y-4"},z={name:"TransportFeeShow"},K=Object.assign(z,{setup(G){R();const _=j(),b=x(),a=N("$trans"),k={},B="transport/fee/",T=[{key:"circle",label:a("transport.circle.circle"),visibility:!0},{key:"arrivalAmount",label:a("transport.fee.props.arrival_amount"),visibility:!0},{key:"departureAmount",label:a("transport.fee.props.departure_amount"),visibility:!0},{key:"roundtripAmount",label:a("transport.fee.props.roundtrip_amount"),visibility:!0}],l=$({...k}),h=g=>{Object.assign(l,g)};return(g,d)=>{const w=o("PageHeaderAction"),C=o("PageHeader"),p=o("ListItemView"),S=o("ListContainerVertical"),y=o("BaseCard"),c=o("DataCell"),V=o("DataRow"),F=o("SimpleTable"),L=o("BaseButton"),D=o("ShowButton"),I=o("DetailLayoutVertical"),P=o("ShowItem"),H=o("ParentTransition");return u(),v(A,null,[n(C,{title:t(a)(t(_).meta.trans,{attribute:t(a)(t(_).meta.label)}),navs:[{label:t(a)("transport.transport"),path:"Transport"},{label:t(a)("transport.fee.fee"),path:"TransportFeeList"}]},{default:e(()=>[n(w,{name:"TransportFee",title:t(a)("transport.fee.fee"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(H,{appear:"",visibility:!0},{default:e(()=>[n(P,{"init-url":B,uuid:t(_).params.uuid,onSetItem:h,onRedirectTo:d[1]||(d[1]=i=>t(b).push({name:"TransportFee"}))},{default:e(()=>[l.uuid?(u(),m(I,{key:0},{detail:e(()=>[n(y,{"no-padding":"","no-content-padding":""},{title:e(()=>[r(s(t(a)("global.detail",{attribute:t(a)("transport.fee.fee")})),1)]),default:e(()=>[n(S,null,{default:e(()=>[n(p,{label:t(a)("transport.fee.props.name")},{default:e(()=>[r(s(l.name),1)]),_:1},8,["label"]),n(p,{label:t(a)("transport.fee.props.description")},{default:e(()=>[r(s(l.description),1)]),_:1},8,["label"]),n(p,{label:t(a)("general.created_at")},{default:e(()=>[r(s(l.createdAt.formatted),1)]),_:1},8,["label"]),n(p,{label:t(a)("general.updated_at")},{default:e(()=>[r(s(l.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})]),default:e(()=>[E("div",U,[n(y,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:e(()=>[r(s(t(a)("transport.fee.fee")),1)]),footer:e(()=>[n(D,null,{default:e(()=>[t(q)("transport-fee:edit")?(u(),m(L,{key:0,design:"primary",onClick:d[0]||(d[0]=i=>t(b).push({name:"TransportFeeEdit",params:{uuid:l.uuid}}))},{default:e(()=>[r(s(t(a)("general.edit")),1)]),_:1})):f("",!0)]),_:1})]),default:e(()=>[l.records.length>0?(u(),m(F,{key:0,header:T},{default:e(()=>[(u(!0),v(A,null,O(l.records,i=>(u(),m(V,{key:i.uuid},{default:e(()=>[n(c,{name:"circle"},{default:e(()=>[r(s(i.circle.name),1)]),_:2},1024),n(c,{name:"arrivalAmount"},{default:e(()=>[r(s(i.arrivalAmount.formatted),1)]),_:2},1024),n(c,{name:"departureAmount"},{default:e(()=>[r(s(i.departureAmount.formatted),1)]),_:2},1024),n(c,{name:"roundtripAmount"},{default:e(()=>[r(s(i.roundtripAmount.formatted),1)]),_:2},1024)]),_:2},1024))),128))]),_:1})):f("",!0)]),_:1})])]),_:1})):f("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{K as default};
