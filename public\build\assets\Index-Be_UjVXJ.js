import{u as B,l as y,n as H,r,q as g,o as _,w as e,d as A,e as t,h as L,j as N,y as C,m as S,f as c,a as x,F as V,v as q,s as o,t as s,b as U}from"./app-DvIo72ZO.js";const E={class:"grid grid-cols-3 gap-6"},O={class:"col-span-3 sm:col-span-1"},z={__name:"Filter",emits:["hide"],setup(R,{emit:v}){B();const f=v,b={startDate:"",endDate:""},d=y({...b}),D=y({isLoaded:!0});return H(async()=>{D.isLoaded=!0}),(m,i)=>{const k=r("DatePicker"),l=r("FilterForm");return _(),g(l,{"init-form":b,form:d,multiple:[],onHide:i[2]||(i[2]=n=>f("hide"))},{default:e(()=>[A("div",E,[A("div",O,[t(k,{start:d.startDate,"onUpdate:start":i[0]||(i[0]=n=>d.startDate=n),end:d.endDate,"onUpdate:end":i[1]||(i[1]=n=>d.endDate=n),name:"dateBetween",as:"range",label:m.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},G={name:"RecruitmentApplicationList"},K=Object.assign(G,{setup(R){const v=L(),f=N("emitter");let b=["filter"],d=[];C("job-application:export")&&(d=["print","pdf","excel"]);const D="recruitment/application/",m=S(!1),i=y({}),k=l=>{Object.assign(i,l)};return(l,n)=>{const I=r("PageHeaderAction"),h=r("PageHeader"),$=r("ParentTransition"),p=r("TextMuted"),u=r("DataCell"),w=r("FloatingMenuItem"),P=r("FloatingMenu"),T=r("DataRow"),M=r("DataTable"),j=r("ListItem");return _(),g(j,{"init-url":D,"additional-query":{},onSetItems:k},{header:e(()=>[t(h,{title:l.$trans("recruitment.application.application"),navs:[{label:l.$trans("recruitment.recruitment"),path:"Recruitment"}]},{default:e(()=>[t(I,{url:"recruitment/applications/",name:"RecruitmentApplication",title:l.$trans("recruitment.application.application"),actions:c(b),"dropdown-actions":c(d),onToggleFilter:n[0]||(n[0]=a=>m.value=!m.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t($,{appear:"",visibility:m.value},{default:e(()=>[t(z,{onRefresh:n[1]||(n[1]=a=>c(f).emit("listItems")),onHide:n[2]||(n[2]=a=>m.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t($,{appear:"",visibility:!0},{default:e(()=>[t(M,{header:i.headers,meta:i.meta,module:"recruitment.application",onRefresh:n[3]||(n[3]=a=>c(f).emit("listItems"))},{actionButton:e(()=>n[4]||(n[4]=[])),default:e(()=>[(_(!0),x(V,null,q(i.data,a=>(_(),g(T,{key:a.uuid,onDoubleClick:F=>c(v).push({name:"RecruitmentApplicationShow",params:{uuid:a.uuid}})},{default:e(()=>[t(u,{name:"vacancy"},{default:e(()=>[o(s(a.vacancy.titleExcerpt)+" ",1),t(p,{block:""},{default:e(()=>[o(s(a.vacancy.codeNumber),1)]),_:2},1024)]),_:2},1024),t(u,{name:"designation"},{default:e(()=>[o(s(a.designation.name),1)]),_:2},1024),t(u,{name:"applicant"},{default:e(()=>[o(s(a.contact.name)+" ",1),t(p,{block:""},{default:e(()=>[o(s(a.qualificationSummary),1)]),_:2},1024)]),_:2},1024),t(u,{name:"contact"},{default:e(()=>[o(s(a.contact.contactNumber)+" ",1),t(p,{block:""},{default:e(()=>[o(s(a.contact.email),1)]),_:2},1024)]),_:2},1024),t(u,{name:"address"},{default:e(()=>[o(s(a.contact.presentAddress.city)+" ",1),t(p,{block:""},{default:e(()=>[o(s(a.contact.presentAddress.state),1)]),_:2},1024)]),_:2},1024),t(u,{name:"applicationDate"},{default:e(()=>[o(s(a.applicationDate.formatted)+" ",1),t(p,{block:""},{default:e(()=>[o(s(a.availabilityDate.formatted),1)]),_:2},1024)]),_:2},1024),t(u,{name:"createdAt"},{default:e(()=>[o(s(a.createdAt.formatted),1)]),_:2},1024),t(u,{name:"action"},{default:e(()=>[t(P,null,{default:e(()=>[t(w,{icon:"fas fa-arrow-circle-right",onClick:F=>c(v).push({name:"RecruitmentApplicationShow",params:{uuid:a.uuid}})},{default:e(()=>[o(s(l.$trans("general.show")),1)]),_:2},1032,["onClick"]),c(C)("job-application:delete")?(_(),g(w,{key:0,icon:"fas fa-trash",onClick:F=>c(f).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[o(s(l.$trans("general.delete")),1)]),_:2},1032,["onClick"])):U("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{K as default};
