import{r as s,q as d,o as m,w as e,e as o,d as r,s as i,t as l}from"./app-DvIo72ZO.js";const _=["innerHTML"],u=["innerHTML"],p={name:"ExamOnlineExamGeneral"},B=Object.assign(p,{props:{onlineExam:{type:Object,default(){return{}}}},setup(n){return(a,x)=>{const t=s("BaseHeading"),c=s("BaseCard");return m(),d(c,null,{default:e(()=>[o(t,null,{default:e(()=>[i(l(a.$trans("exam.online_exam.props.instructions")),1)]),_:1}),r("div",{class:"dark:text-gray-400",innerHTML:n.onlineExam.instructions},null,8,_),o(t,{class:"mt-4"},{default:e(()=>[i(l(a.$trans("exam.online_exam.props.description")),1)]),_:1}),r("div",{class:"dark:text-gray-400",innerHTML:n.onlineExam.description},null,8,u)]),_:1})}}});export{B as default};
