import{i as k,u as v,h as P,l as V,r as n,a as H,o as d,e as o,w as e,f as c,q as p,b as f,d as I,s,t as i,y as N,F as D}from"./app-DvIo72ZO.js";const R={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},j={name:"AcademicCertificateTemplateShow"},M=Object.assign(j,{setup(E){k();const r=v(),u=P(),_={},b="academic/certificateTemplate/",a=V({..._}),g=t=>{Object.assign(a,t)};return(t,l)=>{const B=n("PageHeaderAction"),T=n("PageHeader"),C=n("TextMuted"),m=n("BaseDataView"),$=n("BaseButton"),h=n("ShowButton"),A=n("BaseCard"),w=n("ShowItem"),y=n("ParentTransition");return d(),H(D,null,[o(T,{title:t.$trans(c(r).meta.trans,{attribute:t.$trans(c(r).meta.label)}),navs:[{label:t.$trans("academic.academic"),path:"Academic"},{label:t.$trans("academic.certificate.template.template"),path:"AcademicCertificateTemplateList"}]},{default:e(()=>[o(B,{name:"AcademicCertificateTemplate",title:t.$trans("academic.certificate.template.template"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),o(y,{appear:"",visibility:!0},{default:e(()=>[o(w,{"init-url":b,uuid:c(r).params.uuid,onSetItem:g,onRedirectTo:l[1]||(l[1]=S=>c(u).push({name:"AcademicCertificateTemplate"}))},{default:e(()=>[a.uuid?(d(),p(A,{key:0},{title:e(()=>[s(i(a.name)+" ",1),o(C,{block:""},{default:e(()=>[s(i(a.type.label),1)]),_:1}),s(" ("+i(a.for.label)+") ",1)]),footer:e(()=>[o(h,null,{default:e(()=>[c(N)("certificate-template:edit")?(d(),p($,{key:0,design:"primary",onClick:l[0]||(l[0]=S=>c(u).push({name:"AcademicCertificateTemplateEdit",params:{uuid:a.uuid}}))},{default:e(()=>[s(i(t.$trans("general.edit")),1)]),_:1})):f("",!0)]),_:1})]),default:e(()=>[I("dl",R,[o(m,{class:"col-span-1 sm:col-span-2",label:t.$trans("academic.certificate.template.props.content"),html:""},{default:e(()=>[s(i(a.content),1)]),_:1},8,["label"]),o(m,{label:t.$trans("general.created_at")},{default:e(()=>[s(i(a.createdAt.formatted),1)]),_:1},8,["label"]),o(m,{label:t.$trans("general.updated_at")},{default:e(()=>[s(i(a.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):f("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{M as default};
