import{i as M,u as U,h as G,j as J,l as K,r as p,a as i,o,e as n,w as e,f as a,q as u,b as d,d as f,s as r,t as s,F as g,v as k,x as v,y as Q}from"./app-DvIo72ZO.js";const W={class:"space-y-4"},X={key:0,class:"px-4 py-2"},Y={key:0},Z={key:0},ee={key:1},ae={key:1,class:"mt-2 border-2 border-gray-300 dark:border-gray-300 rounded-md p-2"},te={class:"block mt-1"},le={key:0},oe={class:"block font-semibold"},se={key:2},ne={key:2,class:"grid grid-cols-1 gap-x-4 gap-y-8 px-4 pt-4 sm:grid-cols-2"},pe={name:"EmployeePayrollSalaryTemplateShow"},ye=Object.assign(pe,{setup(re){M();const h=U(),T=G(),t=J("$trans"),S={},C="employee/payroll/salaryTemplate/",V=[{key:"payHead",label:t("employee.payroll.pay_head.pay_head"),visibility:!0},{key:"payHeadCode",label:t("employee.payroll.pay_head.props.code"),visibility:!0},{key:"type",label:t("employee.payroll.salary_template.props.type"),visibility:!0},{key:"attendanceType",label:"",visibility:!0},{key:"action",label:"",visibility:!0}],y=K({...S}),w=H=>{Object.assign(y,H)};return(H,m)=>{const D=p("PageHeaderAction"),E=p("PageHeader"),x=p("TextMuted"),b=p("ListItemView"),L=p("ListContainerVertical"),B=p("BaseCard"),A=p("BaseAlert"),_=p("DataCell"),I=p("DataRow"),R=p("SimpleTable"),j=p("BaseDataView"),N=p("BaseButton"),O=p("ShowButton"),$=p("DetailLayoutVertical"),q=p("ShowItem"),z=p("ParentTransition");return o(),i(g,null,[n(E,{title:a(t)(a(h).meta.trans,{attribute:a(t)(a(h).meta.label)}),navs:[{label:a(t)("employee.employee"),path:"Employee"},{label:a(t)("employee.payroll.payroll"),path:"EmployeePayroll"},{label:a(t)("employee.payroll.salary_template.salary_template"),path:"EmployeePayrollSalaryTemplateList"}]},{default:e(()=>[n(D,{name:"EmployeePayrollSalaryTemplate",title:a(t)("employee.payroll.salary_template.salary_template"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(z,{appear:"",visibility:!0},{default:e(()=>[n(q,{"init-url":C,uuid:a(h).params.uuid,onSetItem:w,onRedirectTo:m[1]||(m[1]=l=>a(T).push({name:"EmployeePayrollSalaryTemplate"}))},{default:e(()=>[y.uuid?(o(),u($,{key:0},{detail:e(()=>[n(B,{"no-padding":"","no-content-padding":""},{title:e(()=>[r(s(a(t)("global.detail",{attribute:a(t)("employee.payroll.salary_template.salary_template")})),1)]),default:e(()=>[n(L,null,{default:e(()=>[n(b,{label:a(t)("employee.payroll.salary_template.props.name")},{default:e(()=>[r(s(y.name)+" ",1),y.hasHourlyPayroll?(o(),u(x,{key:0,block:""},{default:e(()=>[r(s(a(t)("employee.payroll.salary_template.props.hourly_payroll")),1)]),_:1})):d("",!0)]),_:1},8,["label"]),n(b,{label:a(t)("employee.payroll.salary_template.props.alias")},{default:e(()=>[r(s(y.alias),1)]),_:1},8,["label"]),n(b,{label:a(t)("general.created_at")},{default:e(()=>[r(s(y.createdAt.formatted),1)]),_:1},8,["label"]),n(b,{label:a(t)("general.updated_at")},{default:e(()=>[r(s(y.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})]),default:e(()=>[f("div",W,[n(B,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:e(()=>[r(s(a(t)("employee.payroll.salary_template.salary_template")),1)]),footer:e(()=>[n(O,null,{default:e(()=>[a(Q)("salary-template:edit")?(o(),u(N,{key:0,design:"primary",onClick:m[0]||(m[0]=l=>a(T).push({name:"EmployeePayrollSalaryTemplateEdit",params:{uuid:y.uuid}}))},{default:e(()=>[r(s(a(t)("general.edit")),1)]),_:1})):d("",!0)]),_:1})]),default:e(()=>[y.hasHourlyPayroll?(o(),i("div",X,[n(A,{design:"info",size:"xs"},{default:e(()=>[r(s(a(t)("employee.payroll.salary_template.hourly_payroll_info")),1)]),_:1})])):d("",!0),y.records.length>0?(o(),u(R,{key:1,header:V},{default:e(()=>[(o(!0),i(g,null,k(y.records,l=>(o(),u(I,{key:l.uuid},{default:e(()=>[n(_,{name:"payHead"},{default:e(()=>[f("span",{class:v({"text-success":l.payHead.type=="earning","text-danger":l.payHead.type=="deduction"})},[r(s(l.payHead.name)+" ",1),l.asTotal?(o(),i("span",Y,m[2]||(m[2]=[f("i",{class:"fas fa-not-equal"},null,-1)]))):d("",!0)],2)]),_:2},1024),n(_,{name:"payHeadCode"},{default:e(()=>[f("span",{class:v({"text-success":l.payHead.category.value=="earning","text-danger":l.payHead.category.value=="deduction"})},s(l.payHead.code),3)]),_:2},1024),n(_,{name:"type"},{default:e(()=>[r(s(l.type.label),1)]),_:2},1024),n(_,{name:"attendanceType"},{default:e(()=>[l.attendanceType?(o(),i("div",Z,s(l.attendanceType.name),1)):l.computation?(o(),i("div",ee,[r(s(l.computation)+" ",1),l.hasRange&&l.minValue.value>=0?(o(),u(x,{key:0,block:""},{default:e(()=>[r(s(l.minValue.formatted)+" - "+s(l.maxValue.formatted),1)]),_:2},1024)):d("",!0),l.hasCondition?(o(),i("div",ae,[(o(!0),i(g,null,k(l.detailedConditionalFormulas,P=>(o(),i("span",te,[(o(!0),i(g,null,k(P.conditions,(c,F)=>(o(),i("span",{class:v({"ml-1":F>0})},[r(s(a(t)("employee.payroll.salary_template.conditional_formula_if",{pay_head:c.referencePayHead,condition:c.operator.label,value:c.value}))+" ",1),c.logicalOperator?(o(),i("span",le,s(c.logicalOperator.label),1)):d("",!0)],2))),256)),f("span",oe,s(a(t)("employee.payroll.salary_template.conditional_formula_then",{formula:P.formula})),1)]))),256))])):d("",!0)])):(o(),i("div",se,"-"))]),_:2},1024),n(_,{name:"action"})]),_:2},1024))),128))]),_:1})):d("",!0),y.description?(o(),i("dl",ne,[n(j,{class:"col-span-1 sm:col-span-2",label:a(t)("employee.payroll.salary_template.props.description")},{default:e(()=>[r(s(y.description),1)]),_:1},8,["label"])])):d("",!0)]),_:1})])]),_:1})):d("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{ye as default};
