import{u as R,j as B,l as w,I as U,n as S,r as n,q as F,o as $,w as i,d as b,e as r,b as q,s as k,t as _,f as c,i as D,m as C,a as H,F as L,aS as P}from"./app-DvIo72ZO.js";const T={class:"grid grid-cols-3 gap-6"},A={class:"col-span-3 sm:col-span-1"},N={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3 sm:col-span-1"},j={__name:"Filter",props:{initUrl:{type:String,default:""}},emits:["hide"],setup(V,{emit:d}){const u=R();B("moment");const g=d,f=V,l={date:"",batches:[],status:"all"},o=w({...l}),m=U(f.initUrl),s=w({isLoaded:!u.query.batches});return S(async()=>{s.batches=u.query.batches?u.query.batches.split(","):[],s.isLoaded=!0}),(e,a)=>{const h=n("DatePicker"),p=n("BaseSelectSearch"),v=n("CustomCheckbox"),y=n("FilterForm");return $(),F(y,{"init-form":l,multiple:["batches"],form:o,onHide:a[4]||(a[4]=t=>g("hide"))},{default:i(()=>[b("div",T,[b("div",A,[r(h,{modelValue:o.date,"onUpdate:modelValue":a[0]||(a[0]=t=>o.date=t),name:"date",as:"date",label:e.$trans("general.date")},null,8,["modelValue","label"])]),b("div",N,[s.isLoaded?($(),F(p,{key:0,multiple:"",name:"batches",label:e.$trans("global.select",{attribute:e.$trans("academic.batch.batch")}),modelValue:o.batches,"onUpdate:modelValue":a[1]||(a[1]=t=>o.batches=t),"value-prop":"uuid","init-search":s.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:i(t=>[k(_(t.value.course.nameWithTerm)+" "+_(t.value.name),1)]),listOption:i(t=>[k(_(t.option.course.nameWithTerm)+" "+_(t.option.name),1)]),_:1},8,["label","modelValue","init-search"])):q("",!0)]),b("div",W,[r(v,{label:e.$trans("resource.status"),options:[{label:e.$trans("general.all"),value:"all"},{label:e.$trans("resource.submitted"),value:"submitted"},{label:e.$trans("resource.not_submitted"),value:"not_submitted"}],modelValue:o.status,"onUpdate:modelValue":a[2]||(a[2]=t=>o.status=t),error:c(m).status,"onUpdate:error":a[3]||(a[3]=t=>c(m).status=t)},null,8,["label","options","modelValue","error"])])])]),_:1},8,["form"])}}},E={name:"ResourceReportDateWiseLearningMaterial"},O=Object.assign(E,{setup(V){const d=R();D();let u=["filter"],g=[];const f="resource/report/",l=C(!0),o=C(!1),m=async()=>{let s="/app/resource/reports/date-wise-learning-material/export",e=d.query;window.open(P(s,e),"_blank").focus()};return S(async()=>{}),(s,e)=>{const a=n("PageHeaderAction"),h=n("PageHeader"),p=n("ParentTransition"),v=n("BaseCard");return $(),H(L,null,[r(h,{title:s.$trans(c(d).meta.label),navs:[{label:s.$trans("resource.resource"),path:"Resource"},{label:s.$trans("resource.report.report"),path:"ResourceReport"}]},{default:i(()=>[r(a,{url:"resource/reports/date-wise-learning-material/",name:"ResourceReportDateWiseLearningMaterial",title:s.$trans("resource.report.date_wise_learning_material.date_wise_learning_material"),actions:c(u),"dropdown-actions":c(g),onToggleFilter:e[0]||(e[0]=y=>l.value=!0)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"]),r(p,{appear:"",visibility:l.value},{default:i(()=>[r(j,{onAfterFilter:m,"init-url":f,onHide:e[1]||(e[1]=y=>l.value=!1)})]),_:1},8,["visibility"]),r(p,{appear:"",visibility:!0},{default:i(()=>[r(v,{"no-padding":"","no-content-padding":"","is-loading":o.value},null,8,["is-loading"])]),_:1})],64)}}});export{O as default};
