import{i as P,u as T,h as V,l as A,r as l,a as p,o as d,e as t,w as e,f as u,q as f,b as c,d as b,s as n,t as o,F as x,v as H,x as I,y as N}from"./app-DvIo72ZO.js";const D={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},F={key:0},R={key:0},j={name:"ExamGradeShow"},O=Object.assign(j,{setup(L){P();const _=T(),g=V(),B={},$="exam/grade/",r=A({...B}),v=a=>{Object.assign(r,a)};return(a,i)=>{const S=l("PageHeaderAction"),h=l("PageHeader"),m=l("BaseDataView"),w=l("TextMuted"),y=l("BaseButton"),k=l("ShowButton"),E=l("BaseCard"),C=l("ShowItem"),G=l("ParentTransition");return d(),p(x,null,[t(h,{title:a.$trans(u(_).meta.trans,{attribute:a.$trans(u(_).meta.label)}),navs:[{label:a.$trans("exam.exam"),path:"Exam"},{label:a.$trans("exam.grade.grade"),path:"ExamGradeList"}]},{default:e(()=>[t(S,{name:"ExamGrade",title:a.$trans("exam.grade.grade"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(G,{appear:"",visibility:!0},{default:e(()=>[t(C,{"init-url":$,uuid:u(_).params.uuid,onSetItem:v,onRedirectTo:i[1]||(i[1]=s=>u(g).push({name:"ExamGrade"}))},{default:e(()=>[r.uuid?(d(),f(E,{key:0},{title:e(()=>[n(o(r.name),1)]),footer:e(()=>[t(k,null,{default:e(()=>[u(N)("exam-grade:edit")?(d(),f(y,{key:0,design:"primary",onClick:i[0]||(i[0]=s=>u(g).push({name:"ExamGradeEdit",params:{uuid:r.uuid}}))},{default:e(()=>[n(o(a.$trans("general.edit")),1)]),_:1})):c("",!0)]),_:1})]),default:e(()=>[b("dl",D,[t(m,{label:a.$trans("exam.grade.props.name")},{default:e(()=>[n(o(r.name),1)]),_:1},8,["label"]),t(m,{class:"col-span-1 sm:col-span-2",label:a.$trans("exam.grade.props.records")},{default:e(()=>[(d(!0),p(x,null,H(r.records,s=>(d(),p("div",null,[b("div",{class:I({"text-danger":s.isFailGrade})},[n(o(s.code)+" ",1),s.value?(d(),p("span",F,[n("("+o(s.value)+" ",1),s.label?(d(),p("span",R," - "+o(s.label)+") ",1)):c("",!0)])):c("",!0),i[2]||(i[2]=n(" : ")),t(w,null,{default:e(()=>[n(o(s.minScore+"-"+s.maxScore),1)]),_:2},1024)],2)]))),256))]),_:1},8,["label"]),t(m,{class:"col-span-1 sm:col-span-2",label:a.$trans("exam.grade.props.description")},{default:e(()=>[n(o(r.description),1)]),_:1},8,["label"]),t(m,{label:a.$trans("general.created_at")},{default:e(()=>[n(o(r.createdAt.formatted),1)]),_:1},8,["label"]),t(m,{label:a.$trans("general.updated_at")},{default:e(()=>[n(o(r.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):c("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{O as default};
