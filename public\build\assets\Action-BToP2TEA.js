import{I as b,l as v,r as l,z as A,q as V,o as m,w as u,d as p,e as s,f as a,A as $,u as B,a as U,F as G}from"./app-DvIo72ZO.js";const P={class:"grid grid-cols-3 gap-6"},H={class:"col-span-3 sm:col-span-1"},T={class:"col-span-3 sm:col-span-1"},k={class:"fas fa-question-circle"},w={class:"col-span-3"},y={name:"FinanceFeeGroupForm"},E=Object.assign(y,{setup(f){const c={name:"",pgAccount:"",description:""},t="finance/feeGroup/",r=b(t),o=v({...c});return(i,e)=>{const d=l("BaseInput"),_=l("BaseTextarea"),g=l("FormAction"),F=A("tooltip");return m(),V(g,{"init-url":t,"init-form":c,form:o,redirect:"FinanceFeeGroup"},{default:u(()=>[p("div",P,[p("div",H,[s(d,{type:"text",modelValue:o.name,"onUpdate:modelValue":e[0]||(e[0]=n=>o.name=n),name:"name",label:i.$trans("finance.fee_group.props.name"),error:a(r).name,"onUpdate:error":e[1]||(e[1]=n=>a(r).name=n),autofocus:""},null,8,["modelValue","label","error"])]),p("div",T,[s(d,{type:"text",modelValue:o.pgAccount,"onUpdate:modelValue":e[2]||(e[2]=n=>o.pgAccount=n),name:"pgAccount",label:i.$trans("finance.config.props.pg_account"),error:a(r).pgAccount,"onUpdate:error":e[3]||(e[3]=n=>a(r).pgAccount=n)},{"additional-label":u(()=>[$(p("i",k,null,512),[[F,i.$trans("finance.config.props.pg_account_info")]])]),_:1},8,["modelValue","label","error"])]),p("div",w,[s(_,{modelValue:o.description,"onUpdate:modelValue":e[4]||(e[4]=n=>o.description=n),name:"description",label:i.$trans("finance.fee_group.props.description"),error:a(r).description,"onUpdate:error":e[5]||(e[5]=n=>a(r).description=n)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),I={name:"FinanceFeeGroupAction"},q=Object.assign(I,{setup(f){const c=B();return(t,r)=>{const o=l("PageHeaderAction"),i=l("PageHeader"),e=l("ParentTransition");return m(),U(G,null,[s(i,{title:t.$trans(a(c).meta.trans,{attribute:t.$trans(a(c).meta.label)}),navs:[{label:t.$trans("finance.finance"),path:"Finance"},{label:t.$trans("finance.fee_group.fee_group"),path:"FinanceFeeGroupList"}]},{default:u(()=>[s(o,{name:"FinanceFeeGroup",title:t.$trans("finance.fee_group.fee_group"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),s(e,{appear:"",visibility:!0},{default:u(()=>[s(E)]),_:1})],64)}}});export{q as default};
