import{u as g,I as U,l as u,r as i,q as L,o as b,w as _,d as p,e as l,f as s,K as P,a as j,F as q}from"./app-DvIo72ZO.js";const A={class:"grid grid-cols-3 gap-6"},O={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},k={class:"col-span-3"},E={name:"FinanceLedgerTypeForm"},I=Object.assign(E,{setup(y){const d=g(),a={name:"",alias:"",parent:"",description:""},m="finance/ledgerType/",r=U(m),c=u({types:[]}),t=u({...a}),F=u({isLoaded:!d.params.uuid}),V=o=>{Object.assign(c,o)},v=o=>{var e;Object.assign(a,{...o,parent:((e=o.parent)==null?void 0:e.uuid)||""}),Object.assign(t,P(a)),F.isLoaded=!0};return(o,e)=>{const f=i("BaseInput"),$=i("BaseSelect"),B=i("BaseTextarea"),T=i("FormAction");return b(),L(T,{"pre-requisites":!0,onSetPreRequisites:V,"init-url":m,"init-form":a,form:t,"set-form":v,redirect:"FinanceLedgerType"},{default:_(()=>[p("div",A,[p("div",O,[l(f,{type:"text",modelValue:t.name,"onUpdate:modelValue":e[0]||(e[0]=n=>t.name=n),name:"name",label:o.$trans("finance.ledger_type.props.name"),error:s(r).name,"onUpdate:error":e[1]||(e[1]=n=>s(r).name=n),autofocus:""},null,8,["modelValue","label","error"])]),p("div",H,[l(f,{type:"text",modelValue:t.alias,"onUpdate:modelValue":e[2]||(e[2]=n=>t.alias=n),name:"alias",label:o.$trans("finance.ledger_type.props.alias"),error:s(r).alias,"onUpdate:error":e[3]||(e[3]=n=>s(r).alias=n)},null,8,["modelValue","label","error"])]),p("div",R,[l($,{modelValue:t.parent,"onUpdate:modelValue":e[4]||(e[4]=n=>t.parent=n),name:"parent",label:o.$trans("finance.ledger_type.props.parent"),options:c.types,"label-prop":"name","value-prop":"uuid",error:s(r).parent,"onUpdate:error":e[5]||(e[5]=n=>s(r).parent=n)},null,8,["modelValue","label","options","error"])]),p("div",k,[l(B,{modelValue:t.description,"onUpdate:modelValue":e[6]||(e[6]=n=>t.description=n),name:"description",label:o.$trans("finance.ledger_type.props.description"),error:s(r).description,"onUpdate:error":e[7]||(e[7]=n=>s(r).description=n)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),S={name:"FinanceLedgerTypeAction"},C=Object.assign(S,{setup(y){const d=g();return(a,m)=>{const r=i("PageHeaderAction"),c=i("PageHeader"),t=i("ParentTransition");return b(),j(q,null,[l(c,{title:a.$trans(s(d).meta.trans,{attribute:a.$trans(s(d).meta.label)}),navs:[{label:a.$trans("finance.finance"),path:"Finance"},{label:a.$trans("finance.ledger_type.ledger_type"),path:"FinanceLedgerTypeList"}]},{default:_(()=>[l(r,{name:"FinanceLedgerType",title:a.$trans("finance.ledger_type.ledger_type"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(t,{appear:"",visibility:!0},{default:_(()=>[l(I)]),_:1})],64)}}});export{C as default};
