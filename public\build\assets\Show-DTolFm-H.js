import{i as j,u as x,h as A,j as N,l as $,r as o,a as C,o as l,e as a,w as e,f as t,q as u,b as p,d as E,F as y,v as O,s as i,t as c,y as q}from"./app-DvIo72ZO.js";const U={class:"space-y-4"},z={name:"FinanceFeeConcessionShow"},K=Object.assign(z,{setup(G){j();const m=x(),b=A(),n=N("$trans"),F={},B="finance/feeConcession/",V=[{key:"head",label:n("finance.fee_head.fee_head"),visibility:!0},{key:"value",label:n("finance.fee_concession.props.value"),visibility:!0}],s=$({...F}),k=g=>{Object.assign(s,g)};return(g,d)=>{const w=o("PageHeaderAction"),S=o("PageHeader"),_=o("ListItemView"),L=o("ListContainerVertical"),h=o("BaseCard"),f=o("DataCell"),v=o("DataRow"),D=o("SimpleTable"),I=o("BaseButton"),P=o("ShowButton"),T=o("DetailLayoutVertical"),H=o("ShowItem"),R=o("ParentTransition");return l(),C(y,null,[a(S,{title:t(n)(t(m).meta.trans,{attribute:t(n)(t(m).meta.label)}),navs:[{label:t(n)("finance.finance"),path:"Finance"},{label:t(n)("finance.fee_concession.fee_concession"),path:"FinanceFeeConcessionList"}]},{default:e(()=>[a(w,{name:"FinanceFeeConcession",title:t(n)("finance.fee_concession.fee_concession"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(R,{appear:"",visibility:!0},{default:e(()=>[a(H,{"init-url":B,uuid:t(m).params.uuid,onSetItem:k,onRedirectTo:d[1]||(d[1]=r=>t(b).push({name:"FinanceFeeConcession"}))},{default:e(()=>[s.uuid?(l(),u(T,{key:0},{detail:e(()=>[a(h,{"no-padding":"","no-content-padding":""},{title:e(()=>[i(c(t(n)("global.detail",{attribute:t(n)("finance.fee_concession.fee_concession")})),1)]),default:e(()=>[a(L,null,{default:e(()=>[a(_,{label:t(n)("finance.fee_concession.props.name")},{default:e(()=>[i(c(s.name),1)]),_:1},8,["label"]),a(_,{label:t(n)("finance.fee_concession.props.description")},{default:e(()=>[i(c(s.description),1)]),_:1},8,["label"]),a(_,{label:t(n)("general.created_at")},{default:e(()=>[i(c(s.createdAt.formatted),1)]),_:1},8,["label"]),a(_,{label:t(n)("general.updated_at")},{default:e(()=>[i(c(s.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})]),default:e(()=>[E("div",U,[a(h,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:e(()=>[i(c(t(n)("finance.fee_concession.fee_concession")),1)]),footer:e(()=>[a(P,null,{default:e(()=>[t(q)("fee-concession:edit")?(l(),u(I,{key:0,design:"primary",onClick:d[0]||(d[0]=r=>t(b).push({name:"FinanceFeeConcessionEdit",params:{uuid:s.uuid}}))},{default:e(()=>[i(c(t(n)("general.edit")),1)]),_:1})):p("",!0)]),_:1})]),default:e(()=>[s.records.length>0?(l(),u(D,{key:0,header:V},{default:e(()=>[(l(!0),C(y,null,O(s.records,r=>(l(),u(v,{key:r.uuid},{default:e(()=>[a(f,{name:"head"},{default:e(()=>[i(c(r.head.name),1)]),_:2},1024),a(f,{name:"value"},{default:e(()=>[i(c(r.value.formatted),1)]),_:2},1024)]),_:2},1024))),128)),s.transportValue.value>0?(l(),u(v,{key:"transportFee"},{default:e(()=>[a(f,{name:"head"},{default:e(()=>[i(c(t(n)("transport.fee.fee")),1)]),_:1}),a(f,{name:"value"},{default:e(()=>[i(c(s.transportValue.formatted),1)]),_:1})]),_:1})):p("",!0)]),_:1})):p("",!0)]),_:1})])]),_:1})):p("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{K as default};
