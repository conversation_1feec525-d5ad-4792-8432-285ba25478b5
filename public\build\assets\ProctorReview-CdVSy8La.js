import{u as D,h as C,i as S,m as B,l as V,c as O,n as T,r as p,q as A,o as c,w as i,e as l,d as e,b as _,a as y,t as a,s as u,b0 as M}from"./app-DvIo72ZO.js";const R={class:"flex items-center justify-between mb-6"},j={class:"text-2xl font-bold text-gray-900 dark:text-white"},F={class:"mt-1 text-sm text-gray-600 dark:text-gray-400"},L={class:"text-sm text-gray-500 dark:text-gray-500"},U={class:"flex items-center space-x-3"},q={class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6"},Q={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},z={key:1,class:"flex justify-center py-12"},G={key:2,class:"text-center py-12"},H={class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},I={class:"text-gray-600 dark:text-gray-400 mb-4"},J={key:3,class:"text-center py-12"},K={class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},W={class:"text-gray-600 dark:text-gray-400 mb-4"},X={name:"ExamOnlineExamSubmissionProctorReview"},ee=Object.assign(X,{setup(Y){const v=D(),w=C(),N=S(),x=B(!0),f=B(!1),t=V({}),$=O(()=>{var m,r;if(!((m=t.startedAt)!=null&&m.value)||!((r=t.submittedAt)!=null&&r.value))return"N/A";const o=new Date(t.startedAt.value),n=new Date(t.submittedAt.value)-o,d=Math.floor(n/(1e3*60)),b=Math.floor(d/60),g=d%60;return b>0?`${b}h ${g}m`:`${g}m`}),h=async()=>{var o;x.value=!0,f.value=!1;try{const n={...await N.dispatch("exam/onlineExam/submission/getQuestions",{uuid:v.params.uuid,moduleUuid:v.params.submissionUuid})};n.exam&&n.exam.hasOwnProperty("enableProctoring")&&(n.exam.enableProctoring=!!Number(n.exam.enableProctoring)),Object.assign(t,n),(o=t.exam)!=null&&o.enableProctoring||console.warn("Proctoring not enabled for this exam")}catch(s){console.error("Failed to load submission:",s),f.value=!0}finally{x.value=!1}},E=()=>$.value,P=()=>{w.push({name:"ExamOnlineExamShow",params:{uuid:v.params.uuid}})},k=()=>{w.push({name:"ExamOnlineExamSubmissionList"})};return T(()=>{h()}),(o,s)=>{const n=p("BaseButton"),d=p("BaseDataView"),b=p("BaseCard"),g=p("ParentTransition");return c(),A(g,{appear:"",visibility:!0},{default:i(()=>[l(b,null,{default:i(()=>{var m;return[e("div",R,[e("div",null,[e("h1",j,a(o.$trans("exam.proctoring.review.title")||"Proctoring Review"),1),e("p",F,a(t.studentName)+" - "+a(t.admissionNumber),1),e("p",L,a(t.courseName)+" "+a(t.batchName),1)]),e("div",U,[l(n,{design:"secondary",onClick:P},{default:i(()=>[s[0]||(s[0]=e("i",{class:"fas fa-arrow-left mr-2"},null,-1)),u(" "+a(o.$trans("general.back")||"Back"),1)]),_:1}),l(n,{design:"primary",onClick:k},{default:i(()=>[s[1]||(s[1]=e("i",{class:"fas fa-file-alt mr-2"},null,-1)),u(" "+a(o.$trans("exam.online_exam.submission.view_answers")||"View Answers"),1)]),_:1})])]),e("div",q,[e("div",Q,[l(d,{label:"Exam Title"},{default:i(()=>{var r;return[u(a(((r=t.exam)==null?void 0:r.title)||"N/A"),1)]}),_:1}),l(d,{label:"Started At"},{default:i(()=>{var r;return[u(a(((r=t.startedAt)==null?void 0:r.formatted)||"N/A"),1)]}),_:1}),l(d,{label:"Submitted At"},{default:i(()=>{var r;return[u(a(((r=t.submittedAt)==null?void 0:r.formatted)||"N/A"),1)]}),_:1}),l(d,{label:"Duration"},{default:i(()=>[u(a(E()),1)]),_:1})])]),t.uuid&&((m=t.exam)!=null&&m.uuid)?(c(),A(M,{key:0,"exam-uuid":t.exam.uuid,"submission-uuid":t.uuid},null,8,["exam-uuid","submission-uuid"])):_("",!0),x.value?(c(),y("div",z,s[2]||(s[2]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):_("",!0),f.value?(c(),y("div",G,[s[4]||(s[4]=e("div",{class:"text-red-500 mb-4"},[e("i",{class:"fas fa-exclamation-triangle text-4xl"})],-1)),e("h3",H,a(o.$trans("exam.proctoring.review.load_error")||"Failed to load proctoring data"),1),e("p",I,a(o.$trans("exam.proctoring.review.load_error_description")||"There was an error loading the proctoring information for this submission."),1),l(n,{design:"primary",onClick:h},{default:i(()=>[s[3]||(s[3]=e("i",{class:"fas fa-sync-alt mr-2"},null,-1)),u(" "+a(o.$trans("general.retry")||"Retry"),1)]),_:1})])):_("",!0),!x.value&&!f.value&&t.exam&&!t.exam.enableProctoring?(c(),y("div",J,[s[6]||(s[6]=e("div",{class:"text-gray-400 mb-4"},[e("i",{class:"fas fa-shield-alt text-4xl"})],-1)),e("h3",K,a(o.$trans("exam.proctoring.review.not_enabled")||"Proctoring Not Enabled"),1),e("p",W,a(o.$trans("exam.proctoring.review.not_enabled_description")||"This exam was not configured with proctoring features."),1),l(n,{design:"secondary",onClick:k},{default:i(()=>[s[5]||(s[5]=e("i",{class:"fas fa-file-alt mr-2"},null,-1)),u(" "+a(o.$trans("exam.online_exam.submission.view_answers")||"View Answers"),1)]),_:1})])):_("",!0)]}),_:1})]),_:1})}}});export{ee as default};
