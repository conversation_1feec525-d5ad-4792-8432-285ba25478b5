import{i as E,u as B,h as I,I as L,m as M,l as y,r as i,q as F,o as p,w as m,d as c,a as _,b as v,f as n,s as b,e as u,t as r,x as z,K as D,F as K}from"./app-DvIo72ZO.js";const G={class:"grid grid-cols-3 gap-6"},J={class:"col-span-3 sm:col-span-1"},Q={key:0},W={key:2,class:"mt-2 text-sm"},X={key:0},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3"},te={name:"StudentTransferForm"},se=Object.assign(te,{setup(T){const g=E(),d=B();I();const f={student:"",date:"",transferCertificateNumber:"",reason:"",remarks:""},k="student/transfer/",l=L(k),S=M(!1),N=y({reasons:[]}),a=y({selectedStudent:null,feeSummary:{}}),o=y({...f}),$=y({student:"",isLoaded:!d.params.uuid}),U=e=>{Object.assign(N,e),Object.assign(o,D(f))},j=e=>{a.selectedStudent={name:e.name,codeNumber:e.codeNumber,joiningDate:e.joiningDate},Object.assign(f,{student:e.uuid,reason:e.reasonUuid,date:e.leavingDate.value,remarks:e.remarks}),Object.assign(o,D(f)),$.isLoaded=!0},C=async e=>{S.value=!0,await g.dispatch("student/getFeeSummary",{uuid:e}).then(s=>{S.value=!1,a.feeSummary=s}).catch(s=>{S.value=!1})},O=e=>{o.student=e?e.uuid:"",e&&C(e.uuid)},h=()=>{a.selectedStudent=null,o.student=""};return(e,s)=>{const V=i("TextMuted"),P=i("BaseSelectSearch"),R=i("DatePicker"),q=i("BaseInput"),A=i("BaseSelect"),w=i("BaseTextarea"),H=i("FormAction");return p(),F(H,{"pre-requisites":!0,onSetPreRequisites:U,"init-url":k,"init-form":f,form:o,"set-form":j,redirect:"StudentTransfer"},{default:m(()=>[c("div",G,[c("div",J,[n(d).params.uuid&&a.selectedStudent?(p(),_("div",Q,[b(r(a.selectedStudent.name)+" ",1),u(V,{block:""},{default:m(()=>[b(r(a.selectedStudent.codeNumber),1)]),_:1}),u(V,{block:""},{default:m(()=>[b(r(e.$trans("student.admission.props.date"))+": "+r(a.selectedStudent.joiningDate.formatted),1)]),_:1})])):v("",!0),n(d).params.uuid?v("",!0):(p(),F(P,{key:1,name:"student",label:e.$trans("global.select",{attribute:e.$trans("student.student")}),modelValue:a.selectedStudent,"onUpdate:modelValue":s[0]||(s[0]=t=>a.selectedStudent=t),error:n(l).student,"onUpdate:error":s[1]||(s[1]=t=>n(l).student=t),"value-prop":"uuid","object-prop":!0,"init-search":$.student,"search-key":"name","search-action":"student/summary",onSelected:O,onRemoved:h},{selectedOption:m(t=>[b(r(t.value.name)+" ("+r(t.value.courseName+" "+t.value.batchName)+") ",1)]),listOption:m(t=>[b(r(t.option.name)+" ("+r(t.option.courseName+" "+t.option.batchName)+") ",1)]),_:1},8,["label","modelValue","error","init-search"])),!n(d).params.uuid&&a.selectedStudent?(p(),_("div",W,[u(V,{block:""},{default:m(()=>[b(r(a.selectedStudent.codeNumber),1)]),_:1}),c("div",null,r(e.$trans("student.admission.props.date"))+": "+r(a.selectedStudent.joiningDate.formatted),1),a.selectedStudent.leavingDate.value?(p(),_("div",X,r(e.$trans("student.transfer.props.date"))+": "+r(a.selectedStudent.leavingDate.formatted),1)):v("",!0),a.feeSummary.balanceFee?(p(),_("div",{key:1,class:z(["font-semibold",{"text-red-500":a.feeSummary.balanceFee.value>0,"text-green-500":a.feeSummary.balanceFee.value<0}])},r(e.$trans("finance.fee.balance")+": "+a.feeSummary.balanceFee.formatted),3)):v("",!0)])):v("",!0)]),c("div",Y,[u(R,{modelValue:o.date,"onUpdate:modelValue":s[2]||(s[2]=t=>o.date=t),name:"date",label:e.$trans("student.transfer.props.date"),"no-clear":"",error:n(l).date,"onUpdate:error":s[3]||(s[3]=t=>n(l).date=t)},null,8,["modelValue","label","error"])]),c("div",Z,[u(q,{type:"text",modelValue:o.transferCertificateNumber,"onUpdate:modelValue":s[4]||(s[4]=t=>o.transferCertificateNumber=t),name:"transferCertificateNumber",label:e.$trans("student.transfer.props.certificate_number"),error:n(l).transferCertificateNumber,"onUpdate:error":s[5]||(s[5]=t=>n(l).transferCertificateNumber=t)},null,8,["modelValue","label","error"])]),c("div",x,[u(A,{name:"student",label:e.$trans("student.transfer.props.reason"),modelValue:o.reason,"onUpdate:modelValue":s[6]||(s[6]=t=>o.reason=t),error:n(l).reason,"onUpdate:error":s[7]||(s[7]=t=>n(l).reason=t),options:N.reasons,"value-prop":"uuid","label-prop":"name"},null,8,["label","modelValue","error","options"])]),c("div",ee,[u(w,{rows:3,modelValue:o.remarks,"onUpdate:modelValue":s[8]||(s[8]=t=>o.remarks=t),name:"remarks",label:e.$trans("student.transfer.props.remarks"),error:n(l).remarks,"onUpdate:error":s[9]||(s[9]=t=>n(l).remarks=t)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),ae={name:"StudentTransferAction"},re=Object.assign(ae,{setup(T){const g=B();return(d,f)=>{const k=i("PageHeaderAction"),l=i("PageHeader"),S=i("ParentTransition");return p(),_(K,null,[u(l,{title:d.$trans(n(g).meta.trans,{attribute:d.$trans(n(g).meta.label)}),navs:[{label:d.$trans("student.student"),path:"Student"},{label:d.$trans("student.transfer.transfer"),path:"StudentTransfer"}]},{default:m(()=>[u(k,{name:"StudentTransfer",title:d.$trans("student.transfer.transfer"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),u(S,{appear:"",visibility:!0},{default:m(()=>[u(se)]),_:1})],64)}}});export{re as default};
