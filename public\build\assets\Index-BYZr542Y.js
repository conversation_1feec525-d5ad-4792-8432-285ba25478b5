import{u as N,l as I,n as A,r,q as v,o as g,w as t,d as f,e as a,b as V,s as o,t as n,h as H,j as R,y as q,m as j,f as p,a as P,F as E,v as W}from"./app-DvIo72ZO.js";const z={class:"grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},Q={class:"grid grid-cols-3 gap-6 mt-4"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={__name:"Filter",emits:["hide"],setup(w,{emit:b}){const _=N(),D=b,y={title:"",students:[],courses:[],batches:[],categories:[],startDate:"",endDate:""},l=I({...y}),d=I({students:[],courses:[],batches:[],categories:[],isLoaded:!_.query.categories});return A(async()=>{d.categories=_.query.categories?_.query.categories.split(","):[],d.isLoaded=!0}),(c,i)=>{const m=r("BaseInput"),u=r("BaseSelectSearch"),B=r("DatePicker"),C=r("FilterForm");return g(),v(C,{"init-form":y,form:l,multiple:["students","courses","batches","categories"],onHide:i[7]||(i[7]=e=>D("hide"))},{default:t(()=>[f("div",z,[f("div",G,[a(m,{type:"text",modelValue:l.title,"onUpdate:modelValue":i[0]||(i[0]=e=>l.title=e),name:"title",label:c.$trans("discipline.incident.props.title")},null,8,["modelValue","label"])]),f("div",J,[d.isLoaded?(g(),v(u,{key:0,multiple:"",name:"students",label:c.$trans("global.select",{attribute:c.$trans("student.student")}),modelValue:l.students,"onUpdate:modelValue":i[1]||(i[1]=e=>l.students=e),"value-prop":"uuid","init-search":d.students,"search-key":"name","search-action":"student/list"},{selectedOption:t(e=>[o(n(e.value.name)+" ("+n(e.value.codeNumber)+") ",1)]),listOption:t(e=>[o(n(e.option.name)+" ("+n(e.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):V("",!0)]),f("div",K,[d.isLoaded?(g(),v(u,{key:0,multiple:"",name:"courses",label:c.$trans("global.select",{attribute:c.$trans("academic.course.course")}),modelValue:l.courses,"onUpdate:modelValue":i[2]||(i[2]=e=>l.courses=e),"value-prop":"uuid","init-search":d.courses,"search-key":"name_with_term","search-action":"academic/course/list"},{selectedOption:t(e=>[o(n(e.value.nameWithTerm),1)]),listOption:t(e=>[o(n(e.option.nameWithTerm),1)]),_:1},8,["label","modelValue","init-search"])):V("",!0)])]),f("div",Q,[f("div",X,[d.isLoaded?(g(),v(u,{key:0,multiple:"",name:"batches",label:c.$trans("global.select",{attribute:c.$trans("academic.batch.batch")}),modelValue:l.batches,"onUpdate:modelValue":i[3]||(i[3]=e=>l.batches=e),"value-prop":"uuid","init-search":d.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:t(e=>[o(n(e.value.course.name)+" "+n(e.value.name),1)]),listOption:t(e=>[o(n(e.option.course.name)+" "+n(e.option.name),1)]),_:1},8,["label","modelValue","init-search"])):V("",!0)]),f("div",Y,[d.isLoaded?(g(),v(u,{key:0,multiple:"",name:"categories",label:c.$trans("global.select",{attribute:c.$trans("discipline.incident.category.category")}),modelValue:l.categories,"onUpdate:modelValue":i[4]||(i[4]=e=>l.categories=e),"value-prop":"uuid","init-search":d.categories,"search-action":"option/list","additional-search-query":{type:"incident_category"}},null,8,["label","modelValue","init-search"])):V("",!0)]),f("div",Z,[a(B,{start:l.startDate,"onUpdate:start":i[5]||(i[5]=e=>l.startDate=e),end:l.endDate,"onUpdate:end":i[6]||(i[6]=e=>l.endDate=e),name:"dateBetween",as:"range",label:c.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},ee={name:"DisciplineIncidentList"},ae=Object.assign(ee,{setup(w){const b=H(),_=R("emitter");let D=["create","filter"];q("discipline:config")&&D.push("config");let y=["print","pdf","excel"];const l="discipline/incident/",d=j(!1),c=I({}),i=m=>{Object.assign(c,m)};return(m,u)=>{const B=r("PageHeaderAction"),C=r("PageHeader"),e=r("ParentTransition"),$=r("DataCell"),F=r("BaseBadge"),L=r("TextMuted"),k=r("FloatingMenuItem"),T=r("FloatingMenu"),O=r("DataRow"),S=r("BaseButton"),U=r("DataTable"),M=r("ListItem");return g(),v(M,{"init-url":l,onSetItems:i},{header:t(()=>[a(C,{title:m.$trans("discipline.incident.incident"),navs:[{label:m.$trans("discipline.discipline"),path:"Discipline"}]},{default:t(()=>[a(B,{url:"discipline/incidents/",name:"DisciplineIncident",title:m.$trans("discipline.incident.incident"),actions:p(D),"dropdown-actions":p(y),"config-path":"DisciplineConfig",onToggleFilter:u[0]||(u[0]=s=>d.value=!d.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:t(()=>[a(e,{appear:"",visibility:d.value},{default:t(()=>[a(x,{onRefresh:u[1]||(u[1]=s=>p(_).emit("listItems")),onHide:u[2]||(u[2]=s=>d.value=!1)})]),_:1},8,["visibility"])]),default:t(()=>[a(e,{appear:"",visibility:!0},{default:t(()=>[a(U,{header:c.headers,meta:c.meta,module:"discipline.incident",onRefresh:u[4]||(u[4]=s=>p(_).emit("listItems"))},{actionButton:t(()=>[a(S,{onClick:u[3]||(u[3]=s=>p(b).push({name:"DisciplineIncidentCreate"}))},{default:t(()=>[o(n(m.$trans("global.add",{attribute:m.$trans("discipline.incident.incident")})),1)]),_:1})]),default:t(()=>[(g(!0),P(E,null,W(c.data,s=>(g(),v(O,{key:s.uuid,onDoubleClick:h=>p(b).push({name:"DisciplineIncidentShow",params:{uuid:s.uuid}})},{default:t(()=>[a($,{name:"category"},{default:t(()=>[o(n(s.category.name),1)]),_:2},1024),a($,{name:"title"},{default:t(()=>[o(n(s.title)+" ",1),a(F,{design:s.nature.color},{default:t(()=>[o(n(s.nature.label),1)]),_:2},1032,["design"])]),_:2},1024),a($,{name:"name"},{default:t(()=>[o(n(s.name)+" ",1),a(L,{block:""},{default:t(()=>[o(n(s.contactNumber),1)]),_:2},1024)]),_:2},1024),a($,{name:"date"},{default:t(()=>{var h;return[o(n(((h=s.date)==null?void 0:h.formatted)||"-"),1)]}),_:2},1024),a($,{name:"createdAt"},{default:t(()=>[o(n(s.createdAt.formatted),1)]),_:2},1024),a($,{name:"action"},{default:t(()=>[a(T,null,{default:t(()=>[a(k,{icon:"fas fa-arrow-circle-right",onClick:h=>p(b).push({name:"DisciplineIncidentShow",params:{uuid:s.uuid}})},{default:t(()=>[o(n(m.$trans("general.show")),1)]),_:2},1032,["onClick"]),a(k,{icon:"fas fa-edit",onClick:h=>p(b).push({name:"DisciplineIncidentEdit",params:{uuid:s.uuid}})},{default:t(()=>[o(n(m.$trans("general.edit")),1)]),_:2},1032,["onClick"]),a(k,{icon:"fas fa-copy",onClick:h=>p(b).push({name:"DisciplineIncidentDuplicate",params:{uuid:s.uuid}})},{default:t(()=>[o(n(m.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),a(k,{icon:"fas fa-trash",onClick:h=>p(_).emit("deleteItem",{uuid:s.uuid})},{default:t(()=>[o(n(m.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{ae as default};
