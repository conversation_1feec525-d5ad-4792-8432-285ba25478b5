import { ref, onMounted, onUnmounted } from 'vue'
import * as Api from '@core/apis'

export function useExamSecurity(examUuid, submissionUuid, securityConfig) {

    // Reactive submission UUID to allow updates
    const currentSubmissionUuid = ref(submissionUuid)

    // State
    const isFullscreen = ref(false)
    const isWindowFocused = ref(true)
    const tabSwitchCount = ref(0)
    const copyPasteAttempts = ref(0)
    
    // Event listeners
    let fullscreenChangeListener = null
    let visibilityChangeListener = null
    let focusListener = null
    let blurListener = null
    let keydownListener = null
    let contextmenuListener = null
    let copyListener = null
    let pasteListener = null
    let cutListener = null

    /**
     * Initialize security measures
     */
    const initialize = () => {
        if (securityConfig.fullscreenEnforcement) {
            setupFullscreenMonitoring()
            enterFullscreen()
        }

        if (securityConfig.copyPasteBlocking) {
            setupCopyPasteBlocking()
        }

        setupTabSwitchDetection()
        setupContextMenuBlocking()
        setupKeyboardBlocking()
    }

    /**
     * Cleanup security measures
     */
    const cleanup = () => {
        removeAllListeners()
        
        if (isFullscreen.value) {
            exitFullscreen()
        }
    }

    /**
     * Enter fullscreen mode
     */
    const enterFullscreen = async () => {
        try {
            const element = document.documentElement
            
            if (element.requestFullscreen) {
                await element.requestFullscreen()
            } else if (element.webkitRequestFullscreen) {
                await element.webkitRequestFullscreen()
            } else if (element.msRequestFullscreen) {
                await element.msRequestFullscreen()
            }
            
        } catch (error) {
            console.error('Failed to enter fullscreen:', error)
            await logSecurityEvent('fullscreen_entry_failed', error.message)
        }
    }

    /**
     * Exit fullscreen mode
     */
    const exitFullscreen = async () => {
        try {
            if (document.exitFullscreen) {
                await document.exitFullscreen()
            } else if (document.webkitExitFullscreen) {
                await document.webkitExitFullscreen()
            } else if (document.msExitFullscreen) {
                await document.msExitFullscreen()
            }
        } catch (error) {
            console.error('Failed to exit fullscreen:', error)
        }
    }

    /**
     * Setup fullscreen monitoring
     */
    const setupFullscreenMonitoring = () => {
        fullscreenChangeListener = () => {
            const isCurrentlyFullscreen = !!(
                document.fullscreenElement ||
                document.webkitFullscreenElement ||
                document.msFullscreenElement
            )
            
            isFullscreen.value = isCurrentlyFullscreen
            
            if (!isCurrentlyFullscreen && securityConfig.fullscreenEnforcement) {
                logFullscreenExit()

                // Re-enter fullscreen after a short delay
                setTimeout(() => {
                    if (securityConfig.fullscreenEnforcement) {
                        enterFullscreen()
                    }
                }, 1000)
            }
        }
        
        document.addEventListener('fullscreenchange', fullscreenChangeListener)
        document.addEventListener('webkitfullscreenchange', fullscreenChangeListener)
        document.addEventListener('msfullscreenchange', fullscreenChangeListener)
    }

    /**
     * Setup tab switch detection
     */
    const setupTabSwitchDetection = () => {
        visibilityChangeListener = () => {
            if (document.hidden) {
                isWindowFocused.value = false
                tabSwitchCount.value++
                logTabSwitch(document.title)
            } else {
                isWindowFocused.value = true
            }
        }
        
        focusListener = () => {
            isWindowFocused.value = true
        }
        
        blurListener = () => {
            isWindowFocused.value = false
            tabSwitchCount.value++
            logTabSwitch('Window lost focus')
        }
        
        document.addEventListener('visibilitychange', visibilityChangeListener)
        window.addEventListener('focus', focusListener)
        window.addEventListener('blur', blurListener)
    }

    /**
     * Setup copy/paste blocking
     */
    const setupCopyPasteBlocking = () => {
        const blockEvent = (event, action) => {
            event.preventDefault()
            event.stopPropagation()
            
            copyPasteAttempts.value++
            logCopyPasteAttempt(action, event.clipboardData?.getData('text') || null)
            
            // Show warning to user
            showSecurityWarning(`${action} is not allowed during the exam`)
            
            return false
        }
        
        copyListener = (event) => blockEvent(event, 'copy')
        pasteListener = (event) => blockEvent(event, 'paste')
        cutListener = (event) => blockEvent(event, 'cut')
        
        document.addEventListener('copy', copyListener)
        document.addEventListener('paste', pasteListener)
        document.addEventListener('cut', cutListener)
    }

    /**
     * Setup context menu blocking
     */
    const setupContextMenuBlocking = () => {
        contextmenuListener = (event) => {
            event.preventDefault()
            event.stopPropagation()
            
            logSecurityEvent('context_menu_attempt', 'Right-click attempted')
            showSecurityWarning('Right-click is disabled during the exam')
            
            return false
        }
        
        document.addEventListener('contextmenu', contextmenuListener)
    }

    /**
     * Setup keyboard shortcut blocking
     */
    const setupKeyboardBlocking = () => {
        keydownListener = (event) => {
            const blockedKeys = [
                'F12', // Developer tools
                'F5',  // Refresh
                'F11', // Fullscreen toggle
            ]
            
            const blockedCombinations = [
                { ctrl: true, key: 'r' },      // Refresh
                { ctrl: true, key: 'R' },      // Refresh
                { ctrl: true, key: 'u' },      // View source
                { ctrl: true, key: 'U' },      // View source
                { ctrl: true, key: 'i' },      // Developer tools
                { ctrl: true, key: 'I' },      // Developer tools
                { ctrl: true, key: 'j' },      // Console
                { ctrl: true, key: 'J' },      // Console
                { ctrl: true, key: 'k' },      // Console
                { ctrl: true, key: 'K' },      // Console
                { ctrl: true, key: 's' },      // Save
                { ctrl: true, key: 'S' },      // Save
                { ctrl: true, key: 'a' },      // Select all
                { ctrl: true, key: 'A' },      // Select all
                { ctrl: true, key: 'p' },      // Print
                { ctrl: true, key: 'P' },      // Print
                { ctrl: true, shift: true, key: 'i' }, // Developer tools
                { ctrl: true, shift: true, key: 'I' }, // Developer tools
                { ctrl: true, shift: true, key: 'j' }, // Console
                { ctrl: true, shift: true, key: 'J' }, // Console
                { ctrl: true, shift: true, key: 'c' }, // Console
                { ctrl: true, shift: true, key: 'C' }, // Console
                { alt: true, key: 'Tab' },     // Alt+Tab
                { alt: true, key: 'F4' },      // Alt+F4
            ]
            
            // Check blocked keys
            if (blockedKeys.includes(event.key)) {
                event.preventDefault()
                event.stopPropagation()
                
                logSecurityEvent('blocked_key_attempt', `Blocked key: ${event.key}`)
                showSecurityWarning(`Key ${event.key} is disabled during the exam`)
                
                return false
            }
            
            // Check blocked combinations
            for (const combo of blockedCombinations) {
                const ctrlMatch = combo.ctrl ? event.ctrlKey : !event.ctrlKey
                const altMatch = combo.alt ? event.altKey : !event.altKey
                const shiftMatch = combo.shift ? event.shiftKey : !event.shiftKey
                const keyMatch = combo.key === event.key
                
                if (ctrlMatch && altMatch && shiftMatch && keyMatch) {
                    event.preventDefault()
                    event.stopPropagation()
                    
                    const comboStr = [
                        combo.ctrl && 'Ctrl',
                        combo.alt && 'Alt',
                        combo.shift && 'Shift',
                        combo.key
                    ].filter(Boolean).join('+')
                    
                    logSecurityEvent('blocked_combination_attempt', `Blocked combination: ${comboStr}`)
                    showSecurityWarning(`Key combination ${comboStr} is disabled during the exam`)
                    
                    return false
                }
            }
        }
        
        document.addEventListener('keydown', keydownListener, true)
    }

    /**
     * Remove all event listeners
     */
    const removeAllListeners = () => {
        if (fullscreenChangeListener) {
            document.removeEventListener('fullscreenchange', fullscreenChangeListener)
            document.removeEventListener('webkitfullscreenchange', fullscreenChangeListener)
            document.removeEventListener('msfullscreenchange', fullscreenChangeListener)
        }
        
        if (visibilityChangeListener) {
            document.removeEventListener('visibilitychange', visibilityChangeListener)
        }
        
        if (focusListener) {
            window.removeEventListener('focus', focusListener)
        }
        
        if (blurListener) {
            window.removeEventListener('blur', blurListener)
        }
        
        if (keydownListener) {
            document.removeEventListener('keydown', keydownListener, true)
        }
        
        if (contextmenuListener) {
            document.removeEventListener('contextmenu', contextmenuListener)
        }
        
        if (copyListener) {
            document.removeEventListener('copy', copyListener)
        }
        
        if (pasteListener) {
            document.removeEventListener('paste', pasteListener)
        }
        
        if (cutListener) {
            document.removeEventListener('cut', cutListener)
        }
    }

    /**
     * Show security warning to user
     */
    const showSecurityWarning = (message) => {
        // You can customize this to use your app's notification system
        console.warn('Security Warning:', message)
        
        // Could integrate with vue-toastification or similar
        // toast.warning(message)
    }

    // API logging methods
    const logFullscreenExit = async () => {
        if (!currentSubmissionUuid.value) {
            console.error('Cannot log fullscreen exit: submission UUID not set')
            return
        }

        try {
            await Api.custom({
                url: `app/online-exams/${examUuid}/proctoring/fullscreen-exit`,
                method: 'POST',
                data: {
                    submission_uuid: currentSubmissionUuid.value
                }
            })
        } catch (error) {
            console.error('Failed to log fullscreen exit:', error)
        }
    }

    const logTabSwitch = async (tabTitle) => {
        if (!currentSubmissionUuid.value) {
            console.error('Cannot log tab switch: submission UUID not set')
            return
        }

        try {
            await Api.custom({
                url: `app/online-exams/${examUuid}/proctoring/tab-switch`,
                method: 'POST',
                data: {
                    submission_uuid: currentSubmissionUuid.value,
                    tab_title: tabTitle
                }
            })
        } catch (error) {
            console.error('Failed to log tab switch:', error)
        }
    }

    const logCopyPasteAttempt = async (action, content) => {
        if (!currentSubmissionUuid.value) {
            console.error('Cannot log copy/paste attempt: submission UUID not set')
            return
        }

        try {
            await Api.custom({
                url: `app/online-exams/${examUuid}/proctoring/copy-paste-attempt`,
                method: 'POST',
                data: {
                    submission_uuid: currentSubmissionUuid.value,
                    action,
                    content
                }
            })
        } catch (error) {
            console.error('Failed to log copy/paste attempt:', error)
        }
    }

    const logSecurityEvent = async (eventType, details) => {
        console.log('Security event:', eventType, details)
        // Could be logged to a general security endpoint
    }

    /**
     * Update submission UUID after initialization
     */
    const setSubmissionUuid = (newSubmissionUuid) => {
        currentSubmissionUuid.value = newSubmissionUuid
    }

    return {
        // State
        isFullscreen,
        isWindowFocused,
        tabSwitchCount,
        copyPasteAttempts,

        // Methods
        initialize,
        cleanup,
        enterFullscreen,
        exitFullscreen,
        setSubmissionUuid
    }
}
