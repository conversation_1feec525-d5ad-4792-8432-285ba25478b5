import{u as S,h as C,l as P,r as n,a as V,o as p,e as t,w as a,f as o,q as k,b as y,d as A,s as l,t as r,F as H}from"./app-DvIo72ZO.js";const I={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},N={name:"MessMealShow"},R=Object.assign(N,{setup(T){const u=S(),d=C(),c={},_="mess/meal/",s=P({...c}),f=e=>{Object.assign(s,e)};return(e,i)=>{const g=n("PageHeaderAction"),b=n("PageHeader"),m=n("BaseDataView"),B=n("BaseButton"),M=n("ShowButton"),$=n("BaseCard"),h=n("ShowItem"),w=n("ParentTransition");return p(),V(H,null,[t(b,{title:e.$trans(o(u).meta.trans,{attribute:e.$trans(o(u).meta.label)}),navs:[{label:e.$trans("mess.mess"),path:"Mess"},{label:e.$trans("mess.meal.meal"),path:"MessMeal"}]},{default:a(()=>[t(g,{name:"MessMeal",title:e.$trans("mess.meal.meal"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(w,{appear:"",visibility:!0},{default:a(()=>[t(h,{"init-url":_,uuid:o(u).params.uuid,"module-uuid":o(u).params.muuid,onSetItem:f,onRedirectTo:i[1]||(i[1]=v=>o(d).push({name:"MessMeal",params:{uuid:s.uuid}}))},{default:a(()=>[s.uuid?(p(),k($,{key:0},{title:a(()=>[l(r(s.name),1)]),footer:a(()=>[t(M,null,{default:a(()=>[t(B,{design:"primary",onClick:i[0]||(i[0]=v=>o(d).push({name:"MessMealEdit",params:{uuid:s.uuid}}))},{default:a(()=>[l(r(e.$trans("general.edit")),1)]),_:1})]),_:1})]),default:a(()=>[A("dl",I,[t(m,{label:e.$trans("mess.meal.props.name")},{default:a(()=>[l(r(s.name),1)]),_:1},8,["label"]),t(m,{class:"col-span-1 sm:col-span-2",label:e.$trans("mess.meal.props.description")},{default:a(()=>[l(r(s.description),1)]),_:1},8,["label"]),t(m,{label:e.$trans("general.created_at")},{default:a(()=>[l(r(s.createdAt.formatted),1)]),_:1},8,["label"]),t(m,{label:e.$trans("general.updated_at")},{default:a(()=>[l(r(s.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):y("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{R as default};
