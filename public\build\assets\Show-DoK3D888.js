import{i as A,u as C,h as D,l as E,r as o,a as _,o as b,e as l,w as e,f as i,q as g,b as m,d as p,s,t,F as f,y as F}from"./app-DvIo72ZO.js";const H={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-3"},I={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-3"},R={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-3"},j={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-3"},O={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-3"},q={class:"mt-4 grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},U={name:"TransportVehicleShow"},J=Object.assign(U,{setup(z){A();const d=C(),h=D(),v={},$="transport/vehicle/",r=E({...v}),y=a=>{Object.assign(r,a)};return(a,u)=>{const w=o("PageHeaderAction"),B=o("PageHeader"),n=o("BaseDataView"),c=o("BaseFieldset"),N=o("BaseButton"),P=o("ShowButton"),V=o("BaseCard"),S=o("ShowItem"),T=o("ParentTransition");return b(),_(f,null,[l(B,{title:a.$trans(i(d).meta.trans,{attribute:a.$trans(i(d).meta.label)}),navs:[{label:a.$trans("transport.transport"),path:"Transport"},{label:a.$trans("transport.vehicle.vehicle"),path:"TransportVehicle"}]},{default:e(()=>[l(w,{name:"TransportVehicle",title:a.$trans("transport.vehicle.vehicle"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(T,{appear:"",visibility:!0},{default:e(()=>[l(S,{"init-url":$,uuid:i(d).params.uuid,"module-uuid":i(d).params.muuid,onSetItem:y,onRedirectTo:u[1]||(u[1]=k=>i(h).push({name:"TransportVehicle",params:{uuid:r.uuid}}))},{default:e(()=>[r.uuid?(b(),g(V,{key:0},{title:e(()=>[s(t(r.registrationNumber)+" ("+t(r.name)+") ",1)]),footer:e(()=>[l(P,null,{default:e(()=>[i(F)("vehicle:edit")?(b(),g(N,{key:0,design:"primary",onClick:u[0]||(u[0]=k=>i(h).push({name:"TransportVehicleEdit",params:{uuid:r.uuid}}))},{default:e(()=>[s(t(a.$trans("general.edit")),1)]),_:1})):m("",!0)]),_:1})]),default:e(()=>[p("dl",H,[l(n,{label:a.$trans("transport.vehicle.props.fuel_type")},{default:e(()=>[s(t(r.fuelType.label),1)]),_:1},8,["label"]),l(n,{label:a.$trans("transport.vehicle.props.model_number")},{default:e(()=>[s(t(r.modelNumber),1)]),_:1},8,["label"]),l(n,{label:a.$trans("transport.vehicle.props.make")},{default:e(()=>[s(t(r.make),1)]),_:1},8,["label"]),l(n,{label:a.$trans("transport.vehicle.props.class")},{default:e(()=>[s(t(r.class),1)]),_:1},8,["label"]),l(n,{label:a.$trans("transport.vehicle.props.seating_capacity")},{default:e(()=>[s(t(r.seatingCapacity),1)]),_:1},8,["label"]),l(n,{label:a.$trans("transport.vehicle.props.max_seating_allowed")},{default:e(()=>[s(t(r.maxSeatingAllowed),1)]),_:1},8,["label"]),l(n,{label:a.$trans("transport.vehicle.props.fuel_capacity")},{default:e(()=>[s(t(r.fuelCapacity),1)]),_:1},8,["label"])]),l(c,{class:"mt-4"},{legend:e(()=>[s(t(a.$trans("transport.vehicle.registration_info")),1)]),default:e(()=>[p("dl",I,[l(n,{label:a.$trans("transport.vehicle.props.registration_date")},{default:e(()=>[s(t(r.registrationDate.formatted),1)]),_:1},8,["label"]),l(n,{label:a.$trans("transport.vehicle.props.registration_place")},{default:e(()=>[s(t(r.registrationPlace),1)]),_:1},8,["label"]),l(n,{label:a.$trans("transport.vehicle.props.chassis_number")},{default:e(()=>[s(t(r.chassisNumber),1)]),_:1},8,["label"]),l(n,{label:a.$trans("transport.vehicle.props.engine_number")},{default:e(()=>[s(t(r.engineNumber),1)]),_:1},8,["label"]),l(n,{label:a.$trans("transport.vehicle.props.cubic_capacity")},{default:e(()=>[s(t(r.cubicCapacity),1)]),_:1},8,["label"]),l(n,{label:a.$trans("transport.vehicle.props.color")},{default:e(()=>[s(t(r.color),1)]),_:1},8,["label"])])]),_:1}),l(c,{class:"mt-4"},{legend:e(()=>[s(t(a.$trans("transport.vehicle.owner_info")),1)]),default:e(()=>[p("dl",R,[l(n,{label:a.$trans("transport.vehicle.props.owner_name")},{default:e(()=>[s(t(r.ownerName),1)]),_:1},8,["label"]),l(n,{label:a.$trans("transport.vehicle.props.owner_address")},{default:e(()=>[s(t(r.ownerAddress),1)]),_:1},8,["label"]),l(n,{label:a.$trans("transport.vehicle.props.owner_phone")},{default:e(()=>[s(t(r.ownerPhone),1)]),_:1},8,["label"]),l(n,{label:a.$trans("transport.vehicle.props.owner_email")},{default:e(()=>[s(t(r.ownerEmail),1)]),_:1},8,["label"])])]),_:1}),l(c,{class:"mt-4"},{legend:e(()=>[s(t(a.$trans("transport.vehicle.driver_info")),1)]),default:e(()=>[p("dl",j,[l(n,{label:a.$trans("transport.vehicle.props.driver_name")},{default:e(()=>[s(t(r.driverName),1)]),_:1},8,["label"]),l(n,{label:a.$trans("transport.vehicle.props.driver_address")},{default:e(()=>[s(t(r.driverAddress),1)]),_:1},8,["label"]),l(n,{label:a.$trans("transport.vehicle.props.driver_phone")},{default:e(()=>[s(t(r.driverPhone),1)]),_:1},8,["label"]),l(n,{label:a.$trans("transport.vehicle.props.helper_name")},{default:e(()=>[s(t(r.helperName),1)]),_:1},8,["label"]),l(n,{label:a.$trans("transport.vehicle.props.helper_address")},{default:e(()=>[s(t(r.helperAddress),1)]),_:1},8,["label"]),l(n,{label:a.$trans("transport.vehicle.props.helper_phone")},{default:e(()=>[s(t(r.helperPhone),1)]),_:1},8,["label"])])]),_:1}),l(c,{class:"mt-4"},{legend:e(()=>[s(t(a.$trans("transport.vehicle.buyer_info")),1)]),default:e(()=>[p("dl",O,[l(n,{label:a.$trans("transport.vehicle.props.is_sold")},{default:e(()=>[s(t(r.isSold?a.$trans("general.yes"):a.$trans("general.no")),1)]),_:1},8,["label"]),r.isSold?(b(),_(f,{key:0},[l(n,{label:a.$trans("transport.vehicle.props.buyer_name")},{default:e(()=>[s(t(r.buyerName),1)]),_:1},8,["label"]),l(n,{label:a.$trans("transport.vehicle.props.buyer_address")},{default:e(()=>[s(t(r.buyerAddress),1)]),_:1},8,["label"]),l(n,{label:a.$trans("transport.vehicle.props.buyer_phone")},{default:e(()=>[s(t(r.buyerPhone),1)]),_:1},8,["label"]),l(n,{label:a.$trans("transport.vehicle.props.buyer_email")},{default:e(()=>[s(t(r.buyerEmail),1)]),_:1},8,["label"]),l(n,{label:a.$trans("transport.vehicle.props.selling_price")},{default:e(()=>[s(t(r.sellingPrice.formatted),1)]),_:1},8,["label"]),l(n,{label:a.$trans("transport.vehicle.props.selling_date")},{default:e(()=>[s(t(r.sellingDate.formatted),1)]),_:1},8,["label"])],64)):m("",!0)])]),_:1}),p("dl",q,[l(n,{label:a.$trans("general.created_at")},{default:e(()=>[s(t(r.createdAt.formatted),1)]),_:1},8,["label"]),l(n,{label:a.$trans("general.updated_at")},{default:e(()=>[s(t(r.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):m("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{J as default};
