import{u as T,h as V,l as R,y as g,r as l,a as o,o as n,e as m,w as i,f as d,q as B,b as u,d as e,t,F as k,v as $,x as w,s as C}from"./app-DvIo72ZO.js";const F={class:"space-y-6"},L={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},O={class:"block text-sm font-medium text-gray-700 mb-1"},z={class:"text-gray-900"},D={class:"block text-sm font-medium text-gray-700 mb-1"},U={class:"text-gray-900"},G={class:"block text-sm font-medium text-gray-700 mb-1"},J={class:"text-gray-900"},K={class:"block text-sm font-medium text-gray-700 mb-1"},M={class:"text-gray-900"},W={class:"block text-sm font-medium text-gray-700 mb-1"},X={key:0},Y={key:1,class:"text-gray-500"},Z={key:0},ee={class:"block text-sm font-medium text-gray-700 mb-1"},te={class:"bg-gray-50 p-4 rounded-lg"},se={class:"text-gray-900 whitespace-pre-wrap"},ae={key:1},ne={class:"block text-sm font-medium text-gray-700 mb-1"},oe={class:"bg-gray-50 p-4 rounded-lg"},re={class:"text-gray-900 whitespace-pre-wrap"},ie={key:2},le={class:"block text-sm font-medium text-gray-700 mb-3"},de={class:"space-y-2"},ue={class:"flex-1"},ce={key:0,class:"text-green-600 text-sm font-medium"},me={class:"border-t pt-6"},_e={class:"grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600"},pe={class:"block font-medium mb-1"},be={class:"block font-medium mb-1"},ge={name:"ExamQuestionBankShow"},xe=Object.assign(ge,{setup(ke){const h=T(),x=V(),s=R({}),E="exam/questionBank/";let _=[];g("question-bank:edit")&&_.push("edit"),g("question-bank:create")&&_.push("duplicate"),g("question-bank:delete")&&_.push("delete");const S=a=>{Object.assign(s,a)};return(a,p)=>{const P=l("PageHeaderAction"),j=l("PageHeader"),A=l("BaseButton"),Q=l("ShowButton"),H=l("BaseCard"),I=l("ShowItem"),N=l("ParentTransition");return n(),o(k,null,[m(j,{title:a.$trans("exam.question_bank.question_bank"),navs:[{label:a.$trans("exam.exam"),path:"Exam"},{label:a.$trans("exam.question_bank.question_bank"),path:"ExamQuestionBankList"}]},{default:i(()=>[m(P,{name:"ExamQuestionBank",title:a.$trans("exam.question_bank.question_bank"),actions:["list"],"dropdown-actions":d(_),uuid:d(h).params.uuid},null,8,["title","dropdown-actions","uuid"])]),_:1},8,["title","navs"]),m(N,{appear:"",visibility:!0},{default:i(()=>[m(I,{"init-url":E,uuid:d(h).params.uuid,onSetItem:S,onRedirectTo:p[1]||(p[1]=b=>d(x).push({name:"ExamQuestionBank"}))},{default:i(()=>[s.uuid?(n(),B(H,{key:0},{title:i(()=>[C(t(s.title),1)]),footer:i(()=>[m(Q,null,{default:i(()=>[d(g)("question-bank:edit")?(n(),B(A,{key:0,design:"primary",onClick:p[0]||(p[0]=b=>d(x).push({name:"ExamQuestionBankEdit",params:{uuid:s.uuid}}))},{default:i(()=>[C(t(a.$trans("general.edit")),1)]),_:1})):u("",!0)]),_:1})]),default:i(()=>{var b,y,f,v,q;return[e("div",F,[e("div",L,[e("div",null,[e("label",O,t(a.$trans("exam.question_bank.props.title")),1),e("p",z,t(s.title),1)]),e("div",null,[e("label",D,t(a.$trans("exam.question_bank.props.type")),1),e("p",U,t((b=s.type)==null?void 0:b.label),1)]),e("div",null,[e("label",G,t(a.$trans("exam.question_bank.props.subject")),1),e("p",J,t((y=s.subject)==null?void 0:y.name),1)]),e("div",null,[e("label",K,t(a.$trans("exam.question_bank.props.mark")),1),e("p",M,t(s.mark),1)]),e("div",null,[e("label",W,t(a.$trans("exam.question_bank.props.batches")),1),((f=s.batches)==null?void 0:f.length)>0?(n(),o("div",X,[(n(!0),o(k,null,$(s.batches,r=>{var c;return n(),o("span",{key:r.uuid,class:"inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded mr-1 mb-1"},t((c=r.course)==null?void 0:c.name)+" - "+t(r.name),1)}),128))])):(n(),o("p",Y,"-"))])]),s.header?(n(),o("div",Z,[e("label",ee,t(a.$trans("exam.question_bank.props.header")),1),e("div",te,[e("p",se,t(s.header),1)])])):u("",!0),s.description?(n(),o("div",ae,[e("label",ne,t(a.$trans("exam.question_bank.props.description")),1),e("div",oe,[e("p",re,t(s.description),1)])])):u("",!0),((v=s.type)==null?void 0:v.value)==="mcq"&&((q=s.options)==null?void 0:q.length)>0?(n(),o("div",ie,[e("label",le,t(a.$trans("exam.question_bank.props.options")),1),e("div",de,[(n(!0),o(k,null,$(s.options,(r,c)=>(n(),o("div",{key:c,class:w(["flex items-center p-3 border rounded-lg",{"bg-green-50 border-green-200":r.isCorrect,"bg-gray-50":!r.isCorrect}])},[e("span",{class:w(["w-6 h-6 rounded-full border-2 flex items-center justify-center mr-3 text-sm font-medium",{"bg-green-500 border-green-500 text-white":r.isCorrect,"border-gray-300":!r.isCorrect}])},t(String.fromCharCode(65+c)),3),e("span",ue,t(r.title),1),r.isCorrect?(n(),o("span",ce,t(a.$trans("exam.question_bank.correct_answer")),1)):u("",!0)],2))),128))])])):u("",!0),e("div",me,[e("div",_e,[e("div",null,[e("label",pe,t(a.$trans("general.created_at")),1),e("p",null,t(s.createdAt.formatted),1)]),e("div",null,[e("label",be,t(a.$trans("general.updated_at")),1),e("p",null,t(s.updatedAt.formatted),1)])])])])]}),_:1})):u("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{xe as default};
