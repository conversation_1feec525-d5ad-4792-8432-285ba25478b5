import{i as D,u as H,h as M,l as N,r as i,a as r,o,e as n,w as e,f as p,q as _,b as g,d as $,s as u,t as l,F as c,v as y}from"./app-DvIo72ZO.js";const R={class:"space-y-2"},F={class:"flex justify-center gap-2"},O={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},q={name:"CommunicationEmailShow"},G=Object.assign(q,{setup(U){D();const f=H(),C=M(),V={},w="communication/email/",t=N({...V}),L=a=>{Object.assign(t,a)},h=a=>t.audiences.filter(m=>m.type===a);return(a,m)=>{const B=i("PageHeaderAction"),T=i("PageHeader"),d=i("ListItemView"),k=i("TextMuted"),A=i("ListContainerVertical"),v=i("BaseCard"),b=i("BaseDataView"),S=i("ListMedia"),j=i("ShowButton"),E=i("DetailLayoutVertical"),I=i("ShowItem"),P=i("ParentTransition");return o(),r(c,null,[n(T,{title:a.$trans(p(f).meta.trans,{attribute:a.$trans(p(f).meta.label)}),navs:[{label:a.$trans("communication.communication"),path:"Communication"},{label:a.$trans("communication.email.email"),path:"CommunicationEmail"}]},{default:e(()=>[n(B,{name:"CommunicationEmail",title:a.$trans("communication.email.email"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(P,{appear:"",visibility:!0},{default:e(()=>[n(I,{"init-url":w,uuid:p(f).params.uuid,"module-uuid":p(f).params.muuid,onSetItem:L,onRedirectTo:m[0]||(m[0]=s=>p(C).push({name:"CommunicationEmail",params:{uuid:t.uuid}}))},{default:e(()=>[t.uuid?(o(),_(E,{key:0},{detail:e(()=>[$("div",R,[n(v,{"no-padding":"","no-content-padding":""},{title:e(()=>[u(" #"+l(t.subjectExcerpt),1)]),action:e(()=>m[1]||(m[1]=[])),default:e(()=>[n(A,null,{default:e(()=>[n(d,{label:a.$trans("communication.email.props.subject")},{default:e(()=>[u(l(t.subject),1)]),_:1},8,["label"]),n(d,{label:a.$trans("communication.email.props.audience")},{default:e(()=>[u(l(t.studentAudienceType.label)+" ",1),(o(!0),r(c,null,y(h("student"),s=>(o(),_(k,{block:""},{default:e(()=>[u(l(s.name),1)]),_:2},1024))),256))]),_:1},8,["label"]),n(d,{label:a.$trans("communication.email.props.audience")},{default:e(()=>[t.employeeAudienceType.value?(o(),r(c,{key:0},[u(l(t.employeeAudienceType.label)+" ",1),(o(!0),r(c,null,y(h("employee"),s=>(o(),_(k,{block:""},{default:e(()=>[u(l(s.name),1)]),_:2},1024))),256))],64)):(o(),r(c,{key:1},[u("-")],64))]),_:1},8,["label"]),n(d,{label:a.$trans("general.created_at")},{default:e(()=>[u(l(t.createdAt.formatted),1)]),_:1},8,["label"]),n(d,{label:a.$trans("general.updated_at")},{default:e(()=>[u(l(t.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})])]),default:e(()=>[t.uuid?(o(),_(v,{key:0},{title:e(()=>[$("div",F,l(t.subject),1)]),footer:e(()=>[n(j)]),default:e(()=>[$("dl",O,[n(b,{class:"col-span-1 sm:col-span-2",html:""},{default:e(()=>[u(l(t.content),1)]),_:1}),n(b,{label:a.$trans("communication.email.props.inclusion")},{default:e(()=>[(o(!0),r(c,null,y(t.inclusionList,s=>(o(),r("div",{key:s},l(s),1))),128))]),_:1},8,["label"]),n(b,{label:a.$trans("communication.email.props.exclusion")},{default:e(()=>[(o(!0),r(c,null,y(t.exclusionList,s=>(o(),r("div",{key:s},l(s),1))),128))]),_:1},8,["label"]),t.media.length>0?(o(),_(b,{key:0,class:"col-span-1 sm:col-span-2"},{default:e(()=>[n(S,{media:t.media,url:`/app/communication/emails/${t.uuid}/`},null,8,["media","url"])]),_:1})):g("",!0)])]),_:1})):g("",!0)]),_:1})):g("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{G as default};
