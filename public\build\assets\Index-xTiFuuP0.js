import{u as A,l as D,n as H,r,q as v,o as b,w as e,d as w,b as F,s,t as a,e as n,h as P,j,m as E,f as c,a as O,F as U,v as q}from"./app-DvIo72ZO.js";const z={class:"grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={__name:"Filter",emits:["hide"],setup(B,{emit:g}){const f=A(),h=g,k={students:[],startDate:"",endDate:""},l=D({...k});D({});const m=D({students:[],isLoaded:!f.query.students});return H(async()=>{m.students=f.query.students?f.query.students.split(","):[],m.isLoaded=!0}),(p,i)=>{const u=r("BaseSelectSearch"),d=r("DatePicker"),y=r("FilterForm");return b(),v(y,{"init-form":k,form:l,multiple:["students"],onHide:i[3]||(i[3]=o=>h("hide"))},{default:e(()=>[w("div",z,[w("div",G,[m.isLoaded?(b(),v(u,{key:0,multiple:"",name:"students",label:p.$trans("global.select",{attribute:p.$trans("student.student")}),modelValue:l.students,"onUpdate:modelValue":i[0]||(i[0]=o=>l.students=o),"value-prop":"uuid","init-search":m.students,"search-key":"name","search-action":"student/summary"},{selectedOption:e(o=>[s(a(o.value.name)+" ("+a(o.value.codeNumber)+") ",1)]),listOption:e(o=>[s(a(o.option.name)+" ("+a(o.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):F("",!0)]),w("div",J,[n(d,{start:l.startDate,"onUpdate:start":i[1]||(i[1]=o=>l.startDate=o),end:l.endDate,"onUpdate:end":i[2]||(i[2]=o=>l.endDate=o),name:"dateBetween",as:"range",label:p.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},Q={name:"StudentLeaveRequestList"},X=Object.assign(Q,{setup(B){const g=P(),f=j("emitter");let h=["create","filter"],k=["print","pdf","excel"];const l="student/leaveRequest/",m=E(!1),p=D({}),i=u=>{Object.assign(p,u)};return(u,d)=>{const y=r("PageHeaderAction"),o=r("PageHeader"),L=r("ParentTransition"),$=r("TextMuted"),_=r("DataCell"),C=r("FloatingMenuItem"),N=r("FloatingMenu"),I=r("DataRow"),T=r("BaseButton"),M=r("DataTable"),V=r("ListItem");return b(),v(V,{"init-url":l,onSetItems:i},{header:e(()=>[n(o,{title:u.$trans("student.leave_request.leave_request"),navs:[{label:u.$trans("student.student"),path:"Student"}]},{default:e(()=>[n(y,{url:"student/leave-requests/",name:"StudentLeaveRequest",title:u.$trans("student.leave_request.leave_request"),actions:c(h),"dropdown-actions":c(k),onToggleFilter:d[0]||(d[0]=t=>m.value=!m.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[n(L,{appear:"",visibility:m.value},{default:e(()=>[n(K,{onRefresh:d[1]||(d[1]=t=>c(f).emit("listItems")),onHide:d[2]||(d[2]=t=>m.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[n(L,{appear:"",visibility:!0},{default:e(()=>[n(M,{header:p.headers,meta:p.meta,module:"student.leave_request",onRefresh:d[4]||(d[4]=t=>c(f).emit("listItems"))},{actionButton:e(()=>[n(T,{onClick:d[3]||(d[3]=t=>c(g).push({name:"StudentLeaveRequestCreate"}))},{default:e(()=>[s(a(u.$trans("global.add",{attribute:u.$trans("student.leave_request.leave_request")})),1)]),_:1})]),default:e(()=>[(b(!0),O(U,null,q(p.data,t=>(b(),v(I,{key:t.uuid,onDoubleClick:S=>c(g).push({name:"StudentLeaveRequestShow",params:{uuid:t.uuid}})},{default:e(()=>[n(_,{name:"name"},{default:e(()=>[s(a(t.student.name)+" ",1),n($,{block:""},{default:e(()=>[s(a(t.student.contactNumber),1)]),_:2},1024)]),_:2},1024),n(_,{name:"parent"},{default:e(()=>[s(a(t.student.fatherName)+" ",1),n($,{block:""},{default:e(()=>[s(a(t.student.motherName),1)]),_:2},1024)]),_:2},1024),n(_,{name:"admissionDate"},{default:e(()=>[s(a(t.student.joiningDate.formatted)+" ",1),n($,{block:""},{default:e(()=>[s(a(t.student.codeNumber),1)]),_:2},1024)]),_:2},1024),n(_,{name:"course"},{default:e(()=>[s(a(t.student.courseName)+" ",1),n($,{block:""},{default:e(()=>[s(a(t.student.batchName),1)]),_:2},1024)]),_:2},1024),n(_,{name:"period"},{default:e(()=>[s(a(t.period),1)]),_:2},1024),n(_,{name:"category"},{default:e(()=>[s(a(t.category.name),1)]),_:2},1024),n(_,{name:"createdAt"},{default:e(()=>[s(a(t.createdAt.formatted),1)]),_:2},1024),n(_,{name:"action"},{default:e(()=>[n(N,null,{default:e(()=>[n(C,{icon:"fas fa-arrow-circle-right",onClick:S=>c(g).push({name:"StudentLeaveRequestShow",params:{uuid:t.uuid}})},{default:e(()=>[s(a(u.$trans("general.show")),1)]),_:2},1032,["onClick"]),t.isEditable?(b(),v(C,{key:0,icon:"fas fa-edit",onClick:S=>c(g).push({name:"StudentLeaveRequestEdit",params:{uuid:t.uuid}})},{default:e(()=>[s(a(u.$trans("general.edit")),1)]),_:2},1032,["onClick"])):F("",!0),t.isEditable?(b(),v(C,{key:1,icon:"fas fa-trash",onClick:S=>c(f).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[s(a(u.$trans("general.delete")),1)]),_:2},1032,["onClick"])):F("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{X as default};
