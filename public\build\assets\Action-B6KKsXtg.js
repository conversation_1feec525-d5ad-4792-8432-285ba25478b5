import{u as w,H as h,I as j,l as $,r as d,q as C,o as g,w as b,d as r,a as x,b as f,e as l,f as a,F,s as v,t as p,K as N}from"./app-DvIo72ZO.js";const A={class:"grid grid-cols-4 gap-6"},H={class:"col-span-4 sm:col-span-3"},q={class:"col-span-4 sm:col-span-1"},Q={class:"col-span-4"},L={class:"col-span-4 sm:col-span-1"},W={class:"col-span-4 sm:col-span-1"},K={class:"col-span-4 sm:col-span-1"},z={class:"col-span-4 sm:col-span-1"},G={class:"col-span-4 sm:col-span-1"},J={class:"col-span-4 sm:col-span-1"},X={class:"col-span-4 sm:col-span-1"},Y={class:"col-span-4 sm:col-span-1"},Z={class:"col-span-4 sm:col-span-1"},ee={class:"col-span-4"},oe={class:"mt-4 grid grid-cols-4 gap-6"},ne={class:"col-span-4 sm:col-span-1"},te={class:"col-span-4 sm:col-span-1"},re={class:"mt-4 grid grid-cols-4 gap-6"},ae={class:"col-span-4 sm:col-span-1"},ie={class:"col-span-4 sm:col-span-1"},le={key:0,class:"col-span-4 sm:col-span-1"},se={class:"col-span-4"},de={class:"col-span-4"},me={class:"mt-6"},ue={class:"border-b border-gray-200 dark:border-gray-700 pb-2 mb-6"},pe={class:"text-lg font-semibold text-gray-900 dark:text-white"},ce={class:"mt-1 text-sm text-gray-600 dark:text-gray-400"},ge={class:"space-y-6"},be={class:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4"},fe={key:0,class:"space-y-6"},xe={class:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"},Ve={class:"text-lg font-semibold text-gray-900 dark:text-white mb-4"},ve={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ye={class:"space-y-3"},_e={key:0,class:"ml-6 space-y-3"},Te={class:"space-y-3"},ke={key:0,class:"ml-6 space-y-3"},Ue={class:"space-y-3"},$e={key:0,class:"ml-6 space-y-3"},Fe={class:"space-y-3"},Pe={class:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"},he={class:"text-lg font-semibold text-gray-900 dark:text-white mb-4"},Ce={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},we={class:"space-y-3"},Me={class:"space-y-3"},De={class:"space-y-3"},Be={class:"space-y-3"},Se={class:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6"},Ee={class:"text-lg font-semibold text-gray-900 dark:text-white mb-4"},Re={class:"space-y-4"},Ie={class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},Oe={class:"mt-4 grid grid-cols-1"},je={class:"col"},Ne={name:"ExamOnlineExamForm"},Ae=Object.assign(Ne,{setup(M){const y=w(),m={title:"",type:"",isFlexibleTiming:!1,date:"",startTime:"",endDate:"",endTime:"",durationMinutes:null,expiryDate:"",expiryTime:"",autoPublishResultsForFlexibleTiming:!0,batches:[],subject:"",passPercentage:0,hasNegativeMarking:!1,negativeMarkPercentPerQuestion:0,instructions:"",description:"",enableProctoring:!1,proctorConfig:{webcamMonitoring:!0,microphoneMonitoring:!0,screenRecording:!0,fullscreenEnforcement:!0,copyPasteBlocking:!0,faceDetection:!0,captureIntervalSeconds:30,audioThresholdDb:-40,maxFaceDetectionFailures:5,allowTabSwitching:!1,autoSubmitOnViolations:!1,customInstructions:""},media:[],mediaUpdated:!1,mediaToken:h(),mediaHash:[]},U="exam/onlineExam/",i=j(U),_=$({types:[],subjects:[]}),n=$({...m}),k=$({batches:[],subject:"",isLoaded:!y.params.uuid}),D=t=>{Object.assign(_,t)},B=()=>{n.mediaToken=h(),n.mediaHash=[]},S=t=>{var c,T,s,u;let e=t.records.map(V=>V.batch.uuid)||[];Object.assign(m,{...t,type:t.type.value,isFlexibleTiming:!!t.isFlexibleTiming,date:t.date.value,startTime:t.startTime.at,endDate:t.endDate.value,endTime:t.endTime.at,durationMinutes:t.durationMinutes,expiryDate:((c=t.expiryDate)==null?void 0:c.value)||"",expiryTime:((T=t.expiryTime)==null?void 0:T.at)||"",autoPublishResultsForFlexibleTiming:t.autoPublishResultsForFlexibleTiming??!0,passPercentage:t.passPercentage.value,batches:e,subject:((u=(s=t.records[0])==null?void 0:s.subject)==null?void 0:u.uuid)||"",enableProctoring:!!t.enableProctoring,proctorConfig:{...m.proctorConfig,allowTabSwitching:!!t.proctorConfig.allowTabSwitching,audioThresholdDb:Number(t.proctorConfig.audioThresholdDb),autoSubmitOnViolations:!!t.proctorConfig.autoSubmitOnViolations,captureIntervalSeconds:Number(t.proctorConfig.captureIntervalSeconds),copyPasteBlocking:!!t.proctorConfig.copyPasteBlocking,customInstructions:t.proctorConfig.customInstructions,faceDetection:!!t.proctorConfig.faceDetection,fullscreenEnforcement:!!t.proctorConfig.fullscreenEnforcement,maxFaceDetectionFailures:Number(t.proctorConfig.maxFaceDetectionFailures),microphoneMonitoring:!!t.proctorConfig.microphoneMonitoring,screenRecording:!!t.proctorConfig.screenRecording,webcamMonitoring:!!t.proctorConfig.webcamMonitoring}}),Object.assign(n,N(m)),k.batches=e,k.isLoaded=!0};return(t,e)=>{const c=d("BaseInput"),T=d("BaseSelect"),s=d("BaseSwitch"),u=d("DatePicker"),V=d("HelperText"),E=d("BaseSelectSearch"),P=d("BaseEditor"),R=d("BaseTextarea"),I=d("MediaUpload"),O=d("FormAction");return g(),C(O,{"pre-requisites":!0,onSetPreRequisites:D,"init-url":U,"init-form":m,form:n,"set-form":S,redirect:"ExamOnlineExam",onResetMediaFiles:B},{default:b(()=>[r("div",A,[r("div",H,[l(c,{type:"text",modelValue:n.title,"onUpdate:modelValue":e[0]||(e[0]=o=>n.title=o),name:"title",label:t.$trans("exam.online_exam.props.title"),error:a(i).title,"onUpdate:error":e[1]||(e[1]=o=>a(i).title=o)},null,8,["modelValue","label","error"])]),r("div",q,[l(T,{modelValue:n.type,"onUpdate:modelValue":e[2]||(e[2]=o=>n.type=o),name:"type",label:t.$trans("exam.online_exam.props.type"),options:_.types,error:a(i).type,"onUpdate:error":e[3]||(e[3]=o=>a(i).type=o)},null,8,["modelValue","label","options","error"])]),r("div",Q,[l(s,{vertical:"",modelValue:n.isFlexibleTiming,"onUpdate:modelValue":e[4]||(e[4]=o=>n.isFlexibleTiming=o),name:"isFlexibleTiming",label:t.$trans("exam.online_exam.flexible_timing"),description:t.$trans("exam.online_exam.flexible_timing_help"),error:a(i).isFlexibleTiming,"onUpdate:error":e[5]||(e[5]=o=>a(i).isFlexibleTiming=o)},null,8,["modelValue","label","description","error"])]),n.isFlexibleTiming?f("",!0):(g(),x(F,{key:0},[r("div",L,[l(u,{modelValue:n.date,"onUpdate:modelValue":e[6]||(e[6]=o=>n.date=o),name:"date",label:t.$trans("exam.online_exam.props.date"),"no-clear":"",error:a(i).date,"onUpdate:error":e[7]||(e[7]=o=>a(i).date=o)},null,8,["modelValue","label","error"])]),r("div",W,[l(u,{modelValue:n.startTime,"onUpdate:modelValue":e[8]||(e[8]=o=>n.startTime=o),name:"startTime",label:t.$trans("exam.online_exam.props.start_time"),as:"time",error:a(i).startTime,"onUpdate:error":e[9]||(e[9]=o=>a(i).startTime=o)},null,8,["modelValue","label","error"])]),r("div",K,[l(u,{modelValue:n.endDate,"onUpdate:modelValue":e[10]||(e[10]=o=>n.endDate=o),name:"endDate",label:t.$trans("exam.online_exam.props.end_date"),"no-clear":"",error:a(i).endDate,"onUpdate:error":e[11]||(e[11]=o=>a(i).endDate=o)},null,8,["modelValue","label","error"]),l(V,null,{default:b(()=>[v(p(t.$trans("exam.online_exam.end_date_info")),1)]),_:1})]),r("div",z,[l(u,{modelValue:n.endTime,"onUpdate:modelValue":e[12]||(e[12]=o=>n.endTime=o),name:"endTime",label:t.$trans("exam.online_exam.props.end_time"),as:"time",error:a(i).endTime,"onUpdate:error":e[13]||(e[13]=o=>a(i).endTime=o)},null,8,["modelValue","label","error"])])],64)),n.isFlexibleTiming?(g(),x(F,{key:1},[r("div",G,[l(u,{modelValue:n.date,"onUpdate:modelValue":e[14]||(e[14]=o=>n.date=o),name:"date",label:t.$trans("exam.online_exam.props.date"),"no-clear":"",error:a(i).date,"onUpdate:error":e[15]||(e[15]=o=>a(i).date=o)},null,8,["modelValue","label","error"]),l(V,null,{default:b(()=>e[56]||(e[56]=[v("Start Date")])),_:1})]),r("div",J,[l(u,{modelValue:n.startTime,"onUpdate:modelValue":e[16]||(e[16]=o=>n.startTime=o),name:"startTime",label:t.$trans("exam.online_exam.props.start_time"),as:"time",error:a(i).startTime,"onUpdate:error":e[17]||(e[17]=o=>a(i).startTime=o)},null,8,["modelValue","label","error"])]),r("div",X,[l(c,{type:"number",modelValue:n.durationMinutes,"onUpdate:modelValue":e[18]||(e[18]=o=>n.durationMinutes=o),name:"durationMinutes",label:t.$trans("exam.online_exam.duration_minutes"),error:a(i).durationMinutes,"onUpdate:error":e[19]||(e[19]=o=>a(i).durationMinutes=o),min:"1",max:"480"},null,8,["modelValue","label","error"]),l(V,null,{default:b(()=>e[57]||(e[57]=[v("Maximum Duration In Minutes")])),_:1})]),r("div",Y,[l(u,{modelValue:n.expiryDate,"onUpdate:modelValue":e[20]||(e[20]=o=>n.expiryDate=o),name:"expiryDate",label:t.$trans("exam.online_exam.expiry_date"),error:a(i).expiryDate,"onUpdate:error":e[21]||(e[21]=o=>a(i).expiryDate=o)},null,8,["modelValue","label","error"]),l(V,null,{default:b(()=>e[58]||(e[58]=[v("Optional: Leave blank for no expiry")])),_:1})]),r("div",Z,[l(u,{modelValue:n.expiryTime,"onUpdate:modelValue":e[22]||(e[22]=o=>n.expiryTime=o),name:"expiryTime",label:t.$trans("exam.online_exam.expiry_time"),as:"time",error:a(i).expiryTime,"onUpdate:error":e[23]||(e[23]=o=>a(i).expiryTime=o)},null,8,["modelValue","label","error"]),l(V,null,{default:b(()=>e[59]||(e[59]=[v("Optional: Defaults to 23:59:59")])),_:1})]),r("div",ee,[l(s,{vertical:"",modelValue:n.autoPublishResultsForFlexibleTiming,"onUpdate:modelValue":e[24]||(e[24]=o=>n.autoPublishResultsForFlexibleTiming=o),name:"autoPublishResultsForFlexibleTiming",label:t.$trans("exam.online_exam.auto_publish_results"),description:t.$trans("exam.online_exam.auto_publish_results_help"),error:a(i).autoPublishResultsForFlexibleTiming,"onUpdate:error":e[25]||(e[25]=o=>a(i).autoPublishResultsForFlexibleTiming=o)},null,8,["modelValue","label","description","error"])])],64)):f("",!0)]),r("div",oe,[r("div",ne,[k.isLoaded?(g(),C(E,{key:0,multiple:"",name:"batches",label:t.$trans("academic.batch.batch"),modelValue:n.batches,"onUpdate:modelValue":e[26]||(e[26]=o=>n.batches=o),error:a(i).batches,"onUpdate:error":e[27]||(e[27]=o=>a(i).batches=o),"value-prop":"uuid","init-search":k.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:b(o=>[v(p(o.value.course.name)+" - "+p(o.value.name),1)]),listOption:b(o=>[v(p(o.option.course.nameWithTerm)+" - "+p(o.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):f("",!0)]),r("div",te,[l(T,{modelValue:n.subject,"onUpdate:modelValue":e[28]||(e[28]=o=>n.subject=o),name:"subject",label:t.$trans("academic.subject.subject"),"label-prop":"name","value-prop":"uuid",options:_.subjects,error:a(i).subject,"onUpdate:error":e[29]||(e[29]=o=>a(i).subject=o)},null,8,["modelValue","label","options","error"])])]),r("div",re,[r("div",ae,[l(c,{type:"number",modelValue:n.passPercentage,"onUpdate:modelValue":e[30]||(e[30]=o=>n.passPercentage=o),name:"passPercentage",label:t.$trans("exam.online_exam.props.pass_percentage"),error:a(i).passPercentage,"onUpdate:error":e[31]||(e[31]=o=>a(i).passPercentage=o)},null,8,["modelValue","label","error"])]),r("div",ie,[l(s,{vertical:"",modelValue:n.hasNegativeMarking,"onUpdate:modelValue":e[32]||(e[32]=o=>n.hasNegativeMarking=o),name:"hasNegativeMarking",label:t.$trans("exam.online_exam.props.has_negative_marking"),error:a(i).hasNegativeMarking,"onUpdate:error":e[33]||(e[33]=o=>a(i).hasNegativeMarking=o)},null,8,["modelValue","label","error"])]),n.hasNegativeMarking?(g(),x("div",le,[l(c,{type:"number",modelValue:n.negativeMarkPercentPerQuestion,"onUpdate:modelValue":e[34]||(e[34]=o=>n.negativeMarkPercentPerQuestion=o),name:"negativeMarkPercentPerQuestion",label:t.$trans("exam.online_exam.props.negative_mark_percent_per_question"),error:a(i).negativeMarkPercentPerQuestion,"onUpdate:error":e[35]||(e[35]=o=>a(i).negativeMarkPercentPerQuestion=o)},null,8,["modelValue","label","error"])])):f("",!0),r("div",se,[l(P,{modelValue:n.instructions,"onUpdate:modelValue":e[36]||(e[36]=o=>n.instructions=o),name:"instructions",edit:!!a(y).params.uuid,label:t.$trans("exam.online_exam.props.instructions"),error:a(i).instructions,"onUpdate:error":e[37]||(e[37]=o=>a(i).instructions=o)},null,8,["modelValue","edit","label","error"])]),r("div",de,[l(P,{modelValue:n.description,"onUpdate:modelValue":e[38]||(e[38]=o=>n.description=o),name:"description",edit:!!a(y).params.uuid,label:t.$trans("exam.online_exam.props.description"),error:a(i).description,"onUpdate:error":e[39]||(e[39]=o=>a(i).description=o)},null,8,["modelValue","edit","label","error"])])]),r("div",me,[r("div",ue,[r("h3",pe,p(t.$trans("exam.proctoring.config.title")||"Proctoring Configuration"),1),r("p",ce,p(t.$trans("exam.proctoring.config.description")||"Configure monitoring and security features for this exam"),1)]),r("div",ge,[r("div",be,[l(s,{vertical:"",modelValue:n.enableProctoring,"onUpdate:modelValue":e[40]||(e[40]=o=>n.enableProctoring=o),name:"enableProctoring",label:t.$trans("exam.proctoring.config.enable_proctoring")||"Enable Proctoring",description:t.$trans("exam.proctoring.config.enable_proctoring_help")||"Enable comprehensive monitoring and security features for this exam",error:a(i).enableProctoring,"onUpdate:error":e[41]||(e[41]=o=>a(i).enableProctoring=o)},null,8,["modelValue","label","description","error"])]),n.enableProctoring?(g(),x("div",fe,[r("div",xe,[r("h3",Ve,p(t.$trans("exam.proctoring.config.monitoring_features")||"Monitoring Features"),1),r("div",ve,[r("div",ye,[l(s,{modelValue:n.proctorConfig.webcamMonitoring,"onUpdate:modelValue":e[42]||(e[42]=o=>n.proctorConfig.webcamMonitoring=o),name:"webcamMonitoring",label:t.$trans("exam.proctoring.config.webcam_monitoring")||"Webcam Monitoring",description:t.$trans("exam.proctoring.config.webcam_monitoring_help")||"Capture periodic images from student webcam"},null,8,["modelValue","label","description"]),n.proctorConfig.webcamMonitoring?(g(),x("div",_e,[l(c,{type:"number",modelValue:n.proctorConfig.captureIntervalSeconds,"onUpdate:modelValue":e[43]||(e[43]=o=>n.proctorConfig.captureIntervalSeconds=o),name:"captureInterval",label:t.$trans("exam.proctoring.config.capture_interval")||"Capture Interval (seconds)",min:"10",max:"300",class:"max-w-xs"},null,8,["modelValue","label"])])):f("",!0)]),r("div",Te,[l(s,{modelValue:n.proctorConfig.microphoneMonitoring,"onUpdate:modelValue":e[44]||(e[44]=o=>n.proctorConfig.microphoneMonitoring=o),name:"microphoneMonitoring",label:t.$trans("exam.proctoring.config.microphone_monitoring")||"Microphone Monitoring",description:t.$trans("exam.proctoring.config.microphone_monitoring_help")||"Monitor audio levels and detect external voices"},null,8,["modelValue","label","description"]),n.proctorConfig.microphoneMonitoring?(g(),x("div",ke,[l(c,{type:"number",modelValue:n.proctorConfig.audioThresholdDb,"onUpdate:modelValue":e[45]||(e[45]=o=>n.proctorConfig.audioThresholdDb=o),name:"audioThreshold",label:t.$trans("exam.proctoring.config.audio_threshold")||"Audio Threshold (dB)",min:"-80",max:"0",class:"max-w-xs"},null,8,["modelValue","label"])])):f("",!0)]),r("div",Ue,[l(s,{modelValue:n.proctorConfig.faceDetection,"onUpdate:modelValue":e[46]||(e[46]=o=>n.proctorConfig.faceDetection=o),name:"faceDetection",label:t.$trans("exam.proctoring.config.face_detection")||"Face Detection",description:t.$trans("exam.proctoring.config.face_detection_help")||"AI-powered face detection and identity verification"},null,8,["modelValue","label","description"]),n.proctorConfig.faceDetection?(g(),x("div",$e,[l(c,{type:"number",modelValue:n.proctorConfig.maxFaceDetectionFailures,"onUpdate:modelValue":e[47]||(e[47]=o=>n.proctorConfig.maxFaceDetectionFailures=o),name:"maxFaceFailures",label:t.$trans("exam.proctoring.config.max_face_failures")||"Max Face Detection Failures",min:"1",max:"20",class:"max-w-xs"},null,8,["modelValue","label"])])):f("",!0)]),r("div",Fe,[l(s,{modelValue:n.proctorConfig.screenRecording,"onUpdate:modelValue":e[48]||(e[48]=o=>n.proctorConfig.screenRecording=o),name:"screenRecording",label:t.$trans("exam.proctoring.config.screen_recording")||"Screen Recording",description:t.$trans("exam.proctoring.config.screen_recording_help")||"Record screen activity and detect tab switching"},null,8,["modelValue","label","description"])])])]),r("div",Pe,[r("h3",he,p(t.$trans("exam.proctoring.config.security_features")||"Security Features"),1),r("div",Ce,[r("div",we,[l(s,{modelValue:n.proctorConfig.fullscreenEnforcement,"onUpdate:modelValue":e[49]||(e[49]=o=>n.proctorConfig.fullscreenEnforcement=o),name:"fullscreenEnforcement",label:t.$trans("exam.proctoring.config.fullscreen_enforcement")||"Fullscreen Enforcement",description:t.$trans("exam.proctoring.config.fullscreen_enforcement_help")||"Force exam to run in fullscreen mode"},null,8,["modelValue","label","description"])]),r("div",Me,[l(s,{modelValue:n.proctorConfig.copyPasteBlocking,"onUpdate:modelValue":e[50]||(e[50]=o=>n.proctorConfig.copyPasteBlocking=o),name:"copyPasteBlocking",label:t.$trans("exam.proctoring.config.copy_paste_blocking")||"Copy-Paste Blocking",description:t.$trans("exam.proctoring.config.copy_paste_blocking_help")||"Block copy, cut, and paste operations"},null,8,["modelValue","label","description"])]),r("div",De,[l(s,{modelValue:n.proctorConfig.allowTabSwitching,"onUpdate:modelValue":e[51]||(e[51]=o=>n.proctorConfig.allowTabSwitching=o),name:"allowTabSwitching",label:t.$trans("exam.proctoring.config.allow_tab_switching")||"Allow Tab Switching",description:t.$trans("exam.proctoring.config.allow_tab_switching_help")||"Allow students to switch between browser tabs"},null,8,["modelValue","label","description"])]),r("div",Be,[l(s,{modelValue:n.proctorConfig.autoSubmitOnViolations,"onUpdate:modelValue":e[52]||(e[52]=o=>n.proctorConfig.autoSubmitOnViolations=o),name:"autoSubmitOnViolations",label:t.$trans("exam.proctoring.config.auto_submit_on_violations")||"Auto Submit on Violations",description:t.$trans("exam.proctoring.config.auto_submit_on_violations_help")||"Automatically submit exam when critical violations are detected"},null,8,["modelValue","label","description"])])])]),r("div",Se,[r("h3",Ee,p(t.$trans("exam.proctoring.config.advanced_settings")||"Advanced Settings"),1),r("div",Re,[r("div",null,[r("label",Ie,p(t.$trans("exam.proctoring.config.custom_instructions")||"Custom Proctoring Instructions"),1),l(R,{modelValue:n.proctorConfig.customInstructions,"onUpdate:modelValue":e[53]||(e[53]=o=>n.proctorConfig.customInstructions=o),name:"customInstructions",placeholder:t.$trans("exam.proctoring.config.custom_instructions_placeholder")||"Additional instructions for students regarding proctoring requirements...",rows:3},null,8,["modelValue","placeholder"])])])])])):f("",!0)])]),r("div",Oe,[r("div",je,[l(I,{multiple:"",label:t.$trans("general.file"),module:"online_exam",media:n.media,"media-token":n.mediaToken,onIsUpdated:e[54]||(e[54]=o=>n.mediaUpdated=!0),onSetHash:e[55]||(e[55]=o=>n.mediaHash.push(o))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),He={name:"ExamOnlineExamAction"},Qe=Object.assign(He,{setup(M){const y=w();return(m,U)=>{const i=d("PageHeaderAction"),_=d("PageHeader"),n=d("ParentTransition");return g(),x(F,null,[l(_,{title:m.$trans(a(y).meta.trans,{attribute:m.$trans(a(y).meta.label)}),navs:[{label:m.$trans("exam.exam"),path:"Exam"},{label:m.$trans("exam.online_exam.online_exam"),path:"ExamOnlineExamList"}]},{default:b(()=>[l(i,{name:"ExamOnlineExam",title:m.$trans("exam.online_exam.online_exam"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(n,{appear:"",visibility:!0},{default:b(()=>[l(Ae)]),_:1})],64)}}});export{Qe as default};
