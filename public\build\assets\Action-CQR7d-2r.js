import{I as y,l as b,r as m,q as U,o as _,w as v,d as p,e as r,f as t,K as B,u as E,a as F,F as P}from"./app-DvIo72ZO.js";const T={class:"grid grid-cols-3 gap-6"},N={class:"col-span-3 sm:col-span-1"},j={class:"col-span-3 sm:col-span-1"},q={class:"col-span-3 sm:col-span-1"},A={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3"},H={name:"ExamTermForm"},R=Object.assign(H,{setup(f){const l={name:"",division:"",displayName:"",position:"",description:""},i="exam/term/",a=y(i),d=b({divisions:[]}),s=b({...l}),u=n=>{Object.assign(d,n)},V=n=>{var e;Object.assign(l,{...n,division:((e=n.division)==null?void 0:e.uuid)||""}),Object.assign(s,B(l))};return(n,e)=>{const c=m("BaseInput"),g=m("BaseSelect"),x=m("BaseTextarea"),$=m("FormAction");return _(),U($,{"pre-requisites":!0,onSetPreRequisites:u,"init-url":i,"init-form":l,form:s,setForm:V,redirect:"ExamTerm"},{default:v(()=>[p("div",T,[p("div",N,[r(c,{type:"text",modelValue:s.name,"onUpdate:modelValue":e[0]||(e[0]=o=>s.name=o),name:"name",label:n.$trans("exam.term.props.name"),error:t(a).name,"onUpdate:error":e[1]||(e[1]=o=>t(a).name=o),autofocus:""},null,8,["modelValue","label","error"])]),p("div",j,[r(g,{modelValue:s.division,"onUpdate:modelValue":e[2]||(e[2]=o=>s.division=o),name:"division",label:n.$trans("academic.division.division"),"label-prop":"nameWithProgram","value-prop":"uuid",options:d.divisions,error:t(a).division,"onUpdate:error":e[3]||(e[3]=o=>t(a).division=o)},null,8,["modelValue","label","options","error"])]),p("div",q,[r(c,{type:"text",modelValue:s.displayName,"onUpdate:modelValue":e[4]||(e[4]=o=>s.displayName=o),name:"displayName",label:n.$trans("exam.term.props.display_name"),error:t(a).displayName,"onUpdate:error":e[5]||(e[5]=o=>t(a).displayName=o)},null,8,["modelValue","label","error"])]),p("div",A,[r(c,{type:"text",modelValue:s.position,"onUpdate:modelValue":e[6]||(e[6]=o=>s.position=o),name:"position",label:n.$trans("exam.term.props.position"),error:t(a).position,"onUpdate:error":e[7]||(e[7]=o=>t(a).position=o)},null,8,["modelValue","label","error"])]),p("div",O,[r(x,{modelValue:s.description,"onUpdate:modelValue":e[8]||(e[8]=o=>s.description=o),name:"description",label:n.$trans("exam.term.props.description"),error:t(a).description,"onUpdate:error":e[9]||(e[9]=o=>t(a).description=o)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),k={name:"ExamTermAction"},S=Object.assign(k,{setup(f){const l=E();return(i,a)=>{const d=m("PageHeaderAction"),s=m("PageHeader"),u=m("ParentTransition");return _(),F(P,null,[r(s,{title:i.$trans(t(l).meta.trans,{attribute:i.$trans(t(l).meta.label)}),navs:[{label:i.$trans("exam.exam"),path:"Exam"},{label:i.$trans("exam.term.term"),path:"ExamTermList"}]},{default:v(()=>[r(d,{name:"ExamTerm",title:i.$trans("exam.term.term"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),r(u,{appear:"",visibility:!0},{default:v(()=>[r(R)]),_:1})],64)}}});export{S as default};
