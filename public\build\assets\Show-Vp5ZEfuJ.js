import{u as k,h as v,l as S,r as o,a as C,o as p,e as t,w as a,f as r,q as P,b as V,d as y,s as n,t as l,F as I}from"./app-DvIo72ZO.js";const A={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},H={name:"MessMealLogShow"},D=Object.assign(H,{setup(N){const i=k(),d=v(),c={},g="mess/mealLog/",s=S({...c}),_=e=>{Object.assign(s,e)};return(e,m)=>{const f=o("PageHeaderAction"),b=o("PageHeader"),u=o("BaseDataView"),B=o("BaseButton"),$=o("ShowButton"),M=o("BaseCard"),h=o("ShowItem"),w=o("ParentTransition");return p(),C(I,null,[t(b,{title:e.$trans(r(i).meta.trans,{attribute:e.$trans(r(i).meta.label)}),navs:[{label:e.$trans("mess.mess"),path:"Mess"},{label:e.$trans("mess.meal.log.log"),path:"MessMealLog"}]},{default:a(()=>[t(f,{name:"MessMealLog",title:e.$trans("mess.meal.log.log"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(w,{appear:"",visibility:!0},{default:a(()=>[t(h,{"init-url":g,uuid:r(i).params.uuid,"module-uuid":r(i).params.muuid,onSetItem:_,onRedirectTo:m[1]||(m[1]=L=>r(d).push({name:"MessMealLog",params:{uuid:s.uuid}}))},{default:a(()=>[s.uuid?(p(),P(M,{key:0},{title:a(()=>[n(l(s.meal.name),1)]),footer:a(()=>[t($,null,{default:a(()=>[t(B,{design:"primary",onClick:m[0]||(m[0]=L=>r(d).push({name:"MessMealLogEdit",params:{uuid:s.uuid}}))},{default:a(()=>[n(l(e.$trans("general.edit")),1)]),_:1})]),_:1})]),default:a(()=>[y("dl",A,[t(u,{label:e.$trans("mess.menu.item")},{default:a(()=>[n(l(s.menuItems),1)]),_:1},8,["label"]),t(u,{class:"col-span-1 sm:col-span-2",label:e.$trans("mess.meal.log.props.description")},{default:a(()=>[n(l(s.description),1)]),_:1},8,["label"]),t(u,{class:"col-span-1 sm:col-span-2",label:e.$trans("mess.meal.log.props.remarks")},{default:a(()=>[n(l(s.remarks),1)]),_:1},8,["label"]),t(u,{label:e.$trans("general.created_at")},{default:a(()=>[n(l(s.createdAt.formatted),1)]),_:1},8,["label"]),t(u,{label:e.$trans("general.updated_at")},{default:a(()=>[n(l(s.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):V("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{D as default};
