import{u as N,l as V,n as j,r as s,q as h,o as c,w as e,d as y,e as t,h as S,j as U,y as g,m as O,f as o,a as E,F as q,v as z,s as l,t as d,b as D}from"./app-DvIo72ZO.js";import{_ as G}from"./ModuleDropdown-KgrksjVf.js";const J={class:"grid grid-cols-3 gap-6"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},W={__name:"Filter",emits:["hide"],setup(B,{emit:p}){N();const $=p,k={title:"",startDate:"",endDate:""},u=V({...k}),C=V({isLoaded:!0});return j(async()=>{C.isLoaded=!0}),(f,r)=>{const T=s("BaseInput"),n=s("DatePicker"),i=s("FilterForm");return c(),h(i,{"init-form":k,form:u,multiple:[],onHide:r[3]||(r[3]=m=>$("hide"))},{default:e(()=>[y("div",J,[y("div",K,[t(T,{type:"text",modelValue:u.title,"onUpdate:modelValue":r[0]||(r[0]=m=>u.title=m),name:"title",label:f.$trans("transport.vehicle.document.props.title")},null,8,["modelValue","label"])]),y("div",Q,[t(n,{start:u.startDate,"onUpdate:start":r[1]||(r[1]=m=>u.startDate=m),end:u.endDate,"onUpdate:end":r[2]||(r[2]=m=>u.endDate=m),name:"dateBetween",as:"range",label:f.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},X={name:"TransportVehicleDocumentList"},x=Object.assign(X,{setup(B){const p=S(),$=U("emitter");let k=["filter"];g("vehicle-document:create")&&k.unshift("create");let u=[];g("vehicle-document:export")&&(u=["print","pdf","excel"]);const C="transport/vehicle/document/",f=O(!1),r=V({}),T=n=>{Object.assign(r,n)};return(n,i)=>{const m=s("PageHeaderAction"),I=s("PageHeader"),w=s("ParentTransition"),P=s("TextMuted"),_=s("DataCell"),b=s("FloatingMenuItem"),M=s("FloatingMenu"),A=s("DataRow"),H=s("BaseButton"),L=s("DataTable"),R=s("ListItem");return c(),h(R,{"init-url":C,onSetItems:T},{header:e(()=>[t(I,{title:n.$trans("transport.vehicle.document.document"),navs:[{label:n.$trans("transport.transport"),path:"Transport"},{label:n.$trans("transport.vehicle.vehicle"),path:"TransportVehicle"}]},{default:e(()=>[t(m,{url:"transport/vehicle/documents/",name:"TransportVehicleDocument",title:n.$trans("transport.vehicle.document.document"),actions:o(k),"dropdown-actions":o(u),onToggleFilter:i[0]||(i[0]=a=>f.value=!f.value)},{moduleOption:e(()=>[t(G)]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(w,{appear:"",visibility:f.value},{default:e(()=>[t(W,{onRefresh:i[1]||(i[1]=a=>o($).emit("listItems")),onHide:i[2]||(i[2]=a=>f.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(w,{appear:"",visibility:!0},{default:e(()=>[t(L,{header:r.headers,meta:r.meta,module:"transport.vehicle.document",onRefresh:i[4]||(i[4]=a=>o($).emit("listItems"))},{actionButton:e(()=>[o(g)("vehicle-document:create")?(c(),h(H,{key:0,onClick:i[3]||(i[3]=a=>o(p).push({name:"TransportVehicleDocumentCreate"}))},{default:e(()=>[l(d(n.$trans("global.add",{attribute:n.$trans("transport.vehicle.document.document")})),1)]),_:1})):D("",!0)]),default:e(()=>[(c(!0),E(q,null,z(r.data,a=>(c(),h(A,{key:a.uuid,onDoubleClick:v=>o(p).push({name:"TransportVehicleDocumentShow",params:{uuid:a.uuid}})},{default:e(()=>[t(_,{name:"vehicle"},{default:e(()=>{var v;return[l(d((v=a.vehicle)==null?void 0:v.name)+" ",1),t(P,{block:""},{default:e(()=>{var F;return[l(d((F=a.vehicle)==null?void 0:F.registrationNumber),1)]}),_:2},1024)]}),_:2},1024),t(_,{name:"title"},{default:e(()=>[l(d(a.title),1)]),_:2},1024),t(_,{name:"type"},{default:e(()=>[l(d(a.type.name),1)]),_:2},1024),t(_,{name:"startDate"},{default:e(()=>[l(d(a.startDate.formatted),1)]),_:2},1024),t(_,{name:"endDate"},{default:e(()=>[l(d(a.endDate.formatted),1)]),_:2},1024),t(_,{name:"createdAt"},{default:e(()=>[l(d(a.createdAt.formatted),1)]),_:2},1024),t(_,{name:"action"},{default:e(()=>[t(M,null,{default:e(()=>[t(b,{icon:"fas fa-arrow-circle-right",onClick:v=>o(p).push({name:"TransportVehicleDocumentShow",params:{uuid:a.uuid}})},{default:e(()=>[l(d(n.$trans("general.show")),1)]),_:2},1032,["onClick"]),o(g)("vehicle-document:edit")?(c(),h(b,{key:0,icon:"fas fa-edit",onClick:v=>o(p).push({name:"TransportVehicleDocumentEdit",params:{uuid:a.uuid}})},{default:e(()=>[l(d(n.$trans("general.edit")),1)]),_:2},1032,["onClick"])):D("",!0),o(g)("vehicle-document:create")?(c(),h(b,{key:1,icon:"fas fa-copy",onClick:v=>o(p).push({name:"TransportVehicleDocumentDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[l(d(n.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):D("",!0),o(g)("vehicle-document:delete")?(c(),h(b,{key:2,icon:"fas fa-trash",onClick:v=>o($).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[l(d(n.$trans("general.delete")),1)]),_:2},1032,["onClick"])):D("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{x as default};
