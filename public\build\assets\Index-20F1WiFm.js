import{l as A,r as l,q as V,o as R,w as e,d as g,e as n,h as j,j as H,m as T,f as i,a as U,F as L,v as M,s as u,t as d}from"./app-DvIo72ZO.js";const N={class:"grid grid-cols-3 gap-6"},O={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},G={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(w,{emit:m}){const c=m,_=w,v={name:"",number:"",floor:"",block:""},r=A({...v}),f=A({floors:_.preRequisites.floors,blocks:_.preRequisites.blocks});return(p,a)=>{const k=l("BaseInput"),$=l("BaseSelect"),o=l("FilterForm");return R(),V(o,{"init-form":v,form:r,onHide:a[4]||(a[4]=t=>c("hide"))},{default:e(()=>[g("div",N,[g("div",O,[n(k,{type:"text",modelValue:r.name,"onUpdate:modelValue":a[0]||(a[0]=t=>r.name=t),name:"name",label:p.$trans("asset.building.room.props.name")},null,8,["modelValue","label"])]),g("div",E,[n(k,{type:"text",modelValue:r.number,"onUpdate:modelValue":a[1]||(a[1]=t=>r.number=t),name:"number",label:p.$trans("asset.building.room.props.number")},null,8,["modelValue","label"])]),g("div",W,[n($,{modelValue:r.block,"onUpdate:modelValue":a[2]||(a[2]=t=>r.block=t),name:"block","label-prop":"name","value-prop":"uuid",label:p.$trans("asset.building.block.block"),options:f.blocks},null,8,["modelValue","label","options"])]),g("div",z,[n($,{modelValue:r.floor,"onUpdate:modelValue":a[3]||(a[3]=t=>r.floor=t),name:"floor","label-prop":"nameWithBlock","value-prop":"uuid",label:p.$trans("asset.building.floor.floor"),options:f.floors},null,8,["modelValue","label","options"])])])]),_:1},8,["form"])}}},J={name:"AssetBuildingRoomList"},Q=Object.assign(J,{setup(w){const m=j(),c=H("emitter");let _=["create","filter"],v=["print","pdf","excel"];const r="asset/building/room/",f=A({floors:[]}),p=T(!1),a=A({}),k=o=>{Object.assign(a,o)},$=o=>{Object.assign(f,o)};return(o,t)=>{const F=l("BaseButton"),I=l("PageHeaderAction"),q=l("PageHeader"),h=l("ParentTransition"),b=l("DataCell"),B=l("FloatingMenuItem"),D=l("FloatingMenu"),y=l("DataRow"),P=l("DataTable"),S=l("ListItem");return R(),V(S,{"init-url":r,"pre-requisites":!0,onSetPreRequisites:$,onSetItems:k},{header:e(()=>[n(q,{title:o.$trans("asset.building.room.room"),navs:[{label:o.$trans("asset.asset"),path:"Asset"},{label:o.$trans("asset.building.building"),path:"AssetBuilding"}]},{default:e(()=>[n(I,{url:"asset/building/rooms/",name:"AssetBuildingRoom",title:o.$trans("asset.building.room.room"),actions:i(_),"dropdown-actions":i(v),onToggleFilter:t[2]||(t[2]=s=>p.value=!p.value)},{default:e(()=>[n(F,{design:"white",onClick:t[0]||(t[0]=s=>i(m).push({name:"AssetBuildingBlock"}))},{default:e(()=>[u(d(o.$trans("asset.building.block.block")),1)]),_:1}),n(F,{design:"white",onClick:t[1]||(t[1]=s=>i(m).push({name:"AssetBuildingFloor"}))},{default:e(()=>[u(d(o.$trans("asset.building.floor.floor")),1)]),_:1})]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[n(h,{appear:"",visibility:p.value},{default:e(()=>[n(G,{onRefresh:t[3]||(t[3]=s=>i(c).emit("listItems")),"pre-requisites":f,onHide:t[4]||(t[4]=s=>p.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:e(()=>[n(h,{appear:"",visibility:!0},{default:e(()=>[n(P,{header:a.headers,meta:a.meta,module:"asset.building.room",onRefresh:t[6]||(t[6]=s=>i(c).emit("listItems"))},{actionButton:e(()=>[n(F,{onClick:t[5]||(t[5]=s=>i(m).push({name:"AssetBuildingRoomCreate"}))},{default:e(()=>[u(d(o.$trans("global.add",{attribute:o.$trans("asset.building.room.room")})),1)]),_:1})]),default:e(()=>[(R(!0),U(L,null,M(a.data,s=>(R(),V(y,{key:s.uuid,onDoubleClick:C=>i(m).push({name:"AssetBuildingRoomShow",params:{uuid:s.uuid}})},{default:e(()=>[n(b,{name:"name"},{default:e(()=>[u(d(s.name),1)]),_:2},1024),n(b,{name:"number"},{default:e(()=>[u(d(s.number),1)]),_:2},1024),n(b,{name:"floor"},{default:e(()=>[u(d(s.floorNameWithBlock),1)]),_:2},1024),n(b,{name:"createdAt"},{default:e(()=>[u(d(s.createdAt.formatted),1)]),_:2},1024),n(b,{name:"action"},{default:e(()=>[n(D,null,{default:e(()=>[n(B,{icon:"fas fa-arrow-circle-right",onClick:C=>i(m).push({name:"AssetBuildingRoomShow",params:{uuid:s.uuid}})},{default:e(()=>[u(d(o.$trans("general.show")),1)]),_:2},1032,["onClick"]),n(B,{icon:"fas fa-edit",onClick:C=>i(m).push({name:"AssetBuildingRoomEdit",params:{uuid:s.uuid}})},{default:e(()=>[u(d(o.$trans("general.edit")),1)]),_:2},1032,["onClick"]),n(B,{icon:"fas fa-copy",onClick:C=>i(m).push({name:"AssetBuildingRoomDuplicate",params:{uuid:s.uuid}})},{default:e(()=>[u(d(o.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),n(B,{icon:"fas fa-trash",onClick:C=>i(c).emit("deleteItem",{uuid:s.uuid})},{default:e(()=>[u(d(o.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Q as default};
