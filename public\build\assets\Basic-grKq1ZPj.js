import{i as A,I as H,m as R,l as S,L as U,n as G,r as p,q as d,o as m,w as t,a as k,b as y,F as V,v as q,s,t as c,f as a,y as O,K,u as M,j as P,g as F,e as r,d as x}from"./app-DvIo72ZO.js";import{_ as z}from"./EditRequestInfo-BHThp1eZ.js";const J={key:0,class:"space-x-1"},Q={name:"EmployeeTagList"},W=Object.assign(Q,{props:{employee:{type:Object,default(){return{}}}},emits:["refresh"],setup(e,{emit:_}){A();const o=_,C=e,g={tags:[]},E="employee/",L=H(E),f=R(!1),h=S({...g}),B=S({tags:[],isLoaded:!1}),v=i=>{Object.assign(g,{tags:i.map(u=>u.uuid)}),Object.assign(h,K(g)),B.tags=i.map(u=>u.uuid),B.isLoaded=!0},w=()=>{f.value=!1,o("refresh")};return U(()=>C.employee.tags,i=>{v(i)}),G(()=>{var i;v(((i=C.employee)==null?void 0:i.tags)||[])}),(i,u)=>{const D=p("BaseBadge"),$=p("BaseSelectSearch"),n=p("FormAction"),j=p("BaseDataView");return m(),d(j,{class:"col-span-1 sm:col-span-4"},{label:t(()=>[s(c(i.$trans("general.tags"))+" ",1),a(O)("employee:edit")?(m(),k("i",{key:0,class:"fas fa-edit cursor-pointer",onClick:u[0]||(u[0]=b=>f.value=!0)})):y("",!0)]),default:t(()=>[f.value?y("",!0):(m(),k("div",J,[(m(!0),k(V,null,q(e.employee.tags||[],b=>(m(),d(D,{design:"primary"},{default:t(()=>[s(c(b.name),1)]),_:2},1024))),256))])),f.value?(m(),d(n,{key:1,"no-card":"","no-data-fetch":"","cancel-action":"",action:"updateTags","keep-adding":!1,"init-url":E,"init-form":g,form:h,"after-submit":w,onCancelled:u[3]||(u[3]=b=>f.value=!1)},{default:t(()=>[B.isLoaded?(m(),d($,{key:0,tags:"",name:"tags",placeholder:i.$trans("global.select",{attribute:i.$trans("general.tag")}),modelValue:h.tags,"onUpdate:modelValue":u[1]||(u[1]=b=>h.tags=b),error:a(L).tags,"onUpdate:error":u[2]||(u[2]=b=>a(L).tags=b),"init-search":B.tags,"search-action":"tag/list"},null,8,["placeholder","modelValue","error","init-search"])):y("",!0)]),_:1},8,["form"])):y("",!0)]),_:1})}}}),X={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-3"},Y={class:"flex flex-wrap gap-2"},Z={name:"EmployeeShowBasic"},te=Object.assign(Z,{props:{employee:{type:Object,default(){return{}}}},setup(e){const _=M(),o=P("$trans"),C=P("emitter"),g=e,E=F("employee.uniqueIdNumber1Label"),L=F("employee.uniqueIdNumber2Label"),f=F("employee.uniqueIdNumber3Label"),h=F("contact.enableCategoryField"),B=F("contact.enableCasteField");let v=[];O("employee:edit")&&!g.employee.self&&(v.push({label:o("global.edit",{attribute:o("employee.employee")}),path:{name:"EmployeeEditBasic",params:{uuid:g.employee.uuid}}}),v.push({label:o("global.edit",{attribute:o("contact.props.photo")}),path:{name:"EmployeeEditPhoto",params:{uuid:g.employee.uuid}}}));const w=()=>{C.emit("employeeUpdated")};return(i,u)=>{const D=p("PageHeaderAction"),$=p("PageHeader"),n=p("BaseDataView"),j=p("BaseBadge"),b=p("BaseCard"),T=p("ParentTransition");return m(),k(V,null,[e.employee.uuid?(m(),d($,{key:0,title:a(o)(a(_).meta.label),navs:[{label:a(o)("employee.employee"),path:"Employee"},{label:e.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:e.employee.uuid}}}]},{default:t(()=>[r(D,{"additional-actions":a(v)},null,8,["additional-actions"])]),_:1},8,["title","navs"])):y("",!0),r(T,{appear:"",visibility:!0},{default:t(()=>[e.employee.uuid?(m(),d(b,{key:0},{default:t(()=>{var I;return[r(z,{employee:e.employee},null,8,["employee"]),x("dl",X,[r(n,{label:a(o)("employee.props.code_number")},{default:t(()=>[s(c(e.employee.codeNumber),1)]),_:1},8,["label"]),r(n,{label:a(o)("employee.props.joining_date")},{default:t(()=>[s(c(e.employee.joiningDate.formatted),1)]),_:1},8,["label"]),e.employee.leavingDate.value?(m(),d(n,{key:0,label:a(o)("employee.props.leaving_date")},{default:t(()=>[s(c(e.employee.leavingDate.formatted),1)]),_:1},8,["label"])):y("",!0),r(n,{label:a(o)("employee.department.department")},{default:t(()=>{var l,N;return[s(c((N=(l=e.employee.lastRecord)==null?void 0:l.department)==null?void 0:N.name),1)]}),_:1},8,["label"]),r(n,{label:a(o)("employee.designation.designation")},{default:t(()=>{var l,N;return[s(c((N=(l=e.employee.lastRecord)==null?void 0:l.designation)==null?void 0:N.name),1)]}),_:1},8,["label"]),r(n,{label:a(o)("contact.props.birth_date")},{default:t(()=>[s(c(e.employee.contact.birthDate.formatted),1)]),_:1},8,["label"]),r(n,{label:a(o)("contact.props.gender")},{default:t(()=>[s(c(e.employee.contact.gender.label),1)]),_:1},8,["label"]),r(n,{label:a(o)("contact.props.father_name")},{default:t(()=>[s(c(e.employee.contact.fatherName),1)]),_:1},8,["label"]),r(n,{label:a(o)("contact.props.mother_name")},{default:t(()=>[s(c(e.employee.contact.motherName),1)]),_:1},8,["label"]),r(n,{label:a(E)},{default:t(()=>[s(c(e.employee.contact.uniqueIdNumber1),1)]),_:1},8,["label"]),r(n,{label:a(L)},{default:t(()=>[s(c(e.employee.contact.uniqueIdNumber2),1)]),_:1},8,["label"]),r(n,{label:a(f)},{default:t(()=>[s(c(e.employee.contact.uniqueIdNumber3),1)]),_:1},8,["label"]),r(n,{label:a(o)("contact.props.birth_place")},{default:t(()=>[s(c(e.employee.contact.birthPlace),1)]),_:1},8,["label"]),r(n,{label:a(o)("contact.props.nationality")},{default:t(()=>[s(c(e.employee.contact.nationality),1)]),_:1},8,["label"]),r(n,{label:a(o)("contact.props.mother_tongue")},{default:t(()=>[s(c(e.employee.contact.motherTongue),1)]),_:1},8,["label"]),r(n,{label:a(o)("contact.props.blood_group")},{default:t(()=>{var l;return[s(c(((l=e.employee.contact.bloodGroup)==null?void 0:l.label)||"-"),1)]}),_:1},8,["label"]),r(n,{label:a(o)("contact.props.marital_status")},{default:t(()=>{var l;return[s(c(((l=e.employee.contact.maritalStatus)==null?void 0:l.label)||"-"),1)]}),_:1},8,["label"]),r(n,{label:a(o)("contact.religion.religion")},{default:t(()=>{var l;return[s(c(((l=e.employee.contact.religion)==null?void 0:l.name)||"-"),1)]}),_:1},8,["label"]),a(B)?(m(),d(n,{key:1,label:a(o)("contact.caste.caste")},{default:t(()=>{var l;return[s(c(((l=e.employee.contact.caste)==null?void 0:l.name)||"-"),1)]}),_:1},8,["label"])):y("",!0),a(h)?(m(),d(n,{key:2,label:a(o)("contact.category.category")},{default:t(()=>{var l;return[s(c(((l=e.employee.contact.category)==null?void 0:l.name)||"-"),1)]}),_:1},8,["label"])):y("",!0),r(n,{label:a(o)("employee.type")},{default:t(()=>{var l;return[s(c(((l=e.employee.type)==null?void 0:l.label)||"-"),1)]}),_:1},8,["label"]),(m(!0),k(V,null,q(((I=e.employee.contact)==null?void 0:I.customFields)||[],l=>(m(),d(n,{key:l.uuid,label:l.label},{default:t(()=>[s(c(l.formattedValue),1)]),_:2},1032,["label"]))),128)),r(W,{employee:e.employee,onRefresh:w},null,8,["employee"]),r(n,{label:a(o)("employee.group.group")},{default:t(()=>[x("div",Y,[(m(!0),k(V,null,q(e.employee.groups,l=>(m(),d(j,{design:"primary",key:l.uuid},{default:t(()=>[s(c(l.name),1)]),_:2},1024))),128))])]),_:1},8,["label"])])]}),_:1})):y("",!0)]),_:1})],64)}}});export{te as default};
