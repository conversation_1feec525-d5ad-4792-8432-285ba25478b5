import{I as Y,l as H,r as f,z as Z,q as A,o as u,w as F,b as k,d as r,a as d,s as O,t as w,e as c,f as i,F as G,v as L,x as N,A as E,aT as I,H as R,K as z,u as x}from"./app-DvIo72ZO.js";const ee={class:"grid grid-cols-3 gap-6"},ae={class:"col-span-3 sm:col-span-1"},se={class:"col-span-3 sm:col-span-2"},te={class:"col-span-1"},oe=["onClick"],re={class:"grid grid-cols-4 gap-2"},le={class:"col-span-4 sm:col-span-2 lg:col-span-1"},ne={class:"col-span-4 sm:col-span-2 lg:col-span-1"},ue={class:"col-span-4 sm:col-span-2 lg:col-span-1"},ie={class:"space-x-2"},pe=["onClick"],ce={key:0},de={key:1},me=["onClick"],fe={key:0},_e={key:1},be={key:2},Fe={class:"mt-4 grid grid-cols-4 gap-2"},$e={class:"col-span-4 sm:col-span-2 lg:col-span-1"},ge={key:0,class:"col-span-4 sm:col-span-2 lg:col-span-1"},Ve={class:"mt-4 grid grid-cols-4 gap-2"},ye={class:"col-span-4 sm:col-span-2 lg:col-span-1"},ve={class:"col-span-4 sm:col-span-2 lg:col-span-1"},ke={class:"col-span-4 sm:col-span-2 lg:col-span-1"},he={key:0,class:"mt-2 flex justify-end"},Ue={name:"FinanceFeeStructureForm"},Te=Object.assign(Ue,{setup(K){const b={name:"",description:"",feeGroups:[],isEditable:!0},$="finance/feeStructure/",p=Y($),v=H({transportFees:[],feeGroups:[],frequencies:[]}),t=H({...b}),P=e=>{Object.assign(v,e),v.feeGroups.forEach(s=>{let g=[j(s)];b.feeGroups.push({...s,installments:g})}),Object.assign(t,z(b))},J=e=>{e.applicableTo==="new"?e.applicableTo="old":e.applicableTo==="old"?e.applicableTo="all":e.applicableTo="new"},M=e=>{let s=[];e.feeGroups.forEach(g=>{let V=[];g.installments.forEach(_=>{var D,h,U;let q=[];_.heads.forEach(y=>{var C;q.push({name:y.name,uuid:y.uuid,amount:((C=y.amount)==null?void 0:C.value)||0,isOptional:!!y.isOptional,applicableTo:y.applicableTo})}),V.push({title:_.title,dueDate:_.dueDate.value,hasTransportFee:!!_.hasTransportFee,transportFee:((D=_.transportFee)==null?void 0:D.uuid)||"",hasLateFee:!!_.hasLateFee,lateFeeFrequency:((h=_.lateFeeFrequency)==null?void 0:h.value)||"",lateFeeType:_.lateFeeType,lateFeeValue:((U=_.lateFeeValue)==null?void 0:U.value)||"",uuid:_.uuid||R(),heads:q})}),s.push({...g,installments:V})}),Object.assign(b,{...e,feeGroups:s}),Object.assign(t,z(b))},Q=e=>{e.installments.push(j(e))},W=async(e,s)=>{await I()&&e.installments.splice(s,1)},j=e=>{let s=[];return e.heads.forEach(g=>{s.push({...g,amount:0,isOptional:!1,applicableTo:"all"})}),{uuid:R(),title:"",dueDate:null,hasLateFee:!1,hasTransportFee:!1,lateFeeFrequency:"daily",lateFeeValue:0,lateFeeType:"amount",heads:s,transportFee:""}};return(e,s)=>{const g=f("BaseAlert"),V=f("BaseInput"),_=f("BaseTextarea"),q=f("BaseHeading"),D=f("DatePicker"),h=f("BaseCheckbox"),U=f("BaseSelect"),y=f("BaseFieldset"),C=f("BaseButton"),X=f("FormAction"),T=Z("tooltip");return u(),A(X,{"pre-requisites":!0,onSetPreRequisites:P,"init-url":$,"init-form":b,form:t,setForm:M,redirect:"FinanceFeeStructure"},{default:F(()=>[t.isEditable?k("",!0):(u(),A(g,{key:0,class:"mb-6",design:"error"},{default:F(()=>[O(w(e.$trans("finance.fee_structure.not_editable_as_fee_paid")),1)]),_:1})),r("div",ee,[r("div",ae,[c(V,{type:"text",modelValue:t.name,"onUpdate:modelValue":s[0]||(s[0]=m=>t.name=m),name:"name",label:e.$trans("finance.fee_structure.props.name"),error:i(p).name,"onUpdate:error":s[1]||(s[1]=m=>i(p).name=m),autofocus:""},null,8,["modelValue","label","error"])]),r("div",se,[c(_,{modelValue:t.description,"onUpdate:modelValue":s[2]||(s[2]=m=>t.description=m),rows:1,name:"description",label:e.$trans("finance.fee_structure.props.description"),error:i(p).description,"onUpdate:error":s[3]||(s[3]=m=>i(p).description=m)},null,8,["modelValue","label","error"])])]),(u(!0),d(G,null,L(t.feeGroups,(m,l)=>(u(),d("div",{class:"mt-4 grid grid-cols-1",key:m.uuid},[r("div",te,[c(q,null,{default:F(()=>[O(w(m.name),1)]),_:2},1024),(u(!0),d(G,null,L(m.installments,(n,o)=>(u(),A(y,{class:"mt-4",key:n.uuid},{legend:F(()=>[O(w(e.$trans("finance.fee_structure.installment_number",{attribute:o+1}))+" ",1),t.isEditable?(u(),d("span",{key:0,class:"text-danger ml-2 cursor-pointer",onClick:a=>W(m,o)},s[4]||(s[4]=[r("i",{class:"fas fa-times-circle"},null,-1)]),8,oe)):k("",!0)]),default:F(()=>[r("div",re,[r("div",le,[c(V,{type:"text",name:`feeGroups.${l}.installments.${o}.title`,modelValue:n.title,"onUpdate:modelValue":a=>n.title=a,label:e.$trans("finance.fee_structure.props.title"),error:i(p)[`feeGroups.${l}.installments.${o}.title`],"onUpdate:error":a=>i(p)[`feeGroups.${l}.installments.${o}.title`]=a,disabled:!t.isEditable},null,8,["name","modelValue","onUpdate:modelValue","label","error","onUpdate:error","disabled"])]),r("div",ne,[c(D,{modelValue:n.dueDate,"onUpdate:modelValue":a=>n.dueDate=a,name:`feeGroups.${l}.installments.${o}.dueDate`,label:e.$trans("finance.fee_structure.props.due_date"),"no-clear":"",error:i(p)[`feeGroups.${l}.installments.${o}.dueDate`],"onUpdate:error":a=>i(p)[`feeGroups.${l}.installments.${o}.dueDate`]=a,disabled:!t.isEditable},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error","disabled"])]),(u(!0),d(G,null,L(n.heads,(a,S)=>(u(),d("div",ue,[c(V,{name:`feeGroups.${l}.installments.${o}.heads.${S}.amount`,modelValue:a.amount,"onUpdate:modelValue":B=>a.amount=B,label:a.name,currency:"",error:i(p)[`feeGroups.${l}.installments.${o}.heads.${S}.amount`],"onUpdate:error":B=>i(p)[`feeGroups.${l}.installments.${o}.heads.${S}.amount`]=B,disabled:!t.isEditable},{"additional-label":F(()=>[r("div",ie,[r("span",{class:N({"cursor-pointer":t.isEditable}),onClick:B=>a.isOptional=!a.isOptional},[a.isOptional?E((u(),d("span",de,s[6]||(s[6]=[r("i",{class:"fas fa-question-circle text-gray-900 dark:text-gray-300"},null,-1)]))),[[T,e.$trans("finance.fee_structure.props.is_optional")]]):E((u(),d("span",ce,s[5]||(s[5]=[r("i",{class:"fas fa-check-circle text-gray-900 dark:text-gray-300"},null,-1)]))),[[T,e.$trans("finance.fee_structure.props.is_mandatory")]])],10,pe),r("span",{class:N({"cursor-pointer":t.isEditable}),onClick:B=>J(a)},[a.applicableTo==="new"?E((u(),d("span",fe,s[7]||(s[7]=[r("i",{class:"fas fa-user-plus text-gray-900 dark:text-gray-300"},null,-1)]))),[[T,e.$trans("finance.fee_structure.props.new_student")]]):a.applicableTo==="old"?E((u(),d("span",_e,s[8]||(s[8]=[r("i",{class:"fas fa-user-lock text-gray-900 dark:text-gray-300"},null,-1)]))),[[T,e.$trans("finance.fee_structure.props.old_student")]]):a.applicableTo==="all"?E((u(),d("span",be,s[9]||(s[9]=[r("i",{class:"fas fa-user-graduate text-gray-900 dark:text-gray-300"},null,-1)]))),[[T,e.$trans("finance.fee_structure.props.all_student")]]):k("",!0)],10,me)])]),_:2},1032,["name","modelValue","onUpdate:modelValue","label","error","onUpdate:error","disabled"])]))),256))]),r("div",Fe,[r("div",$e,[c(h,{modelValue:n.hasTransportFee,"onUpdate:modelValue":a=>n.hasTransportFee=a,name:`feeGroups.${l}.installments.${o}.hasTransportFee`,label:e.$trans("finance.fee_structure.props.has_transport_fee"),disabled:!t.isEditable},null,8,["modelValue","onUpdate:modelValue","name","label","disabled"])]),n.hasTransportFee?(u(),d("div",ge,[c(U,{modelValue:n.transportFee,"onUpdate:modelValue":a=>n.transportFee=a,name:`feeGroups.${l}.installments.${o}.transportFee`,label:e.$trans("transport.fee.fee"),options:v.transportFees,"label-prop":"name","value-prop":"uuid",error:i(p)[`feeGroups.${l}.installments.${o}.transportFee`],"onUpdate:error":a=>i(p)[`feeGroups.${l}.installments.${o}.transportFee`]=a,disabled:!t.isEditable},null,8,["modelValue","onUpdate:modelValue","name","label","options","error","onUpdate:error","disabled"])])):k("",!0)]),r("div",Ve,[r("div",ye,[c(h,{modelValue:n.hasLateFee,"onUpdate:modelValue":a=>n.hasLateFee=a,name:`feeGroups.${l}.installments.${o}.hasLateFee`,label:e.$trans("finance.fee_structure.props.has_late_fee"),disabled:!t.isEditable},null,8,["modelValue","onUpdate:modelValue","name","label","disabled"])]),n.hasLateFee?(u(),d(G,{key:0},[r("div",ve,[c(U,{modelValue:n.lateFeeFrequency,"onUpdate:modelValue":a=>n.lateFeeFrequency=a,name:`feeGroups.${l}.installments.${o}.lateFeeFrequency`,label:e.$trans("finance.fee_structure.props.late_fee_frequency"),options:v.frequencies,"label-prop":"label","value-prop":"value",error:i(p)[`feeGroups.${l}.installments.${o}.lateFeeFrequency`],"onUpdate:error":a=>i(p)[`feeGroups.${l}.installments.${o}.lateFeeFrequency`]=a,disabled:!t.isEditable},null,8,["modelValue","onUpdate:modelValue","name","label","options","error","onUpdate:error","disabled"])]),r("div",ke,[c(V,{modelValue:n.lateFeeValue,"onUpdate:modelValue":a=>n.lateFeeValue=a,name:`feeGroups.${l}.installments.${o}.lateFeeValue`,label:e.$trans("finance.fee_structure.props.late_fee_value"),currency:"",error:i(p)[`feeGroups.${l}.installments.${o}.lateFeeValue`],"onUpdate:error":a=>i(p)[`feeGroups.${l}.installments.${o}.lateFeeValue`]=a,disabled:!t.isEditable},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error","disabled"])])],64)):k("",!0)])]),_:2},1024))),128)),t.isEditable?(u(),d("div",he,[c(C,{design:"primary",size:"xs",onClick:n=>Q(m)},{default:F(()=>[O(w(e.$trans("global.add",{attribute:e.$trans("finance.fee_structure.installment")})),1)]),_:2},1032,["onClick"])])):k("",!0)])]))),128))]),_:1},8,["form"])}}}),Be={name:"FinanceFeeStructureAction"},Ge=Object.assign(Be,{setup(K){const b=x();return($,p)=>{const v=f("PageHeaderAction"),t=f("PageHeader"),P=f("ParentTransition");return u(),d(G,null,[c(t,{title:$.$trans(i(b).meta.trans,{attribute:$.$trans(i(b).meta.label)}),navs:[{label:$.$trans("finance.finance"),path:"Finance"},{label:$.$trans("finance.fee_structure.fee_structure"),path:"FinanceFeeStructureList"}]},{default:F(()=>[c(v,{name:"FinanceFeeStructure",title:$.$trans("finance.fee_structure.fee_structure"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),c(P,{appear:"",visibility:!0},{default:F(()=>[c(Te)]),_:1})],64)}}});export{Ge as default};
