import{I as O,l as T,r as f,q as _,o as d,w as v,d as u,a as g,e as i,f as a,F as C,v as q,s as h,t as B,b as y,K as k,u as A}from"./app-DvIo72ZO.js";const E={class:"grid grid-cols-3 gap-6"},H={class:"col-span-3 sm:col-span-1"},L={class:"col-span-3 sm:col-span-2"},N={class:"col-span-4 sm:col-span-1"},w={class:"col-span-4 sm:col-span-1"},I={class:"col-span-4 sm:col-span-1"},z={class:"mt-4 grid grid-cols-4 gap-3"},D={class:"col-span-4 sm:col-span-1"},G={key:0,class:"col-span-4 sm:col-span-1"},S={class:"col-span-4 sm:col-span-1"},K={name:"FinanceFeeConcessionForm"},J=Object.assign(K,{setup(P){const l={name:"",transportValue:0,transportType:"percent",description:"",records:[]},m="finance/feeConcession/",r=O(m),V=T({heads:[],types:[]}),n=T({...l}),U=s=>{Object.assign(V,s),V.heads.forEach(o=>{l.records.push({head:o,value:0,type:"percent"})}),Object.assign(n,k(l))},R=s=>{Object.assign(l,{name:s.name,transportValue:s.transportValue.value,transportType:s.transportType,description:s.description}),l.records.forEach(o=>{let p=s.records.find($=>$.head.uuid==o.head.uuid);p!==void 0&&(o.head=p.head,o.value=p.value.value,o.type=p.type)}),Object.assign(n,k(l))};return(s,o)=>{const p=f("BaseInput"),$=f("BaseTextarea"),F=f("BaseLabel"),b=f("BaseRadioGroup"),j=f("FormAction");return d(),_(j,{"pre-requisites":!0,onSetPreRequisites:U,"init-url":m,"init-form":l,form:n,setForm:R,redirect:"FinanceFeeConcession"},{default:v(()=>[u("div",E,[u("div",H,[i(p,{type:"text",modelValue:n.name,"onUpdate:modelValue":o[0]||(o[0]=e=>n.name=e),name:"name",label:s.$trans("finance.fee_concession.props.name"),error:a(r).name,"onUpdate:error":o[1]||(o[1]=e=>a(r).name=e),autofocus:""},null,8,["modelValue","label","error"])]),u("div",L,[i($,{modelValue:n.description,"onUpdate:modelValue":o[2]||(o[2]=e=>n.description=e),rows:1,name:"description",label:s.$trans("finance.fee_concession.props.description"),error:a(r).description,"onUpdate:error":o[3]||(o[3]=e=>a(r).description=e)},null,8,["modelValue","label","error"])])]),(d(!0),g(C,null,q(n.records,(e,c)=>(d(),g("div",{class:"mt-4 grid grid-cols-4 gap-3",key:e.head.uuid},[u("div",N,[i(F,{class:"mt-4"},{default:v(()=>[h(B(e.head.name),1)]),_:2},1024)]),u("div",w,[i(b,{"top-margin":"",options:V.types,name:`records.${c}.type`,modelValue:e.type,"onUpdate:modelValue":t=>e.type=t,error:a(r)[`records.${c}.type`],"onUpdate:error":t=>a(r)[`records.${c}.type`]=t,horizontal:""},null,8,["options","name","modelValue","onUpdate:modelValue","error","onUpdate:error"])]),u("div",I,[e.type=="amount"?(d(),_(p,{key:0,name:`records.${c}.value`,modelValue:e.value,"onUpdate:modelValue":t=>e.value=t,placeholder:s.$trans("finance.fee_concession.props.value"),currency:"",percentage:e.type=="percent",error:a(r)[`records.${c}.value`],"onUpdate:error":t=>a(r)[`records.${c}.value`]=t},null,8,["name","modelValue","onUpdate:modelValue","placeholder","percentage","error","onUpdate:error"])):y("",!0),e.type=="percent"?(d(),_(p,{key:1,name:`records.${c}.value`,modelValue:e.value,"onUpdate:modelValue":t=>e.value=t,placeholder:s.$trans("finance.fee_concession.props.value"),percentage:"",error:a(r)[`records.${c}.value`],"onUpdate:error":t=>a(r)[`records.${c}.value`]=t},null,8,["name","modelValue","onUpdate:modelValue","placeholder","error","onUpdate:error"])):y("",!0)])]))),128)),u("div",z,[u("div",D,[i(F,{class:"mt-4"},{default:v(()=>[h(B(s.$trans("transport.fee.fee")),1)]),_:1})]),n.transportType?(d(),g("div",G,[i(b,{"top-margin":"",options:V.types,name:"transportType",modelValue:n.transportType,"onUpdate:modelValue":o[4]||(o[4]=e=>n.transportType=e),error:a(r).transportType,"onUpdate:error":o[5]||(o[5]=e=>a(r).transportType=e),horizontal:""},null,8,["options","modelValue","error"])])):y("",!0),u("div",S,[n.transportType=="amount"?(d(),_(p,{key:0,name:"transportValue",modelValue:n.transportValue,"onUpdate:modelValue":o[6]||(o[6]=e=>n.transportValue=e),placeholder:s.$trans("finance.fee_concession.props.value"),currency:"",percentage:n.transportType=="percent",error:a(r).transportValue,"onUpdate:error":o[7]||(o[7]=e=>a(r).transportValue=e)},null,8,["modelValue","placeholder","percentage","error"])):y("",!0),n.transportType=="percent"?(d(),_(p,{key:1,name:"transportValue",modelValue:n.transportValue,"onUpdate:modelValue":o[8]||(o[8]=e=>n.transportValue=e),placeholder:s.$trans("finance.fee_concession.props.value"),percentage:"",error:a(r).transportValue,"onUpdate:error":o[9]||(o[9]=e=>a(r).transportValue=e)},null,8,["modelValue","placeholder","error"])):y("",!0)])])]),_:1},8,["form"])}}}),M={name:"FinanceFeeConcessionAction"},W=Object.assign(M,{setup(P){const l=A();return(m,r)=>{const V=f("PageHeaderAction"),n=f("PageHeader"),U=f("ParentTransition");return d(),g(C,null,[i(n,{title:m.$trans(a(l).meta.trans,{attribute:m.$trans(a(l).meta.label)}),navs:[{label:m.$trans("finance.finance"),path:"Finance"},{label:m.$trans("finance.fee_concession.fee_concession"),path:"FinanceFeeConcessionList"}]},{default:v(()=>[i(V,{name:"FinanceFeeConcession",title:m.$trans("finance.fee_concession.fee_concession"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),i(U,{appear:"",visibility:!0},{default:v(()=>[i(J)]),_:1})],64)}}});export{W as default};
