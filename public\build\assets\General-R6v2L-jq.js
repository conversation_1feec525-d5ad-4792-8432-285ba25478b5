import{u as v,j as x,I as R,c as L,l as f,r as a,a as P,o as S,e as n,f as o,w as m,d as i,s as B,t as D,F as E}from"./app-DvIo72ZO.js";const F={class:"grid grid-cols-3 gap-4"},j={class:"col-span-3 sm:col-span-1"},w={class:"col-span-3 sm:col-span-1"},A={class:"col-span-3 sm:col-span-1"},C={class:"col-span-3"},T={class:"col-span-3 sm:col-span-1"},k={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},$={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},z={name:"EmployeeConfigGeneral"},M=Object.assign(z,{setup(J){const c=v(),s=x("$trans"),b="config/",u=R(b),I=L(()=>s("global.placeholder_info",{attribute:p.datePlaceholders})),p=f({datePlaceholders:""}),q={codeNumberPrefix:"",codeNumberDigit:"",codeNumberSuffix:"",uniqueIdNumber1Label:"",uniqueIdNumber2Label:"",uniqueIdNumber3Label:"",isUniqueIdNumber1Required:!1,isUniqueIdNumber2Required:!1,isUniqueIdNumber3Required:!1,type:"employee"},l=f({...q}),U=N=>{Object.assign(p,{datePlaceholders:N.datePlaceholders.map(e=>e.value).join(", ")})};return(N,e)=>{const g=a("PageHeader"),d=a("BaseInput"),V=a("BaseAlert"),t=a("BaseSwitch"),_=a("FormAction"),y=a("ParentTransition");return S(),P(E,null,[n(g,{title:o(s)(o(c).meta.label),navs:[{label:o(s)("employee.employee"),path:"Employee"}]},null,8,["title","navs"]),n(y,{appear:"",visibility:!0},{default:m(()=>[n(_,{"pre-requisites":{data:["datePlaceholders"]},onSetPreRequisites:U,"init-url":b,"data-fetch":"employee",action:"store","init-form":q,form:l,"stay-on":"",redirect:"Employee"},{default:m(()=>[i("div",F,[i("div",j,[n(d,{type:"text",modelValue:l.codeNumberPrefix,"onUpdate:modelValue":e[0]||(e[0]=r=>l.codeNumberPrefix=r),name:"codeNumberPrefix",label:o(s)("employee.config.props.number_prefix"),error:o(u).codeNumberPrefix,"onUpdate:error":e[1]||(e[1]=r=>o(u).codeNumberPrefix=r)},null,8,["modelValue","label","error"])]),i("div",w,[n(d,{type:"number",modelValue:l.codeNumberDigit,"onUpdate:modelValue":e[2]||(e[2]=r=>l.codeNumberDigit=r),name:"codeNumberDigit",label:o(s)("employee.config.props.number_digit"),error:o(u).codeNumberDigit,"onUpdate:error":e[3]||(e[3]=r=>o(u).codeNumberDigit=r)},null,8,["modelValue","label","error"])]),i("div",A,[n(d,{type:"text",modelValue:l.codeNumberSuffix,"onUpdate:modelValue":e[4]||(e[4]=r=>l.codeNumberSuffix=r),name:"codeNumberSuffix",label:o(s)("employee.config.props.number_suffix"),error:o(u).codeNumberSuffix,"onUpdate:error":e[5]||(e[5]=r=>o(u).codeNumberSuffix=r)},null,8,["modelValue","label","error"])]),i("div",C,[n(V,{design:"info"},{default:m(()=>[B(D(I.value),1)]),_:1})]),i("div",T,[n(d,{type:"text",modelValue:l.uniqueIdNumber1Label,"onUpdate:modelValue":e[6]||(e[6]=r=>l.uniqueIdNumber1Label=r),name:"uniqueIdNumber1Label",label:o(s)("employee.config.props.unique_id_number1_label"),error:o(u).uniqueIdNumber1Label,"onUpdate:error":e[7]||(e[7]=r=>o(u).uniqueIdNumber1Label=r)},null,8,["modelValue","label","error"])]),i("div",k,[n(t,{vertical:"",modelValue:l.isUniqueIdNumber1Required,"onUpdate:modelValue":e[8]||(e[8]=r=>l.isUniqueIdNumber1Required=r),name:"uniqueIdNumber1Required",label:o(s)("employee.config.props.unique_id_number1_required"),error:o(u).isUniqueIdNumber1Required,"onUpdate:error":e[9]||(e[9]=r=>o(u).isUniqueIdNumber1Required=r)},null,8,["modelValue","label","error"])]),e[18]||(e[18]=i("div",{class:"col-span-3 sm:col-span-1"},null,-1)),i("div",H,[n(d,{type:"text",modelValue:l.uniqueIdNumber2Label,"onUpdate:modelValue":e[10]||(e[10]=r=>l.uniqueIdNumber2Label=r),name:"uniqueIdNumber2Label",label:o(s)("employee.config.props.unique_id_number2_label"),error:o(u).uniqueIdNumber2Label,"onUpdate:error":e[11]||(e[11]=r=>o(u).uniqueIdNumber2Label=r)},null,8,["modelValue","label","error"])]),i("div",O,[n(t,{vertical:"",modelValue:l.isUniqueIdNumber2Required,"onUpdate:modelValue":e[12]||(e[12]=r=>l.isUniqueIdNumber2Required=r),name:"uniqueIdNumber2Required",label:o(s)("employee.config.props.unique_id_number2_required"),error:o(u).isUniqueIdNumber2Required,"onUpdate:error":e[13]||(e[13]=r=>o(u).isUniqueIdNumber2Required=r)},null,8,["modelValue","label","error"])]),e[19]||(e[19]=i("div",{class:"col-span-3 sm:col-span-1"},null,-1)),i("div",$,[n(d,{type:"text",modelValue:l.uniqueIdNumber3Label,"onUpdate:modelValue":e[14]||(e[14]=r=>l.uniqueIdNumber3Label=r),name:"uniqueIdNumber3Label",label:o(s)("employee.config.props.unique_id_number3_label"),error:o(u).uniqueIdNumber3Label,"onUpdate:error":e[15]||(e[15]=r=>o(u).uniqueIdNumber3Label=r)},null,8,["modelValue","label","error"])]),i("div",G,[n(t,{vertical:"",modelValue:l.isUniqueIdNumber3Required,"onUpdate:modelValue":e[16]||(e[16]=r=>l.isUniqueIdNumber3Required=r),name:"uniqueIdNumber3Required",label:o(s)("employee.config.props.unique_id_number3_required"),error:o(u).isUniqueIdNumber3Required,"onUpdate:error":e[17]||(e[17]=r=>o(u).isUniqueIdNumber3Required=r)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])]),_:1})],64)}}});export{M as default};
