import{u as ae,l as R,n as se,r as p,q as u,o as a,w as s,d as m,e as d,h as ie,j as z,y as F,m as ne,z as le,f as t,B as $,a as r,F as b,v as j,b as _,s as l,A as S,t as i}from"./app-DvIo72ZO.js";const ue={class:"grid grid-cols-3 gap-6"},oe={class:"col-span-3 sm:col-span-1"},re={__name:"Filter",emits:["hide"],setup(G,{emit:g}){ae();const T=g,n={title:""},C=R({...n}),A=R({isLoaded:!0});return se(async()=>{A.isLoaded=!0}),(O,D)=>{const w=p("BaseInput"),y=p("FilterForm");return a(),u(y,{"init-form":n,form:C,multiple:[],onHide:D[1]||(D[1]=L=>T("hide"))},{default:s(()=>[m("div",ue,[m("div",oe,[d(w,{type:"text",modelValue:C.title,"onUpdate:modelValue":D[0]||(D[0]=L=>C.title=L),name:"title",label:O.$trans("exam.online_exam.props.title")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},de={class:"grid grid-cols-1 gap-4 px-4 pt-4 lg:grid-cols-2"},me={class:"text-center dark:text-gray-400"},ce={key:0,class:"text-sm"},_e=["onClick"],pe=["onClick"],fe=["onClick"],be={key:4,class:"ml-2 text-sm text-yellow-600"},ge={key:5,class:"ml-2 text-sm text-gray-500"},ye={class:"text-sm text-center dark:text-gray-500"},ke={class:"text-sm text-center dark:text-gray-500"},ve={key:0},xe={key:0},he={class:"text-xl font-semibold"},Te={key:0},Ce={name:"ExamOnlineExamList"},we=Object.assign(Ce,{setup(G){const g=ie(),T=z("emitter"),n=z("$trans");let C=["filter"];F("online-exam:create")&&C.unshift("create");let A=[];F("online-exam:export")&&(A=["print","pdf","excel"]);const O=[{key:"course",label:n("academic.course.course"),visibility:!0},{key:"subject",label:n("academic.subject.subject"),visibility:!0}],D="exam/onlineExam/",w=ne(!1),y=R({}),L=V=>{Object.assign(y,V)};return(V,o)=>{const J=p("PageHeaderAction"),K=p("PageHeader"),P=p("ParentTransition"),f=p("BaseBadge"),k=p("DataCell"),H=p("DataRow"),Q=p("SimpleTable"),W=p("CardView"),X=p("Pagination"),Y=p("CardList"),x=p("TextMuted"),I=p("FloatingMenuItem"),Z=p("FloatingMenu"),E=p("BaseButton"),ee=p("DataTable"),te=p("ListItem"),B=le("tooltip");return a(),u(te,{"init-url":D,onSetItems:L},{header:s(()=>[d(K,{title:t(n)("exam.online_exam.online_exam"),navs:[{label:t(n)("exam.exam"),path:"Exam"}]},{default:s(()=>[d(J,{url:"exam/online-exams/",name:"ExamOnlineExam",title:t(n)("exam.online_exam.online_exam"),actions:t(C),"dropdown-actions":t(A),onToggleFilter:o[0]||(o[0]=e=>w.value=!w.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:s(()=>[d(P,{appear:"",visibility:w.value},{default:s(()=>[d(re,{onRefresh:o[1]||(o[1]=e=>t(T).emit("listItems")),onHide:o[2]||(o[2]=e=>w.value=!1)})]),_:1},8,["visibility"])]),default:s(()=>[t($)(["student","guardian"],"any")?(a(),u(P,{key:0,appear:"",visibility:!0},{default:s(()=>[d(Y,{header:y.headers,meta:y.meta},{content:s(()=>[m("div",he,i(t(n)("dashboard.nothing_to_show")),1)]),default:s(()=>[m("div",de,[(a(!0),r(b,null,j(y.data,e=>(a(),u(W,{key:e.uuid,"no-padding":""},{default:s(()=>{var c,v,M,U,N;return[m("div",me,[l(i(e.title)+" ",1),e.type?(a(),r("span",ce,"("+i((c=e.type)==null?void 0:c.label)+")",1)):_("",!0),t($)("student")&&e.shouldShowSubmitButton?S((a(),r("span",{key:1,class:"cursor-pointer ml-2",onClick:h=>t(g).push({name:"ExamOnlineExamSubmit",params:{uuid:e.uuid}})},o[6]||(o[6]=[m("i",{class:"fas fa-arrow-up-right-from-square text-blue-600"},null,-1)]),8,_e)),[[B,e.hasSubmission&&!e.submittedAt.value?t(n)("exam.online_exam.continue_exam"):t(n)("exam.online_exam.submit")]]):_("",!0),t($)("student")&&e.hasSubmission&&!e.submittedAt.value?S((a(),r("span",{key:2,class:"cursor-pointer ml-2",onClick:h=>t(g).push({name:"ExamOnlineExamSubmit",params:{uuid:e.uuid}})},o[7]||(o[7]=[m("i",{class:"fas fa-arrow-up-right-from-square text-blue-600"},null,-1)]),8,pe)),[[B,e.hasSubmission&&!e.submittedAt.value?t(n)("exam.online_exam.continue_exam"):t(n)("exam.online_exam.submit")]]):_("",!0),t($)(["student","guardian"],"any")&&e.shouldShowResultButton?S((a(),r("span",{key:3,class:"cursor-pointer ml-2",onClick:h=>t(g).push({name:"ExamOnlineExamStudentSubmission",params:{uuid:e.uuid}})},o[8]||(o[8]=[m("i",{class:"fas fa-arrow-up-right-from-square text-green-600"},null,-1)]),8,fe)),[[B,t(n)("exam.online_exam.result")]]):_("",!0),t($)("student")&&e.hasSubmission&&((v=e.submittedAt)!=null&&v.value)&&!((M=e.resultPublishedAt)!=null&&M.value)&&e.isFlexibleTiming&&e.autoPublishResultsForFlexibleTiming?S((a(),r("span",be,o[9]||(o[9]=[m("i",{class:"fas fa-clock"},null,-1)]))),[[B,t(n)("exam.online_exam.processing_results")]]):_("",!0),t($)("student")&&e.hasSubmission&&((U=e.submittedAt)!=null&&U.value)&&!((N=e.resultPublishedAt)!=null&&N.value)&&(!e.isFlexibleTiming||!e.autoPublishResultsForFlexibleTiming)?S((a(),r("span",ge,o[10]||(o[10]=[m("i",{class:"fas fa-hourglass-half"},null,-1)]))),[[B,t(n)("exam.online_exam.results_pending")]]):_("",!0)]),m("div",ye,[e.isFlexibleTiming?(a(),r(b,{key:0},[e.isAvailable?(a(),u(f,{key:0,design:"success"},{default:s(()=>[l(i(t(n)("exam.online_exam.available")),1)]),_:1})):e.expiryDateTime&&new Date>new Date(e.expiryDateTime)?(a(),u(f,{key:1,design:"danger"},{default:s(()=>[l(i(t(n)("exam.online_exam.expired")),1)]),_:1})):(a(),u(f,{key:2,design:"secondary"},{default:s(()=>[l(i(t(n)("exam.online_exam.not_available")),1)]),_:1}))],64)):(a(),r(b,{key:1},[e.isUpcoming&&e.timeLeft>e.upcomingThreshold?(a(),u(f,{key:0,design:"info"},{default:s(()=>[l(i(t(n)("exam.online_exam.upcoming")),1)]),_:1})):e.isUpcoming&&e.timeLeft<=e.upcomingThreshold?(a(),u(f,{key:1,design:"info"},{default:s(()=>[l(i(t(n)("exam.online_exam.starting_in",{attribute:e.timeLeft})),1)]),_:2},1024)):e.isLive?(a(),u(f,{key:2,design:"success"},{default:s(()=>[l(i(t(n)("exam.online_exam.live")),1)]),_:1})):e.isCompleted?(a(),u(f,{key:3,design:"primary"},{default:s(()=>[l(i(t(n)("exam.online_exam.completed")),1)]),_:1})):_("",!0)],64))]),m("div",ke,[e.isFlexibleTiming?(a(),r(b,{key:0},[m("div",null,i(t(n)("exam.online_exam.flexible_timing")),1),m("div",null,"Duration: "+i(e.duration),1),e.expiryDate?(a(),r("div",ve,[l(" Expires: "+i(e.expiryDate.formatted)+" ",1),e.expiryTime?(a(),r("span",xe,i(e.expiryTime.at),1)):_("",!0)])):_("",!0)],64)):(a(),r(b,{key:1},[l(i(e.date.formatted)+" "+i(e.period)+" ("+i(e.duration)+") ",1)],64))]),o[11]||(o[11]=m("div",{class:"flex justify-between"},null,-1)),e.records.length>0?(a(),u(Q,{key:0,header:O},{default:s(()=>[(a(!0),r(b,null,j(e.records,h=>(a(),u(H,{key:h.uuid},{default:s(()=>[d(k,{name:"course"},{default:s(()=>{var q;return[l(i(((q=h.batch.course)==null?void 0:q.name)+" "+h.batch.name),1)]}),_:2},1024),d(k,{name:"subject"},{default:s(()=>[l(i(h.subject.name),1)]),_:2},1024)]),_:2},1024))),128))]),_:2},1024)):_("",!0)]}),_:2},1024))),128))]),m("div",null,[d(X,{"card-view":"",meta:y.meta,onRefresh:o[3]||(o[3]=e=>t(T).emit("listItems"))},null,8,["meta"])])]),_:1},8,["header","meta"])]),_:1})):(a(),u(P,{key:1,appear:"",visibility:!0},{default:s(()=>[d(ee,{header:y.headers,meta:y.meta,module:"exam.online_exam",onRefresh:o[5]||(o[5]=e=>t(T).emit("listItems"))},{actionButton:s(()=>[t(F)("online-exam:create")?(a(),u(E,{key:0,onClick:o[4]||(o[4]=e=>t(g).push({name:"ExamOnlineExamCreate"}))},{default:s(()=>[l(i(t(n)("global.add",{attribute:t(n)("exam.online_exam.online_exam")})),1)]),_:1})):_("",!0)]),default:s(()=>[(a(!0),r(b,null,j(y.data,e=>(a(),u(H,{key:e.uuid,onDoubleClick:c=>t(g).push({name:"ExamOnlineExamShow",params:{uuid:e.uuid}})},{default:s(()=>[d(k,{name:"title"},{default:s(()=>[l(i(e.title)+" ",1),d(x,{block:""},{default:s(()=>{var c;return[l(i((c=e.type)==null?void 0:c.label),1)]}),_:2},1024)]),_:2},1024),d(k,{name:"records"},{default:s(()=>[(a(!0),r(b,null,j(e.records,c=>{var v;return a(),r("div",null,[l(i(((v=c.batch.course)==null?void 0:v.name)+" "+c.batch.name)+" ",1),c.subject?(a(),u(x,{key:0},{default:s(()=>[l(i(c.subject.name),1)]),_:2},1024)):_("",!0)])}),256))]),_:2},1024),d(k,{name:"date"},{default:s(()=>[e.isFlexibleTiming?(a(),r(b,{key:0},[m("div",null,i(t(n)("exam.online_exam.flexible_timing")),1),e.expiryDate?(a(),u(x,{key:0,block:""},{default:s(()=>[l(" Expires: "+i(e.expiryDate.formatted)+" ",1),e.expiryTime?(a(),r("span",Te,i(e.expiryTime.at),1)):_("",!0)]),_:2},1024)):_("",!0)],64)):(a(),r(b,{key:1},[l(i(e.date.formatted)+" ",1),d(x,{block:""},{default:s(()=>[l(i(e.endDate.formatted),1)]),_:2},1024)],64)),m("div",null,[e.isFlexibleTiming?(a(),r(b,{key:0},[e.isAvailable?(a(),u(f,{key:0,design:"success"},{default:s(()=>[l(i(t(n)("exam.online_exam.available")),1)]),_:1})):e.expiryDateTime&&new Date>new Date(e.expiryDateTime)?(a(),u(f,{key:1,design:"danger"},{default:s(()=>[l(i(t(n)("exam.online_exam.expired")),1)]),_:1})):(a(),u(f,{key:2,design:"secondary"},{default:s(()=>[l(i(t(n)("exam.online_exam.not_available")),1)]),_:1}))],64)):(a(),r(b,{key:1},[e.isUpcoming&&e.timeLeft>e.upcomingThreshold?(a(),u(f,{key:0,design:"info"},{default:s(()=>[l(i(t(n)("exam.online_exam.upcoming")),1)]),_:1})):e.isUpcoming&&e.timeLeft<=e.upcomingThreshold?(a(),u(f,{key:1,design:"info"},{default:s(()=>[l(i(t(n)("exam.online_exam.starting_in",{attribute:e.timeLeft})),1)]),_:2},1024)):e.isLive?(a(),u(f,{key:2,design:"success"},{default:s(()=>[l(i(t(n)("exam.online_exam.live")),1)]),_:1})):e.isCompleted?(a(),u(f,{key:3,design:"primary"},{default:s(()=>[l(i(t(n)("exam.online_exam.completed")),1)]),_:1})):_("",!0)],64))])]),_:2},1024),d(k,{name:"time"},{default:s(()=>[e.isFlexibleTiming?(a(),r(b,{key:0},[m("div",null,i(e.duration),1),d(x,{block:""},{default:s(()=>o[12]||(o[12]=[l("Individual Duration")])),_:1})],64)):(a(),r(b,{key:1},[l(i(e.period)+" ",1),d(x,{block:""},{default:s(()=>[l(i(e.duration),1)]),_:2},1024)],64))]),_:2},1024),d(k,{name:"employee"},{default:s(()=>{var c;return[l(i(((c=e.employee)==null?void 0:c.name)||"-")+" ",1),d(x,{block:""},{default:s(()=>{var v;return[l(i((v=e.employee)==null?void 0:v.codeNumber),1)]}),_:2},1024)]}),_:2},1024),d(k,{name:"createdAt"},{default:s(()=>[l(i(e.createdAt.formatted),1)]),_:2},1024),d(k,{name:"action"},{default:s(()=>[d(Z,null,{default:s(()=>[d(I,{icon:"fas fa-arrow-circle-right",onClick:c=>t(g).push({name:"ExamOnlineExamShow",params:{uuid:e.uuid}})},{default:s(()=>[l(i(t(n)("general.show")),1)]),_:2},1032,["onClick"]),t(F)("online-exam:edit")&&e.isEditable?(a(),u(I,{key:0,icon:"fas fa-edit",onClick:c=>t(g).push({name:"ExamOnlineExamEdit",params:{uuid:e.uuid}})},{default:s(()=>[l(i(t(n)("general.edit")),1)]),_:2},1032,["onClick"])):_("",!0),t(F)("online-exam:create")?(a(),u(I,{key:1,icon:"fas fa-copy",onClick:c=>t(g).push({name:"ExamOnlineExamDuplicate",params:{uuid:e.uuid}})},{default:s(()=>[l(i(t(n)("general.duplicate")),1)]),_:2},1032,["onClick"])):_("",!0),t(F)("online-exam:delete")&&e.isDeletable?(a(),u(I,{key:2,icon:"fas fa-trash",onClick:c=>t(T).emit("deleteItem",{uuid:e.uuid})},{default:s(()=>[l(i(t(n)("general.delete")),1)]),_:2},1032,["onClick"])):_("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1}))]),_:1})}}});export{we as default};
