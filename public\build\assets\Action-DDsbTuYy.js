import{u as D,H as _,I as F,l as v,r as d,q as P,o as h,w as g,d as i,e as n,f as s,K as H,a as L,F as S}from"./app-DvIo72ZO.js";const j={class:"grid grid-cols-3 gap-6"},q={class:"col-span-3 sm:col-span-1"},A={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},y={class:"col-span-3 sm:col-span-1"},I={class:"col-span-3 sm:col-span-1"},M={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3"},N={class:"grid grid-cols-1"},w={class:"col"},C={name:"TransportVehicleServiceRecordForm"},K=Object.assign(C,{setup(f){const m=D(),l={vehicle:"",date:"",amount:"",log:"",nextDueDate:"",nextDueLog:"",remarks:"",media:[],mediaUpdated:!1,mediaToken:_(),mediaHash:[]},p="transport/vehicle/serviceRecord/",a=F(p),u=v({vehicles:[]}),o=v({...l}),b=v({vehicle:"",isLoaded:!m.params.uuid}),U=t=>{Object.assign(u,t)},k=()=>{o.mediaToken=_(),o.mediaHash=[]},$=t=>{var e;Object.assign(l,{...t,amount:t.amount.value,date:t.date.value,nextDueDate:t.nextDueDate.value,vehicle:(e=t.vehicle)==null?void 0:e.uuid}),Object.assign(o,H(l)),b.vehicle=t.vehicle.name,b.isLoaded=!0};return(t,e)=>{const T=d("BaseSelect"),V=d("DatePicker"),c=d("BaseInput"),R=d("BaseTextarea"),x=d("MediaUpload"),B=d("FormAction");return h(),P(B,{"pre-requisites":!0,onSetPreRequisites:U,"init-url":p,"init-form":l,form:o,"set-form":$,redirect:"TransportVehicleServiceRecord",onResetMediaFiles:k},{default:g(()=>[i("div",j,[i("div",q,[n(T,{modelValue:o.vehicle,"onUpdate:modelValue":e[0]||(e[0]=r=>o.vehicle=r),name:"vehicle",label:t.$trans("transport.vehicle.vehicle"),"label-prop":"nameWithRegistrationNumber","value-prop":"uuid",options:u.vehicles,error:s(a).vehicle,"onUpdate:error":e[1]||(e[1]=r=>s(a).vehicle=r)},null,8,["modelValue","label","options","error"])]),i("div",A,[n(V,{modelValue:o.date,"onUpdate:modelValue":e[2]||(e[2]=r=>o.date=r),name:"date",label:t.$trans("transport.vehicle.service_record.props.date"),"no-clear":"",error:s(a).date,"onUpdate:error":e[3]||(e[3]=r=>s(a).date=r)},null,8,["modelValue","label","error"])]),i("div",O,[n(c,{currency:"",modelValue:o.amount,"onUpdate:modelValue":e[4]||(e[4]=r=>o.amount=r),name:"amount",label:t.$trans("transport.vehicle.service_record.props.amount"),error:s(a).amount,"onUpdate:error":e[5]||(e[5]=r=>s(a).amount=r)},null,8,["modelValue","label","error"])]),i("div",y,[n(c,{type:"number",modelValue:o.log,"onUpdate:modelValue":e[6]||(e[6]=r=>o.log=r),"trailing-text":t.$trans("list.unit.km"),name:"log",label:t.$trans("transport.vehicle.service_record.props.log"),error:s(a).log,"onUpdate:error":e[7]||(e[7]=r=>s(a).log=r)},null,8,["modelValue","trailing-text","label","error"])]),i("div",I,[n(V,{modelValue:o.nextDueDate,"onUpdate:modelValue":e[8]||(e[8]=r=>o.nextDueDate=r),name:"nextDueDate",label:t.$trans("transport.vehicle.service_record.props.next_due_date"),"no-clear":"",error:s(a).nextDueDate,"onUpdate:error":e[9]||(e[9]=r=>s(a).nextDueDate=r)},null,8,["modelValue","label","error"])]),i("div",M,[n(c,{type:"number",modelValue:o.nextDueLog,"onUpdate:modelValue":e[10]||(e[10]=r=>o.nextDueLog=r),"trailing-text":t.$trans("list.unit.km"),name:"nextDueLog",label:t.$trans("transport.vehicle.service_record.props.next_due_log"),error:s(a).nextDueLog,"onUpdate:error":e[11]||(e[11]=r=>s(a).nextDueLog=r)},null,8,["modelValue","trailing-text","label","error"])]),i("div",E,[n(R,{modelValue:o.remarks,"onUpdate:modelValue":e[12]||(e[12]=r=>o.remarks=r),name:"remarks",label:t.$trans("transport.vehicle.service_record.props.remarks"),error:s(a).remarks,"onUpdate:error":e[13]||(e[13]=r=>s(a).remarks=r)},null,8,["modelValue","label","error"])])]),i("div",N,[i("div",w,[n(x,{multiple:"",label:t.$trans("general.file"),module:"vehicle_service_record",media:o.media,"media-token":o.mediaToken,onIsUpdated:e[14]||(e[14]=r=>o.mediaUpdated=!0),onSetHash:e[15]||(e[15]=r=>o.mediaHash.push(r))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),W={name:"TransportVehicleServiceRecordAction"},G=Object.assign(W,{setup(f){const m=D();return(l,p)=>{const a=d("PageHeaderAction"),u=d("PageHeader"),o=d("ParentTransition");return h(),L(S,null,[n(u,{title:l.$trans(s(m).meta.trans,{attribute:l.$trans(s(m).meta.label)}),navs:[{label:l.$trans("transport.transport"),path:"Transport"},{label:l.$trans("transport.vehicle.vehicle"),path:"TransportVehicle"},{label:l.$trans("transport.vehicle.service_record.service_record"),path:"TransportVehicleServiceRecordList"}]},{default:g(()=>[n(a,{name:"TransportVehicleServiceRecord",title:l.$trans("transport.vehicle.service_record.service_record"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(o,{appear:"",visibility:!0},{default:g(()=>[n(K)]),_:1})],64)}}});export{G as default};
