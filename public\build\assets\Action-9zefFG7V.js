import{u as N,H as h,I as L,l as V,n as T,r as d,q as D,o as g,w as f,d as s,a as B,b as J,e as l,f as r,J as K,F,v as z,s as P,t as M,N as G,K as Q}from"./app-DvIo72ZO.js";const W={class:"grid grid-cols-3 gap-6"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x=["onClick"],ee={class:"mt-4 grid grid-cols-3 gap-4"},te={class:"col-span-3 sm:col-span-1"},ne={class:"col-span-3 sm:col-span-1"},ae={class:"mt-4"},re={class:"mt-4 grid grid-cols-3 gap-6"},oe={class:"col-span-3"},se={name:"StudentFeeRefundForm"},de=Object.assign(se,{setup(y){const p=N(),u={date:"",records:[],ledger:"",paymentMethod:"",instrumentNumber:"",instrumentDate:"",clearingDate:"",bankDetail:"",referenceNumber:"",remarks:""},k={uuid:h(),head:"",amount:""},_="student/feeRefund/",o=L(_),c=V({feeHeads:[],ledgers:[],paymentMethods:[]}),b=V({selectedPaymentMethod:{}}),n=V({...u}),U=V({isLoaded:!p.params.muuid}),R=a=>{Object.assign(c,a)},S=a=>{n.paymentMethod=a?a.uuid:{},b.selectedPaymentMethod=a||{}},$=()=>{n.records.push({...k,uuid:h()}),U.isLoaded=!0},j=async a=>{await G()&&(n.records.length==1?n.records=[k]:n.records.splice(a,1))},C=a=>{Object.assign(u,{...a}),Object.assign(n,Q(u)),U.isLoaded=!0};return T(async()=>{$()}),(a,t)=>{const H=d("DatePicker"),v=d("BaseSelect"),w=d("PaymentMethodInput"),A=d("BaseInput"),E=d("BaseFieldset"),O=d("BaseBadge"),q=d("BaseTextarea"),I=d("FormAction");return g(),D(I,{"no-data-fetch":"","pre-requisites":!0,onSetPreRequisites:R,"init-url":_,uuid:r(p).params.uuid,"module-uuid":r(p).params.muuid,"init-form":u,form:n,"set-form":C,redirect:{name:"StudentFeeRefund",params:{uuid:r(p).params.uuid}}},{default:f(()=>[s("div",W,[s("div",X,[l(H,{modelValue:n.date,"onUpdate:modelValue":t[0]||(t[0]=e=>n.date=e),name:"date",label:a.$trans("student.fee_refund.props.date"),error:r(o).date,"onUpdate:error":t[1]||(t[1]=e=>r(o).date=e)},null,8,["modelValue","label","error"])]),s("div",Y,[l(v,{modelValue:n.ledger,"onUpdate:modelValue":t[2]||(t[2]=e=>n.ledger=e),name:"ledger",label:a.$trans("finance.ledger.ledger"),options:c.ledgers,"label-prop":"name","value-prop":"uuid",error:r(o).ledger,"onUpdate:error":t[3]||(t[3]=e=>r(o).ledger=e)},null,8,["modelValue","label","options","error"])]),s("div",Z,[l(v,{modelValue:b.selectedPaymentMethod,"onUpdate:modelValue":t[4]||(t[4]=e=>b.selectedPaymentMethod=e),name:"paymentMethod",label:a.$trans("finance.payment_method.payment_method"),options:c.paymentMethods,"object-prop":!0,"label-prop":"name","value-prop":"uuid",error:r(o).paymentMethod,"onUpdate:error":t[5]||(t[5]=e=>r(o).paymentMethod=e),onChange:S},null,8,["modelValue","label","options","error"])]),b.selectedPaymentMethod?(g(),D(w,{key:0,"selected-payment-method":b.selectedPaymentMethod,instrumentNumber:n.instrumentNumber,"onUpdate:instrumentNumber":t[6]||(t[6]=e=>n.instrumentNumber=e),instrumentDate:n.instrumentDate,"onUpdate:instrumentDate":t[7]||(t[7]=e=>n.instrumentDate=e),clearingDate:n.clearingDate,"onUpdate:clearingDate":t[8]||(t[8]=e=>n.clearingDate=e),bankDetail:n.bankDetail,"onUpdate:bankDetail":t[9]||(t[9]=e=>n.bankDetail=e),referenceNumber:n.referenceNumber,"onUpdate:referenceNumber":t[10]||(t[10]=e=>n.referenceNumber=e),formErrors:r(o),"onUpdate:formErrors":t[11]||(t[11]=e=>K(o)?o.value=e:null)},null,8,["selected-payment-method","instrumentNumber","instrumentDate","clearingDate","bankDetail","referenceNumber","formErrors"])):J("",!0)]),(g(!0),B(F,null,z(n.records,(e,m)=>(g(),D(E,{class:"mt-4",key:e.uuid},{legend:f(()=>[P(M(m+1)+". ",1),s("span",{class:"text-danger ml-2 cursor-pointer",onClick:i=>j(m)},t[14]||(t[14]=[s("i",{class:"fas fa-times-circle"},null,-1)]),8,x)]),default:f(()=>[s("div",ee,[s("div",te,[l(v,{modelValue:e.head,"onUpdate:modelValue":i=>e.head=i,name:`records.${m}.head`,label:a.$trans("finance.fee_head.fee_head"),options:c.feeHeads,"label-prop":"name","value-prop":"uuid",error:r(o)[`records.${m}.head`],"onUpdate:error":i=>r(o)[`records.${m}.head`]=i},null,8,["modelValue","onUpdate:modelValue","name","label","options","error","onUpdate:error"])]),s("div",ne,[l(A,{currency:"",modelValue:e.amount,"onUpdate:modelValue":i=>e.amount=i,name:`records.${m}.amount`,label:a.$trans("student.fee_refund.props.amount"),error:r(o)[`records.${m}.amount`],"onUpdate:error":i=>r(o)[`records.${m}.amount`]=i},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])])])]),_:2},1024))),128)),s("div",ae,[l(O,{design:"primary",onClick:$,class:"cursor-pointer"},{default:f(()=>[P(M(a.$trans("global.add",{attribute:a.$trans("general.record")})),1)]),_:1})]),s("div",re,[s("div",oe,[l(q,{modelValue:n.remarks,"onUpdate:modelValue":t[12]||(t[12]=e=>n.remarks=e),name:"remarks",label:a.$trans("student.payment.props.remarks"),error:r(o).remarks,"onUpdate:error":t[13]||(t[13]=e=>r(o).remarks=e)},null,8,["modelValue","label","error"])])])]),_:1},8,["uuid","module-uuid","form","redirect"])}}}),le={name:"StudentFeeRefundAction"},me=Object.assign(le,{props:{student:{type:Object,default(){return{}}}},setup(y){const p=N();return(u,k)=>{const _=d("PageHeaderAction"),o=d("PageHeader"),c=d("ParentTransition");return g(),B(F,null,[l(o,{title:u.$trans(r(p).meta.trans,{attribute:u.$trans(r(p).meta.label)}),navs:[{label:u.$trans("student.student"),path:"StudentList"},{label:y.student.contact.name,path:{name:"StudentShow",params:{uuid:y.student.uuid}}},{label:u.$trans("student.fee_refund.fee_refund"),path:{name:"StudentFeeRefund",params:{uuid:y.student.uuid}}}]},{default:f(()=>[l(_,{name:"StudentFeeRefund",title:u.$trans("student.fee_refund.fee_refund"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(c,{appear:"",visibility:!0},{default:f(()=>[l(de)]),_:1})],64)}}});export{me as default};
