import{u as R,h as T,j,g as y,m as A,l as H,r as t,q as f,o as r,w as e,e as n,f as o,a as L,F as M,v as N,s as c,t as m,b as O}from"./app-DvIo72ZO.js";const V={name:"StudentProfileEditRequestList"},x=Object.assign(V,{props:{student:{type:Object,default(){return{}}}},setup(s){const h=R(),_=T(),b=j("emitter"),v=y("student.allowStudentToSubmitContactEditRequest");let g=[];v.value&&g.unshift("new");const w="student/profileEditRequest/",p=A(!1),i=H({}),S=u=>{Object.assign(i,u)};return(u,d)=>{const C=t("PageHeaderAction"),P=t("PageHeader"),l=t("DataCell"),k=t("BaseBadge"),q=t("FloatingMenuItem"),D=t("FloatingMenu"),$=t("DataRow"),E=t("DataTable"),F=t("ParentTransition"),B=t("ListItem");return r(),f(B,{"init-url":w,uuid:o(h).params.uuid,onSetItems:S},{header:e(()=>[s.student.uuid?(r(),f(P,{key:0,title:u.$trans("student.edit_request.edit_request"),navs:[{label:u.$trans("student.student"),path:"Student"},{label:s.student.contact.name,path:{name:"StudentShow",params:{uuid:s.student.uuid}}}]},{default:e(()=>[n(C,{url:`students/${s.student.uuid}/edit-requests/`,name:"StudentProfileEditRequest",title:u.$trans("student.edit_request.edit_request"),actions:o(g),onToggleFilter:d[0]||(d[0]=a=>p.value=!p.value)},null,8,["url","title","actions"])]),_:1},8,["title","navs"])):O("",!0)]),default:e(()=>[n(F,{appear:"",visibility:!0},{default:e(()=>[n(E,{header:i.headers,meta:i.meta,module:"student.edit_request",onRefresh:d[1]||(d[1]=a=>o(b).emit("listItems"))},{default:e(()=>[(r(!0),L(M,null,N(i.data,a=>(r(),f($,{key:a.uuid,onDoubleClick:I=>o(_).push({name:"StudentProfileEditRequestShow",params:{uuid:s.student.uuid,muuid:a.uuid}})},{default:e(()=>[n(l,{name:"user"},{default:e(()=>[c(m(a.user.profile.name),1)]),_:2},1024),n(l,{name:"status"},{default:e(()=>[n(k,{design:a.status.color},{default:e(()=>[c(m(a.status.label),1)]),_:2},1032,["design"])]),_:2},1024),n(l,{name:"createdAt"},{default:e(()=>[c(m(a.createdAt.formatted),1)]),_:2},1024),n(l,{name:"action"},{default:e(()=>[n(D,null,{default:e(()=>[n(q,{icon:"fas fa-arrow-circle-right",onClick:I=>o(_).push({name:"StudentProfileEditRequestShow",params:{uuid:s.student.uuid,muuid:a.uuid}})},{default:e(()=>[c(m(u.$trans("general.show")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1},8,["uuid"])}}});export{x as default};
