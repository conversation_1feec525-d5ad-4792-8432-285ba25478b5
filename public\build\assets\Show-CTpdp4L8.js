import{i as S,u as v,h as A,l as C,r as n,a as P,o as c,e as o,w as e,f as u,q as d,b as m,d as T,s,t as r,y as V,F as H}from"./app-DvIo72ZO.js";const I={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},N={name:"FinanceFeeGroupShow"},j=Object.assign(N,{setup(D){S();const p=v(),f=A(),_="finance/feeGroup/",a=C({...initiFeeGroup}),g=t=>{Object.assign(a,t)};return(t,i)=>{const b=n("PageHeaderAction"),B=n("PageHeader"),F=n("TextMuted"),l=n("BaseDataView"),$=n("BaseButton"),h=n("ShowButton"),w=n("BaseCard"),k=n("ShowItem"),y=n("ParentTransition");return c(),P(H,null,[o(B,{title:t.$trans(u(p).meta.trans,{attribute:t.$trans(u(p).meta.label)}),navs:[{label:t.$trans("finance.finance"),path:"Finance"},{label:t.$trans("finance.fee_group.fee_group"),path:"FinanceFeeGroupList"}]},{default:e(()=>[o(b,{name:"FinanceFeeGroup",title:t.$trans("finance.fee_group.fee_group"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),o(y,{appear:"",visibility:!0},{default:e(()=>[o(k,{"init-url":_,uuid:u(p).params.uuid,onSetItem:g,onRedirectTo:i[1]||(i[1]=G=>u(f).push({name:"FinanceFeeGroup"}))},{default:e(()=>[a.uuid?(c(),d(w,{key:0},{title:e(()=>[s(r(a.name),1)]),footer:e(()=>[o(h,null,{default:e(()=>[u(V)("fee-group:edit")?(c(),d($,{key:0,design:"primary",onClick:i[0]||(i[0]=G=>u(f).push({name:"FinanceFeeGroupEdit",params:{uuid:a.uuid}}))},{default:e(()=>[s(r(t.$trans("general.edit")),1)]),_:1})):m("",!0)]),_:1})]),default:e(()=>[T("dl",I,[o(l,{label:t.$trans("finance.fee_group.props.name")},{default:e(()=>[s(r(a.name)+" ",1),a.pgAccount?(c(),d(F,{key:0,block:""},{default:e(()=>[s(r(a.pgAccount),1)]),_:1})):m("",!0)]),_:1},8,["label"]),o(l,{class:"col-span-1 sm:col-span-2",label:t.$trans("finance.fee_group.props.description")},{default:e(()=>[s(r(a.description),1)]),_:1},8,["label"]),o(l,{label:t.$trans("general.created_at")},{default:e(()=>[s(r(a.createdAt.formatted),1)]),_:1},8,["label"]),o(l,{label:t.$trans("general.updated_at")},{default:e(()=>[s(r(a.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):m("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{j as default};
