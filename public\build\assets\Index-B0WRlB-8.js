import{u as V,l as H,n as P,r as s,q as _,o as p,w as e,d as F,b as $,s as u,t as l,e as o,h as A,j,y as g,m as q,f as n,a as O,F as U,v as E}from"./app-DvIo72ZO.js";const z={class:"grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={__name:"Filter",emits:["hide"],setup(B,{emit:h}){const v=V(),D=h,b={students:[],startDate:"",endDate:""},m=H({...b}),c=H({students:[],isLoaded:!v.query.students});return P(async()=>{c.students=v.query.students?v.query.students.split(","):[],c.isLoaded=!0}),(f,d)=>{const i=s("BaseSelectSearch"),r=s("DatePicker"),w=s("FilterForm");return p(),_(w,{"init-form":b,form:m,multiple:["students"],onHide:d[3]||(d[3]=a=>D("hide"))},{default:e(()=>[F("div",z,[F("div",G,[c.isLoaded?(p(),_(i,{key:0,multiple:"",name:"students",label:f.$trans("global.select",{attribute:f.$trans("student.student")}),modelValue:m.students,"onUpdate:modelValue":d[0]||(d[0]=a=>m.students=a),"value-prop":"uuid","init-search":c.students,"search-key":"name","search-action":"student/summary"},{selectedOption:e(a=>[u(l(a.value.name)+" ("+l(a.value.codeNumber)+") ",1)]),listOption:e(a=>[u(l(a.option.name)+" ("+l(a.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):$("",!0)]),F("div",J,[o(r,{start:m.startDate,"onUpdate:start":d[1]||(d[1]=a=>m.startDate=a),end:m.endDate,"onUpdate:end":d[2]||(d[2]=a=>m.endDate=a),name:"dateBetween",as:"range",label:f.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},Q={name:"HostelRoomAllocationList"},X=Object.assign(Q,{setup(B){const h=A(),v=j("emitter");let D=["filter"];g("hostel-room-allocation:create")&&D.unshift("create");let b=[];g("hostel-room-allocation:export")&&(b=["print","pdf","excel"]);const m="hostel/roomAllocation/",c=q(!1),f=H({}),d=i=>{Object.assign(f,i)};return(i,r)=>{const w=s("PageHeaderAction"),a=s("PageHeader"),R=s("ParentTransition"),k=s("DataCell"),I=s("TextMuted"),y=s("FloatingMenuItem"),N=s("FloatingMenu"),S=s("DataRow"),T=s("BaseButton"),L=s("DataTable"),M=s("ListItem");return p(),_(M,{"init-url":m,onSetItems:d},{header:e(()=>[o(a,{title:i.$trans("hostel.room_allocation.room_allocation"),navs:[{label:i.$trans("hostel.hostel"),path:"Hostel"}]},{default:e(()=>[o(w,{url:"hostel/room-allocations/",name:"HostelRoomAllocation",title:i.$trans("hostel.room_allocation.room_allocation"),actions:n(D),"dropdown-actions":n(b),onToggleFilter:r[0]||(r[0]=t=>c.value=!c.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[o(R,{appear:"",visibility:c.value},{default:e(()=>[o(K,{onRefresh:r[1]||(r[1]=t=>n(v).emit("listItems")),onHide:r[2]||(r[2]=t=>c.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[o(R,{appear:"",visibility:!0},{default:e(()=>[o(L,{header:f.headers,meta:f.meta,module:"hostel.room_allocation",onRefresh:r[4]||(r[4]=t=>n(v).emit("listItems"))},{actionButton:e(()=>[n(g)("hostel-room-allocation:create")?(p(),_(T,{key:0,onClick:r[3]||(r[3]=t=>n(h).push({name:"HostelRoomAllocationCreate"}))},{default:e(()=>[u(l(i.$trans("global.add",{attribute:i.$trans("hostel.room_allocation.room_allocation")})),1)]),_:1})):$("",!0)]),default:e(()=>[(p(!0),O(U,null,E(f.data,t=>(p(),_(S,{key:t.uuid,onDoubleClick:C=>n(h).push({name:"HostelRoomAllocationShow",params:{uuid:t.uuid}})},{default:e(()=>[o(k,{name:"room"},{default:e(()=>[u(l(t.room.fullName),1)]),_:2},1024),o(k,{name:"student"},{default:e(()=>[u(l(t.student.name)+" ",1),o(I,{block:""},{default:e(()=>[u(l(t.student.codeNumber),1)]),_:2},1024)]),_:2},1024),o(k,{name:"period"},{default:e(()=>[u(l(t.period),1)]),_:2},1024),o(k,{name:"createdAt"},{default:e(()=>[u(l(t.createdAt.formatted),1)]),_:2},1024),o(k,{name:"action"},{default:e(()=>[o(N,null,{default:e(()=>[o(y,{icon:"fas fa-arrow-circle-right",onClick:C=>n(h).push({name:"HostelRoomAllocationShow",params:{uuid:t.uuid}})},{default:e(()=>[u(l(i.$trans("general.show")),1)]),_:2},1032,["onClick"]),n(g)("hostel-room-allocation:edit")?(p(),_(y,{key:0,icon:"fas fa-edit",onClick:C=>n(h).push({name:"HostelRoomAllocationEdit",params:{uuid:t.uuid}})},{default:e(()=>[u(l(i.$trans("general.edit")),1)]),_:2},1032,["onClick"])):$("",!0),n(g)("hostel-room-allocation:create")?(p(),_(y,{key:1,icon:"fas fa-copy",onClick:C=>n(h).push({name:"HostelRoomAllocationDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[u(l(i.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):$("",!0),n(g)("hostel-room-allocation:delete")?(p(),_(y,{key:2,icon:"fas fa-trash",onClick:C=>n(v).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[u(l(i.$trans("general.delete")),1)]),_:2},1032,["onClick"])):$("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{X as default};
