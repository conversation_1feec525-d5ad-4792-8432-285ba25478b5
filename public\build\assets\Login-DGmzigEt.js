import{u as P,i as V,j as A,y as C,c as j,r as o,a as m,o as l,q as u,b as y,e as n,w as a,f as t,s as r,t as d,d as H,F as f,v as N}from"./app-DvIo72ZO.js";const S={key:1,class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-3"},D={class:"space-x-1"},L={name:"EmployeeShowLogin"},O=Object.assign(L,{props:{employee:{type:Object,default(){return{}}}},setup(s){const b=P();V();const e=A("$trans"),p=s;let g=[];C("employee:edit")&&g.push({label:e("global.edit",{attribute:e("contact.login.login")}),path:{name:"EmployeeEditLogin",params:{uuid:p.employee.uuid}}});const i=j(()=>p.employee.contact.user);return(T,_)=>{const x=o("PageHeaderAction"),B=o("PageHeader"),v=o("BaseAlert"),c=o("BaseDataView"),h=o("BaseBadge"),k=o("BaseCard"),w=o("ParentTransition");return l(),m(f,null,[s.employee.uuid?(l(),u(B,{key:0,title:t(e)(t(b).meta.label),navs:[{label:t(e)("employee.employee"),path:"Employee"},{label:s.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:s.employee.uuid}}}]},{default:a(()=>[n(x,{"additional-actions":t(g)},null,8,["additional-actions"])]),_:1},8,["title","navs"])):y("",!0),n(w,{appear:"",visibility:!0},{default:a(()=>[s.employee.uuid?(l(),u(k,{key:0},{default:a(()=>[i.value?(l(),m("dl",S,[n(c,{label:t(e)("contact.login.props.email")},{default:a(()=>[r(d(i.value.email),1)]),_:1},8,["label"]),n(c,{label:t(e)("contact.login.props.username")},{default:a(()=>[r(d(i.value.username),1)]),_:1},8,["label"]),n(c,{label:t(e)("contact.login.props.password")},{default:a(()=>_[0]||(_[0]=[r(" xxxxxxxxx ")])),_:1},8,["label"]),n(c,{label:t(e)("team.config.role.role")},{default:a(()=>[H("div",D,[(l(!0),m(f,null,N(i.value.roles,E=>(l(),u(h,{design:"primary"},{default:a(()=>[r(d(E.label),1)]),_:2},1024))),256))])]),_:1},8,["label"])])):(l(),u(v,{key:0,design:"error"},{default:a(()=>[r(d(t(e)("contact.login.no_login_found")),1)]),_:1}))]),_:1})):y("",!0)]),_:1})],64)}}});export{O as default};
