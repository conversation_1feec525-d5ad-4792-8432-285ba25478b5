import{l as F,r as s,q as C,o as b,w as e,d as g,e as t,h as H,j as P,m as A,f as m,a as R,F as T,v as j,s as r,t as p}from"./app-DvIo72ZO.js";import{_ as M}from"./ModuleDropdown-DdkPlpRE.js";const S={class:"grid grid-cols-3 gap-6"},U={class:"col-span-3 sm:col-span-1"},N={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},q={__name:"Filter",emits:["hide"],setup(L,{emit:d}){const c=d,_={name:"",code:"",alias:""},i=F({..._});return(u,l)=>{const f=s("BaseInput"),o=s("FilterForm");return b(),C(o,{"init-form":_,form:i,onHide:l[3]||(l[3]=a=>c("hide"))},{default:e(()=>[g("div",S,[g("div",U,[t(f,{type:"text",modelValue:i.name,"onUpdate:modelValue":l[0]||(l[0]=a=>i.name=a),name:"name",label:u.$trans("employee.leave.type.props.name")},null,8,["modelValue","label"])]),g("div",N,[t(f,{type:"text",modelValue:i.code,"onUpdate:modelValue":l[1]||(l[1]=a=>i.code=a),name:"code",label:u.$trans("employee.leave.type.props.code")},null,8,["modelValue","label"])]),g("div",O,[t(f,{type:"text",modelValue:i.alias,"onUpdate:modelValue":l[2]||(l[2]=a=>i.alias=a),name:"alias",label:u.$trans("employee.leave.type.props.alias")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},z={name:"EmployeeLeaveTypeList"},K=Object.assign(z,{setup(L){const d=H(),c=P("emitter");let _=["create","filter"];const i="employee/leave/type/",u=A(!1),l=F({}),f=o=>{Object.assign(l,o)};return(o,a)=>{const V=s("PageHeaderAction"),h=s("PageHeader"),k=s("ParentTransition"),y=s("DataCell"),v=s("FloatingMenuItem"),B=s("FloatingMenu"),E=s("DataRow"),I=s("BaseButton"),w=s("DataTable"),D=s("ListItem");return b(),C(D,{"init-url":i,onSetItems:f},{header:e(()=>[t(h,{title:o.$trans("employee.leave.type.type"),navs:[{label:o.$trans("employee.employee"),path:"Employee"},{label:o.$trans("employee.leave.leave"),path:"EmployeeLeave"}]},{default:e(()=>[t(V,{url:"employee/leave/types/",name:"EmployeeLeaveType",title:o.$trans("employee.leave.type.type"),actions:m(_),"dropdown-actions":["print","pdf","excel"],onToggleFilter:a[0]||(a[0]=n=>u.value=!u.value)},{moduleOption:e(()=>[t(M)]),_:1},8,["title","actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(k,{appear:"",visibility:u.value},{default:e(()=>[t(q,{onRefresh:a[1]||(a[1]=n=>m(c).emit("listItems")),onHide:a[2]||(a[2]=n=>u.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(k,{appear:"",visibility:!0},{default:e(()=>[t(w,{header:l.headers,meta:l.meta,module:"employee.leave.type",onRefresh:a[4]||(a[4]=n=>m(c).emit("listItems"))},{actionButton:e(()=>[t(I,{onClick:a[3]||(a[3]=n=>m(d).push({name:"EmployeeLeaveTypeCreate"}))},{default:e(()=>[r(p(o.$trans("global.add",{attribute:o.$trans("employee.leave.type.type")})),1)]),_:1})]),default:e(()=>[(b(!0),R(T,null,j(l.data,n=>(b(),C(E,{key:n.uuid,onDoubleClick:$=>m(d).push({name:"EmployeeLeaveTypeShow",params:{uuid:n.uuid}})},{default:e(()=>[t(y,{name:"name"},{default:e(()=>[r(p(n.name),1)]),_:2},1024),t(y,{name:"code"},{default:e(()=>[r(p(n.code),1)]),_:2},1024),t(y,{name:"alias"},{default:e(()=>[r(p(n.alias),1)]),_:2},1024),t(y,{name:"createdAt"},{default:e(()=>[r(p(n.createdAt.formatted),1)]),_:2},1024),t(y,{name:"action"},{default:e(()=>[t(B,null,{default:e(()=>[t(v,{icon:"fas fa-arrow-circle-right",onClick:$=>m(d).push({name:"EmployeeLeaveTypeShow",params:{uuid:n.uuid}})},{default:e(()=>[r(p(o.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(v,{icon:"fas fa-edit",onClick:$=>m(d).push({name:"EmployeeLeaveTypeEdit",params:{uuid:n.uuid}})},{default:e(()=>[r(p(o.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t(v,{icon:"fas fa-copy",onClick:$=>m(d).push({name:"EmployeeLeaveTypeDuplicate",params:{uuid:n.uuid}})},{default:e(()=>[r(p(o.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t(v,{icon:"fas fa-trash",onClick:$=>m(c).emit("deleteItem",{uuid:n.uuid})},{default:e(()=>[r(p(o.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{K as default};
