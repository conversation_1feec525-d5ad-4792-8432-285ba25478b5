<template>
    <PageHeader
        :title="$trans('exam.proctoring.config.global_title') || 'Proctoring Configuration'"
        :navs="[
            { label: $trans('exam.exam'), path: 'Exam' },
            { label: $trans('exam.config.config'), path: 'ExamConfig' },
        ]"
    >
        <PageHeaderAction
            name="ExamConfig"
            :title="$trans('exam.config.config')"
            :actions="['list']"
        />
    </PageHeader>

    <ParentTransition appear :visibility="true">
        <FormAction
            :pre-requisites="false"
            :init-url="initUrl"
            data-fetch="exam"
            :init-form="initForm"
            :form="form"
            action="store"
            stay-on
            redirect="ExamConfig"
        >
            <!-- Global Proctoring Settings -->
            <div class="space-y-8">
                <!-- Enable/Disable Proctoring -->
                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
                    <BaseSwitch
                        vertical
                        v-model="form.enableGlobalProctoring"
                        name="enableGlobalProctoring"
                        :label="$trans('exam.proctoring.config.enable_global') || 'Enable Proctoring System'"
                        :description="$trans('exam.proctoring.config.enable_global_help') || 'Enable proctoring features across the entire system'"
                    />
                </div>

                <!-- Default Configuration -->
                <div v-if="form.enableGlobalProctoring" class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        {{ $trans("exam.proctoring.config.default_settings") || "Default Proctoring Settings" }}
                    </h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">
                        {{ $trans("exam.proctoring.config.default_settings_help") || "These settings will be applied as defaults when creating new exams with proctoring enabled." }}
                    </p>
                    
                    <ProctorConfigurationPanel
                        v-model="defaultProctorConfig"
                    />
                </div>

                <!-- System Requirements -->
                <div v-if="form.enableGlobalProctoring" class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        {{ $trans("exam.proctoring.config.system_requirements") || "System Requirements" }}
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Browser Requirements -->
                        <div class="space-y-4">
                            <h4 class="font-medium text-gray-900 dark:text-white">
                                {{ $trans("exam.proctoring.config.browser_requirements") || "Browser Requirements" }}
                            </h4>
                            <div class="space-y-3">
                                <BaseCheckbox
                                    v-model="form.requireModernBrowser"
                                    :label="$trans('exam.proctoring.config.require_modern_browser') || 'Require Modern Browser'"
                                    :description="$trans('exam.proctoring.config.require_modern_browser_help') || 'Block access from outdated browsers'"
                                />
                                <BaseCheckbox
                                    v-model="form.requireHttps"
                                    :label="$trans('exam.proctoring.config.require_https') || 'Require HTTPS'"
                                    :description="$trans('exam.proctoring.config.require_https_help') || 'Ensure secure connection for media access'"
                                />
                                <BaseCheckbox
                                    v-model="form.blockIncognitoMode"
                                    :label="$trans('exam.proctoring.config.block_incognito') || 'Block Incognito/Private Mode'"
                                    :description="$trans('exam.proctoring.config.block_incognito_help') || 'Prevent access from private browsing modes'"
                                />
                            </div>
                        </div>

                        <!-- Hardware Requirements -->
                        <div class="space-y-4">
                            <h4 class="font-medium text-gray-900 dark:text-white">
                                {{ $trans("exam.proctoring.config.hardware_requirements") || "Hardware Requirements" }}
                            </h4>
                            <div class="space-y-3">
                                <BaseCheckbox
                                    v-model="form.requireWebcam"
                                    :label="$trans('exam.proctoring.config.require_webcam') || 'Require Webcam'"
                                    :description="$trans('exam.proctoring.config.require_webcam_help') || 'Mandatory webcam access for all proctored exams'"
                                />
                                <BaseCheckbox
                                    v-model="form.requireMicrophone"
                                    :label="$trans('exam.proctoring.config.require_microphone') || 'Require Microphone'"
                                    :description="$trans('exam.proctoring.config.require_microphone_help') || 'Mandatory microphone access for audio monitoring'"
                                />
                                <BaseCheckbox
                                    v-model="form.requireFullscreen"
                                    :label="$trans('exam.proctoring.config.require_fullscreen') || 'Require Fullscreen'"
                                    :description="$trans('exam.proctoring.config.require_fullscreen_help') || 'Force fullscreen mode for all proctored exams'"
                                />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Storage and Retention -->
                <div v-if="form.enableGlobalProctoring" class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        {{ $trans("exam.proctoring.config.storage_retention") || "Storage & Retention" }}
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <BaseInput
                                type="number"
                                v-model="form.mediaRetentionDays"
                                name="mediaRetentionDays"
                                :label="$trans('exam.proctoring.config.media_retention_days') || 'Media Retention (Days)'"
                                :description="$trans('exam.proctoring.config.media_retention_days_help') || 'How long to keep captured images and recordings'"
                                min="1"
                                max="365"
                            />
                        </div>
                        <div class="space-y-4">
                            <BaseInput
                                type="number"
                                v-model="form.logRetentionDays"
                                name="logRetentionDays"
                                :label="$trans('exam.proctoring.config.log_retention_days') || 'Log Retention (Days)'"
                                :description="$trans('exam.proctoring.config.log_retention_days_help') || 'How long to keep proctoring event logs'"
                                min="1"
                                max="1095"
                            />
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <BaseCheckbox
                            v-model="form.autoCleanupExpiredData"
                            :label="$trans('exam.proctoring.config.auto_cleanup') || 'Auto Cleanup Expired Data'"
                            :description="$trans('exam.proctoring.config.auto_cleanup_help') || 'Automatically delete expired proctoring data'"
                        />
                    </div>
                </div>

                <!-- Privacy and Compliance -->
                <div v-if="form.enableGlobalProctoring" class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        {{ $trans("exam.proctoring.config.privacy_compliance") || "Privacy & Compliance" }}
                    </h3>
                    
                    <div class="space-y-4">
                        <BaseCheckbox
                            v-model="form.requirePrivacyConsent"
                            :label="$trans('exam.proctoring.config.require_privacy_consent') || 'Require Privacy Consent'"
                            :description="$trans('exam.proctoring.config.require_privacy_consent_help') || 'Students must explicitly consent to proctoring before exam'"
                        />
                        
                        <div v-if="form.requirePrivacyConsent" class="ml-6">
                            <BaseTextarea
                                v-model="form.privacyConsentText"
                                name="privacyConsentText"
                                :label="$trans('exam.proctoring.config.privacy_consent_text') || 'Privacy Consent Text'"
                                :placeholder="$trans('exam.proctoring.config.privacy_consent_placeholder') || 'Enter the privacy consent message students will see...'"
                                rows="4"
                            />
                        </div>
                        
                        <BaseCheckbox
                            v-model="form.allowDataExport"
                            :label="$trans('exam.proctoring.config.allow_data_export') || 'Allow Data Export'"
                            :description="$trans('exam.proctoring.config.allow_data_export_help') || 'Allow students to request export of their proctoring data'"
                        />
                        
                        <BaseCheckbox
                            v-model="form.allowDataDeletion"
                            :label="$trans('exam.proctoring.config.allow_data_deletion') || 'Allow Data Deletion'"
                            :description="$trans('exam.proctoring.config.allow_data_deletion_help') || 'Allow students to request deletion of their proctoring data'"
                        />
                    </div>
                </div>

                <!-- Notification Settings -->
                <div v-if="form.enableGlobalProctoring" class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        {{ $trans("exam.proctoring.config.notifications") || "Notification Settings" }}
                    </h3>
                    
                    <div class="space-y-4">
                        <BaseCheckbox
                            v-model="form.notifyOnCriticalViolations"
                            :label="$trans('exam.proctoring.config.notify_critical_violations') || 'Notify on Critical Violations'"
                            :description="$trans('exam.proctoring.config.notify_critical_violations_help') || 'Send real-time notifications for critical proctoring violations'"
                        />
                        
                        <BaseCheckbox
                            v-model="form.notifyOnExamCompletion"
                            :label="$trans('exam.proctoring.config.notify_exam_completion') || 'Notify on Exam Completion'"
                            :description="$trans('exam.proctoring.config.notify_exam_completion_help') || 'Send summary notifications when proctored exams are completed'"
                        />
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <BaseInput
                                type="email"
                                v-model="form.notificationEmail"
                                name="notificationEmail"
                                :label="$trans('exam.proctoring.config.notification_email') || 'Notification Email'"
                                :placeholder="$trans('exam.proctoring.config.notification_email_placeholder') || '<EMAIL>'"
                            />
                            <BaseSelect
                                v-model="form.notificationFrequency"
                                name="notificationFrequency"
                                :label="$trans('exam.proctoring.config.notification_frequency') || 'Notification Frequency'"
                                :options="notificationFrequencyOptions"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </FormAction>
    </ParentTransition>
</template>

<script>
export default {
    name: "ExamProctorConfig",
}
</script>

<script setup>
import { reactive, watch } from "vue"
import { getFormErrors } from "@core/helpers/action"
import ProctorConfigurationPanel from "@core/components/Exam/ProctorConfigurationPanel.vue"

const initForm = {
    enableGlobalProctoring: false,
    requireModernBrowser: true,
    requireHttps: true,
    blockIncognitoMode: true,
    requireWebcam: true,
    requireMicrophone: true,
    requireFullscreen: true,
    mediaRetentionDays: 90,
    logRetentionDays: 365,
    autoCleanupExpiredData: true,
    requirePrivacyConsent: true,
    privacyConsentText: "By proceeding with this exam, you consent to being monitored through webcam, microphone, and screen recording for academic integrity purposes. This data will be used solely for exam proctoring and will be retained according to our data retention policy.",
    allowDataExport: true,
    allowDataDeletion: false,
    notifyOnCriticalViolations: true,
    notifyOnExamCompletion: false,
    notificationEmail: "",
    notificationFrequency: "immediate"
}

const initUrl = "exam/config/"
const formErrors = getFormErrors(initUrl)

const form = reactive({ ...initForm })

// Default proctoring configuration
const defaultProctorConfig = reactive({
    enableProctoring: true,
    proctorConfig: {
        webcam_monitoring: true,
        microphone_monitoring: true,
        screen_recording: true,
        fullscreen_enforcement: true,
        copy_paste_blocking: true,
        face_detection: true,
        capture_interval_seconds: 30,
        audio_threshold_db: -40,
        audio_recording_duration_seconds: 15,
        max_face_detection_failures: 5,
        allow_tab_switching: false,
        auto_submit_on_violations: false,
        custom_instructions: ''
    }
})

// Notification frequency options
const notificationFrequencyOptions = [
    { label: 'Immediate', value: 'immediate' },
    { label: 'Hourly', value: 'hourly' },
    { label: 'Daily', value: 'daily' },
    { label: 'Weekly', value: 'weekly' }
]

// Watch for changes in default proctor config and sync with form
watch(defaultProctorConfig, (newValue) => {
    form.defaultProctorConfig = newValue.proctorConfig
}, { deep: true })
</script>
