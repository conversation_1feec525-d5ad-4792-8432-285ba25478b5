import{i as O,u as q,h as U,l as z,r as i,a as f,o as s,e as t,w as e,f as u,q as c,b as m,B as v,d as g,s as o,t as l,y as C,F as b,v as V}from"./app-DvIo72ZO.js";const G={class:"flex items-center justify-center gap-2"},J={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},K={class:"mt-8 grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},Q={class:"space-y-2"},W={class:"flex justify-center gap-2"},X={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},Y={name:"CommunicationAnnouncementShow"},ee=Object.assign(Y,{setup(Z){O();const y=q(),B=U(),L={},T="communication/announcement/",n=z({...L}),P=a=>{Object.assign(n,a)},k=a=>n.audiences.filter(d=>d.type===a);return(a,d)=>{const S=i("PageHeaderAction"),I=i("PageHeader"),r=i("BaseDataView"),w=i("ListMedia"),$=i("BaseCard"),p=i("ListItemView"),h=i("TextMuted"),N=i("ListContainerVertical"),D=i("BaseBadge"),j=i("ViewLog"),H=i("BaseButton"),M=i("ShowButton"),R=i("DetailLayoutVertical"),E=i("ShowItem"),F=i("ParentTransition");return s(),f(b,null,[t(I,{title:a.$trans(u(y).meta.trans,{attribute:a.$trans(u(y).meta.label)}),navs:[{label:a.$trans("communication.communication"),path:"Communication"},{label:a.$trans("communication.announcement.announcement"),path:"CommunicationAnnouncement"}]},{default:e(()=>[t(S,{name:"CommunicationAnnouncement",title:a.$trans("communication.announcement.announcement"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(F,{appear:"",visibility:!0},{default:e(()=>[t(E,{"init-url":T,uuid:u(y).params.uuid,"module-uuid":u(y).params.muuid,onSetItem:P,onRedirectTo:d[1]||(d[1]=_=>u(B).push({name:"CommunicationAnnouncement",params:{uuid:n.uuid}}))},{default:e(()=>[n.uuid&&u(v)(["student","guardian"],"any")?(s(),c($,{key:0},{title:e(()=>[g("div",G,l(n.title),1)]),default:e(()=>[g("dl",J,[t(r,{label:a.$trans("communication.announcement.props.code_number")},{default:e(()=>[o(l(n.codeNumber),1)]),_:1},8,["label"]),t(r,{label:a.$trans("communication.announcement.props.type")},{default:e(()=>[o(l(n.type.name),1)]),_:1},8,["label"]),t(r,{label:a.$trans("general.date"),class:"col-span-1 sm:col-span-2"},{default:e(()=>[o(l(n.publishedAt.formatted),1)]),_:1},8,["label"])]),g("dl",K,[t(r,{class:"col-span-1 sm:col-span-2",html:""},{default:e(()=>[o(l(n.description),1)]),_:1}),n.media.length>0?(s(),c(r,{key:0,class:"col-span-1 sm:col-span-2"},{default:e(()=>[t(w,{media:n.media,url:`/app/communication/announcements/${n.uuid}/`},null,8,["media","url"])]),_:1})):m("",!0)])]),_:1})):m("",!0),n.uuid&&!u(v)(["student","guardian"],"any")?(s(),c(R,{key:1},{detail:e(()=>[g("div",Q,[t($,{"no-padding":"","no-content-padding":""},{title:e(()=>[o(" #"+l(n.codeNumber),1)]),action:e(()=>d[2]||(d[2]=[])),default:e(()=>[t(N,null,{default:e(()=>[t(p,{label:a.$trans("communication.announcement.props.title")},{default:e(()=>[o(l(n.title),1)]),_:1},8,["label"]),t(p,{label:a.$trans("communication.announcement.type.type")},{default:e(()=>[o(l(n.type.name),1)]),_:1},8,["label"]),t(p,{label:a.$trans("employee.employee")},{default:e(()=>{var _;return[o(l(((_=n.employee)==null?void 0:_.name)||"-")+" ",1),t(h,{block:""},{default:e(()=>{var A;return[o(l(((A=n.employee)==null?void 0:A.designation)||""),1)]}),_:1})]}),_:1},8,["label"]),n.isPublic?m("",!0):(s(),f(b,{key:0},[t(p,{label:a.$trans("communication.announcement.props.audience")},{default:e(()=>[o(l(n.studentAudienceType.label)+" ",1),(s(!0),f(b,null,V(k("student"),_=>(s(),c(h,{block:""},{default:e(()=>[o(l(_.name),1)]),_:2},1024))),256))]),_:1},8,["label"]),t(p,{label:a.$trans("communication.announcement.props.audience")},{default:e(()=>[n.employeeAudienceType.value?(s(),f(b,{key:0},[o(l(n.employeeAudienceType.label)+" ",1),(s(!0),f(b,null,V(k("employee"),_=>(s(),c(h,{block:""},{default:e(()=>[o(l(_.name),1)]),_:2},1024))),256))],64)):(s(),f(b,{key:1},[o("-")],64))]),_:1},8,["label"]),t(p,{label:a.$trans("general.created_at")},{default:e(()=>[o(l(n.createdAt.formatted),1)]),_:1},8,["label"]),t(p,{label:a.$trans("general.updated_at")},{default:e(()=>[o(l(n.updatedAt.formatted),1)]),_:1},8,["label"])],64)),t(p,{label:a.$trans("communication.announcement.props.published_at")},{default:e(()=>[o(l(n.publishedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})])]),default:e(()=>[n.uuid?(s(),c($,{key:0},{title:e(()=>[g("div",W,[o(l(n.title)+" ",1),n.isPublic?(s(),c(D,{key:0,design:"primary"},{default:e(()=>[o(l(a.$trans("general.public")),1)]),_:1})):m("",!0)])]),footer:e(()=>[t(M,null,{default:e(()=>[u(C)("announcement:edit")?(s(),c(H,{key:0,design:"primary",onClick:d[0]||(d[0]=_=>u(B).push({name:"CommunicationAnnouncementEdit",params:{uuid:n.uuid}}))},{default:e(()=>[o(l(a.$trans("general.edit")),1)]),_:1})):m("",!0)]),_:1})]),default:e(()=>[g("dl",X,[t(r,{class:"col-span-1 sm:col-span-2",html:""},{default:e(()=>[o(l(n.description),1)]),_:1}),n.media.length>0?(s(),c(r,{key:0,class:"col-span-1 sm:col-span-2"},{default:e(()=>[t(w,{media:n.media,url:`/app/communication/announcements/${n.uuid}/`},null,8,["media","url"])]),_:1})):m("",!0),u(C)("announcement:view-log")?(s(),c(r,{key:1,class:"col-span-1 sm:col-span-2"},{default:e(()=>[t(j,{"view-logs":n.viewLogs},null,8,["view-logs"])]),_:1})):m("",!0)])]),_:1})):m("",!0)]),_:1})):m("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{ee as default};
