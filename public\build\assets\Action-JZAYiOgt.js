import{u as f,I as P,l as p,r as m,q as V,o as b,w as _,d,e as r,f as i,K as j,a as q,F as A}from"./app-DvIo72ZO.js";const O={class:"grid grid-cols-3 gap-6"},U={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3 sm:col-span-1"},R={name:"MessMenuItemForm"},T=Object.assign(R,{setup(g){const l=f(),e={name:"",description:""},u="mess/menuItem/",o=P(u),c=p({}),s=p({...e}),M=p({isLoaded:!l.params.uuid}),I=a=>{Object.assign(c,a)},$=a=>{Object.assign(e,{...a}),Object.assign(s,j(e)),M.isLoaded=!0};return(a,t)=>{const v=m("BaseInput"),B=m("BaseTextarea"),F=m("FormAction");return b(),V(F,{"pre-requisites":!1,onSetPreRequisites:I,"init-url":u,"init-form":e,form:s,"set-form":$,redirect:"MessMenuItem"},{default:_(()=>[d("div",O,[d("div",U,[r(v,{type:"text",modelValue:s.name,"onUpdate:modelValue":t[0]||(t[0]=n=>s.name=n),name:"name",label:a.$trans("mess.menu.props.name"),error:i(o).name,"onUpdate:error":t[1]||(t[1]=n=>i(o).name=n),autofocus:""},null,8,["modelValue","label","error"])]),d("div",H,[r(B,{rows:1,modelValue:s.description,"onUpdate:modelValue":t[2]||(t[2]=n=>s.description=n),name:"description",label:a.$trans("mess.menu.props.description"),error:i(o).description,"onUpdate:error":t[3]||(t[3]=n=>i(o).description=n)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),k={name:"MessMenuItemAction"},E=Object.assign(k,{setup(g){const l=f();return(e,u)=>{const o=m("PageHeaderAction"),c=m("PageHeader"),s=m("ParentTransition");return b(),q(A,null,[r(c,{title:e.$trans(i(l).meta.trans,{attribute:e.$trans(i(l).meta.label)}),navs:[{label:e.$trans("mess.mess"),path:"Mess"},{label:e.$trans("mess.menu.item"),path:"MessMenuItemList"}]},{default:_(()=>[r(o,{name:"MessMenuItem",title:e.$trans("mess.menu.item"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),r(s,{appear:"",visibility:!0},{default:_(()=>[r(T)]),_:1})],64)}}});export{E as default};
