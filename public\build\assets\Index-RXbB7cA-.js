import{u as P,l as I,n as U,r as s,q as g,o as c,w as e,d as R,e as t,b as w,h as j,j as E,y as k,m as O,f as r,a as F,F as S,v as V,s as i,t as l,aS as Q}from"./app-DvIo72ZO.js";const z={class:"grid grid-cols-3 gap-6"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3 sm:col-span-1"},X={__name:"Filter",emits:["hide"],setup(T,{emit:b}){const v=P(),y=b,C={codeNumber:"",purposes:[],startDate:"",endDate:""},p=I({...C}),f=I({purposes:[],isLoaded:!v.query.purposes});return U(async()=>{f.purposes=v.query.purposes?v.query.purposes.split(","):[],f.isLoaded=!0}),(_,u)=>{const B=s("BaseInput"),n=s("BaseSelectSearch"),o=s("DatePicker"),h=s("FilterForm");return c(),g(h,{"init-form":C,form:p,multiple:["purposes"],onHide:u[4]||(u[4]=m=>y("hide"))},{default:e(()=>[R("div",z,[R("div",J,[t(B,{type:"text",modelValue:p.codeNumber,"onUpdate:modelValue":u[0]||(u[0]=m=>p.codeNumber=m),name:"codeNumber",label:_.$trans("reception.gate_pass.props.code_number")},null,8,["modelValue","label"])]),R("div",K,[f.isLoaded?(c(),g(n,{key:0,multiple:"",name:"purposes",label:_.$trans("global.select",{attribute:_.$trans("reception.gate_pass.purpose.purpose")}),modelValue:p.purposes,"onUpdate:modelValue":u[1]||(u[1]=m=>p.purposes=m),"value-prop":"uuid","init-search":f.purposes,"search-action":"option/list","additional-search-query":{type:"gate_pass_purpose"}},null,8,["label","modelValue","init-search"])):w("",!0)]),R("div",W,[t(o,{start:p.startDate,"onUpdate:start":u[2]||(u[2]=m=>p.startDate=m),end:p.endDate,"onUpdate:end":u[3]||(u[3]=m=>p.endDate=m),name:"dateBetween",as:"range",label:_.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},Y={name:"ReceptionGatePassList"},x=Object.assign(Y,{setup(T){const b=j(),v=E("emitter");let y=["filter"];k("reception:config")&&y.push("config"),k("gate-pass:create")&&y.unshift("create");let C=[];k("gate-pass:export")&&(C=["print","pdf","excel"]);const p="reception/gatePass/",f=O(!1),_=I({}),u=n=>{Object.assign(_,n)},B=n=>{let o="/app/reception/gate-passes/"+n.uuid+"/export",h={};window.open(Q(o,h),"_blank").focus()};return(n,o)=>{const h=s("PageHeaderAction"),m=s("PageHeader"),N=s("ParentTransition"),$=s("DataCell"),q=s("TextMuted"),D=s("FloatingMenuItem"),A=s("FloatingMenu"),G=s("DataRow"),L=s("BaseButton"),M=s("DataTable"),H=s("ListItem");return c(),g(H,{"init-url":p,"additional-query":{},onSetItems:u},{header:e(()=>[t(m,{title:n.$trans("reception.gate_pass.gate_pass"),navs:[{label:n.$trans("reception.reception"),path:"Reception"}]},{default:e(()=>[t(h,{url:"reception/gate-passes/",name:"ReceptionGatePass",title:n.$trans("reception.gate_pass.gate_pass"),actions:r(y),"dropdown-actions":r(C),"config-path":"ReceptionConfig",onToggleFilter:o[0]||(o[0]=a=>f.value=!f.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(N,{appear:"",visibility:f.value},{default:e(()=>[t(X,{onRefresh:o[1]||(o[1]=a=>r(v).emit("listItems")),onHide:o[2]||(o[2]=a=>f.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(N,{appear:"",visibility:!0},{default:e(()=>[t(M,{header:_.headers,meta:_.meta,module:"reception.gate_pass",onRefresh:o[4]||(o[4]=a=>r(v).emit("listItems"))},{actionButton:e(()=>[r(k)("gate-pass:create")?(c(),g(L,{key:0,onClick:o[3]||(o[3]=a=>r(b).push({name:"ReceptionGatePassCreate"}))},{default:e(()=>[i(l(n.$trans("global.add",{attribute:n.$trans("reception.gate_pass.gate_pass")})),1)]),_:1})):w("",!0)]),default:e(()=>[(c(!0),F(S,null,V(_.data,a=>(c(),g(G,{key:a.uuid,onDoubleClick:d=>r(b).push({name:"ReceptionGatePassShow",params:{uuid:a.uuid}})},{default:e(()=>[t($,{name:"codeNumber"},{default:e(()=>[i(l(a.codeNumber),1)]),_:2},1024),t($,{name:"startAt"},{default:e(()=>[i(l(a.startAt.formatted),1)]),_:2},1024),t($,{name:"to"},{default:e(()=>[i(l(a.requesterType.label),1)]),_:2},1024),t($,{name:"name"},{default:e(()=>[(c(!0),F(S,null,V(a.audiences,d=>(c(),F("div",null,[i(l(d.name)+" ",1),t(q,null,{default:e(()=>[i(l(d.detail),1)]),_:2},1024)]))),256))]),_:2},1024),t($,{name:"purpose"},{default:e(()=>{var d;return[i(l(((d=a.purpose)==null?void 0:d.name)||"-"),1)]}),_:2},1024),t($,{name:"createdAt"},{default:e(()=>[i(l(a.createdAt.formatted),1)]),_:2},1024),t($,{name:"action"},{default:e(()=>[t(A,null,{default:e(()=>[t(D,{icon:"fas fa-arrow-circle-right",onClick:d=>r(b).push({name:"ReceptionGatePassShow",params:{uuid:a.uuid}})},{default:e(()=>[i(l(n.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(D,{icon:"fas fa-arrow-circle-right",onClick:d=>B(a)},{default:e(()=>[i(l(n.$trans("global.print",{attribute:n.$trans("reception.gate_pass.gate_pass")})),1)]),_:2},1032,["onClick"]),r(k)("gate-pass:edit")?(c(),g(D,{key:0,icon:"fas fa-edit",onClick:d=>r(b).push({name:"ReceptionGatePassEdit",params:{uuid:a.uuid}})},{default:e(()=>[i(l(n.$trans("general.edit")),1)]),_:2},1032,["onClick"])):w("",!0),r(k)("gate-pass:create")?(c(),g(D,{key:1,icon:"fas fa-copy",onClick:d=>r(b).push({name:"ReceptionGatePassDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[i(l(n.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):w("",!0),r(k)("gate-pass:delete")?(c(),g(D,{key:2,icon:"fas fa-trash",onClick:d=>r(v).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[i(l(n.$trans("general.delete")),1)]),_:2},1032,["onClick"])):w("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{x as default};
