import{i as F,u as O,h as q,j as z,l as U,r as b,a as v,o as u,e as o,w as e,f as a,q as c,b as y,d as k,F as w,v as G,s as r,t as i,y as J}from"./app-DvIo72ZO.js";const K={class:"space-y-2"},M={class:"px-4 py-2"},Q={class:"grid grid-cols-1 gap-x-4 gap-y-8 px-4 pt-4 sm:grid-cols-2"},W={name:"LibraryBookShow"},Z=Object.assign(W,{setup(X){F();const _=O(),f=q(),t=z("$trans"),L={},V="library/book/",C=[{key:"number",label:t("library.book.props.number"),visibility:!0},{key:"condition",label:t("library.book.props.condition"),visibility:!0},{key:"date",label:t("library.book_addition.props.date"),visibility:!0},{key:"action",label:"",visibility:!0}],l=U({...L}),S=g=>{Object.assign(l,g)};return(g,d)=>{const D=b("PageHeaderAction"),x=b("PageHeader"),s=b("ListItemView"),P=b("ListContainerVertical"),B=b("BaseCard"),m=b("DataCell"),T=b("DataRow"),j=b("SimpleTable"),A=b("BaseAlert"),I=b("BaseDataView"),N=b("BaseButton"),R=b("ShowButton"),H=b("DetailLayoutVertical"),$=b("ShowItem"),E=b("ParentTransition");return u(),v(w,null,[o(x,{title:a(t)(a(_).meta.trans,{attribute:a(t)(a(_).meta.label)}),navs:[{label:a(t)("library.library"),path:"Library"},{label:a(t)("library.book.book"),path:"LibraryBook"}]},{default:e(()=>[o(D,{name:"LibraryBook",title:a(t)("library.book.book"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),o(E,{appear:"",visibility:!0},{default:e(()=>[o($,{"init-url":V,uuid:a(_).params.uuid,"module-uuid":a(_).params.muuid,onSetItem:S,onRedirectTo:d[1]||(d[1]=n=>a(f).push({name:"LibraryBook",params:{uuid:l.uuid}}))},{default:e(()=>[l.uuid?(u(),c(H,{key:0},{detail:e(()=>[k("div",K,[o(B,{"no-padding":"","no-content-padding":""},{title:e(()=>[r(i(a(t)("library.book.book")),1)]),action:e(()=>d[2]||(d[2]=[])),default:e(()=>[o(P,null,{default:e(()=>[o(s,{label:a(t)("library.book.props.sub_title")},{default:e(()=>[r(i(l.subTitle),1)]),_:1},8,["label"]),o(s,{label:a(t)("library.book.props.subject")},{default:e(()=>[r(i(l.subject),1)]),_:1},8,["label"]),o(s,{label:a(t)("library.book.props.author")},{default:e(()=>{var n;return[r(i(((n=l.author)==null?void 0:n.name)||"-"),1)]}),_:1},8,["label"]),o(s,{label:a(t)("library.book.props.publisher")},{default:e(()=>{var n;return[r(i(((n=l.publisher)==null?void 0:n.name)||"-"),1)]}),_:1},8,["label"]),o(s,{label:a(t)("library.book.props.topic")},{default:e(()=>{var n;return[r(i(((n=l.topic)==null?void 0:n.name)||"-"),1)]}),_:1},8,["label"]),o(s,{label:a(t)("library.book.props.language")},{default:e(()=>{var n;return[r(i(((n=l.language)==null?void 0:n.name)||"-"),1)]}),_:1},8,["label"]),o(s,{label:a(t)("library.book.props.year_published")},{default:e(()=>[r(i(l.yearPublished),1)]),_:1},8,["label"]),o(s,{label:a(t)("library.book.props.volume")},{default:e(()=>[r(i(l.volume),1)]),_:1},8,["label"]),o(s,{label:a(t)("library.book.props.isbn_number")},{default:e(()=>[r(i(l.isbnNumber),1)]),_:1},8,["label"]),o(s,{label:a(t)("library.book.props.edition")},{default:e(()=>[r(i(l.edition),1)]),_:1},8,["label"]),o(s,{label:a(t)("library.book.props.call_number")},{default:e(()=>[r(i(l.callNumber),1)]),_:1},8,["label"]),o(s,{label:a(t)("library.book.props.type")},{default:e(()=>[r(i(l.type),1)]),_:1},8,["label"]),o(s,{label:a(t)("library.book.props.page")},{default:e(()=>[r(i(l.page),1)]),_:1},8,["label"]),o(s,{label:a(t)("library.book.props.price")},{default:e(()=>{var n;return[r(i(((n=l.price)==null?void 0:n.formatted)||"-"),1)]}),_:1},8,["label"]),o(s,{label:a(t)("general.created_at")},{default:e(()=>[r(i(l.createdAt.formatted),1)]),_:1},8,["label"]),o(s,{label:a(t)("general.updated_at")},{default:e(()=>[r(i(l.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})])]),default:e(()=>[o(B,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:e(()=>[r(i(a(t)("global.detail",{attribute:a(t)("library.book_addition.props.copies")})),1)]),footer:e(()=>[o(R,null,{default:e(()=>[a(J)("book:edit")?(u(),c(N,{key:0,design:"primary",onClick:d[0]||(d[0]=n=>a(f).push({name:"LibraryBookEdit",params:{uuid:l.uuid}}))},{default:e(()=>[r(i(a(t)("general.edit")),1)]),_:1})):y("",!0)]),_:1})]),default:e(()=>[l.copies.length>0?(u(),c(j,{key:0,header:C},{default:e(()=>[(u(!0),v(w,null,G(l.copies,n=>(u(),c(T,{key:n.uuid},{default:e(()=>[o(m,{name:"number"},{default:e(()=>[r(i(n.number),1)]),_:2},1024),o(m,{name:"condition"},{default:e(()=>{var p;return[r(i(((p=n.condition)==null?void 0:p.name)||"-"),1)]}),_:2},1024),o(m,{name:"addition"},{default:e(()=>{var p,h;return[r(i(((h=(p=n.addition)==null?void 0:p.date)==null?void 0:h.formatted)||"-"),1)]}),_:2},1024),o(m,{name:"action"})]),_:2},1024))),128))]),_:1})):y("",!0),k("div",M,[l.copies.length===0?(u(),c(A,{key:0,size:"xs",design:"info"},{default:e(()=>[r(i(a(t)("general.errors.record_not_found")),1)]),_:1})):y("",!0)]),k("dl",Q,[o(I,{class:"col-span-1 sm:col-span-2",label:a(t)("library.book.props.summary")},{default:e(()=>[r(i(l.summary),1)]),_:1},8,["label"])])]),_:1})]),_:1})):y("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{Z as default};
