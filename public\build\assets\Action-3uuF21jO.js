import{u as k,l as S,I as A,r as n,q as E,o as i,w as c,d as y,a as d,b as $,f as o,s as D,t as m,e as p,F,v as I,x as C,K as w}from"./app-DvIo72ZO.js";const z={class:"grid grid-cols-3 gap-6"},K={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},M={key:0,class:"mt-4 grid grid-cols-3 gap-3"},Q={key:1,class:"mt-4 grid grid-cols-3 gap-3"},W={key:0},X={class:"mt-4 grid grid-cols-3 gap-3"},Y={class:"col-span-3"},Z={name:"EmployeePayrollSalaryStructureForm"},x=Object.assign(Z,{setup(L){const b=k(),s={employee:"",salaryTemplate:"",effectiveDate:"",hourlyPay:0,records:[],description:""},T="employee/payroll/salaryStructure/",v=S({salaryTemplates:[]}),r=A(T),l=S({...s}),f=S({employee:"",salaryTemplate:"",isLoaded:!b.params.uuid}),H=S({hasHourlyPayroll:!1}),O=t=>{Object.assign(v,t)},R=t=>{var P,V,_,h;let a=t.records.filter(u=>u.enableUserInput),g=[];a.forEach(u=>{g.push({uuid:u.uuid,unit:u.unit,payHead:u.payHead,amount:u.amount.value})}),Object.assign(s,{employee:(P=t.employee)==null?void 0:P.uuid,salaryTemplate:(V=t.template)==null?void 0:V.uuid,effectiveDate:t.effectiveDate.value,records:g,description:t.description}),Object.assign(l,w(s)),f.employee=(_=t.employee)==null?void 0:_.uuid,f.salaryTemplate=(h=t.template)==null?void 0:h.uuid,f.isLoaded=!0},q=t=>{t.hasHourlyPayroll?H.hasHourlyPayroll=!0:H.hasHourlyPayroll=!1,l.records=[],t.records.forEach(a=>{a.enableUserInput&&l.records.push({uuid:a.uuid,unit:a.unit,payHead:a.payHead,amount:""})})},N=t=>{l.records=[]};return(t,a)=>{const g=n("BaseSelectSearch"),P=n("BaseSelect"),V=n("DatePicker"),_=n("BaseInput"),h=n("BaseLabel"),u=n("BaseTextarea"),j=n("FormAction");return i(),E(j,{"pre-requisites":!0,onSetPreRequisites:O,"init-url":T,"init-form":s,form:l,"set-form":R,redirect:"EmployeePayrollSalaryStructure"},{default:c(()=>[y("div",z,[y("div",K,[f.isLoaded?(i(),E(g,{key:0,name:"employee",label:t.$trans("global.select",{attribute:t.$trans("employee.employee")}),modelValue:l.employee,"onUpdate:modelValue":a[0]||(a[0]=e=>l.employee=e),error:o(r).employee,"onUpdate:error":a[1]||(a[1]=e=>o(r).employee=e),"value-prop":"uuid","init-search":f.employee,"additional-search-query":{self:0},"search-action":"employee/list"},{selectedOption:c(e=>[D(m(e.value.name)+" ("+m(e.value.codeNumber)+") ",1)]),listOption:c(e=>[D(m(e.option.name)+" ("+m(e.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","error","init-search"])):$("",!0)]),y("div",G,[p(P,{name:"salaryTemplate",label:t.$trans("global.select",{attribute:t.$trans("employee.payroll.salary_template.salary_template")}),modelValue:l.salaryTemplate,"onUpdate:modelValue":a[2]||(a[2]=e=>l.salaryTemplate=e),error:o(r).salaryTemplate,"onUpdate:error":a[3]||(a[3]=e=>o(r).salaryTemplate=e),"label-prop":"name","value-prop":"uuid",options:v.salaryTemplates,onSelected:q,onRemoved:N},null,8,["label","modelValue","error","options"])]),y("div",J,[p(V,{modelValue:l.effectiveDate,"onUpdate:modelValue":a[4]||(a[4]=e=>l.effectiveDate=e),name:"effectiveDate",label:t.$trans("employee.payroll.salary_structure.props.effective_date"),"no-clear":"",error:o(r).effectiveDate,"onUpdate:error":a[5]||(a[5]=e=>o(r).effectiveDate=e)},null,8,["modelValue","label","error"])])]),H.hasHourlyPayroll?(i(),d("div",M,[p(_,{currency:"",type:"text",modelValue:l.hourlyPay,"onUpdate:modelValue":a[6]||(a[6]=e=>l.hourlyPay=e),name:"hourlyPay",label:t.$trans("employee.payroll.salary_structure.props.hourly_pay"),error:o(r).hourlyPay,"onUpdate:error":a[7]||(a[7]=e=>o(r).hourlyPay=e)},null,8,["modelValue","label","error"])])):$("",!0),l.records.length>0?(i(),d("div",Q,[(i(!0),d(F,null,I(l.records,(e,U)=>(i(),d("div",{key:e.uuid,class:"col-span-3 sm:col-span-1"},[p(h,{class:C({"text-success":e.payHead.type=="earning","text-danger":e.payHead.type=="deduction"})},{default:c(()=>[D(m(e.payHead.name)+" "+m(e.payHead.code)+" ",1),e.unit?(i(),d("span",W,"("+m(e.unit.label)+")",1)):$("",!0)]),_:2},1032,["class"]),p(_,{currency:"",type:"text",modelValue:e.amount,"onUpdate:modelValue":B=>e.amount=B,name:`records.${U}.amount`,placeholder:t.$trans("employee.payroll.salary_structure.props.amount"),error:o(r)[`records.${U}.amount`],"onUpdate:error":B=>o(r)[`records.${U}.amount`]=B},null,8,["modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"])]))),128))])):$("",!0),y("div",X,[y("div",Y,[p(u,{modelValue:l.description,"onUpdate:modelValue":a[8]||(a[8]=e=>l.description=e),name:"description",label:t.$trans("employee.payroll.salary_structure.props.description"),error:o(r).description,"onUpdate:error":a[9]||(a[9]=e=>o(r).description=e)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),ee={name:"EmployeePayrollSalaryStructureAction"},te=Object.assign(ee,{setup(L){const b=k();return(s,T)=>{const v=n("PageHeaderAction"),r=n("PageHeader"),l=n("ParentTransition");return i(),d(F,null,[p(r,{title:s.$trans(o(b).meta.trans,{attribute:s.$trans(o(b).meta.label)}),navs:[{label:s.$trans("employee.employee"),path:"Employee"},{label:s.$trans("employee.payroll.payroll"),path:"EmployeePayroll"},{label:s.$trans("employee.payroll.salary_structure.salary_structure"),path:"EmployeePayrollSalaryStructureList"}]},{default:c(()=>[p(v,{name:"EmployeePayrollSalaryStructure",title:s.$trans("employee.payroll.salary_structure.salary_structure"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),p(l,{appear:"",visibility:!0},{default:c(()=>[p(x)]),_:1})],64)}}});export{te as default};
