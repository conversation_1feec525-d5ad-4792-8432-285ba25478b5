import{u as f,h as P,I as S,l as g,r as n,q as D,o as _,w as c,d as m,e as i,f as s,b as I,s as V,t as b,K as O,a as N,F as j}from"./app-DvIo72ZO.js";const q={class:"grid grid-cols-3 gap-6"},A={class:"col-span-3 sm:col-span-1"},R={class:"col-span-2 sm:col-span-1"},T={class:"col-span-2 sm:col-span-1"},L={class:"col-span-2 sm:col-span-1"},C={class:"col-span-3"},E={name:"HostelBlockInchargeForm"},w=Object.assign(E,{setup(h){const p=f();P();const l={block:"",employee:"",startDate:"",endDate:"",remarks:""},k="hostel/blockIncharge/",r=S(k),d=g({blocks:[]}),a=g({...l}),u=g({employee:"",isLoaded:!p.params.uuid}),v=t=>{Object.assign(d,t)},B=t=>{Object.assign(l,{...t,startDate:t.startDate.value,endDate:t.endDate.value,block:t.block.uuid,employee:t.employee.uuid}),Object.assign(a,O(l)),u.employee=t.employee.uuid,u.isLoaded=!0};return(t,o)=>{const $=n("BaseSelect"),U=n("BaseSelectSearch"),y=n("DatePicker"),H=n("BaseTextarea"),F=n("FormAction");return _(),D(F,{"pre-requisites":!0,onSetPreRequisites:v,"init-url":k,"init-form":l,form:a,setForm:B,redirect:"HostelBlockIncharge"},{default:c(()=>[m("div",q,[m("div",A,[i($,{name:"block",label:t.$trans("hostel.block.block"),modelValue:a.block,"onUpdate:modelValue":o[0]||(o[0]=e=>a.block=e),error:s(r).block,"onUpdate:error":o[1]||(o[1]=e=>s(r).block=e),"label-prop":"name","value-prop":"uuid",options:d.blocks},null,8,["label","modelValue","error","options"])]),m("div",R,[u.isLoaded?(_(),D(U,{key:0,name:"employee",label:t.$trans("global.select",{attribute:t.$trans("employee.employee")}),modelValue:a.employee,"onUpdate:modelValue":o[2]||(o[2]=e=>a.employee=e),error:s(r).employee,"onUpdate:error":o[3]||(o[3]=e=>s(r).employee=e),"value-prop":"uuid","init-search":u.employee,"search-key":"name","search-action":"employee/list"},{selectedOption:c(e=>[V(b(e.value.name)+" ("+b(e.value.codeNumber)+") ",1)]),listOption:c(e=>[V(b(e.option.name)+" ("+b(e.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","error","init-search"])):I("",!0)]),m("div",T,[i(y,{modelValue:a.startDate,"onUpdate:modelValue":o[4]||(o[4]=e=>a.startDate=e),name:"startDate",label:t.$trans("employee.incharge.props.start_date"),"no-clear":"",error:s(r).startDate,"onUpdate:error":o[5]||(o[5]=e=>s(r).startDate=e)},null,8,["modelValue","label","error"])]),m("div",L,[i(y,{modelValue:a.endDate,"onUpdate:modelValue":o[6]||(o[6]=e=>a.endDate=e),name:"endDate",label:t.$trans("employee.incharge.props.end_date"),"no-clear":"",error:s(r).endDate,"onUpdate:error":o[7]||(o[7]=e=>s(r).endDate=e)},null,8,["modelValue","label","error"])]),m("div",C,[i(H,{modelValue:a.remarks,"onUpdate:modelValue":o[8]||(o[8]=e=>a.remarks=e),name:"remarks",label:t.$trans("employee.incharge.props.remarks"),error:s(r).remarks,"onUpdate:error":o[9]||(o[9]=e=>s(r).remarks=e)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),K={name:"HostelBlockInchargeAction"},G=Object.assign(K,{setup(h){const p=f();return(l,k)=>{const r=n("PageHeaderAction"),d=n("PageHeader"),a=n("ParentTransition");return _(),N(j,null,[i(d,{title:l.$trans(s(p).meta.trans,{attribute:l.$trans(s(p).meta.label)}),navs:[{label:l.$trans("hostel.hostel"),path:"Hostel"},{label:l.$trans("hostel.block_incharge.block_incharge"),path:"HostelBlockInchargeList"}]},{default:c(()=>[i(r,{name:"HostelBlockIncharge",title:l.$trans("hostel.block_incharge.block_incharge"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),i(a,{appear:"",visibility:!0},{default:c(()=>[i(w)]),_:1})],64)}}});export{G as default};
