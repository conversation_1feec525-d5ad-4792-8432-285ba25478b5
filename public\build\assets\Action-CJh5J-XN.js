import{u as F,I as B,l as b,r as u,q as P,o as V,w as H,d as m,e as d,f as a,K as U,a as C,F as j}from"./app-DvIo72ZO.js";const O={class:"grid grid-cols-3 gap-6"},q={class:"col-span-3 sm:col-span-1"},A={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},T={class:"col-span-3 sm:col-span-2"},w={name:"StudentCustomFeeForm"},y=Object.assign(w,{setup(i){const l=F(),n={feeHead:"",amount:"",dueDate:"",remarks:""},_="student/customFee/",r=B(_),p=b({feeHeads:[]}),s=b({...n}),g=b({feeHead:"",isLoaded:!l.params.muuid}),v=o=>{Object.assign(p,o)},k=o=>{var e,c,f;Object.assign(n,{...o,feeHead:(e=o.head)==null?void 0:e.uuid,amount:((c=o.amount)==null?void 0:c.value)||"",dueDate:((f=o.dueDate)==null?void 0:f.value)||""}),Object.assign(s,U(n)),g.feeHead=o.head.name,g.isLoaded=!0};return(o,e)=>{const c=u("BaseSelect"),f=u("BaseInput"),D=u("DatePicker"),S=u("BaseTextarea"),$=u("FormAction");return V(),P($,{"no-data-fetch":"","pre-requisites":!0,onSetPreRequisites:v,"init-url":_,uuid:a(l).params.uuid,"module-uuid":a(l).params.muuid,"init-form":n,form:s,"set-form":k,redirect:{name:"StudentCustomFee",params:{uuid:a(l).params.uuid}}},{default:H(()=>[m("div",O,[m("div",q,[d(c,{modelValue:s.feeHead,"onUpdate:modelValue":e[0]||(e[0]=t=>s.feeHead=t),name:"feeHead",label:o.$trans("student.fee.custom_fee"),"label-prop":"name","value-prop":"uuid",options:p.feeHeads,error:a(r).feeHead,"onUpdate:error":e[1]||(e[1]=t=>a(r).feeHead=t)},null,8,["modelValue","label","options","error"])]),m("div",A,[d(f,{currency:"",modelValue:s.amount,"onUpdate:modelValue":e[2]||(e[2]=t=>s.amount=t),name:"amount",label:o.$trans("student.fee.props.amount"),error:a(r).amount,"onUpdate:error":e[3]||(e[3]=t=>a(r).amount=t)},null,8,["modelValue","label","error"])]),m("div",R,[d(D,{modelValue:s.dueDate,"onUpdate:modelValue":e[4]||(e[4]=t=>s.dueDate=t),name:"dueDate",label:o.$trans("student.fee.props.due_date"),error:a(r).dueDate,"onUpdate:error":e[5]||(e[5]=t=>a(r).dueDate=t)},null,8,["modelValue","label","error"])]),m("div",T,[d(S,{modelValue:s.remarks,"onUpdate:modelValue":e[6]||(e[6]=t=>s.remarks=t),name:"remarks",label:o.$trans("student.fee.props.remarks"),error:a(r).remarks,"onUpdate:error":e[7]||(e[7]=t=>a(r).remarks=t)},null,8,["modelValue","label","error"])])])]),_:1},8,["uuid","module-uuid","form","redirect"])}}}),E={name:"StudentCustomFeeAction"},L=Object.assign(E,{props:{student:{type:Object,default(){return{}}}},setup(i){const l=F();return(n,_)=>{const r=u("PageHeaderAction"),p=u("PageHeader"),s=u("ParentTransition");return V(),C(j,null,[d(p,{title:n.$trans(a(l).meta.trans,{attribute:n.$trans(a(l).meta.label)}),navs:[{label:n.$trans("student.student"),path:"StudentList"},{label:i.student.contact.name,path:{name:"StudentShow",params:{uuid:i.student.uuid}}},{label:n.$trans("student.fee.custom_fee"),path:{name:"StudentCustomFee",params:{uuid:i.student.uuid}}}]},{default:H(()=>[d(r,{name:"StudentCustomFee",title:n.$trans("student.fee.custom_fee"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),d(s,{appear:"",visibility:!0},{default:H(()=>[d(y)]),_:1})],64)}}});export{L as default};
