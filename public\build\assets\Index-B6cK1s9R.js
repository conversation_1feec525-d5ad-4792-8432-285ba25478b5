import{l as I,r as s,q as _,o as c,w as t,d as S,e,u as L,h as M,j as N,y as h,m as O,f as u,a as w,F as B,v as U,s as d,t as l,b as y}from"./app-DvIo72ZO.js";const E={class:"grid grid-cols-3 gap-6"},q={class:"col-span-3 sm:col-span-1"},z={__name:"Filter",emits:["hide"],setup(o,{emit:k}){const m=k,f={title:""},p=I({...f});return(b,r)=>{const v=s("BaseInput"),C=s("FilterForm");return c(),_(C,{"init-form":f,form:p,onHide:r[1]||(r[1]=a=>m("hide"))},{default:t(()=>[S("div",E,[S("div",q,[e(v,{type:"text",modelValue:p.title,"onUpdate:modelValue":r[0]||(r[0]=a=>p.title=a),name:"title",label:b.$trans("student.document.props.title")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},G={name:"StudentDocumentList"},K=Object.assign(G,{props:{student:{type:Object,default(){return{}}}},setup(o){const k=L(),m=M(),f=N("emitter");let p=["filter"];h("student:edit")&&p.unshift("create");const b="student/document/",r=O(!1),v=I({}),C=a=>{Object.assign(v,a)};return(a,i)=>{const V=s("PageHeaderAction"),H=s("PageHeader"),F=s("ParentTransition"),g=s("DataCell"),$=s("FloatingMenuItem"),P=s("FloatingMenu"),R=s("DataRow"),T=s("BaseButton"),j=s("DataTable"),A=s("ListItem");return c(),_(A,{"init-url":b,uuid:u(k).params.uuid,onSetItems:C},{header:t(()=>[o.student.uuid?(c(),_(H,{key:0,title:a.$trans("student.document.document"),navs:[{label:a.$trans("student.student"),path:"Student"},{label:o.student.contact.name,path:{name:"StudentShow",params:{uuid:o.student.uuid}}}]},{default:t(()=>[e(V,{url:`students/${o.student.uuid}/documents/`,name:"StudentDocument",title:a.$trans("student.document.document"),actions:u(p),"dropdown-actions":["print","pdf","excel"],onToggleFilter:i[0]||(i[0]=n=>r.value=!r.value)},null,8,["url","title","actions"])]),_:1},8,["title","navs"])):y("",!0)]),filter:t(()=>[e(F,{appear:"",visibility:r.value},{default:t(()=>[e(z,{onRefresh:i[1]||(i[1]=n=>u(f).emit("listItems")),onHide:i[2]||(i[2]=n=>r.value=!1)})]),_:1},8,["visibility"])]),default:t(()=>[e(F,{appear:"",visibility:!0},{default:t(()=>[e(j,{header:v.headers,meta:v.meta,module:"student.document",onRefresh:i[4]||(i[4]=n=>u(f).emit("listItems"))},{actionButton:t(()=>[u(h)("student:edit")?(c(),_(T,{key:0,onClick:i[3]||(i[3]=n=>u(m).push({name:"StudentDocumentCreate"}))},{default:t(()=>[d(l(a.$trans("global.add",{attribute:a.$trans("student.document.document")})),1)]),_:1})):y("",!0)]),default:t(()=>[(c(!0),w(B,null,U(v.data,n=>(c(),_(R,{key:n.uuid,onDoubleClick:D=>u(m).push({name:"StudentDocumentShow",params:{uuid:o.student.uuid,muuid:n.uuid}})},{default:t(()=>[e(g,{name:"title"},{default:t(()=>[d(l(n.title),1)]),_:2},1024),e(g,{name:"type"},{default:t(()=>[d(l(n.type.name),1)]),_:2},1024),e(g,{name:"startDate"},{default:t(()=>[d(l(n.startDate.formatted),1)]),_:2},1024),e(g,{name:"endDate"},{default:t(()=>[d(l(n.endDate.formatted),1)]),_:2},1024),e(g,{name:"createdAt"},{default:t(()=>[d(l(n.createdAt.formatted),1)]),_:2},1024),e(g,{name:"action"},{default:t(()=>[e(P,null,{default:t(()=>[e($,{icon:"fas fa-arrow-circle-right",onClick:D=>u(m).push({name:"StudentDocumentShow",params:{uuid:o.student.uuid,muuid:n.uuid}})},{default:t(()=>[d(l(a.$trans("general.show")),1)]),_:2},1032,["onClick"]),u(h)("student:edit")?(c(),w(B,{key:0},[e($,{icon:"fas fa-edit",onClick:D=>u(m).push({name:"StudentDocumentEdit",params:{uuid:o.student.uuid,muuid:n.uuid}})},{default:t(()=>[d(l(a.$trans("general.edit")),1)]),_:2},1032,["onClick"]),e($,{icon:"fas fa-copy",onClick:D=>u(m).push({name:"StudentDocumentDuplicate",params:{uuid:o.student.uuid,muuid:n.uuid}})},{default:t(()=>[d(l(a.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),e($,{icon:"fas fa-trash",onClick:D=>u(f).emit("deleteItem",{uuid:o.student.uuid,moduleUuid:n.uuid})},{default:t(()=>[d(l(a.$trans("general.delete")),1)]),_:2},1032,["onClick"])],64)):y("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1},8,["uuid"])}}});export{K as default};
