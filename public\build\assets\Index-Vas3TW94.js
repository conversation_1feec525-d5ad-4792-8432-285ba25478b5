import{u as M,l as D,n as J,r,q as p,o as c,w as t,d as _,e as a,h as K,j as W,y as g,m as X,f as m,a as S,F as T,v as U,s as l,b as k,t as i,aS as Y}from"./app-DvIo72ZO.js";const Z={class:"grid grid-cols-3 gap-6"},x={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3 sm:col-span-1"},te={class:"col-span-3 sm:col-span-1"},ae={class:"col-span-3 sm:col-span-1"},ne={class:"col-span-3 sm:col-span-1"},oe={class:"col-span-3 sm:col-span-1"},se={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(q,{emit:V}){M();const b=V,C=q,y={codeNumber:"",admissionNumber:"",employeeNumber:"",template:"",search:"",startDate:"",endDate:""},s=D({...y}),B=D({templates:C.preRequisites.templates}),N=D({isLoaded:!0});return J(async()=>{N.isLoaded=!0}),(u,o)=>{const $=r("BaseInput"),h=r("BaseSelect"),A=r("DatePicker"),F=r("FilterForm");return c(),p(F,{"init-form":y,form:s,multiple:[],onHide:o[7]||(o[7]=e=>b("hide"))},{default:t(()=>[_("div",Z,[_("div",x,[a($,{type:"text",modelValue:s.codeNumber,"onUpdate:modelValue":o[0]||(o[0]=e=>s.codeNumber=e),name:"codeNumber",label:u.$trans("academic.certificate.props.code_number")},null,8,["modelValue","label"])]),_("div",ee,[a($,{type:"text",modelValue:s.admissionNumber,"onUpdate:modelValue":o[1]||(o[1]=e=>s.admissionNumber=e),name:"admissionNumber",label:u.$trans("student.admission.props.code_number")},null,8,["modelValue","label"])]),_("div",te,[a($,{type:"text",modelValue:s.employeeNumber,"onUpdate:modelValue":o[2]||(o[2]=e=>s.employeeNumber=e),name:"employeeNumber",label:u.$trans("employee.props.code_number")},null,8,["modelValue","label"])]),_("div",ae,[a(h,{modelValue:s.template,"onUpdate:modelValue":o[3]||(o[3]=e=>s.template=e),name:"template",label:u.$trans("academic.certificate.template.template"),options:B.templates,"label-prop":"name","value-prop":"uuid"},null,8,["modelValue","label","options"])]),_("div",ne,[a($,{type:"search",modelValue:s.search,"onUpdate:modelValue":o[4]||(o[4]=e=>s.search=e),name:"search",label:u.$trans("general.search"),placeholder:u.$trans("academic.certificate.search_placeholder")},null,8,["modelValue","label","placeholder"])]),_("div",oe,[a(A,{start:s.startDate,"onUpdate:start":o[5]||(o[5]=e=>s.startDate=e),end:s.endDate,"onUpdate:end":o[6]||(o[6]=e=>s.endDate=e),name:"dateBetween",as:"range",label:u.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},le={name:"AcademicCertificateList"},re=Object.assign(le,{setup(q){const V=M(),b=K(),C=W("emitter");let y=["filter"];g("certificate:create")&&y.unshift("create");let s=[];g("certificate:export")&&(s=["print","pdf","excel"]);const B="academic/certificate/",N=D({templates:[]}),u=X(!1),o=D({}),$=e=>{Object.assign(o,e)},h=e=>{Object.assign(N,e)},A=e=>{window.open(`/app/academic/certificates/${e.uuid}/export?action=print`)},F=()=>{let e="/app/academic/certificates/export-all",d=V.query;window.open(Y(e,d),"_blank").focus()};return(e,d)=>{const R=r("BaseButton"),j=r("DropdownItem"),H=r("PageHeaderAction"),L=r("PageHeader"),P=r("ParentTransition"),O=r("BaseBadge"),v=r("DataCell"),I=r("TextMuted"),w=r("FloatingMenuItem"),E=r("FloatingMenu"),Q=r("DataRow"),z=r("DataTable"),G=r("ListItem");return c(),p(G,{"init-url":B,"pre-requisites":!0,onSetPreRequisites:h,onSetItems:$},{header:t(()=>[a(L,{title:e.$trans("academic.certificate.certificate"),navs:[{label:e.$trans("academic.academic"),path:"Academic"}]},{default:t(()=>[a(H,{url:"academic/certificates/",name:"AcademicCertificate",title:e.$trans("academic.certificate.certificate"),actions:m(y),"dropdown-actions":m(s),onToggleFilter:d[2]||(d[2]=n=>u.value=!u.value)},{dropdown:t(()=>[a(j,{icon:"fas fa-arrow-right-from-file",onClick:d[1]||(d[1]=n=>F())},{default:t(()=>[l(i(e.$trans("general.export_all")),1)]),_:1})]),default:t(()=>[m(g)("certificate-template:read")?(c(),p(R,{key:0,design:"white",onClick:d[0]||(d[0]=n=>m(b).push({name:"AcademicCertificateTemplate"}))},{default:t(()=>[l(i(e.$trans("academic.certificate.template.template")),1)]),_:1})):k("",!0)]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:t(()=>[a(P,{appear:"",visibility:u.value},{default:t(()=>[a(se,{onRefresh:d[3]||(d[3]=n=>m(C).emit("listItems")),"pre-requisites":N,onHide:d[4]||(d[4]=n=>u.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:t(()=>[a(P,{appear:"",visibility:!0},{default:t(()=>[a(z,{header:o.headers,meta:o.meta,module:"academic.certificate",onRefresh:d[6]||(d[6]=n=>m(C).emit("listItems"))},{actionButton:t(()=>[m(g)("certificate:create")?(c(),p(R,{key:0,onClick:d[5]||(d[5]=n=>m(b).push({name:"AcademicCertificateCreate"}))},{default:t(()=>[l(i(e.$trans("global.add",{attribute:e.$trans("academic.certificate.certificate")})),1)]),_:1})):k("",!0)]),default:t(()=>[(c(!0),S(T,null,U(o.data,n=>(c(),p(Q,{key:n.uuid,onDoubleClick:f=>m(b).push({name:"AcademicCertificateShow",params:{uuid:n.uuid}})},{default:t(()=>[a(v,{name:"codeNumber"},{default:t(()=>[l(i(n.codeNumber)+" ",1),n.isDuplicate?(c(),p(O,{key:0,design:"primary"},{default:t(()=>[l(i(e.$trans("general.duplicate")),1)]),_:1})):k("",!0)]),_:2},1024),a(v,{name:"template"},{default:t(()=>[l(i(n.template.name)+" ",1),a(I,{block:""},{default:t(()=>[l(i(n.template.for.label),1)]),_:2},1024)]),_:2},1024),a(v,{name:"to"},{default:t(()=>[l(i(n.to.name)+" ",1),a(I,{block:""},{default:t(()=>[l(i(n.to.contactNumber),1)]),_:2},1024),(c(!0),S(T,null,U(n.customFields,f=>(c(),p(I,{block:"",key:f.label},{default:t(()=>[l(i(f.label+": "+f.value),1)]),_:2},1024))),128))]),_:2},1024),a(v,{name:"date"},{default:t(()=>[l(i(n.date.formatted),1)]),_:2},1024),a(v,{name:"createdAt"},{default:t(()=>[l(i(n.createdAt.formatted),1)]),_:2},1024),a(v,{name:"action"},{default:t(()=>[a(E,null,{default:t(()=>[a(w,{icon:"fas fa-arrow-circle-right",onClick:f=>m(b).push({name:"AcademicCertificateShow",params:{uuid:n.uuid}})},{default:t(()=>[l(i(e.$trans("general.show")),1)]),_:2},1032,["onClick"]),a(w,{icon:"fas fa-print",onClick:f=>A(n)},{default:t(()=>[l(i(e.$trans("general.print")),1)]),_:2},1032,["onClick"]),m(g)("certificate:edit")?(c(),p(w,{key:0,icon:"fas fa-edit",onClick:f=>m(b).push({name:"AcademicCertificateEdit",params:{uuid:n.uuid}})},{default:t(()=>[l(i(e.$trans("general.edit")),1)]),_:2},1032,["onClick"])):k("",!0),m(g)("certificate:create")?(c(),p(w,{key:1,icon:"fas fa-copy",onClick:f=>m(b).push({name:"AcademicCertificateDuplicate",params:{uuid:n.uuid}})},{default:t(()=>[l(i(e.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):k("",!0),m(g)("certificate:delete")?(c(),p(w,{key:2,icon:"fas fa-trash",onClick:f=>m(C).emit("deleteItem",{uuid:n.uuid})},{default:t(()=>[l(i(e.$trans("general.delete")),1)]),_:2},1032,["onClick"])):k("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{re as default};
