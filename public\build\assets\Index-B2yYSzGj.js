import{u as H,l as C,n as N,r as o,q as g,o as _,w as e,d as B,e as t,h as j,j as S,m as V,f as u,a as q,F as U,v as E,s as i,t as l,b as O}from"./app-DvIo72ZO.js";const z={class:"grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},J={__name:"Filter",emits:["hide"],setup(w,{emit:m}){H();const p=m,b={startDate:"",endDate:""},d=C({...b}),v=C({isLoaded:!1});return N(async()=>{v.isLoaded=!0}),(c,s)=>{const y=o("DatePicker"),r=o("FilterForm");return _(),g(r,{"init-form":b,form:d,multiple:[],onHide:s[2]||(s[2]=n=>p("hide"))},{default:e(()=>[B("div",z,[B("div",G,[t(y,{start:d.startDate,"onUpdate:start":s[0]||(s[0]=n=>d.startDate=n),end:d.endDate,"onUpdate:end":s[1]||(s[1]=n=>d.endDate=n),name:"dateBetween",as:"range",label:c.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},K={name:"LibraryTransactionList"},W=Object.assign(K,{setup(w){const m=j(),p=S("emitter");let b=["create","filter"],d=["print","pdf","excel"];const v="library/transaction/",c=V(!1),s=C({}),y=r=>{Object.assign(s,r)};return(r,n)=>{const F=o("PageHeaderAction"),T=o("PageHeader"),k=o("ParentTransition"),f=o("DataCell"),L=o("TextMuted"),h=o("BaseBadge"),$=o("FloatingMenuItem"),I=o("FloatingMenu"),P=o("DataRow"),M=o("BaseButton"),R=o("DataTable"),A=o("ListItem");return _(),g(A,{"init-url":v,onSetItems:y},{header:e(()=>[t(T,{title:r.$trans("library.transaction.transaction"),navs:[{label:r.$trans("library.library"),path:"Library"}]},{default:e(()=>[t(F,{url:"library/transactions/",name:"LibraryTransaction",title:r.$trans("library.transaction.transaction"),actions:u(b),"dropdown-actions":u(d),onToggleFilter:n[0]||(n[0]=a=>c.value=!c.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(k,{appear:"",visibility:c.value},{default:e(()=>[t(J,{onRefresh:n[1]||(n[1]=a=>u(p).emit("listItems")),onHide:n[2]||(n[2]=a=>c.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(k,{appear:"",visibility:!0},{default:e(()=>[t(R,{header:s.headers,meta:s.meta,module:"library.transaction",onRefresh:n[4]||(n[4]=a=>u(p).emit("listItems"))},{actionButton:e(()=>[t(M,{onClick:n[3]||(n[3]=a=>u(m).push({name:"LibraryTransactionCreate"}))},{default:e(()=>[i(l(r.$trans("global.add",{attribute:r.$trans("library.transaction.transaction")})),1)]),_:1})]),default:e(()=>[(_(!0),q(U,null,E(s.data,a=>(_(),g(P,{key:a.uuid,onDoubleClick:D=>u(m).push({name:"LibraryTransactionShow",params:{uuid:a.uuid}})},{default:e(()=>[t(f,{name:"to"},{default:e(()=>[i(l(a.to.label),1)]),_:2},1024),t(f,{name:"name"},{default:e(()=>[i(l(a.requester.name)+" ",1),t(L,{block:""},{default:e(()=>[i(l(a.requester.contactNumber),1)]),_:2},1024)]),_:2},1024),t(f,{name:"issueDate"},{default:e(()=>[i(l(a.issueDate.formatted)+" ",1),a.nonReturnedBooksCount<a.recordsCount?(_(),g(h,{key:0,design:"success"},{default:e(()=>[i(l(r.$trans("library.transaction.statuses.returned")),1)]),_:1})):O("",!0)]),_:2},1024),t(f,{name:"recordsCount"},{default:e(()=>[i(l(a.recordsCount),1)]),_:2},1024),t(f,{name:"createdAt"},{default:e(()=>[i(l(a.createdAt.formatted),1)]),_:2},1024),t(f,{name:"action"},{default:e(()=>[t(I,null,{default:e(()=>[t($,{icon:"fas fa-arrow-circle-right",onClick:D=>u(m).push({name:"LibraryTransactionShow",params:{uuid:a.uuid}})},{default:e(()=>[i(l(r.$trans("general.show")),1)]),_:2},1032,["onClick"]),t($,{icon:"fas fa-edit",onClick:D=>u(m).push({name:"LibraryTransactionEdit",params:{uuid:a.uuid}})},{default:e(()=>[i(l(r.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t($,{icon:"fas fa-trash",onClick:D=>u(p).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[i(l(r.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{W as default};
