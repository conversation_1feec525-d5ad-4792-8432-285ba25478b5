import{u as O,i as R,m as U,I as T,l as P,n as L,ad as H,r as i,q as S,o as r,w as d,a as p,b as m,d as A,e as l,f as s,s as v,t as b,B as I,h as j,F as D}from"./app-DvIo72ZO.js";const E={key:0,class:"grid grid-cols-3 gap-6"},M={class:"col-span-3 sm:col-span-1"},W={key:0,class:"col-span-3 sm:col-span-1"},z={key:1,class:"col-span-3 sm:col-span-1"},G={key:2,class:"col-span-3 sm:col-span-1"},J={key:3,class:"col-span-3 sm:col-span-1"},K={class:"flex items-center gap-2"},Q={key:0,class:"grid grid-cols-3 gap-6"},X={key:0,class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={__name:"Filter",props:{initUrl:{type:String,default:""},preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(k,{emit:h}){const F=O();R();const V=h,w=k,_={template:"",codeNumber:"",batch:"",department:"",showAllStudent:!1,column:1,cardPerPage:2};U(!1);const f=U(!1),n=T(w.initUrl),o=P({..._}),u=P({for:null}),c=P({isLoaded:!0}),q=a=>{a=w.preRequisites.templates.find(e=>e.value===a),u.for=a==null?void 0:a.for};return L(async()=>{o.showAllStudent=H(F.query.showAllStudent||""),c.isLoaded=!0}),(a,e)=>{const C=i("BaseSelect"),B=i("BaseSelectSearch"),g=i("BaseInput"),y=i("BaseSwitch"),N=i("BaseFieldset"),$=i("FilterForm");return r(),S($,{"init-form":_,multiple:[],form:o,onHide:e[14]||(e[14]=t=>V("hide"))},{default:d(()=>[c.isLoaded?(r(),p("div",E,[A("div",M,[l(C,{modelValue:o.template,"onUpdate:modelValue":e[0]||(e[0]=t=>o.template=t),options:k.preRequisites.templates,label:a.$trans("academic.id_card.template.template"),error:s(n).template,onChange:q},null,8,["modelValue","options","label","error"])]),u.for=="student"||u.for=="guardian"?(r(),p("div",W,[c.isLoaded?(r(),S(B,{key:0,name:"batch",label:a.$trans("global.select",{attribute:a.$trans("academic.batch.batch")}),modelValue:o.batch,"onUpdate:modelValue":e[1]||(e[1]=t=>o.batch=t),error:s(n).batch,"onUpdate:error":e[2]||(e[2]=t=>s(n).batch=t),"value-prop":"uuid","init-search":c.batch,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:d(t=>[v(b(t.value.course.name)+" "+b(t.value.name),1)]),listOption:d(t=>[v(b(t.option.course.nameWithTerm)+" "+b(t.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):m("",!0)])):m("",!0),u.for=="employee"?(r(),p("div",z,[c.isLoaded?(r(),S(B,{key:0,name:"batch",label:a.$trans("global.select",{attribute:a.$trans("employee.department.department")}),modelValue:o.department,"onUpdate:modelValue":e[3]||(e[3]=t=>o.department=t),error:s(n).department,"onUpdate:error":e[4]||(e[4]=t=>s(n).department=t),"value-prop":"uuid","init-search":c.batch,"search-key":"name","search-action":"employee/department/list"},{selectedOption:d(t=>[v(b(t.value.name),1)]),listOption:d(t=>[v(b(t.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):m("",!0)])):m("",!0),u.for=="student"?(r(),p("div",G,[l(g,{modelValue:o.codeNumber,"onUpdate:modelValue":e[5]||(e[5]=t=>o.codeNumber=t),label:a.$trans("student.admission.props.code_number"),error:s(n).codeNumber},null,8,["modelValue","label","error"])])):m("",!0),u.for=="employee"?(r(),p("div",J,[l(g,{modelValue:o.codeNumber,"onUpdate:modelValue":e[6]||(e[6]=t=>o.codeNumber=t),label:a.$trans("employee.props.code_number"),error:s(n).codeNumber},null,8,["modelValue","label","error"])])):m("",!0)])):m("",!0),c.isLoaded&&!s(I)(["student","guardian"],"any")?(r(),S(N,{key:1,class:"mt-4"},{legend:d(()=>[A("div",K,[v(b(a.$trans("global.show",{attribute:a.$trans("general.options")}))+" ",1),l(y,{reverse:"",modelValue:f.value,"onUpdate:modelValue":e[7]||(e[7]=t=>f.value=t),name:"showOptions"},null,8,["modelValue"])])]),default:d(()=>[f.value?(r(),p("div",Q,[u.for=="student"?(r(),p("div",X,[l(y,{vertical:"",modelValue:o.showAllStudent,"onUpdate:modelValue":e[8]||(e[8]=t=>o.showAllStudent=t),name:"showAllStudent",label:a.$trans("global.list_all",{attribute:a.$trans("student.student")}),error:s(n).showAllStudent,"onUpdate:error":e[9]||(e[9]=t=>s(n).showAllStudent=t)},null,8,["modelValue","label","error"])])):m("",!0),A("div",Y,[l(g,{type:"number",modelValue:o.column,"onUpdate:modelValue":e[10]||(e[10]=t=>o.column=t),name:"column",label:a.$trans("print.column"),error:s(n).column,"onUpdate:error":e[11]||(e[11]=t=>s(n).column=t)},null,8,["modelValue","label","error"])]),A("div",Z,[l(g,{type:"number",modelValue:o.cardPerPage,"onUpdate:modelValue":e[12]||(e[12]=t=>o.cardPerPage=t),name:"cardPerPage",label:a.$trans("academic.id_card.card_per_page"),error:s(n).cardPerPage,"onUpdate:error":e[13]||(e[13]=t=>s(n).cardPerPage=t)},null,8,["modelValue","label","error"])])])):m("",!0)]),_:1})):m("",!0)]),_:1},8,["form"])}}},ee={name:"AcademicIdCard"},ae=Object.assign(ee,{setup(k){const h=O(),F=j(),V=R();let w=[],_=[];const f="academic/idCard/",n=U(!0),o=U(!1),u=P({templates:[]}),c=async()=>{o.value=!0,await V.dispatch(f+"printPreRequisite").then(a=>{o.value=!1,Object.assign(u,a)}).catch(a=>{o.value=!1})},q=async()=>{o.value=!0,await V.dispatch(f+"print",{params:h.query}).then(a=>{o.value=!1,window.open("/print").document.write(a)}).catch(a=>{o.value=!1})};return L(async()=>{await c()}),(a,e)=>{const C=i("BaseButton"),B=i("PageHeaderAction"),g=i("PageHeader"),y=i("ParentTransition"),N=i("BaseCard");return r(),p(D,null,[l(g,{title:a.$trans(s(h).meta.label),navs:[{label:a.$trans("academic.academic"),path:"Academic"}]},{default:d(()=>[l(B,{name:"AcademicIdCard",title:a.$trans("academic.id-card.id-card"),actions:s(w),"dropdown-actions":s(_),onToggleFilter:e[1]||(e[1]=$=>n.value=!n.value)},{default:d(()=>[l(C,{design:"white",onClick:e[0]||(e[0]=$=>s(F).push({name:"AcademicIdCardTemplate"}))},{default:d(()=>[v(b(a.$trans("academic.id_card.template.template")),1)]),_:1})]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"]),l(y,{appear:"",visibility:n.value},{default:d(()=>[l(x,{"is-loading":o.value,onAfterFilter:q,"init-url":f,"pre-requisites":u,onHide:e[2]||(e[2]=$=>n.value=!1)},null,8,["is-loading","pre-requisites"])]),_:1},8,["visibility"]),l(y,{appear:"",visibility:!0},{default:d(()=>[l(N,{"no-padding":"","no-content-padding":"","is-loading":o.value},null,8,["is-loading"])]),_:1})],64)}}});export{ae as default};
