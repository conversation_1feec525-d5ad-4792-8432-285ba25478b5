import{i as j,u as A,h as H,l as I,r as l,a as _,o as d,e as s,w as e,f as i,q as b,b as f,d as P,F as g,v as y,s as r,t as o,y as M}from"./app-DvIo72ZO.js";const N={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},E={name:"ResourceLessonPlanShow"},q=Object.assign(E,{setup(F){j();const p=A(),$=H(),h={},k="resource/lessonPlan/",a=I({...h}),v=t=>{Object.assign(a,t)};return(t,m)=>{const w=l("PageHeaderAction"),L=l("PageHeader"),B=l("TextMuted"),u=l("BaseDataView"),R=l("ListMedia"),S=l("BaseButton"),C=l("ShowButton"),T=l("BaseCard"),V=l("ShowItem"),D=l("ParentTransition");return d(),_(g,null,[s(L,{title:t.$trans(i(p).meta.trans,{attribute:t.$trans(i(p).meta.label)}),navs:[{label:t.$trans("resource.resource"),path:"Resource"},{label:t.$trans("resource.lesson_plan.lesson_plan"),path:"ResourceLessonPlan"}]},{default:e(()=>[s(w,{name:"ResourceLessonPlan",title:t.$trans("resource.lesson_plan.lesson_plan"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),s(D,{appear:"",visibility:!0},{default:e(()=>[s(V,{"init-url":k,uuid:i(p).params.uuid,"module-uuid":i(p).params.muuid,onSetItem:v,onRedirectTo:m[1]||(m[1]=n=>i($).push({name:"ResourceLessonPlan",params:{uuid:a.uuid}}))},{default:e(()=>[a.uuid?(d(),b(T,{key:0},{title:e(()=>[r(o(a.topic),1)]),footer:e(()=>[s(C,null,{default:e(()=>[i(M)("lesson-plan:edit")&&a.isEditable?(d(),b(S,{key:0,design:"primary",onClick:m[0]||(m[0]=n=>i($).push({name:"ResourceLessonPlanEdit",params:{uuid:a.uuid}}))},{default:e(()=>[r(o(t.$trans("general.edit")),1)]),_:1})):f("",!0)]),_:1})]),default:e(()=>[P("dl",N,[s(u,{label:t.$trans("academic.course.course")},{default:e(()=>[(d(!0),_(g,null,y(a.records,n=>{var c;return d(),_("div",null,[r(o(((c=n.batch.course)==null?void 0:c.name)+" "+n.batch.name)+" ",1),n.subject?(d(),b(B,{key:0},{default:e(()=>[r(o(n.subject.name),1)]),_:2},1024)):f("",!0)])}),256))]),_:1},8,["label"]),s(u,{label:t.$trans("employee.employee")},{default:e(()=>{var n;return[r(o(((n=a.employee)==null?void 0:n.name)||"-")+" ",1),s(B,{block:""},{default:e(()=>{var c;return[r(o(((c=a.employee)==null?void 0:c.designation)||""),1)]}),_:1})]}),_:1},8,["label"]),s(u,{label:t.$trans("resource.lesson_plan.props.start_date")},{default:e(()=>[r(o(a.startDate.formatted),1)]),_:1},8,["label"]),s(u,{label:t.$trans("resource.lesson_plan.props.end_date")},{default:e(()=>[r(o(a.endDate.formatted),1)]),_:1},8,["label"]),(d(!0),_(g,null,y(a.details,n=>(d(),b(u,{class:"col-span-1 sm:col-span-2",key:n.uuid,label:n.heading},{default:e(()=>[P("pre",null,o(n.description),1)]),_:2},1032,["label"]))),128)),s(u,{class:"col-span-1 sm:col-span-2"},{default:e(()=>[s(R,{media:a.media,url:`/app/resource/lesson-plans/${a.uuid}/`},null,8,["media","url"])]),_:1}),s(u,{label:t.$trans("general.created_at")},{default:e(()=>[r(o(a.createdAt.formatted),1)]),_:1},8,["label"]),s(u,{label:t.$trans("general.updated_at")},{default:e(()=>[r(o(a.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):f("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{q as default};
