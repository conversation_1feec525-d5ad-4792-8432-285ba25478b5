import{i as w,u as k,h as S,l as v,r as n,a as C,o as u,e as t,w as a,f as c,q as T,b as V,d as N,s as o,t as s,F as H}from"./app-DvIo72ZO.js";const D={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},R={name:"AcademicProgramInchargeShow"},F=Object.assign(R,{setup(j){w();const m=k(),d=S(),p={},g="academic/programIncharge/",r=v({...p}),_=e=>{Object.assign(r,e)};return(e,i)=>{const h=n("PageHeaderAction"),b=n("PageHeader"),f=n("TextMuted"),l=n("BaseDataView"),B=n("BaseButton"),$=n("ShowButton"),P=n("BaseCard"),y=n("ShowItem"),A=n("ParentTransition");return u(),C(H,null,[t(b,{title:e.$trans(c(m).meta.trans,{attribute:e.$trans(c(m).meta.label)}),navs:[{label:e.$trans("academic.academic"),path:"Academic"},{label:e.$trans("academic.program.program"),path:"AcademicProgram"},{label:e.$trans("academic.program_incharge.program_incharge"),path:"AcademicProgramInchargeList"}]},{default:a(()=>[t(h,{name:"AcademicProgramIncharge",title:e.$trans("academic.program_incharge.program_incharge"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(A,{appear:"",visibility:!0},{default:a(()=>[t(y,{"init-url":g,uuid:c(m).params.uuid,onSetItem:_,onRedirectTo:i[1]||(i[1]=I=>c(d).push({name:"AcademicProgramIncharge"}))},{default:a(()=>[r.uuid?(u(),T(P,{key:0},{title:a(()=>[o(s(r.program.name),1)]),footer:a(()=>[t($,null,{default:a(()=>[t(B,{design:"primary",onClick:i[0]||(i[0]=I=>c(d).push({name:"AcademicProgramInchargeEdit",params:{uuid:r.uuid}}))},{default:a(()=>[o(s(e.$trans("general.edit")),1)]),_:1})]),_:1})]),default:a(()=>[N("dl",D,[t(l,{label:e.$trans("employee.employee")},{default:a(()=>[o(s(r.employee.name)+" ",1),t(f,{block:""},{default:a(()=>[o(s(r.employee.codeNumber),1)]),_:1})]),_:1},8,["label"]),t(l,{label:e.$trans("employee.incharge.props.period")},{default:a(()=>[o(s(r.period),1)]),_:1},8,["label"]),t(l,{class:"col-span-1 sm:col-span-2",label:e.$trans("employee.incharge.props.remarks")},{default:a(()=>[o(s(r.remarks),1)]),_:1},8,["label"]),t(l,{label:e.$trans("general.created_at")},{default:a(()=>[o(s(r.createdAt.formatted),1)]),_:1},8,["label"]),t(l,{label:e.$trans("general.updated_at")},{default:a(()=>[o(s(r.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):V("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{F as default};
