import{u as U,l as I,n as j,r as n,q as b,o as m,w as e,d as C,e as o,b as $,s as d,t as s,h as E,j as O,y as g,m as x,f as l,a as F,F as T,v as q}from"./app-DvIo72ZO.js";const z={class:"grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},Q={__name:"Filter",emits:["hide"],setup(A,{emit:v}){const p=U(),h=v,w={title:"",employees:[],batches:[],startDate:"",endDate:""},c=I({...w}),f=I({employees:[],batches:[],isLoaded:!(p.query.employees||p.query.batches)});return j(async()=>{f.employees=p.query.employees?p.query.employees.split(","):[],f.batches=p.query.batches?p.query.batches.split(","):[],f.isLoaded=!0}),(_,i)=>{const r=n("BaseInput"),u=n("BaseSelectSearch"),R=n("DatePicker"),B=n("FilterForm");return m(),b(B,{"init-form":w,form:c,multiple:["employees","batches"],onHide:i[4]||(i[4]=a=>h("hide"))},{default:e(()=>[C("div",z,[C("div",G,[o(r,{type:"text",modelValue:c.title,"onUpdate:modelValue":i[0]||(i[0]=a=>c.title=a),name:"title",label:_.$trans("resource.download.props.title")},null,8,["modelValue","label"])]),C("div",J,[f.isLoaded?(m(),b(u,{key:0,multiple:"",name:"employees",label:_.$trans("global.select",{attribute:_.$trans("employee.employee")}),modelValue:c.employees,"onUpdate:modelValue":i[1]||(i[1]=a=>c.employees=a),"value-prop":"uuid","init-search":f.employees,"search-key":"name","search-action":"employee/list"},{selectedOption:e(a=>[d(s(a.value.name)+" ("+s(a.value.codeNumber)+") ",1)]),listOption:e(a=>[d(s(a.option.name)+" ("+s(a.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):$("",!0)]),C("div",K,[o(R,{start:c.startDate,"onUpdate:start":i[2]||(i[2]=a=>c.startDate=a),end:c.endDate,"onUpdate:end":i[3]||(i[3]=a=>c.endDate=a),name:"startDateBetween",as:"range",label:_.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},W={name:"ResourceDownloadList"},Y=Object.assign(W,{setup(A){const v=E(),p=O("emitter");let h=["filter"];g("resource:config")&&h.push("config"),g("download:create")&&h.unshift("create");let w=[];g("download:export")&&(w=["print","pdf","excel"]);const c="resource/download/",f=x(!1),_=I({}),i=r=>{Object.assign(_,r)};return(r,u)=>{const R=n("PageHeaderAction"),B=n("PageHeader"),a=n("ParentTransition"),D=n("DataCell"),S=n("TextMuted"),k=n("FloatingMenuItem"),L=n("FloatingMenu"),M=n("DataRow"),N=n("BaseButton"),H=n("DataTable"),P=n("ListItem");return m(),b(P,{"init-url":c,"additional-query":{},onSetItems:i},{header:e(()=>[o(B,{title:r.$trans("resource.download.download"),navs:[{label:r.$trans("resource.resource"),path:"Resource"}]},{default:e(()=>[o(R,{url:"resource/downloads/",name:"ResourceDownload",title:r.$trans("resource.download.download"),actions:l(h),"dropdown-actions":l(w),"config-path":"ResourceConfig",onToggleFilter:u[0]||(u[0]=t=>f.value=!f.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[o(a,{appear:"",visibility:f.value},{default:e(()=>[o(Q,{onRefresh:u[1]||(u[1]=t=>l(p).emit("listItems")),onHide:u[2]||(u[2]=t=>f.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[o(a,{appear:"",visibility:!0},{default:e(()=>[o(H,{header:_.headers,meta:_.meta,module:"resource.download",onRefresh:u[4]||(u[4]=t=>l(p).emit("listItems"))},{actionButton:e(()=>[l(g)("download:create")?(m(),b(N,{key:0,onClick:u[3]||(u[3]=t=>l(v).push({name:"ResourceDownloadCreate"}))},{default:e(()=>[d(s(r.$trans("global.add",{attribute:r.$trans("resource.download.download")})),1)]),_:1})):$("",!0)]),default:e(()=>[(m(!0),F(T,null,q(_.data,t=>(m(),b(M,{key:t.uuid,onDoubleClick:y=>l(v).push({name:"ResourceDownloadShow",params:{uuid:t.uuid}})},{default:e(()=>[o(D,{name:"title"},{default:e(()=>[d(s(t.titleExcerpt),1)]),_:2},1024),o(D,{name:"employee"},{default:e(()=>{var y;return[d(s(((y=t.employee)==null?void 0:y.name)||"-")+" ",1),o(S,{block:""},{default:e(()=>{var V;return[d(s((V=t.employee)==null?void 0:V.codeNumber),1)]}),_:2},1024)]}),_:2},1024),o(D,{name:"expiresAt"},{default:e(()=>[d(s(t.expiresAt.formatted),1)]),_:2},1024),o(D,{name:"audience"},{default:e(()=>[(m(!0),F(T,null,q(t.audienceTypes,y=>(m(),F("div",null,s(y),1))),256))]),_:2},1024),o(D,{name:"createdAt"},{default:e(()=>[d(s(t.createdAt.formatted),1)]),_:2},1024),o(D,{name:"action"},{default:e(()=>[o(L,null,{default:e(()=>[o(k,{icon:"fas fa-arrow-circle-right",onClick:y=>l(v).push({name:"ResourceDownloadShow",params:{uuid:t.uuid}})},{default:e(()=>[d(s(r.$trans("general.show")),1)]),_:2},1032,["onClick"]),l(g)("download:edit")&&t.isEditable?(m(),b(k,{key:0,icon:"fas fa-edit",onClick:y=>l(v).push({name:"ResourceDownloadEdit",params:{uuid:t.uuid}})},{default:e(()=>[d(s(r.$trans("general.edit")),1)]),_:2},1032,["onClick"])):$("",!0),l(g)("download:create")?(m(),b(k,{key:1,icon:"fas fa-copy",onClick:y=>l(v).push({name:"ResourceDownloadDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[d(s(r.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):$("",!0),l(g)("download:delete")&&t.isDeletable?(m(),b(k,{key:2,icon:"fas fa-trash",onClick:y=>l(p).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[d(s(r.$trans("general.delete")),1)]),_:2},1032,["onClick"])):$("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Y as default};
