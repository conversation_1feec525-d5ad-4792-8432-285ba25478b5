import{u as R,i as C,m as z,I as N,l as F,n as E,ad as D,r as c,q as _,o as b,w as p,d as u,e as m,b as w,f as o,s as S,t as y,a as B,F as O}from"./app-DvIo72ZO.js";import{_ as I}from"./lodash-BPUmB9Gy.js";const M={class:"grid grid-cols-3 gap-6"},x={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={key:0,class:"ml-1"},Q={key:0,class:"ml-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"flex items-center gap-2"},Z={class:"grid grid-cols-3 gap-6"},ee={class:"col-span-3 sm:col-span-1"},te={class:"col-span-3 sm:col-span-1"},ae={class:"mt-4 grid grid-cols-4 gap-6"},se={class:"col-span-3 sm:col-span-1"},re={class:"col-span-3 sm:col-span-1"},oe={class:"mt-4 grid grid-cols-4 gap-6"},le={class:"col-span-4 sm:col-span-1"},ne={class:"col-span-4 sm:col-span-1"},ie={class:"col-span-4 sm:col-span-1"},ue={class:"col-span-4 sm:col-span-1"},me={__name:"Filter",props:{initUrl:{type:String,default:""},preRequisites:{type:Object,default(){return{}}}},emits:["hide","afterFilter"],setup(A,{emit:V}){const r=R(),h=C(),g=A,d=V,q={attempt:"first",batch:"",terms:[],subjects:[],title:"",showPrintDateTime:!1,signatory1:"",signatory2:"",signatory3:"",signatory4:"",showWatermark:!1,paper_size:"A4-L"},L=[{label:"A4 Portrait",value:"A4"},{label:"A4 Landscape",value:"A4-L"},{label:"A3 Portrait",value:"A3"},{label:"A3 Landscape",value:"A3-L"}],k=z(!1),$=z(!1),l=N(g.initUrl),a=F({...q}),i=F({batches:[],subjects:[]}),n=F({batch:"",subjects:[],isLoaded:!(r.query.batch&&r.query.subjects)}),v=async s=>{if(!s){a.batch="",a.subjects=[],i.subjects=[];return}a.batch=s,i.subjects=[],a.subjects=[],k.value=!0,await h.dispatch("academic/batch/listSubjects",{uuid:s}).then(e=>{i.subjects=e,k.value=!1}).catch(e=>{k.value=!1})};return E(async()=>{if(I.isEmpty(r.query)){n.isLoaded=!0;return}a.attempt=r.query.attempt||"first",n.batch=r.query.batch,a.batch=r.query.batch,a.showWatermark=D(r.query.showWatermark||""),a.showPrintDateTime=D(r.query.showPrintDateTime||""),a.paper_size=r.query.paper_size||"A4-L",a.title=r.query.title||"",a.signatory1=r.query.signatory1||"",a.signatory2=r.query.signatory2||"",a.signatory3=r.query.signatory3||"",a.signatory4=r.query.signatory4||"",r.query.terms&&(a.terms=Array.isArray(r.query.terms)?r.query.terms:r.query.terms.split(",")),r.query.batch&&(await v(r.query.batch),a.subjects=r.query.subjects?r.query.subjects.split(","):[],n.subjects=r.query.subjects?r.query.subjects.split(","):[]),n.isLoaded=!0}),(s,e)=>{const f=c("BaseSelect"),P=c("BaseSelectSearch"),T=c("BaseSwitch"),U=c("BaseInput"),W=c("BaseFieldset"),H=c("FilterForm");return b(),_(H,{"init-form":q,multiple:["terms","subjects"],form:a,onHide:e[25]||(e[25]=t=>d("hide")),onAfterFilter:e[26]||(e[26]=t=>d("afterFilter"))},{default:p(()=>[u("div",M,[u("div",x,[n.isLoaded?(b(),_(f,{key:0,modelValue:a.attempt,"onUpdate:modelValue":e[0]||(e[0]=t=>a.attempt=t),name:"attempt",label:s.$trans("exam.schedule.props.attempt"),options:A.preRequisites.attempts||[],error:o(l).attempt,"onUpdate:error":e[1]||(e[1]=t=>o(l).attempt=t)},null,8,["modelValue","label","options","error"])):w("",!0)]),u("div",G,[n.isLoaded?(b(),_(P,{key:0,name:"batch",label:s.$trans("global.select",{attribute:s.$trans("academic.batch.batch")}),modelValue:a.batch,"onUpdate:modelValue":e[2]||(e[2]=t=>a.batch=t),error:o(l).batch,"onUpdate:error":e[3]||(e[3]=t=>o(l).batch=t),"value-prop":"uuid","init-search":n.batch,"search-key":"course_batch","search-action":"academic/batch/list",onChange:v},{selectedOption:p(t=>[S(y(t.value.course.name)+" "+y(t.value.name),1)]),listOption:p(t=>[S(y(t.option.course.nameWithTerm)+" "+y(t.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):w("",!0)]),u("div",J,[n.isLoaded?(b(),_(f,{key:0,modelValue:a.terms,"onUpdate:modelValue":e[4]||(e[4]=t=>a.terms=t),name:"terms",label:s.$trans("exam.term.term"),"value-prop":"uuid",options:A.preRequisites.terms||[],error:o(l).terms,"onUpdate:error":e[5]||(e[5]=t=>o(l).terms=t),multiple:!0,placeholder:s.$trans("exam.report.cumulative_mark_summary.all_terms")},{selectedOption:p(t=>{var j;return[S(y(t.value.name)+" ",1),t.value.division?(b(),B("span",K,"("+y(((j=t.value.division)==null?void 0:j.name)||s.$trans("general.all"))+")",1)):w("",!0)]}),listOption:p(t=>{var j;return[S(y(t.option.name)+" ",1),t.option.division?(b(),B("span",Q,"("+y(((j=t.option.division)==null?void 0:j.name)||s.$trans("general.all"))+")",1)):w("",!0)]}),_:1},8,["modelValue","label","options","error","placeholder"])):w("",!0)]),u("div",X,[m(f,{multiple:"",modelValue:a.subjects,"onUpdate:modelValue":e[6]||(e[6]=t=>a.subjects=t),name:"subjects",label:s.$trans("academic.subject.subject"),"label-prop":"name","value-prop":"uuid",options:i.subjects,error:o(l).subjects,"onUpdate:error":e[7]||(e[7]=t=>o(l).subjects=t)},null,8,["modelValue","label","options","error"])])]),m(W,{class:"mt-4"},{legend:p(()=>[u("div",Y,[S(y(s.$trans("global.show",{attribute:s.$trans("general.options")}))+" ",1),m(T,{reverse:"",modelValue:$.value,"onUpdate:modelValue":e[8]||(e[8]=t=>$.value=t),name:"showOptions"},null,8,["modelValue"])])]),default:p(()=>[$.value?(b(),B(O,{key:0},[u("div",Z,[u("div",ee,[m(T,{vertical:"",modelValue:a.showWatermark,"onUpdate:modelValue":e[9]||(e[9]=t=>a.showWatermark=t),name:"showWatermark",label:s.$trans("global.show",{attribute:s.$trans("print.watermark")}),error:o(l).showWatermark,"onUpdate:error":e[10]||(e[10]=t=>o(l).showWatermark=t)},null,8,["modelValue","label","error"])]),u("div",te,[m(T,{vertical:"",modelValue:a.showPrintDateTime,"onUpdate:modelValue":e[11]||(e[11]=t=>a.showPrintDateTime=t),name:"showPrintDateTime",label:s.$trans("global.show",{attribute:s.$trans("general.print_date_time")}),error:o(l).showPrintDateTime,"onUpdate:error":e[12]||(e[12]=t=>o(l).showPrintDateTime=t)},null,8,["modelValue","label","error"])])]),u("div",ae,[u("div",se,[m(U,{type:"text",modelValue:a.title,"onUpdate:modelValue":e[13]||(e[13]=t=>a.title=t),name:"title",label:s.$trans("print.title"),error:o(l).title,"onUpdate:error":e[14]||(e[14]=t=>o(l).title=t)},null,8,["modelValue","label","error"])]),u("div",re,[m(f,{modelValue:a.paper_size,"onUpdate:modelValue":e[15]||(e[15]=t=>a.paper_size=t),name:"paper_size",label:s.$trans("print.paper_size"),options:L,error:o(l).paper_size,"onUpdate:error":e[16]||(e[16]=t=>o(l).paper_size=t)},null,8,["modelValue","label","error"])])]),u("div",oe,[u("div",le,[m(U,{type:"text",modelValue:a.signatory1,"onUpdate:modelValue":e[17]||(e[17]=t=>a.signatory1=t),name:"signatory1",label:s.$trans("print.signatory1"),error:o(l).signatory1,"onUpdate:error":e[18]||(e[18]=t=>o(l).signatory1=t)},null,8,["modelValue","label","error"])]),u("div",ne,[m(U,{type:"text",modelValue:a.signatory2,"onUpdate:modelValue":e[19]||(e[19]=t=>a.signatory2=t),name:"signatory2",label:s.$trans("print.signatory2"),error:o(l).signatory2,"onUpdate:error":e[20]||(e[20]=t=>o(l).signatory2=t)},null,8,["modelValue","label","error"])]),u("div",ie,[m(U,{type:"text",modelValue:a.signatory3,"onUpdate:modelValue":e[21]||(e[21]=t=>a.signatory3=t),name:"signatory3",label:s.$trans("print.signatory3"),error:o(l).signatory3,"onUpdate:error":e[22]||(e[22]=t=>o(l).signatory3=t)},null,8,["modelValue","label","error"])]),u("div",ue,[m(U,{type:"text",modelValue:a.signatory4,"onUpdate:modelValue":e[23]||(e[23]=t=>a.signatory4=t),name:"signatory4",label:s.$trans("print.signatory4"),error:o(l).signatory4,"onUpdate:error":e[24]||(e[24]=t=>o(l).signatory4=t)},null,8,["modelValue","label","error"])])])],64)):w("",!0)]),_:1})]),_:1},8,["form"])}}},de={name:"ExamReportCumulativeMarkSummary"},ye=Object.assign(de,{setup(A){const V=R(),r=C(),h="exam/report/",g=z(!0),d=z(!1),q=F({terms:[],attempts:[]}),L=async()=>{d.value=!0;try{const i="/exam/reports/cumulative-mark-summary/download-pdf",n={...V.query};!n.paper_size&&n.paperSize&&(n.paper_size=n.paperSize,delete n.paperSize);const v=new URLSearchParams(n).toString(),s=`${i}?${v}`,e=document.createElement("a");e.href=s,e.setAttribute("download","cumulative-mark-summary.pdf"),e.setAttribute("target","_blank"),document.body.appendChild(e),e.click(),setTimeout(()=>{document.body.removeChild(e)},100)}catch(i){console.error("Download failed:",i)}finally{d.value=!1}};let k=["filter"],$=[{name:"download_pdf",label:"Download PDF",icon:"fas fa-file-pdf",action:L}];const l=async()=>{d.value=!0,await r.dispatch(h+"preRequisite",{name:"cumulative-mark-summary"}).then(i=>{d.value=!1,Object.assign(q,i)}).catch(i=>{d.value=!1})},a=async()=>{d.value=!0,await r.dispatch(h+"fetchReport",{name:"cumulative-mark-summary",params:V.query}).then(i=>{d.value=!1,window.open("/print").document.write(i)}).catch(i=>{d.value=!1})};return E(async()=>{await l()}),(i,n)=>{const v=c("PageHeaderAction"),s=c("PageHeader"),e=c("ParentTransition"),f=c("BaseCard");return b(),B(O,null,[m(s,{title:i.$trans(o(V).meta.label),navs:[{label:i.$trans("exam.exam"),path:"Exam"},{label:i.$trans("exam.report.report"),path:"ExamReport"}]},{default:p(()=>[m(v,{url:"exam/reports/cumulative-mark-summary/",name:"ExamReportCumulativeMarkSummary",title:i.$trans("exam.report.cumulative_mark_summary.cumulative_mark_summary"),actions:o(k),"dropdown-actions":o($),onToggleFilter:n[0]||(n[0]=P=>g.value=!g.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"]),m(e,{appear:"",visibility:g.value},{default:p(()=>[m(me,{onAfterFilter:a,"init-url":h,"pre-requisites":q,onHide:n[1]||(n[1]=P=>g.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"]),m(e,{appear:"",visibility:!0},{default:p(()=>[m(f,{"no-padding":"","no-content-padding":"","is-loading":d.value},null,8,["is-loading"])]),_:1})],64)}}});export{ye as default};
