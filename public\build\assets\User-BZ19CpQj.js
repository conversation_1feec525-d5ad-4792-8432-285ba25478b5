import{u as x,h as S,i as j,j as H,I as N,l as h,r as n,a as P,o as V,q as A,b as E,e as r,w as l,d as u,f as t,s as g,t as m,F as L}from"./app-DvIo72ZO.js";const $={class:"space-x-4"},G={class:"grid grid-cols-2 gap-6"},q={class:"col-span-2"},z={class:"col-span-2 sm:col-span-1"},D={class:"col-span-2 sm:col-span-1"},I={name:"TeamConfigUserPermission"},J=Object.assign(I,{props:{team:{type:Object,default(){return{name:""}}}},setup(f){const c=x(),B=S(),_=j(),o=H("$trans"),p="team/permission/",i=N(p),v={action:"",users:[],permissions:[]};h({});const a=h({...v}),C=[{label:o("general.assign"),value:"assign"},{label:o("general.revoke"),value:"revoke"}],y=async d=>await _.dispatch(p+"search",{query:d,uuid:c.params.uuid}).then(s=>s).catch(s=>{}),U=async d=>await _.dispatch(p+"searchUser",{query:d,uuid:c.params.uuid}).then(s=>s).catch(s=>{});return(d,s)=>{const k=n("BaseButton"),T=n("PageHeader"),w=n("CardHeader"),O=n("BaseRadioGroup"),b=n("BaseSelect"),F=n("FormAction"),R=n("ParentTransition");return V(),P(L,null,[f.team.uuid?(V(),A(T,{key:0,title:t(o)(t(c).meta.label),navs:[{label:t(o)("team.team"),path:"TeamList"},{label:f.team.name,path:{name:"TeamShow",params:{uuid:f.team.uuid}}},{label:t(o)("team.config.config"),path:"TeamConfig"}]},{default:l(()=>[u("div",$,[r(k,{onClick:s[0]||(s[0]=e=>t(B).push({name:"TeamConfigPermission"}))},{default:l(()=>[g(m(t(o)("team.config.permission.role_permission")),1)]),_:1})])]),_:1},8,["title","navs"])):E("",!0),r(R,{appear:"",visibility:!0},{default:l(()=>[r(F,{"no-data-fetch":"","init-url":p,uuid:t(c).params.uuid,action:"userWiseAssign","init-form":v,form:a,"reset-form":"","keep-adding":!1},{default:l(()=>[r(w,{first:"",title:t(o)("team.config.permission.user_permission_config"),description:t(o)("team.config.permission.user_permission_info")},null,8,["title","description"]),u("div",G,[u("div",q,[r(O,{options:C,name:"action",modelValue:a.action,"onUpdate:modelValue":s[1]||(s[1]=e=>a.action=e),error:t(i).action,"onUpdate:error":s[2]||(s[2]=e=>t(i).action=e),horizontal:""},null,8,["modelValue","error"])]),u("div",z,[r(b,{modelValue:a.users,"onUpdate:modelValue":s[3]||(s[3]=e=>a.users=e),filterResults:!1,minChars:1,resolveOnLoad:!1,delay:500,multiple:"",name:"users","value-prop":"uuid",options:async function(e){return await U(e)},label:t(o)("global.select",{attribute:t(o)("user.user")}),error:t(i).users,"onUpdate:error":s[4]||(s[4]=e=>t(i).users=e)},{selectedOption:l(e=>[g(m(e.value.profile.name)+" ("+m(e.value.email)+") ",1)]),listOption:l(e=>[g(m(e.option.profile.name)+" ("+m(e.option.email)+") ",1)]),_:1},8,["modelValue","options","label","error"])]),u("div",D,[r(b,{modelValue:a.permissions,"onUpdate:modelValue":s[5]||(s[5]=e=>a.permissions=e),filterResults:!1,minChars:1,resolveOnLoad:!1,delay:500,multiple:"",name:"permissions",options:async function(e){return await y(e)},label:t(o)("global.select",{attribute:t(o)("team.config.permission.permission")}),error:t(i).permissions,"onUpdate:error":s[6]||(s[6]=e=>t(i).permissions=e)},null,8,["modelValue","options","label","error"])])])]),_:1},8,["uuid","form"])]),_:1})],64)}}});export{J as default};
