import{i as V,u as F,h as R,j as C,l as k,I as U,m as j,L as z,n as H,B as G,r as p,a as O,o as u,q as r,b as m,e as d,w as n,f as e,F as A,s,t as o,y as v,d as J}from"./app-DvIo72ZO.js";const K={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-4"},W={name:"ExamOnlineExamShow"},Z=Object.assign(W,{setup(X){V();const _=F(),g=R(),a=C("$trans"),b=C("emitter");let B=["list"];const S={},$={},y="exam/onlineExam/";k({}),U(y),k({...$});const f=j(!1),t=k({...S}),I=E=>{Object.assign(t,E)};return z(()=>_.params.uuid,E=>{f.value=!0}),H(()=>{G(["student","guardian"],"any")&&g.push({name:"ExamOnlineExam",params:{uuid:t.uuid}})}),(E,i)=>{const c=p("BaseButton"),h=p("DropdownItem"),Q=p("PageHeaderAction"),q=p("PageHeader"),P=p("BaseTab"),x=p("BaseDataView"),M=p("TextMuted"),T=p("BaseCard"),D=p("router-view"),L=p("ShowItem"),N=p("ParentTransition");return u(),O(A,null,[t.uuid?(u(),r(q,{key:0,title:e(a)(e(_).meta.trans,{attribute:e(a)(e(_).meta.label)}),navs:[{label:e(a)("exam.online_exam.online_exam"),path:"ExamOnlineExamList"},{label:t.title,path:{name:"ExamOnlineExamShow",params:{uuid:t.uuid}}}]},{default:n(()=>[d(Q,{name:"ExamOnlineExam",title:e(a)("exam.online_exam.online_exam"),actions:e(B)},{dropdown:n(()=>[e(v)("online-exam:edit")&&t.isEditable?(u(),r(h,{key:0,icon:"fas fa-pencil",onClick:i[3]||(i[3]=l=>e(g).push({name:"ExamOnlineExamEdit",params:{uuid:t.uuid}}))},{default:n(()=>[s(o(e(a)("global.edit",{attribute:e(a)("exam.online_exam.online_exam")})),1)]),_:1})):m("",!0),e(v)("online-exam:delete")&&t.canDelete?(u(),r(h,{key:1,icon:"fas fa-trash",onClick:i[4]||(i[4]=l=>e(b).emit("showDeleteItem",{uuid:t.uuid}))},{default:n(()=>[s(o(e(a)("global.delete",{attribute:e(a)("exam.online_exam.online_exam")})),1)]),_:1})):m("",!0)]),default:n(()=>[t.publishedAt.value?m("",!0):(u(),O(A,{key:0},[e(_).name=="ExamOnlineExamQuestionList"&&t.canManageQuestion?(u(),r(c,{key:0,design:"white",onClick:i[0]||(i[0]=l=>e(b).emit("addQuestion"))},{default:n(()=>[s(o(e(a)("global.add",{attribute:e(a)("exam.online_exam.props.question")})),1)]),_:1})):m("",!0),e(_).name=="ExamOnlineExamQuestionList"&&t.canManageQuestion&&e(v)("question-bank:read")?(u(),r(c,{key:1,design:"white",onClick:i[1]||(i[1]=l=>e(b).emit("importFromQuestionBank"))},{default:n(()=>[s(o(e(a)("exam.question_bank.import_from_question_bank")),1)]),_:1})):m("",!0),e(_).name=="ExamOnlineExamQuestionList"&&t.canManageQuestion?(u(),r(c,{key:2,design:"white",onClick:i[2]||(i[2]=l=>e(b).emit("reorderQuestion"))},{default:n(()=>[s(o(e(a)("global.reorder",{attribute:e(a)("exam.online_exam.props.question")})),1)]),_:1})):m("",!0)],64))]),_:1},8,["title","actions"])]),_:1},8,["title","navs"])):m("",!0),d(N,{appear:"",visibility:!0},{default:n(()=>[d(L,{"init-url":y,uuid:e(_).params.uuid,onSetItem:I,onRedirectTo:i[10]||(i[10]=l=>e(g).push({name:"ExamOnlineExam"})),refresh:f.value,onRefreshed:i[11]||(i[11]=l=>f.value=!1)},{default:n(()=>[t?(u(),r(P,{key:0,tabs:[{name:"ExamOnlineExamShowGeneral",icon:"fas fa-home",label:e(a)("general.detail"),path:"ExamOnlineExamShowGeneral"},{name:"ExamOnlineExamQuestion",icon:"far fa-circle-question",label:e(a)("exam.online_exam.question.question"),count:t.questionsCount,path:"ExamOnlineExamQuestionList"},{name:"ExamOnlineExamSubmission",icon:"fas fa-list",label:e(a)("exam.online_exam.submission.submission"),count:t.submissionsCount,path:"ExamOnlineExamSubmissionList"}]},null,8,["tabs"])):m("",!0),t.uuid?(u(),r(T,{key:1},{title:n(()=>[s(o(t.title),1)]),action:n(()=>[t.canUpdateStatus&&!t.isCompleted&&!t.publishedAt.value?(u(),r(c,{key:0,size:"xs",design:"success",onClick:i[5]||(i[5]=l=>e(b).emit("showActionItem",{uuid:t.uuid,action:"status",data:{status:"publish"},confirmation:!0}))},{default:n(()=>[s(o(e(a)("global.publish",{attribute:e(a)("exam.online_exam.online_exam")})),1)]),_:1})):m("",!0),t.canUpdateStatus&&!t.publishedAt.value&&t.publishedAt.value?(u(),r(c,{key:1,size:"xs",design:"danger",onClick:i[6]||(i[6]=l=>e(b).emit("showActionItem",{uuid:t.uuid,action:"status",data:{status:"unpublish"},confirmation:!0}))},{default:n(()=>[s(o(e(a)("global.unpublish",{attribute:e(a)("exam.online_exam.online_exam")})),1)]),_:1})):m("",!0),t.canUpdateStatus&&t.isCompleted&&!t.resultPublishedAt.value?(u(),r(c,{key:2,size:"xs",design:"success",onClick:i[7]||(i[7]=l=>e(b).emit("showActionItem",{uuid:t.uuid,action:"status",data:{status:"publish_result"},confirmation:!0}))},{default:n(()=>[s(o(e(a)("global.publish",{attribute:e(a)("exam.online_exam.result")})),1)]),_:1})):m("",!0),t.canUpdateStatus&&t.isCompleted&&t.resultPublishedAt.value?(u(),r(c,{key:3,size:"xs",design:"danger",onClick:i[8]||(i[8]=l=>e(b).emit("showActionItem",{uuid:t.uuid,action:"status",data:{status:"unpublish_result"},confirmation:!0}))},{default:n(()=>[s(o(e(a)("global.unpublish",{attribute:e(a)("exam.online_exam.result")})),1)]),_:1})):m("",!0)]),default:n(()=>[J("dl",K,[d(x,{label:e(a)("exam.online_exam.props.type")},{default:n(()=>{var l;return[s(o((l=t.type)==null?void 0:l.label),1)]}),_:1},8,["label"]),d(x,{label:e(a)("exam.online_exam.props.date")},{default:n(()=>[s(o(t.date.formatted),1)]),_:1},8,["label"]),d(x,{label:e(a)("exam.online_exam.props.period")},{default:n(()=>[s(o(t.period),1)]),_:1},8,["label"]),d(x,{label:e(a)("exam.online_exam.props.duration")},{default:n(()=>[s(o(t.duration),1)]),_:1},8,["label"]),d(x,{label:e(a)("exam.online_exam.props.pass_percentage")},{default:n(()=>[s(o(t.passPercentage.formatted),1)]),_:1},8,["label"]),t.hasNegativeMarking?(u(),r(x,{key:0,label:e(a)("exam.online_exam.props.negative_mark_percent_per_question")},{default:n(()=>[s(o(t.negativeMarkPercentPerQuestion),1)]),_:1},8,["label"])):m("",!0),d(x,{class:"col-span-1 sm:col-span-2",label:e(a)("employee.employee")},{default:n(()=>{var l;return[s(o(((l=t.employee)==null?void 0:l.name)||"-")+" ",1),d(M,{block:""},{default:n(()=>{var w;return[s(o((w=t.employee)==null?void 0:w.codeNumber),1)]}),_:1})]}),_:1},8,["label"]),d(x,{label:e(a)("general.created_at")},{default:n(()=>[s(o(t.createdAt.formatted),1)]),_:1},8,["label"]),d(x,{label:e(a)("general.updated_at")},{default:n(()=>[s(o(t.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):m("",!0),t.uuid?(u(),r(D,{key:2,"online-exam":t,onRefresh:i[9]||(i[9]=l=>f.value=!0)},null,8,["online-exam"])):m("",!0)]),_:1},8,["uuid","refresh"])]),_:1})],64)}}});export{Z as default};
