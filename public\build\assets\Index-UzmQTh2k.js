import{u as A,l as D,n as H,r,q as b,o as v,w as e,d as $,e as t,h as L,j as T,y,m as O,f as i,a as E,F as z,v as G,s as c,t as p,b as w}from"./app-DvIo72ZO.js";const J={class:"grid grid-cols-3 gap-6"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(N,{emit:_}){A();const P=_,k=N,I={codeNumber:"",startDate:"",endDate:"",inventory:"",fromPlaces:[],toPlaces:[]},s=D({...I}),f=D({inventories:k.preRequisites.inventories,places:k.preRequisites.places}),C=D({isLoaded:!0});return H(async()=>{C.isLoaded=!0}),(d,a)=>{const B=r("BaseInput"),n=r("BaseSelect"),m=r("DatePicker"),F=r("FilterForm");return v(),b(F,{"init-form":I,form:s,multiple:["fromPlaces","toPlaces"],onHide:a[6]||(a[6]=l=>P("hide"))},{default:e(()=>[$("div",J,[$("div",K,[t(B,{type:"text",modelValue:s.codeNumber,"onUpdate:modelValue":a[0]||(a[0]=l=>s.codeNumber=l),name:"codeNumber",label:d.$trans("inventory.stock_transfer.props.code_number")},null,8,["modelValue","label"])]),$("div",Q,[t(n,{modelValue:s.inventory,"onUpdate:modelValue":a[1]||(a[1]=l=>s.inventory=l),name:"inventory","label-prop":"name","value-prop":"uuid",label:d.$trans("inventory.inventory"),options:f.inventories},null,8,["modelValue","label","options"])]),$("div",W,[t(n,{multiple:"",modelValue:s.fromPlaces,"onUpdate:modelValue":a[2]||(a[2]=l=>s.fromPlaces=l),name:"fromPlaces","label-prop":"fullName","value-prop":"uuid",label:d.$trans("global.select",{attribute:d.$trans("inventory.stock_transfer.props.from")}),options:f.places},null,8,["modelValue","label","options"])]),$("div",X,[t(n,{multiple:"",modelValue:s.toPlaces,"onUpdate:modelValue":a[3]||(a[3]=l=>s.toPlaces=l),name:"toPlaces","label-prop":"fullName","value-prop":"uuid",label:d.$trans("global.select",{attribute:d.$trans("inventory.stock_transfer.props.to")}),options:f.places},null,8,["modelValue","label","options"])]),$("div",Y,[t(m,{start:s.startDate,"onUpdate:start":a[4]||(a[4]=l=>s.startDate=l),end:s.endDate,"onUpdate:end":a[5]||(a[5]=l=>s.endDate=l),name:"dateBetween",as:"range",label:d.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},x={name:"InventoryStockTransferList"},te=Object.assign(x,{setup(N){const _=L(),P=T("emitter");let k=["filter"];y("inventory:config")&&k.push("config"),y("stock-transfer:create")&&k.unshift("create");let I=[];y("stock-transfer:export")&&(I=["print","pdf","excel"]);const s="inventory/stockTransfer/",f=O(!1),C=D({inventories:[],places:[]}),d=D({}),a=n=>{Object.assign(C,n)},B=n=>{Object.assign(d,n)};return(n,m)=>{const F=r("PageHeaderAction"),l=r("PageHeader"),S=r("ParentTransition"),R=r("TextMuted"),g=r("DataCell"),V=r("FloatingMenuItem"),h=r("FloatingMenu"),q=r("DataRow"),M=r("BaseButton"),U=r("DataTable"),j=r("ListItem");return v(),b(j,{"init-url":s,"pre-requisites":!0,onSetPreRequisites:a,onSetItems:B},{header:e(()=>[t(l,{title:n.$trans("inventory.stock_transfer.stock_transfer"),navs:[{label:n.$trans("inventory.inventory"),path:"Inventory"}]},{default:e(()=>[t(F,{url:"inventory/stock-transfers/",name:"InventoryStockTransfer",title:n.$trans("inventory.stock_transfer.stock_transfer"),actions:i(k),"dropdown-actions":i(I),"config-path":"InventoryConfig",onToggleFilter:m[0]||(m[0]=o=>f.value=!f.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(S,{appear:"",visibility:f.value},{default:e(()=>[t(Z,{onRefresh:m[1]||(m[1]=o=>i(P).emit("listItems")),"pre-requisites":C,onHide:m[2]||(m[2]=o=>f.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:e(()=>[t(S,{appear:"",visibility:!0},{default:e(()=>[t(U,{header:d.headers,meta:d.meta,module:"inventory.stock_transfer",onRefresh:m[4]||(m[4]=o=>i(P).emit("listItems"))},{actionButton:e(()=>[i(y)("stock-transfer:create")?(v(),b(M,{key:0,onClick:m[3]||(m[3]=o=>i(_).push({name:"InventoryStockTransferCreate"}))},{default:e(()=>[c(p(n.$trans("global.add",{attribute:n.$trans("inventory.stock_transfer.stock_transfer")})),1)]),_:1})):w("",!0)]),default:e(()=>[(v(!0),E(z,null,G(d.data,o=>(v(),b(q,{key:o.uuid,onDoubleClick:u=>i(_).push({name:"InventoryStockTransferShow",params:{uuid:o.uuid}})},{default:e(()=>[t(g,{name:"codeNumber"},{default:e(()=>[c(p(o.codeNumber)+" ",1),t(R,{block:""},{default:e(()=>{var u;return[c(p((u=o.inventory)==null?void 0:u.name),1)]}),_:2},1024)]),_:2},1024),t(g,{name:"date"},{default:e(()=>[c(p(o.date.formatted),1)]),_:2},1024),t(g,{name:"fromPlace"},{default:e(()=>{var u;return[c(p(((u=o.fromPlace)==null?void 0:u.fullName)||"-"),1)]}),_:2},1024),t(g,{name:"toPlace"},{default:e(()=>{var u;return[c(p(((u=o.toPlace)==null?void 0:u.fullName)||"-"),1)]}),_:2},1024),t(g,{name:"createdAt"},{default:e(()=>[c(p(o.createdAt.formatted),1)]),_:2},1024),t(g,{name:"action"},{default:e(()=>[t(h,null,{default:e(()=>[t(V,{icon:"fas fa-arrow-circle-right",onClick:u=>i(_).push({name:"InventoryStockTransferShow",params:{uuid:o.uuid}})},{default:e(()=>[c(p(n.$trans("general.show")),1)]),_:2},1032,["onClick"]),i(y)("stock-transfer:edit")?(v(),b(V,{key:0,icon:"fas fa-edit",onClick:u=>i(_).push({name:"InventoryStockTransferEdit",params:{uuid:o.uuid}})},{default:e(()=>[c(p(n.$trans("general.edit")),1)]),_:2},1032,["onClick"])):w("",!0),i(y)("stock-transfer:create")?(v(),b(V,{key:1,icon:"fas fa-copy",onClick:u=>i(_).push({name:"InventoryStockTransferDuplicate",params:{uuid:o.uuid}})},{default:e(()=>[c(p(n.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):w("",!0),i(y)("stock-transfer:delete")?(v(),b(V,{key:2,icon:"fas fa-trash",onClick:u=>i(P).emit("deleteItem",{uuid:o.uuid})},{default:e(()=>[c(p(n.$trans("general.delete")),1)]),_:2},1032,["onClick"])):w("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{te as default};
