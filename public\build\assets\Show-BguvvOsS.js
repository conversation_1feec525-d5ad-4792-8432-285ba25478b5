import{i as P,u as V,h as D,l as Q,r as l,a as T,o as m,e as a,w as t,f as o,q as p,b,d as A,s as u,t as s,y as H,F as I}from"./app-DvIo72ZO.js";const N={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},j={name:"StudentQualificationShow"},R=Object.assign(j,{props:{student:{type:Object,default(){return{}}}},setup(d){P();const r=V(),f=D(),_={},g="student/qualification/",n=Q({..._}),$=e=>{Object.assign(n,e)};return(e,c)=>{const S=l("PageHeaderAction"),B=l("PageHeader"),i=l("BaseDataView"),q=l("ListMedia"),h=l("BaseButton"),w=l("ShowButton"),v=l("BaseCard"),y=l("ShowItem"),k=l("ParentTransition");return m(),T(I,null,[a(B,{title:e.$trans(o(r).meta.trans,{attribute:e.$trans(o(r).meta.label)}),navs:[{label:e.$trans("student.student"),path:"Student"},{label:d.student.contact.name,path:{name:"StudentShow",params:{uuid:d.student.uuid}}}]},{default:t(()=>[a(S,{name:"StudentQualification",title:e.$trans("student.qualification.qualification"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(k,{appear:"",visibility:!0},{default:t(()=>[a(y,{"init-url":g,uuid:o(r).params.uuid,"module-uuid":o(r).params.muuid,onSetItem:$,onRedirectTo:c[1]||(c[1]=C=>o(f).push({name:"StudentQualification",params:{uuid:d.student.uuid}}))},{default:t(()=>[n.uuid?(m(),p(v,{key:0},{title:t(()=>[u(s(n.level.name),1)]),footer:t(()=>[a(w,null,{default:t(()=>[o(H)("student:edit")?(m(),p(h,{key:0,design:"primary",onClick:c[0]||(c[0]=C=>o(f).push({name:"StudentQualificationEdit",params:{uuid:d.student.uuid,muuid:n.uuid}}))},{default:t(()=>[u(s(e.$trans("general.edit")),1)]),_:1})):b("",!0)]),_:1})]),default:t(()=>[A("dl",N,[a(i,{label:e.$trans("student.qualification.props.course")},{default:t(()=>[u(s(n.course),1)]),_:1},8,["label"]),a(i,{label:e.$trans("student.qualification.props.institute")},{default:t(()=>[u(s(n.institute),1)]),_:1},8,["label"]),a(i,{label:e.$trans("student.qualification.props.affiliated_to")},{default:t(()=>[u(s(n.affiliatedTo),1)]),_:1},8,["label"]),a(i,{label:e.$trans("student.qualification.props.result")},{default:t(()=>[u(s(n.result),1)]),_:1},8,["label"]),a(i,{label:e.$trans("student.qualification.props.start_date")},{default:t(()=>[u(s(n.startDate.formatted),1)]),_:1},8,["label"]),a(i,{label:e.$trans("student.qualification.props.end_date")},{default:t(()=>[u(s(n.endDate.formatted),1)]),_:1},8,["label"]),a(i,{class:"col-span-1 sm:col-span-2"},{default:t(()=>[a(q,{media:n.media,url:`/app/students/${d.student.uuid}/qualifications/${n.uuid}/`},null,8,["media","url"])]),_:1}),a(i,{label:e.$trans("general.created_at")},{default:t(()=>[u(s(n.createdAt.formatted),1)]),_:1},8,["label"]),a(i,{label:e.$trans("general.updated_at")},{default:t(()=>[u(s(n.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):b("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{R as default};
