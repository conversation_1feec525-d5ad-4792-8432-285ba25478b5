import{i as N,u as R,h as O,j as L,I as M,l as V,m as z,r as n,a as G,o as c,e as o,w as a,f as i,q as f,b as y,d as b,s as r,t as s,y as k,F as J}from"./app-DvIo72ZO.js";const K={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},W={class:"text-danger"},X={class:"grid grid-cols-3 gap-6"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3"},x={name:"EmployeeQualificationShow"},te=Object.assign(x,{props:{employee:{type:Object,default(){return{}}}},setup(m){N();const v=R(),B=O();L("emitter");const w={},S={status:"",comment:""},$="employee/qualification/",g=M($),p=V({...S}),_=z(!1),e=V({...w}),E=t=>{Object.assign(e,t)},h=()=>{_.value=!0};return(t,l)=>{const j=n("PageHeaderAction"),U=n("PageHeader"),T=n("BaseBadge"),u=n("BaseDataView"),A=n("ListMedia"),C=n("BaseButton"),F=n("ShowButton"),q=n("BaseCard"),I=n("BaseSelect"),P=n("BaseTextarea"),D=n("FormAction"),Q=n("ShowItem"),H=n("ParentTransition");return c(),G(J,null,[o(U,{title:t.$trans(i(v).meta.trans,{attribute:t.$trans(i(v).meta.label)}),navs:[{label:t.$trans("employee.employee"),path:"Employee"},{label:m.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:m.employee.uuid}}}]},{default:a(()=>[o(j,{name:"EmployeeQualification",title:t.$trans("employee.qualification.qualification"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),o(H,{appear:"",visibility:!0},{default:a(()=>[o(Q,{"init-url":$,uuid:i(v).params.uuid,"module-uuid":i(v).params.muuid,onSetItem:E,onRedirectTo:l[5]||(l[5]=d=>i(B).push({name:"EmployeeQualification",params:{uuid:m.employee.uuid}})),refresh:_.value,onRefreshed:l[6]||(l[6]=d=>_.value=!1)},{default:a(()=>[e.uuid?(c(),f(q,{key:0},{title:a(()=>[r(s(e.level.name),1)]),footer:a(()=>[o(F,null,{default:a(()=>[i(k)("employee:edit")?(c(),f(C,{key:0,design:"primary",onClick:l[0]||(l[0]=d=>i(B).push({name:"EmployeeQualificationEdit",params:{uuid:m.employee.uuid,muuid:e.uuid}}))},{default:a(()=>[r(s(t.$trans("general.edit")),1)]),_:1})):y("",!0)]),_:1})]),default:a(()=>[b("dl",K,[o(u,{label:t.$trans("employee.qualification.props.course")},{default:a(()=>[r(s(e.course)+" ",1),e.selfUpload?(c(),f(T,{key:0,design:e.verificationStatus.color},{default:a(()=>[r(s(e.verificationStatus.label),1)]),_:1},8,["design"])):y("",!0)]),_:1},8,["label"]),o(u,{label:t.$trans("employee.qualification.props.institute")},{default:a(()=>[r(s(e.institute),1)]),_:1},8,["label"]),o(u,{label:t.$trans("employee.qualification.props.affiliated_to")},{default:a(()=>[r(s(e.affiliatedTo),1)]),_:1},8,["label"]),o(u,{label:t.$trans("employee.qualification.props.result")},{default:a(()=>[r(s(e.result),1)]),_:1},8,["label"]),o(u,{label:t.$trans("employee.qualification.props.start_date")},{default:a(()=>[r(s(e.startDate.formatted),1)]),_:1},8,["label"]),o(u,{label:t.$trans("employee.qualification.props.end_date")},{default:a(()=>[r(s(e.endDate.formatted),1)]),_:1},8,["label"]),e.selfUpload&&e.verificationStatus.value=="rejected"?(c(),f(u,{key:0,class:"col-span-1 sm:col-span-2",label:t.$trans("contact.verification.props.comment")},{default:a(()=>[b("span",W,s(e.comment),1)]),_:1},8,["label"])):y("",!0),o(u,{class:"col-span-1 sm:col-span-2"},{default:a(()=>[o(A,{media:e.media,url:`/app/employees/${m.employee.uuid}/qualifications/${e.uuid}/`},null,8,["media","url"])]),_:1}),o(u,{label:t.$trans("general.created_at")},{default:a(()=>[r(s(e.createdAt.formatted),1)]),_:1},8,["label"]),o(u,{label:t.$trans("general.updated_at")},{default:a(()=>[r(s(e.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):y("",!0),i(k)("employee:self-service-action")&&e.uuid&&e.selfUpload&&e.verificationStatus.value=="pending"?(c(),f(q,{key:1},{title:a(()=>[r(s(t.$trans("contact.verification.props.action")),1)]),default:a(()=>[o(D,{"no-card":"","keep-adding":!1,"init-url":$,uuid:m.employee.uuid,"module-uuid":e.uuid,"no-data-fetch":!0,action:"action","init-form":S,form:p,"after-submit":h},{default:a(()=>[b("div",X,[b("div",Y,[o(I,{name:"status",label:t.$trans("global.select",{attribute:t.$trans("contact.verification.props.status")}),modelValue:p.status,"onUpdate:modelValue":l[1]||(l[1]=d=>p.status=d),options:[{value:"verify",label:t.$trans("contact.verification.action.verify")},{value:"reject",label:t.$trans("contact.verification.action.reject")}],error:i(g).status,"onUpdate:error":l[2]||(l[2]=d=>i(g).status=d)},null,8,["label","modelValue","options","error"])]),b("div",Z,[o(P,{modelValue:p.comment,"onUpdate:modelValue":l[3]||(l[3]=d=>p.comment=d),name:"comment",label:t.$trans("contact.comment"),error:i(g).comment,"onUpdate:error":l[4]||(l[4]=d=>i(g).comment=d)},null,8,["modelValue","label","error"])])])]),_:1},8,["uuid","module-uuid","form"])]),_:1})):y("",!0)]),_:1},8,["uuid","module-uuid","refresh"])]),_:1})],64)}}});export{te as default};
