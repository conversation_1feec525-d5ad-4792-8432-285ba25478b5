import{u as g,I as B,l as f,r as m,q as N,o as p,w as _,d as i,a as b,b as P,e as l,f as o,K as U,F as k}from"./app-DvIo72ZO.js";const M={class:"grid grid-cols-3 gap-6"},j={class:"col-span-3 sm:col-span-1"},q={class:"col-span-3 sm:col-span-1"},A={class:"col-span-3 sm:col-span-1"},D={key:0,class:"col-span-3 sm:col-span-1"},I={key:0,class:"mt-6 grid grid-cols-3 gap-6"},O={class:"col-span-3"},R={name:"FinancePaymentMethodForm"},C=Object.assign(R,{setup(w){const d=g(),s={name:"",code:"",hasInstrumentDate:!1,hasInstrumentNumber:!1,hasClearingDate:!1,hasBankDetail:!1,hasReferenceNumber:!1,isPaymentGateway:!1,paymentGatewayName:"",description:""},c="finance/paymentMethod/",n=B(c),u=f({}),t=f({...s}),V=f({isLoaded:!d.params.uuid}),h=r=>{Object.assign(u,r)},F=r=>{Object.assign(s,{...r}),Object.assign(t,U(s)),V.isLoaded=!0};return(r,e)=>{const y=m("BaseInput"),G=m("BaseSwitch"),v=m("BaseTextarea"),$=m("FormAction");return p(),N($,{"pre-requisites":!1,onSetPreRequisites:h,"init-url":c,"init-form":s,form:t,"set-form":F,redirect:"FinancePaymentMethod"},{default:_(()=>[i("div",M,[i("div",j,[l(y,{type:"text",modelValue:t.name,"onUpdate:modelValue":e[0]||(e[0]=a=>t.name=a),name:"name",label:r.$trans("finance.payment_method.props.name"),error:o(n).name,"onUpdate:error":e[1]||(e[1]=a=>o(n).name=a)},null,8,["modelValue","label","error"])]),i("div",q,[l(y,{type:"text",modelValue:t.code,"onUpdate:modelValue":e[2]||(e[2]=a=>t.code=a),name:"code",label:r.$trans("finance.payment_method.props.code"),error:o(n).code,"onUpdate:error":e[3]||(e[3]=a=>o(n).code=a)},null,8,["modelValue","label","error"])]),i("div",A,[l(G,{vertical:"",modelValue:t.isPaymentGateway,"onUpdate:modelValue":e[4]||(e[4]=a=>t.isPaymentGateway=a),name:"isPaymentGateway",label:r.$trans("finance.payment_method.props.is_payment_gateway"),error:o(n).isPaymentGateway,"onUpdate:error":e[5]||(e[5]=a=>o(n).isPaymentGateway=a)},null,8,["modelValue","label","error"])]),t.isPaymentGateway?(p(),b("div",D,[l(y,{type:"text",modelValue:t.paymentGatewayName,"onUpdate:modelValue":e[6]||(e[6]=a=>t.paymentGatewayName=a),name:"paymentGatewayName",label:r.$trans("finance.payment_method.props.payment_gateway_name"),error:o(n).paymentGatewayName,"onUpdate:error":e[7]||(e[7]=a=>o(n).paymentGatewayName=a)},null,8,["modelValue","label","error"])])):P("",!0)]),t.isPaymentGateway?P("",!0):(p(),b("div",I,[i("div",O,[l(v,{modelValue:t.description,"onUpdate:modelValue":e[8]||(e[8]=a=>t.description=a),name:"description",label:r.$trans("finance.payment_method.props.description"),error:o(n).description,"onUpdate:error":e[9]||(e[9]=a=>o(n).description=a)},null,8,["modelValue","label","error"])])]))]),_:1},8,["form"])}}}),H={name:"FinancePaymentMethodAction"},E=Object.assign(H,{setup(w){const d=g();return(s,c)=>{const n=m("PageHeaderAction"),u=m("PageHeader"),t=m("ParentTransition");return p(),b(k,null,[l(u,{title:s.$trans(o(d).meta.trans,{attribute:s.$trans(o(d).meta.label)}),navs:[{label:s.$trans("finance.finance"),path:"Finance"},{label:s.$trans("finance.payment_method.payment_method"),path:"FinancePaymentMethodList"}]},{default:_(()=>[l(n,{name:"FinancePaymentMethod",title:s.$trans("finance.payment_method.payment_method"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(t,{appear:"",visibility:!0},{default:_(()=>[l(C)]),_:1})],64)}}});export{E as default};
