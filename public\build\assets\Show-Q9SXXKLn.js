import{i as S,u as P,h as T,l as V,r,a as A,o as c,e as l,w as a,f as i,q as d,b as m,d as _,s as n,t as o,y as H,F as I}from"./app-DvIo72ZO.js";const M={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},D={name:"ReceptionCallLogShow"},F=Object.assign(D,{setup(j){S();const p=P(),g=T(),b={},f="reception/callLog/",e=V({...b}),$=t=>{Object.assign(e,t)};return(t,u)=>{const B=r("PageHeaderAction"),y=r("PageHeader"),s=r("BaseDataView"),C=r("TextMuted"),k=r("ListMedia"),w=r("BaseButton"),L=r("ShowButton"),N=r("BaseCard"),v=r("ShowItem"),R=r("ParentTransition");return c(),A(I,null,[l(y,{title:t.$trans(i(p).meta.trans,{attribute:t.$trans(i(p).meta.label)}),navs:[{label:t.$trans("reception.reception"),path:"Reception"},{label:t.$trans("reception.call_log.call_log"),path:"ReceptionCallLog"}]},{default:a(()=>[l(B,{name:"ReceptionCallLog",title:t.$trans("reception.call_log.call_log"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(R,{appear:"",visibility:!0},{default:a(()=>[l(v,{"init-url":f,uuid:i(p).params.uuid,"module-uuid":i(p).params.muuid,onSetItem:$,onRedirectTo:u[1]||(u[1]=h=>i(g).push({name:"ReceptionCallLog",params:{uuid:e.uuid}}))},{default:a(()=>[e.uuid?(c(),d(N,{key:0},{title:a(()=>[n(o(e.incomingNumber)+" ",1),u[2]||(u[2]=_("i",{class:"fas fa-arrow-right"},null,-1)),n(" "+o(e.outgoingNumber),1)]),footer:a(()=>[l(L,null,{default:a(()=>[i(H)("call-log:edit")?(c(),d(w,{key:0,design:"primary",onClick:u[0]||(u[0]=h=>i(g).push({name:"ReceptionCallLogEdit",params:{uuid:e.uuid}}))},{default:a(()=>[n(o(t.$trans("general.edit")),1)]),_:1})):m("",!0)]),_:1})]),default:a(()=>[_("dl",M,[l(s,{label:t.$trans("reception.call_log.props.type")},{default:a(()=>[n(o(e.type.label),1)]),_:1},8,["label"]),l(s,{label:t.$trans("reception.call_log.props.purpose")},{default:a(()=>[n(o(e.purpose.name),1)]),_:1},8,["label"]),l(s,{label:t.$trans("reception.call_log.props.incoming_number")},{default:a(()=>[n(o(e.incomingNumber),1)]),_:1},8,["label"]),l(s,{label:t.$trans("reception.call_log.props.outgoing_number")},{default:a(()=>[n(o(e.outgoingNumber),1)]),_:1},8,["label"]),l(s,{label:t.$trans("reception.call_log.props.call_at")},{default:a(()=>[n(o(e.callAt.formatted)+" ("+o(e.duration.label)+") ",1)]),_:1},8,["label"]),l(s,{label:t.$trans("reception.call_log.props.name")},{default:a(()=>[n(o(e.name)+" ("+o(e.count)+") ",1),e.companyName?(c(),d(C,{key:0,block:""},{default:a(()=>[n(o(e.companyName),1)]),_:1})):m("",!0)]),_:1},8,["label"]),l(s,{class:"col-span-1 sm:col-span-2",label:t.$trans("reception.call_log.props.conversation")},{default:a(()=>[n(o(e.conversation),1)]),_:1},8,["label"]),l(s,{class:"col-span-1 sm:col-span-2",label:t.$trans("reception.call_log.props.remarks")},{default:a(()=>[n(o(e.remarks),1)]),_:1},8,["label"]),l(s,{class:"col-span-1 sm:col-span-2"},{default:a(()=>[l(k,{media:e.media,url:`/app/reception/call-logs/${e.uuid}/`},null,8,["media","url"])]),_:1}),l(s,{label:t.$trans("general.created_at")},{default:a(()=>[n(o(e.createdAt.formatted),1)]),_:1},8,["label"]),l(s,{label:t.$trans("general.updated_at")},{default:a(()=>[n(o(e.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):m("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{F as default};
