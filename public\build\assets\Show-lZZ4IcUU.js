import{h as L,l as C,r as d,a as b,o as l,e as o,w as n,q as c,b as p,d as m,s,t as r,F as R,v as A,f as g,y as I}from"./app-DvIo72ZO.js";const M={class:"flex gap-2 mt-2"},O={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},q={class:"flex flex-wrap gap-1"},z={class:"flex flex-wrap gap-1"},J={name:"FinancePaymentRestrictionShow"},W=Object.assign(J,{setup(K){const v=L(),V="finance/paymentRestriction/",e=C({});C({value:!1});const N=a=>{Object.assign(e,a)},D=a=>({percentage:"primary",fixed_amount:"success",specific_fee:"warning"})[a]||"secondary",T=()=>{v.push({name:"FinancePaymentRestrictionDuplicate",params:{uuid:e.uuid}})};return(a,_)=>{const E=d("PageHeaderAction"),H=d("PageHeader"),u=d("BaseBadge"),i=d("BaseDataView"),$=d("BaseButton"),U=d("ShowButton"),j=d("BaseCard"),G=d("ShowItem");return l(),b("div",null,[o(H,{title:e.name||a.$trans("finance.payment_restriction.payment_restriction"),navs:[{label:a.$trans("finance.finance"),path:"Finance"},{label:a.$trans("finance.payment_restriction.payment_restrictions"),path:"FinancePaymentRestrictionList"}]},{default:n(()=>[o(E,{name:"FinancePaymentRestriction",title:a.$trans("finance.payment_restriction.payment_restrictions"),actions:["list","edit","delete"],uuid:e.uuid,permissions:{edit:"payment-restriction:edit",delete:"payment-restriction:delete"}},null,8,["title","uuid"])]),_:1},8,["title","navs"]),o(G,{"init-url":V,uuid:a.$route.params.uuid,onSetItem:N},{default:n(()=>[e.uuid?(l(),c(j,{key:0},{title:n(()=>[s(r(e.name)+" ",1),m("div",M,[o(u,{variant:e.isActive?"success":"danger"},{default:n(()=>[s(r(e.isActive?a.$trans("general.active"):a.$trans("general.inactive")),1)]),_:1},8,["variant"]),o(u,{variant:e.isCurrentlyEffective?"success":"warning"},{default:n(()=>[s(r(e.isCurrentlyEffective?a.$trans("general.effective"):a.$trans("general.not_effective")),1)]),_:1},8,["variant"])])]),footer:n(()=>[o(U,null,{default:n(()=>[g(I)("payment-restriction:edit")?(l(),c($,{key:0,design:"primary",onClick:_[0]||(_[0]=y=>g(v).push({name:"FinancePaymentRestrictionEdit",params:{uuid:e.uuid}}))},{default:n(()=>[s(r(a.$trans("general.edit")),1)]),_:1})):p("",!0),g(I)("payment-restriction:create")?(l(),c($,{key:1,design:"secondary",onClick:T},{default:n(()=>[s(r(a.$trans("general.duplicate")),1)]),_:1})):p("",!0)]),_:1})]),default:n(()=>{var y,h,k,B,w,P,S,F;return[m("dl",O,[o(i,{label:a.$trans("finance.payment_restriction.props.name")},{default:n(()=>[s(r(e.name),1)]),_:1},8,["label"]),o(i,{label:a.$trans("finance.payment_restriction.props.type")},{default:n(()=>{var t;return[o(u,{variant:D((t=e.type)==null?void 0:t.value)},{default:n(()=>{var f;return[s(r((f=e.type)==null?void 0:f.label),1)]}),_:1},8,["variant"])]}),_:1},8,["label"]),o(i,{label:a.$trans("finance.payment_restriction.props.scope")},{default:n(()=>[o(u,{variant:"secondary"},{default:n(()=>{var t;return[s(r((t=e.scope)==null?void 0:t.label),1)]}),_:1})]),_:1},8,["label"]),o(i,{label:a.$trans("finance.payment_restriction.props.target_users")},{default:n(()=>[o(u,{variant:"info"},{default:n(()=>{var t;return[s(r((t=e.targetUsers)==null?void 0:t.label),1)]}),_:1})]),_:1},8,["label"]),e.description?(l(),c(i,{key:0,class:"col-span-1 sm:col-span-2",label:a.$trans("finance.payment_restriction.props.description")},{default:n(()=>[s(r(e.description),1)]),_:1},8,["label"])):p("",!0),((y=e.type)==null?void 0:y.value)==="percentage"?(l(),c(i,{key:1,label:a.$trans("finance.payment_restriction.props.percentage_threshold")},{default:n(()=>{var t;return[s(r((t=e.percentageThreshold)==null?void 0:t.formatted),1)]}),_:1},8,["label"])):p("",!0),((h=e.type)==null?void 0:h.value)==="fixed_amount"?(l(),c(i,{key:2,label:a.$trans("finance.payment_restriction.props.fixed_amount")},{default:n(()=>{var t;return[s(r((t=e.fixedAmount)==null?void 0:t.formatted),1)]}),_:1},8,["label"])):p("",!0),((k=e.type)==null?void 0:k.value)==="specific_fee"&&e.feeStructure?(l(),c(i,{key:3,label:a.$trans("finance.payment_restriction.props.fee_structure")},{default:n(()=>[s(r(e.feeStructure.name),1)]),_:1},8,["label"])):p("",!0),((B=e.type)==null?void 0:B.value)==="specific_fee"&&e.feeInstallment?(l(),c(i,{key:4,label:a.$trans("finance.payment_restriction.props.fee_installment")},{default:n(()=>[s(r(e.feeInstallment.title),1)]),_:1},8,["label"])):p("",!0),((w=e.scope)==null?void 0:w.value)==="batch"&&((P=e.applicableBatches)!=null&&P.length)?(l(),c(i,{key:5,class:"col-span-1 sm:col-span-2",label:a.$trans("finance.payment_restriction.props.applicable_batches")},{default:n(()=>[m("div",q,[(l(!0),b(R,null,A(e.applicableBatches,t=>(l(),c(u,{key:t.uuid,variant:"outline"},{default:n(()=>{var f;return[s(r((f=t.course)==null?void 0:f.name)+" "+r(t.name),1)]}),_:2},1024))),128))])]),_:1},8,["label"])):p("",!0),((S=e.scope)==null?void 0:S.value)==="individual"&&((F=e.applicableStudents)!=null&&F.length)?(l(),c(i,{key:6,class:"col-span-1 sm:col-span-2",label:a.$trans("finance.payment_restriction.props.applicable_students")},{default:n(()=>[m("div",z,[(l(!0),b(R,null,A(e.applicableStudents,t=>(l(),c(u,{key:t.uuid,variant:"outline"},{default:n(()=>[s(r(t.name)+" ("+r(t.courseName)+" "+r(t.batchName)+") ",1)]),_:2},1024))),128))])]),_:1},8,["label"])):p("",!0),o(i,{label:a.$trans("finance.payment_restriction.props.grace_period_days")},{default:n(()=>[s(r(e.gracePeriodDays)+" "+r(a.$trans("general.days")),1)]),_:1},8,["label"]),e.effectiveFrom?(l(),c(i,{key:7,label:a.$trans("finance.payment_restriction.props.effective_from")},{default:n(()=>{var t;return[s(r((t=e.effectiveFrom)==null?void 0:t.formatted),1)]}),_:1},8,["label"])):p("",!0),e.effectiveUntil?(l(),c(i,{key:8,label:a.$trans("finance.payment_restriction.props.effective_until")},{default:n(()=>{var t;return[s(r((t=e.effectiveUntil)==null?void 0:t.formatted),1)]}),_:1},8,["label"])):p("",!0),o(i,{label:a.$trans("finance.payment_restriction.props.redirect_to_guest_payment")},{default:n(()=>[o(u,{variant:e.redirectToGuestPayment?"success":"secondary"},{default:n(()=>[s(r(e.redirectToGuestPayment?a.$trans("general.yes"):a.$trans("general.no")),1)]),_:1},8,["variant"])]),_:1},8,["label"]),e.restrictionMessage?(l(),c(i,{key:9,class:"col-span-1 sm:col-span-2",label:a.$trans("finance.payment_restriction.props.restriction_message")},{default:n(()=>[s(r(e.restrictionMessage),1)]),_:1},8,["label"])):p("",!0),o(i,{label:a.$trans("general.created_at")},{default:n(()=>{var t;return[s(r((t=e.createdAt)==null?void 0:t.formatted),1)]}),_:1},8,["label"]),o(i,{label:a.$trans("general.updated_at")},{default:n(()=>{var t;return[s(r((t=e.updatedAt)==null?void 0:t.formatted),1)]}),_:1},8,["label"])])]}),_:1})):p("",!0)]),_:1},8,["uuid"])])}}});export{W as default};
