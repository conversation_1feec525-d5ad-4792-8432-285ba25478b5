import{i as F,u as S,h as v,l as C,r as s,a as P,o as p,e as a,w as t,f as i,q as V,b as y,d as N,s as l,t as n,F as A}from"./app-DvIo72ZO.js";const I={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},T={name:"HostelFloorShow"},j=Object.assign(T,{setup(D){F();const d=S(),c=v(),m={},f="hostel/floor/",o=C({...m}),_=e=>{Object.assign(o,e)};return(e,u)=>{const b=s("PageHeaderAction"),h=s("PageHeader"),r=s("BaseDataView"),g=s("BaseButton"),B=s("ShowButton"),$=s("BaseCard"),H=s("ShowItem"),w=s("ParentTransition");return p(),P(A,null,[a(h,{title:e.$trans(i(d).meta.trans,{attribute:e.$trans(i(d).meta.label)}),navs:[{label:e.$trans("hostel.hostel"),path:"Hostel"},{label:e.$trans("hostel.floor.floor"),path:"HostelFloorList"}]},{default:t(()=>[a(b,{name:"HostelFloor",title:e.$trans("hostel.floor.floor"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(w,{appear:"",visibility:!0},{default:t(()=>[a(H,{"init-url":f,uuid:i(d).params.uuid,onSetItem:_,onRedirectTo:u[1]||(u[1]=k=>i(c).push({name:"HostelFloor"}))},{default:t(()=>[o.uuid?(p(),V($,{key:0},{title:t(()=>[l(n(o.name),1)]),footer:t(()=>[a(B,null,{default:t(()=>[a(g,{design:"primary",onClick:u[0]||(u[0]=k=>i(c).push({name:"HostelFloorEdit",params:{uuid:o.uuid}}))},{default:t(()=>[l(n(e.$trans("general.edit")),1)]),_:1})]),_:1})]),default:t(()=>[N("dl",I,[a(r,{label:e.$trans("hostel.floor.props.name")},{default:t(()=>[l(n(o.name),1)]),_:1},8,["label"]),a(r,{label:e.$trans("hostel.floor.props.alias")},{default:t(()=>[l(n(o.alias),1)]),_:1},8,["label"]),a(r,{label:e.$trans("hostel.block.block")},{default:t(()=>[l(n(o.blockName),1)]),_:1},8,["label"]),a(r,{class:"col-span-1 sm:col-span-2",label:e.$trans("hostel.floor.props.description")},{default:t(()=>[l(n(o.description),1)]),_:1},8,["label"]),a(r,{label:e.$trans("general.created_at")},{default:t(()=>[l(n(o.createdAt.formatted),1)]),_:1},8,["label"]),a(r,{label:e.$trans("general.updated_at")},{default:t(()=>[l(n(o.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):y("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{j as default};
