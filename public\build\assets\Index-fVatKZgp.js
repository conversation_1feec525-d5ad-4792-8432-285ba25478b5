import{u as A,j as P,l as S,I as U,n as C,r as n,q as v,o as u,w as e,d as T,b as M,s as p,t as l,i as W,y as x,m as D,a as w,e as a,f as F,F as B,v as k}from"./app-DvIo72ZO.js";const E={class:"grid grid-cols-3 gap-6"},I={class:"col-span-3 sm:col-span-1"},z={__name:"Filter",props:{initUrl:{type:String,default:""}},emits:["hide"],setup($,{emit:f}){const h=A();P("moment");const g=f,b=$,_={batches:[]},r=S({..._});U(b.initUrl);const s=S({isLoaded:!h.query.batches});return C(async()=>{s.batches=h.query.batches?h.query.batches.split(","):[],s.isLoaded=!0}),(i,c)=>{const o=n("BaseSelectSearch"),m=n("FilterForm");return u(),v(m,{"init-form":_,multiple:["batches"],form:r,onHide:c[1]||(c[1]=t=>g("hide"))},{default:e(()=>[T("div",E,[T("div",I,[s.isLoaded?(u(),v(o,{key:0,multiple:"",name:"batches",label:i.$trans("global.select",{attribute:i.$trans("academic.batch.batch")}),modelValue:r.batches,"onUpdate:modelValue":c[0]||(c[0]=t=>r.batches=t),"value-prop":"uuid","init-search":s.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:e(t=>[p(l(t.value.course.name)+" "+l(t.value.name),1)]),listOption:e(t=>[p(l(t.option.course.nameWithTerm)+" "+l(t.option.name),1)]),_:1},8,["label","modelValue","init-search"])):M("",!0)])])]),_:1},8,["form"])}}},G={name:"StudentReportBatchWiseAttendance"},K=Object.assign(G,{setup($){const f=A(),h=W();let g=["filter"],b=[];x("student:list-attendance")&&(b=["print","pdf","excel"]);const _="student/report/",r=D(!1),s=D(!1),i=S({headers:[],data:[],meta:{total:0}}),c=async()=>{s.value=!0,await h.dispatch(_+"fetchReport",{name:"batch-wise-attendance",params:f.query}).then(o=>{s.value=!1,Object.assign(i,o)}).catch(o=>{s.value=!1})};return C(async()=>{await c()}),(o,m)=>{const t=n("PageHeaderAction"),V=n("PageHeader"),R=n("ParentTransition"),H=n("TextMuted"),y=n("DataCell"),q=n("DataRow"),L=n("DataTable"),j=n("BaseCard");return u(),w(B,null,[a(V,{title:o.$trans(F(f).meta.label),navs:[{label:o.$trans("student.student"),path:"Student"},{label:o.$trans("student.report.report"),path:"StudentReport"}]},{default:e(()=>[a(t,{url:"student/reports/batch-wise-attendance/",name:"StudentReportBatchWiseAttendance",title:o.$trans("student.report.batch_wise_attendance.batch_wise_attendance"),actions:F(g),"dropdown-actions":F(b),headers:i.headers,onToggleFilter:m[0]||(m[0]=d=>r.value=!r.value)},null,8,["title","actions","dropdown-actions","headers"])]),_:1},8,["title","navs"]),a(R,{appear:"",visibility:r.value},{default:e(()=>[a(z,{onAfterFilter:c,"init-url":_,onHide:m[1]||(m[1]=d=>r.value=!1)})]),_:1},8,["visibility"]),a(R,{appear:"",visibility:!0},{default:e(()=>[a(j,{"no-padding":"","no-content-padding":"","is-loading":s.value},{default:e(()=>[a(L,{header:i.headers,meta:i.meta,module:"student.report.batch_wise_attendance",onRefresh:c},{default:e(()=>[(u(!0),w(B,null,k(i.data,d=>(u(),v(q,{key:d.uuid},{default:e(()=>[a(y,{name:"course_batch"},{default:e(()=>[p(l(d.courseBatch)+" ",1),a(H,{block:""},{default:e(()=>[p(l(d.incharge),1)]),_:2},1024)]),_:2},1024),(u(!0),w(B,null,k(d.attendances,(N,O)=>(u(),v(y,{name:O},{default:e(()=>[p(l(N),1)]),_:2},1032,["name"]))),256)),a(y,{name:"total"},{default:e(()=>[p(l(d.total),1)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","meta"])]),_:1},8,["is-loading"])]),_:1})],64)}}});export{K as default};
