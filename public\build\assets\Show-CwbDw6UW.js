import{G as C,u as D,h as S,j as y,l as P,r as g,a as c,o,e as d,w as l,f as e,b as i,d as s,q as p,s as _,t as n,am as T,x as v,F as h,v as V}from"./app-DvIo72ZO.js";const K={key:0,class:"grid grid-cols-1 gap-6 lg:grid-cols-3"},N={class:"lg:col-span-1"},$={class:"grid grid-cols-1 gap-x-4 gap-y-6"},L={class:"flex items-center"},R={class:"badge bg-primary"},z={class:"badge bg-info"},F={class:"badge bg-secondary"},O={class:"d-flex flex-wrap gap-1"},q={class:"d-flex align-items-center gap-2"},E={key:0,class:"badge bg-info"},G={class:"text-muted"},U={class:"lg:col-span-2"},J={class:"prose max-w-none"},Q=["innerHTML"],W={key:1,class:"text-center text-muted py-8"},X={name:"AIKnowledgeShow"},Z=Object.assign(X,{setup(ee){const m=D(),x=S(),a=y("$trans"),k=y("moment"),I="ai/knowledge/",t=P({}),H=u=>{Object.assign(t,u)},M=u=>u.replace(/\n\n/g,"</p><p>").replace(/\n/g,"<br>").replace(/^/,"<p>").replace(/$/,"</p>");return(u,b)=>{const B=g("PageHeaderAction"),Y=g("PageHeader"),r=g("BaseDataView"),w=g("BaseCard"),j=g("ShowItem"),A=g("ParentTransition");return o(),c(h,null,[d(Y,{title:e(a)(e(m).meta.trans,{attribute:e(a)(e(m).meta.label)}),navs:[{label:e(a)("ai.ai"),path:"AIIndex"},{label:e(a)("ai.knowledge.knowledge_base"),path:"AIKnowledge"}]},{default:l(()=>[d(B,{name:"AIKnowledge",title:e(a)("ai.knowledge.knowledge"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),d(A,{appear:"",visibility:!0},{default:l(()=>[d(j,{"init-url":I,uuid:e(m).params.uuid,onSetItem:H,onRedirectTo:b[0]||(b[0]=f=>e(x).push({name:"AIKnowledge"}))},{default:l(()=>[t.uuid?(o(),c("div",K,[s("div",N,[d(w,null,{title:l(()=>[_(n(e(a)("ai.knowledge.knowledge")),1)]),default:l(()=>[s("dl",$,[d(r,{label:e(a)("ai.knowledge.props.title")},{default:l(()=>[_(n(t.title),1)]),_:1},8,["label"]),d(r,{label:e(a)("ai.knowledge.props.type")},{default:l(()=>[s("div",L,[s("i",{class:v([t.type_icon,"me-2"]),style:T({color:t.type_color})},null,6),_(" "+n(t.type_name),1)])]),_:1},8,["label"]),t.description?(o(),p(r,{key:0,label:e(a)("ai.knowledge.props.description")},{default:l(()=>[_(n(t.description),1)]),_:1},8,["label"])):i("",!0),t.category?(o(),p(r,{key:1,label:e(a)("ai.knowledge.props.category")},{default:l(()=>[s("span",R,n(t.category),1)]),_:1},8,["label"])):i("",!0),t.subject?(o(),p(r,{key:2,label:e(a)("ai.knowledge.props.subject")},{default:l(()=>[s("span",z,n(t.subject),1)]),_:1},8,["label"])):i("",!0),t.level?(o(),p(r,{key:3,label:e(a)("ai.knowledge.props.level")},{default:l(()=>[s("span",F,n(t.level),1)]),_:1},8,["label"])):i("",!0),t.tags&&t.tags.length>0?(o(),p(r,{key:4,label:e(a)("ai.knowledge.props.tags")},{default:l(()=>[s("div",O,[(o(!0),c(h,null,V(t.tags,f=>(o(),c("span",{key:f,class:"badge bg-light text-dark"},n(f),1))),128))])]),_:1},8,["label"])):i("",!0),d(r,{label:e(a)("ai.knowledge.props.status")},{default:l(()=>[s("div",q,[s("span",{class:v(["badge",t.is_approved?"bg-success":"bg-warning"])},n(t.is_approved?e(a)("general.approved"):e(a)("general.pending")),3),t.is_public?(o(),c("span",E,n(e(a)("general.public")),1)):i("",!0)])]),_:1},8,["label"]),t.user?(o(),p(r,{key:5,label:e(a)("general.created_by")},{default:l(()=>[_(n(t.user.name),1)]),_:1},8,["label"])):i("",!0),d(r,{label:e(a)("general.created_at")},{default:l(()=>[_(n(e(k)(t.created_at).format("MMMM DD, YYYY [at] HH:mm")),1)]),_:1},8,["label"]),t.approved_by?(o(),p(r,{key:6,label:e(a)("general.approved_by")},{default:l(()=>[s("div",null,[s("div",null,n(t.approved_by.name),1),s("small",G,n(e(k)(t.approved_at).format("MMMM DD, YYYY [at] HH:mm")),1)])]),_:1},8,["label"])):i("",!0)])]),_:1})]),s("div",U,[d(w,null,{title:l(()=>[_(n(e(a)("ai.knowledge.props.content")),1)]),default:l(()=>[s("div",J,[t.content?(o(),c("div",{key:0,innerHTML:M(t.content)},null,8,Q)):(o(),c("div",W,[b[1]||(b[1]=s("i",{class:"fas fa-file-text fa-3x mb-3"},null,-1)),s("h4",null,n(e(a)("general.no_content")),1),s("p",null,n(e(a)("ai.knowledge.no_content_description")),1)]))])]),_:1})])])):i("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}}),ae=C(Z,[["__scopeId","data-v-ad05c908"]]);export{ae as default};
