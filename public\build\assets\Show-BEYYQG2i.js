import{i as R,u as $,h as F,j as g,l as O,r as o,a as p,o as u,e,w as t,f as n,q as y,b as v,d as _,F as j,v as z,s as r,t as i}from"./app-DvIo72ZO.js";const E={class:"space-y-2"},U=["innerHTML"],G={class:"space-y-4"},J={key:1,class:"px-4 py-2"},K={class:"px-4 py-2"},Q={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},W={name:"InventoryStockAdjustmentShow"},Z=Object.assign(W,{setup(X){R();const m=$(),A=F(),a=g("$trans");g("emitter");const S={},h="inventory/stockAdjustment/",L=[{key:"name",label:a("inventory.stock_item.props.name"),visibility:!0},{key:"quantity",label:a("inventory.stock_adjustment.props.quantity"),visibility:!0}],s=O({...S}),V=f=>{Object.assign(s,f)};return(f,d)=>{const w=o("PageHeaderAction"),I=o("PageHeader"),c=o("ListItemView"),x=o("ListContainerVertical"),b=o("BaseCard"),B=o("TextMuted"),k=o("DataCell"),T=o("DataRow"),C=o("SimpleTable"),D=o("BaseAlert"),H=o("ListMedia"),M=o("BaseDataView"),N=o("DetailLayoutVertical"),P=o("ShowItem"),q=o("ParentTransition");return u(),p(j,null,[e(I,{title:n(a)(n(m).meta.trans,{attribute:n(a)(n(m).meta.label)}),navs:[{label:n(a)("inventory.inventory"),path:"Inventory"},{label:n(a)("inventory.stock_adjustment.stock_adjustment"),path:"InventoryStockAdjustmentList"}]},{default:t(()=>[e(w,{name:"InventoryStockAdjustment",title:n(a)("inventory.stock_adjustment.stock_adjustment"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),e(q,{appear:"",visibility:!0},{default:t(()=>[e(P,{"init-url":h,uuid:n(m).params.uuid,onSetItem:V,onRedirectTo:d[0]||(d[0]=l=>n(A).push({name:"InventoryStockAdjustment"}))},{default:t(()=>[s.uuid?(u(),y(N,{key:0},{detail:t(()=>[_("div",E,[e(b,{"no-padding":"","no-content-padding":""},{title:t(()=>[r(i(n(a)("inventory.stock_adjustment.props.code_number"))+" "+i(s.codeNumber),1)]),action:t(()=>d[1]||(d[1]=[])),default:t(()=>[e(x,null,{default:t(()=>[e(c,{label:n(a)("inventory.inventory")},{default:t(()=>{var l;return[r(i((l=s.inventory)==null?void 0:l.name),1)]}),_:1},8,["label"]),e(c,{label:n(a)("inventory.place")},{default:t(()=>{var l;return[r(i(((l=s.place)==null?void 0:l.fullName)||"-"),1)]}),_:1},8,["label"]),e(c,{label:n(a)("inventory.stock_adjustment.props.date")},{default:t(()=>[r(i(s.date.formatted),1)]),_:1},8,["label"]),e(c,{label:n(a)("inventory.stock_adjustment.props.description")},{default:t(()=>[_("div",{innerHTML:s.description},null,8,U)]),_:1},8,["label"]),e(c,{label:n(a)("general.created_at")},{default:t(()=>[r(i(s.createdAt.formatted),1)]),_:1},8,["label"]),e(c,{label:n(a)("general.updated_at")},{default:t(()=>[r(i(s.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})])]),default:t(()=>[_("div",G,[e(b,{"no-padding":"","no-content-padding":""},{title:t(()=>[r(i(n(a)("inventory.stock_adjustment.props.items")),1)]),footer:t(()=>d[2]||(d[2]=[])),default:t(()=>[s.items.length>0?(u(),y(C,{key:0,header:L},{default:t(()=>[(u(!0),p(j,null,z(s.items,l=>(u(),y(T,{key:l.uuid},{default:t(()=>[e(k,{name:"name"},{default:t(()=>[r(i(l.item.name)+" ",1),e(B,{block:""},{default:t(()=>[r(i(l.description),1)]),_:2},1024)]),_:2},1024),e(k,{name:"quantity"},{default:t(()=>[r(i(l.quantity),1)]),_:2},1024)]),_:2},1024))),128))]),_:1})):v("",!0),s.items.length===0?(u(),p("div",J,[e(D,{design:"info",size:"xs"},{default:t(()=>[r(i(n(a)("general.errors.record_not_found")),1)]),_:1})])):v("",!0),_("div",K,[_("dl",Q,[e(M,{class:"col-span-1 sm:col-span-2"},{default:t(()=>[e(H,{media:s.media,url:`/app/inventory/stock-adjustments/${s.uuid}/`},null,8,["media","url"])]),_:1})])])]),_:1})])]),_:1})):v("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{Z as default};
