import{u as f,I as P,l as d,r as l,q as U,o as b,w as _,d as c,e as i,f as r,K as j,a as q,F as A}from"./app-DvIo72ZO.js";const O={class:"grid grid-cols-3 gap-6"},H={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},T={class:"col-span-3 sm:col-span-1"},k={name:"MessMealForm"},w=Object.assign(k,{setup(g){const p=f(),t={name:"",type:"",description:""},u="mess/meal/",o=P(u),m=d({types:[]}),a=d({...t}),y=d({isLoaded:!p.params.uuid}),v=n=>{Object.assign(m,n)},M=n=>{Object.assign(t,{...n,type:n.type.value}),Object.assign(a,j(t)),y.isLoaded=!0};return(n,e)=>{const V=l("BaseInput"),B=l("BaseSelect"),$=l("BaseTextarea"),F=l("FormAction");return b(),U(F,{"pre-requisites":!0,onSetPreRequisites:v,"init-url":u,"init-form":t,form:a,"set-form":M,redirect:"MessMeal"},{default:_(()=>[c("div",O,[c("div",H,[i(V,{type:"text",modelValue:a.name,"onUpdate:modelValue":e[0]||(e[0]=s=>a.name=s),name:"name",label:n.$trans("mess.meal.props.name"),error:r(o).name,"onUpdate:error":e[1]||(e[1]=s=>r(o).name=s),autofocus:""},null,8,["modelValue","label","error"])]),c("div",R,[i(B,{modelValue:a.type,"onUpdate:modelValue":e[2]||(e[2]=s=>a.type=s),name:"type",label:n.$trans("mess.meal.props.type"),"label-prop":"label","value-prop":"value",options:m.types,error:r(o).type,"onUpdate:error":e[3]||(e[3]=s=>r(o).type=s)},null,8,["modelValue","label","options","error"])]),c("div",T,[i($,{rows:1,modelValue:a.description,"onUpdate:modelValue":e[4]||(e[4]=s=>a.description=s),name:"description",label:n.$trans("mess.meal.props.description"),error:r(o).description,"onUpdate:error":e[5]||(e[5]=s=>r(o).description=s)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),E={name:"MessMealAction"},L=Object.assign(E,{setup(g){const p=f();return(t,u)=>{const o=l("PageHeaderAction"),m=l("PageHeader"),a=l("ParentTransition");return b(),q(A,null,[i(m,{title:t.$trans(r(p).meta.trans,{attribute:t.$trans(r(p).meta.label)}),navs:[{label:t.$trans("mess.mess"),path:"Mess"},{label:t.$trans("mess.meal.meal"),path:"MessMealList"}]},{default:_(()=>[i(o,{name:"MessMeal",title:t.$trans("mess.meal.meal"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),i(a,{appear:"",visibility:!0},{default:_(()=>[i(w)]),_:1})],64)}}});export{L as default};
