import{u as L,l as F,n as P,r,q as c,o as l,w as e,d as w,e as s,h as R,j as T,y as _,m as M,f as t,a as j,F as N,v as S,s as m,t as u,b as x}from"./app-DvIo72ZO.js";const O={class:"grid grid-cols-3 gap-6"},U={class:"col-span-3 sm:col-span-1"},q={__name:"Filter",emits:["hide"],setup(B,{emit:d}){L();const g=d,v={name:""},p=F({...v}),C=F({isLoaded:!0});return P(async()=>{C.isLoaded=!0}),(f,i)=>{const b=r("BaseInput"),a=r("FilterForm");return l(),c(a,{"init-form":v,form:p,multiple:[],onHide:i[1]||(i[1]=n=>g("hide"))},{default:e(()=>[w("div",O,[w("div",U,[s(b,{type:"text",modelValue:p.name,"onUpdate:modelValue":i[0]||(i[0]=n=>p.name=n),name:"name",label:f.$trans("exam.grade.props.name")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},z={name:"ExamGradeList"},K=Object.assign(z,{setup(B){const d=R(),g=T("emitter");let v=["filter"];_("grade:create")&&v.unshift("create");let p=[];_("grade:export")&&(p=["print","pdf","excel"]);const C="exam/grade/",f=M(!1),i=F({}),b=a=>{Object.assign(i,a)};return(a,n)=>{const D=r("PageHeaderAction"),I=r("PageHeader"),y=r("ParentTransition"),h=r("DataCell"),$=r("FloatingMenuItem"),E=r("FloatingMenu"),G=r("DataRow"),V=r("BaseButton"),A=r("DataTable"),H=r("ListItem");return l(),c(H,{"init-url":C,onSetItems:b},{header:e(()=>[s(I,{title:a.$trans("exam.grade.grade"),navs:[{label:a.$trans("exam.exam"),path:"Exam"}]},{default:e(()=>[s(D,{url:"exam/grades/",name:"ExamGrade",title:a.$trans("exam.grade.grade"),actions:t(v),"dropdown-actions":t(p),onToggleFilter:n[0]||(n[0]=o=>f.value=!f.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[s(y,{appear:"",visibility:f.value},{default:e(()=>[s(q,{onRefresh:n[1]||(n[1]=o=>t(g).emit("listItems")),onHide:n[2]||(n[2]=o=>f.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[s(y,{appear:"",visibility:!0},{default:e(()=>[s(A,{header:i.headers,meta:i.meta,module:"exam.grade",onRefresh:n[4]||(n[4]=o=>t(g).emit("listItems"))},{actionButton:e(()=>[t(_)("exam-grade:create")?(l(),c(V,{key:0,onClick:n[3]||(n[3]=o=>t(d).push({name:"ExamGradeCreate"}))},{default:e(()=>[m(u(a.$trans("global.add",{attribute:a.$trans("exam.grade.grade")})),1)]),_:1})):x("",!0)]),default:e(()=>[(l(!0),j(N,null,S(i.data,o=>(l(),c(G,{key:o.uuid,onDoubleClick:k=>t(d).push({name:"ExamGradeShow",params:{uuid:o.uuid}})},{default:e(()=>[s(h,{name:"name"},{default:e(()=>[m(u(o.name),1)]),_:2},1024),s(h,{name:"createdAt"},{default:e(()=>[m(u(o.createdAt.formatted),1)]),_:2},1024),s(h,{name:"action"},{default:e(()=>[s(E,null,{default:e(()=>[s($,{icon:"fas fa-arrow-circle-right",onClick:k=>t(d).push({name:"ExamGradeShow",params:{uuid:o.uuid}})},{default:e(()=>[m(u(a.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(_)("exam-grade:edit")?(l(),c($,{key:0,icon:"fas fa-edit",onClick:k=>t(d).push({name:"ExamGradeEdit",params:{uuid:o.uuid}})},{default:e(()=>[m(u(a.$trans("general.edit")),1)]),_:2},1032,["onClick"])):x("",!0),t(_)("exam-grade:create")?(l(),c($,{key:1,icon:"fas fa-copy",onClick:k=>t(d).push({name:"ExamGradeDuplicate",params:{uuid:o.uuid}})},{default:e(()=>[m(u(a.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):x("",!0),t(_)("exam-grade:delete")?(l(),c($,{key:2,icon:"fas fa-trash",onClick:k=>t(g).emit("deleteItem",{uuid:o.uuid})},{default:e(()=>[m(u(a.$trans("general.delete")),1)]),_:2},1032,["onClick"])):x("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{K as default};
