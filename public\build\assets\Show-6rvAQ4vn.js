import{i as b,u as h,h as M,l as P,r as t,q as i,o as r,w as e,e as o,f as l,b as S,d as v,s as w,t as B}from"./app-DvIo72ZO.js";const k=["innerHTML"],y={name:"ConfigMailTemplateShow"},V=Object.assign(y,{setup(H){b();const c=h(),u=M(),m={},p="config/mailTemplate/",a=P({...m}),d=n=>{Object.assign(a,n)};return(n,s)=>{const _=t("PageHeaderAction"),f=t("BaseCard"),g=t("ShowItem"),T=t("ParentTransition"),C=t("ConfigPage");return r(),i(C,{"no-background":""},{action:e(()=>[o(_,{name:"ConfigMailTemplate",title:n.$trans("config.mail.template.template")},null,8,["title"])]),default:e(()=>[o(T,{appear:"",visibility:!0},{default:e(()=>[o(g,{"init-url":p,uuid:l(c).params.uuid,onSetItem:d,onRedirectTo:s[0]||(s[0]=I=>l(u).push({name:"ConfigMailTemplate"}))},{default:e(()=>[a.uuid?(r(),i(f,{key:0},{title:e(()=>[w(B(a.subject),1)]),default:e(()=>[v("div",{innerHTML:a.body},null,8,k)]),_:1})):S("",!0)]),_:1},8,["uuid"])]),_:1})]),_:1})}}});export{V as default};
