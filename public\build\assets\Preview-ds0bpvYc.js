import{i as H,u as L,h as N,m as j,l as M,n as T,r as a,a as l,o as n,e as o,w as t,f as y,q as f,b as v,d as c,F as p,v as b,s as _,t as r}from"./app-DvIo72ZO.js";const q={class:"flex items-center"},A={class:"ml-4 text-sm"},O={class:"space-y-4"},x={key:0},E={class:"mt-4"},U={name:"ResourceDiaryShow"},J=Object.assign(U,{setup(z){const g=H(),u=L(),w=N(),B={},D="resource/diary/",d=j(!1),i=M({...B}),P=async()=>{d.value=!0,await g.dispatch(D+"preview",{params:u.query}).then(e=>{d.value=!1,Object.assign(i,e)}).catch(e=>{d.value=!1,w.push({name:"ResourceDiary"})})};return T(async()=>{await P()}),(e,G)=>{const R=a("PageHeaderAction"),k=a("PageHeader"),h=a("BaseDataView"),V=a("ListMedia"),$=a("BaseFieldset"),C=a("ShowButton"),S=a("BaseCard"),F=a("ParentTransition");return n(),l(p,null,[o(k,{title:e.$trans(y(u).meta.trans,{attribute:e.$trans(y(u).meta.label)}),navs:[{label:e.$trans("resource.resource"),path:"Resource"},{label:e.$trans("resource.diary.diary"),path:"ResourceDiary"}]},{default:t(()=>[o(R,{name:"ResourceDiary",title:e.$trans("resource.diary.diary"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),o(F,{appear:"",visibility:!0},{default:t(()=>[i.batch?(n(),f(S,{key:0},{title:t(()=>[c("div",q,[_(r(i.batch)+" ",1),c("span",A,r(i.date.formatted),1)])]),default:t(()=>[c("div",O,[(n(!0),l(p,null,b(i.records,s=>(n(),l("div",null,[o($,null,{legend:t(()=>[_(r(s.subject)+" ",1),s.employee?(n(),l("span",x," - "+r(s.employee.name)+" ("+r(s.employee.designation)+") ",1)):v("",!0)]),default:t(()=>[(n(!0),l(p,null,b(s.details,m=>(n(),f(h,{class:"mt-4 col-span-1 sm:col-span-2",key:m.heading,label:m.heading},{default:t(()=>[_(r(m.description),1)]),_:2},1032,["label"]))),128)),o(h,{class:"mt-4 col-span-1 sm:col-span-2"},{default:t(()=>[o(V,{"show-no-media-alert":!1,media:s.media,url:`/app/resource/diaries/${s.uuid}/`},null,8,["media","url"])]),_:2},1024)]),_:2},1024)]))),256))]),c("div",E,[o(C)])]),_:1})):v("",!0)]),_:1})],64)}}});export{J as default};
