import{l as u,m,c as d,r as g,a as f,o as c,d as t,e as a,al as p,t as x,w as b,s as v,aV as C}from"./app-DvIo72ZO.js";const _={class:"p-6"},w={class:"bg-white rounded-lg shadow p-6 mb-6"},y={class:"bg-gray-50 rounded-lg p-6"},B={class:"bg-gray-800 text-green-400 p-4 rounded text-sm overflow-auto"},V={class:"mt-6"},k={name:"ExamOnlineExamTestProctoring"},P=Object.assign(k,{setup(E){const o=u({enableProctoring:!1,proctorConfig:{}}),s=m(!1),l=d(()=>({uuid:"test-exam-uuid",title:"Test Exam",enableProctoring:o.enableProctoring,proctorConfig:o.proctorConfig})),i=()=>{s.value=!1,alert("Requirements met! Exam would start now.")};return(T,e)=>{const r=g("BaseButton");return c(),f("div",_,[e[6]||(e[6]=t("h1",{class:"text-2xl font-bold mb-6"},"Proctoring Configuration Test",-1)),t("div",w,[e[3]||(e[3]=t("h2",{class:"text-lg font-semibold mb-4"},"Test Configuration",-1)),a(p,{modelValue:o,"onUpdate:modelValue":e[0]||(e[0]=n=>o=n)},null,8,["modelValue"])]),t("div",y,[e[4]||(e[4]=t("h2",{class:"text-lg font-semibold mb-4"},"Configuration Output",-1)),t("pre",B,x(JSON.stringify(o,null,2)),1)]),t("div",V,[a(r,{onClick:e[1]||(e[1]=n=>s.value=!0),design:"primary"},{default:b(()=>e[5]||(e[5]=[v(" Test Requirements Check ")])),_:1})]),a(C,{visibility:s.value,exam:l.value,onClose:e[2]||(e[2]=n=>s.value=!1),onRequirementsMet:i},null,8,["visibility","exam"])])}}});export{P as default};
