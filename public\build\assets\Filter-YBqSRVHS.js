import{u as $,j as q,l as y,I as B,n as h,r,q as d,o as i,w as k,d as l,e as b,f as g,b as p}from"./app-DvIo72ZO.js";const F={class:"grid grid-cols-3 gap-6"},N={class:"col-span-3 sm:col-span-1"},L={class:"col-span-3 sm:col-span-1"},w={class:"col-span-3 sm:col-span-1"},C={class:"col-span-3 sm:col-span-1"},I={class:"col-span-3 sm:col-span-1"},E={__name:"Filter",props:{initUrl:{type:String,default:""}},emits:["hide"],setup(V,{emit:S}){const s=$();q("moment");const f=S,v=V,u={startDate:"",endDate:"",codeNumber:"",department:"",designation:"",employmentStatus:""},a=y({...u}),c=B(v.initUrl),n=y({department:"",designation:"",employmentStatus:"",isLoaded:!(s.query.department||s.query.designation||s.query.employmentStatus)});return h(async()=>{n.department=s.query.department,n.designation=s.query.designation,n.employmentStatus=s.query.employmentStatus,n.isLoaded=!0}),(o,e)=>{const D=r("DatePicker"),U=r("BaseInput"),m=r("BaseSelectSearch"),_=r("FilterForm");return i(),d(_,{"init-form":u,form:a,onHide:e[7]||(e[7]=t=>f("hide"))},{default:k(()=>[l("div",F,[l("div",N,[b(D,{start:a.startDate,"onUpdate:start":e[0]||(e[0]=t=>a.startDate=t),end:a.endDate,"onUpdate:end":e[1]||(e[1]=t=>a.endDate=t),as:"range",name:"dateBetween",label:o.$trans("general.date_between"),error:g(c).startDate,"onUpdate:error":e[2]||(e[2]=t=>g(c).startDate=t),"no-clear":""},null,8,["start","end","label","error"])]),l("div",L,[b(U,{type:"text",modelValue:a.codeNumber,"onUpdate:modelValue":e[3]||(e[3]=t=>a.codeNumber=t),name:"codeNumber",label:o.$trans("employee.props.code_number")},null,8,["modelValue","label"])]),l("div",w,[n.isLoaded?(i(),d(m,{key:0,name:"department",label:o.$trans("global.select",{attribute:o.$trans("employee.department.department")}),modelValue:a.department,"onUpdate:modelValue":e[4]||(e[4]=t=>a.department=t),"value-prop":"uuid","init-search":n.department,"search-action":"employee/department/list"},null,8,["label","modelValue","init-search"])):p("",!0)]),l("div",C,[n.isLoaded?(i(),d(m,{key:0,name:"designation",label:o.$trans("global.select",{attribute:o.$trans("employee.designation.designation")}),modelValue:a.designation,"onUpdate:modelValue":e[5]||(e[5]=t=>a.designation=t),"value-prop":"uuid","init-search":n.designation,"search-action":"employee/designation/list"},null,8,["label","modelValue","init-search"])):p("",!0)]),l("div",I,[n.isLoaded?(i(),d(m,{key:0,name:"employmentStatus",label:o.$trans("global.select",{attribute:o.$trans("employee.employment_status.employment_status")}),modelValue:a.employmentStatus,"onUpdate:modelValue":e[6]||(e[6]=t=>a.employmentStatus=t),"value-prop":"uuid","init-search":n.employmentStatus,"search-action":"option/list","additional-search-query":{type:"employment_status"}},null,8,["label","modelValue","init-search"])):p("",!0)])])]),_:1},8,["form"])}}};export{E as _};
