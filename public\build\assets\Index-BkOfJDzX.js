import{u as P,l as w,n as N,r as o,q as b,o as d,w as e,d as $,e as n,i as G,I as J,m as V,a as C,b as I,f as u,s as l,t as i,h as K,j as Q,z as W,F as H,v as X,A as Y}from"./app-DvIo72ZO.js";import{d as Z}from"./vuedraggable.umd-DSTqH_PI.js";const x={class:"grid grid-cols-3 gap-6"},ee={class:"col-span-3 sm:col-span-1"},te={__name:"Filter",emits:["hide"],setup(M,{emit:B}){P();const c=B,v={search:""},p=w({...v}),h=w({isLoaded:!0});return N(async()=>{h.isLoaded=!0}),(f,r)=>{const k=o("BaseInput"),g=o("FilterForm");return d(),b(g,{"init-form":v,form:p,multiple:[],onHide:r[1]||(r[1]=_=>c("hide"))},{default:e(()=>[$("div",x,[$("div",ee,[n(k,{type:"text",modelValue:p.search,"onUpdate:modelValue":r[0]||(r[0]=_=>p.search=_),name:"search",label:f.$trans("general.search")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},se={key:0},ne={class:"flex border rounded-xl px-4 py-2"},oe={key:1},ae={key:2,class:"mt-4 flex justify-end"},le={name:"SiteBlockReorder"},ie=Object.assign(le,{props:{visibility:{type:Boolean,default:!1}},emits:["close","refresh"],setup(M,{emit:B}){P();const c=G(),v=B,p={blocks:[]};J("site/block/");const f=V(!1),r=w({blocks:[]});w({...p});const k=async()=>{f.value=!0,await c.dispatch("site/block/list",{params:{all:!0}}).then(m=>{f.value=!1,r.blocks=m}).catch(m=>{f.value=!1})},g=async()=>{f.value=!0,await c.dispatch("site/block/reorder",{data:{blocks:r.blocks}}).then(m=>{f.value=!1,v("refresh"),v("close")}).catch(m=>{f.value=!1})},_=()=>{v("close")};return N(()=>{k()}),(m,a)=>{const t=o("BaseLabel"),y=o("BaseAlert"),L=o("BaseButton"),R=o("BaseModal");return d(),b(R,{show:M.visibility,onClose:_},{title:e(()=>[l(i(m.$trans("global.reorder",{attribute:m.$trans("site.block.block")})),1)]),default:e(()=>[r.blocks.length?(d(),C("div",se,[n(u(Z),{class:"space-y-2",list:r.blocks,"item-key":"uuid"},{item:e(({element:S,index:j})=>[$("div",ne,[a[0]||(a[0]=$("i",{class:"fas fa-arrows mr-2 cursor-pointer"},null,-1)),n(t,null,{default:e(()=>[l(i(S.name),1)]),_:2},1024)])]),_:1},8,["list"])])):(d(),C("div",oe,[n(y,{design:"info",size:"xs"},{default:e(()=>[l(i(m.$trans("general.errors.record_not_found")),1)]),_:1})])),r.blocks.length?(d(),C("div",ae,[n(L,{onClick:g},{default:e(()=>[l(i(m.$trans("general.reorder")),1)]),_:1})])):I("",!0)]),_:1},8,["show"])}}}),re={name:"SiteBlockList"},ce=Object.assign(re,{emits:["refresh"],setup(M,{emit:B}){const c=K(),v=B,p=Q("emitter");let h=["filter"];h.unshift("create");let f=["print","pdf","excel"];const r="site/block/",k=V(!1),g=V(!1),_=w({}),m=a=>{Object.assign(_,a)};return(a,t)=>{const y=o("BaseButton"),L=o("PageHeaderAction"),R=o("PageHeader"),S=o("ParentTransition"),j=o("BaseBadge"),T=o("DataCell"),A=o("TextMuted"),F=o("FloatingMenuItem"),U=o("FloatingMenu"),E=o("DataRow"),O=o("DataTable"),q=o("ListItem"),z=W("tooltip");return d(),C(H,null,[n(q,{"init-url":r,"additional-query":{},onSetItems:m},{header:e(()=>[n(R,{title:a.$trans("site.block.block"),navs:[{label:a.$trans("site.site"),path:"Site"}]},{default:e(()=>[n(L,{url:"site/blocks/",name:"SiteBlock",title:a.$trans("site.block.block"),actions:u(h),"dropdown-actions":u(f),"config-path":"SiteConfig",onToggleFilter:t[2]||(t[2]=s=>k.value=!k.value)},{after:e(()=>[n(y,{design:"white",onClick:t[1]||(t[1]=s=>u(c).push({name:"SiteConfig"}))},{default:e(()=>t[10]||(t[10]=[$("i",{class:"fas fa-cog"},null,-1)])),_:1})]),default:e(()=>[Y((d(),b(y,{design:"white",onClick:t[0]||(t[0]=s=>g.value=!g.value)},{default:e(()=>t[9]||(t[9]=[$("i",{class:"fas fa-arrows-up-down-left-right"},null,-1)])),_:1})),[[z,a.$trans("global.reorder",{attribute:a.$trans("site.block.block")})]])]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[n(S,{appear:"",visibility:k.value},{default:e(()=>[n(te,{onRefresh:t[3]||(t[3]=s=>u(p).emit("listItems")),onHide:t[4]||(t[4]=s=>k.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[n(S,{appear:"",visibility:!0},{default:e(()=>[n(O,{header:_.headers,meta:_.meta,module:"site.block",onRefresh:t[6]||(t[6]=s=>u(p).emit("listItems"))},{actionButton:e(()=>[n(y,{onClick:t[5]||(t[5]=s=>u(c).push({name:"SiteBlockCreate"}))},{default:e(()=>[l(i(a.$trans("global.add",{attribute:a.$trans("site.block.block")})),1)]),_:1})]),default:e(()=>[(d(!0),C(H,null,X(_.data,s=>(d(),b(E,{key:s.uuid,onDoubleClick:D=>u(c).push({name:"SiteBlockShow",params:{uuid:s.uuid}})},{default:e(()=>[n(T,{name:"name"},{default:e(()=>[l(i(s.name)+" ",1),s.isSlider?(d(),b(j,{key:0,design:"info"},{default:e(()=>[l(i(a.$trans("site.block.props.slider")),1)]),_:1})):I("",!0)]),_:2},1024),n(T,{name:"title"},{default:e(()=>[l(i(s.title)+" ",1),n(A,{block:""},{default:e(()=>[l(i(s.subTitle),1)]),_:2},1024),s.menu?(d(),b(A,{key:0,block:""},{default:e(()=>[l(i(s.menu.name),1)]),_:2},1024)):I("",!0),!s.menu&&s.url?(d(),b(A,{key:1,block:""},{default:e(()=>[l(i(s.url),1)]),_:2},1024)):I("",!0)]),_:2},1024),n(T,{name:"action"},{default:e(()=>[n(U,null,{default:e(()=>[n(F,{icon:"fas fa-arrow-circle-right",onClick:D=>u(c).push({name:"SiteBlockShow",params:{uuid:s.uuid}})},{default:e(()=>[l(i(a.$trans("general.show")),1)]),_:2},1032,["onClick"]),n(F,{icon:"fas fa-edit",onClick:D=>u(c).push({name:"SiteBlockEdit",params:{uuid:s.uuid}})},{default:e(()=>[l(i(a.$trans("general.edit")),1)]),_:2},1032,["onClick"]),n(F,{icon:"fas fa-copy",onClick:D=>u(c).push({name:"SiteBlockDuplicate",params:{uuid:s.uuid}})},{default:e(()=>[l(i(a.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),n(F,{icon:"fas fa-trash",onClick:D=>u(p).emit("deleteItem",{uuid:s.uuid})},{default:e(()=>[l(i(a.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1}),n(ie,{visibility:g.value,onClose:t[7]||(t[7]=s=>g.value=!1),onRefresh:t[8]||(t[8]=s=>v("refresh"))},null,8,["visibility"])],64)}}});export{ce as default};
