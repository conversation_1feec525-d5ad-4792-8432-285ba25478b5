import{u as K,H as W,I as Y,l as E,L as z,r as f,q as U,o as c,w as d,e as p,d as l,a as _,b as V,f as u,F as P,v as G,s as k,t as i,N as te,K as X,i as Z,m as M,n as J,j as ee,x as se,h as ae,y as oe,p as ne}from"./app-DvIo72ZO.js";import{d as le}from"./vuedraggable.umd-DSTqH_PI.js";import{l as ie}from"./lodash-BPUmB9Gy.js";const re={key:0},ue={key:1},de={class:"grid grid-cols-2 gap-6"},ce={class:"col-span-2 sm:col-span-1"},me={class:"col-span-2 sm:col-span-1"},pe={class:"col-span-2"},be=["onClick"],ve={class:"mt-4 grid grid-cols-4 gap-4"},fe={class:"col-span-3 sm:col-span-3"},ye={class:"col-span-3 sm:col-span-1"},_e={class:"mt-4"},he={key:0,class:"col-span-2"},ge={name:"OnlineExamQuestionForm"},xe=Object.assign(ge,{props:{onlineExam:{type:Object,default(){return{}}},visibility:{type:Boolean,default:!1},action:{type:String,default:"create"},selectedQuestion:{type:Object,default(){return{}}}},emits:["close","completed"],setup(y,{emit:R}){K();const C=R,a=y,q={title:"",description:"",type:"mcq",mark:0,options:[],header:"",showHeader:!1},O={uuid:W(),title:"",isCorrect:!1},j="exam/onlineExam/question/",v=Y(j),h=E({types:[]}),e=E({}),$=E({isLoaded:!0}),Q=b=>{Object.assign(h,b)},r=()=>{e.options.push({...O,uuid:W()}),$.isLoaded=!0},x=async b=>{await te()&&(e.options.length==1?e.options=[O]:e.options.splice(b,1))},F=b=>{e.options[b].isCorrect&&e.options.forEach((o,I)=>{I!==b&&(o.isCorrect=!1)})};return z(()=>[a.action,a.selectedQuestion.uuid],b=>{if(!a.selectedQuestion.uuid){Object.assign(e,X(q));return}Object.assign(q,{title:a.selectedQuestion.title,mark:a.selectedQuestion.mark,type:a.selectedQuestion.type.value,options:a.selectedQuestion.options,header:a.selectedQuestion.header,showHeader:!!a.selectedQuestion.header}),Object.assign(e,X(q))}),(b,o)=>{const I=f("BaseSelect"),L=f("BaseInput"),A=f("BaseEditor"),t=f("BaseSwitch"),n=f("BaseFieldset"),g=f("BaseBadge"),S=f("FormAction"),D=f("BaseSideover");return c(),U(D,{visibility:y.visibility,onClose:o[12]||(o[12]=H=>C("close"))},{title:d(()=>[y.action=="create"?(c(),_("span",re,i(b.$trans("global.add",{attribute:b.$trans("exam.online_exam.question.question")})),1)):(c(),_("span",ue,i(b.$trans("global.edit",{attribute:b.$trans("exam.online_exam.question.question")})),1))]),default:d(()=>{var H;return[p(S,{sideover:"","no-card":"","no-data-fetch":"","cancel-action":"","pre-requisites":!0,onSetPreRequisites:Q,"keep-adding":y.action!="update",action:y.action,"init-url":j,"init-form":q,moduleUuid:(H=y.selectedQuestion)==null?void 0:H.uuid,form:e,onEnd:o[9]||(o[9]=m=>C("close")),onCompleted:o[10]||(o[10]=m=>C("completed")),onCancelled:o[11]||(o[11]=m=>C("close"))},{default:d(()=>[l("div",de,[l("div",ce,[p(I,{disabled:y.onlineExam.type.value=="mcq",modelValue:e.type,"onUpdate:modelValue":o[0]||(o[0]=m=>e.type=m),name:"type",label:b.$trans("exam.online_exam.question.props.type"),options:h.types,error:u(v).type,"onUpdate:error":o[1]||(o[1]=m=>u(v).type=m)},null,8,["disabled","modelValue","label","options","error"])]),l("div",me,[p(L,{type:"number",modelValue:e.mark,"onUpdate:modelValue":o[2]||(o[2]=m=>e.mark=m),name:"mark",label:b.$trans("exam.online_exam.question.props.mark"),error:u(v).mark,"onUpdate:error":o[3]||(o[3]=m=>u(v).mark=m)},null,8,["modelValue","label","error"])]),l("div",pe,[p(A,{modelValue:e.title,"onUpdate:modelValue":o[4]||(o[4]=m=>e.title=m),name:"title",edit:!0,toolbar:"minimal",label:b.$trans("exam.online_exam.question.props.title"),error:u(v).title,"onUpdate:error":o[5]||(o[5]=m=>u(v).title=m),height:100},null,8,["modelValue","label","error"])])]),e.type=="mcq"?(c(),_(P,{key:0},[(c(!0),_(P,null,G(e.options,(m,w)=>(c(),U(n,{class:"mt-4",key:m.uuid},{legend:d(()=>[k(i(w+1)+". ",1),l("span",{class:"text-danger ml-2 cursor-pointer",onClick:s=>x(w)},o[13]||(o[13]=[l("i",{class:"fas fa-times-circle"},null,-1)]),8,be)]),default:d(()=>[l("div",ve,[l("div",fe,[p(L,{type:"text",modelValue:m.title,"onUpdate:modelValue":s=>m.title=s,name:`options.${w}.title`,label:b.$trans("exam.online_exam.question.props.option"),error:u(v)[`options.${w}.title`],"onUpdate:error":s=>u(v)[`options.${w}.title`]=s},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),l("div",ye,[p(t,{vertical:"",modelValue:m.isCorrect,"onUpdate:modelValue":s=>m.isCorrect=s,name:`options.${w}.isCorrect`,label:b.$trans("exam.online_exam.question.props.correct_answer"),error:u(v)[`options.${w}.isCorrect`],"onUpdate:error":s=>u(v)[`options.${w}.isCorrect`]=s,onChange:s=>F(w)},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error","onChange"])])])]),_:2},1024))),128)),l("div",_e,[p(g,{design:"primary",onClick:r,class:"cursor-pointer"},{default:d(()=>[k(i(b.$trans("global.add",{attribute:b.$trans("general.option")})),1)]),_:1})])],64)):V("",!0),p(n,{class:"mt-4"},{legend:d(()=>[p(t,{reverse:"",modelValue:e.showHeader,"onUpdate:modelValue":o[6]||(o[6]=m=>e.showHeader=m),name:"showHeader",label:b.$trans("global.show",{attribute:b.$trans("exam.online_exam.question.props.header")})},null,8,["modelValue","label"])]),default:d(()=>[e.showHeader?(c(),_("div",he,[p(A,{modelValue:e.header,"onUpdate:modelValue":o[7]||(o[7]=m=>e.header=m),name:"header",edit:!0,toolbar:"minimal",label:b.$trans("exam.online_exam.question.props.header"),error:u(v).header,"onUpdate:error":o[8]||(o[8]=m=>u(v).header=m),height:100},null,8,["modelValue","label","error"])])):V("",!0)]),_:1})]),_:1},8,["keep-adding","action","moduleUuid","form"])]}),_:1},8,["visibility"])}}}),ke={key:0},$e={class:"flex border rounded-xl px-4 py-2"},Be=["innerHTML"],qe={key:1},Ve={key:2,class:"mt-4 flex justify-end"},Ce={name:"ExamOnlineExamQuestionReorder"},je=Object.assign(Ce,{props:{visibility:{type:Boolean,default:!1}},emits:["close","refresh"],setup(y,{emit:R}){const C=K(),a=Z(),q=R,O=y,j={questions:[]};Y("exam/onlineExam/question/");const h=M(!1),e=E({questions:[]});E({...j});const $=async()=>{h.value=!0,await a.dispatch("exam/onlineExam/question/list",{uuid:C.params.uuid,params:{all:!0}}).then(x=>{h.value=!1,e.questions=x}).catch(x=>{h.value=!1})},Q=async()=>{h.value=!0,await a.dispatch("exam/onlineExam/question/reorder",{uuid:C.params.uuid,data:{questions:e.questions}}).then(x=>{h.value=!1,q("refresh"),q("close")}).catch(x=>{h.value=!1})},r=()=>{q("close")};return J(()=>{}),z(O.submission,x=>{$()},{deep:!0,immediate:!0}),(x,F)=>{const b=f("BaseAlert"),o=f("BaseButton"),I=f("BaseModal");return c(),U(I,{show:y.visibility,onClose:r},{title:d(()=>[k(i(x.$trans("global.reorder",{attribute:x.$trans("exam.online_exam.question.question")})),1)]),default:d(()=>[e.questions.length?(c(),_("div",ke,[p(u(le),{class:"space-y-2",list:e.questions,"item-key":"uuid"},{item:d(({element:L,index:A})=>[l("div",$e,[F[0]||(F[0]=l("i",{class:"fas fa-arrows mr-2 cursor-pointer dark:text-gray-400"},null,-1)),l("div",{class:"dark:text-gray-400",innerHTML:L.title},null,8,Be)])]),_:1},8,["list"])])):(c(),_("div",qe,[p(b,{design:"info",size:"xs"},{default:d(()=>[k(i(x.$trans("general.errors.record_not_found")),1)]),_:1})])),e.questions.length?(c(),_("div",Ve,[p(o,{onClick:Q},{default:d(()=>[k(i(x.$trans("general.reorder")),1)]),_:1})])):V("",!0)]),_:1},8,["show"])}}}),we={class:"text-lg font-medium text-gray-900"},Ee={class:"space-y-6"},Qe={class:"bg-gray-50 p-4 rounded-lg"},Ue={class:"text-sm font-medium text-gray-700 mb-3"},Oe={class:"grid grid-cols-3 gap-6"},Fe={class:"col-span-3 sm:col-span-1"},Me={class:"col-span-3 sm:col-span-1"},Ie={class:"col-span-3 sm:col-span-1"},Se={class:"mt-4"},Le={class:"max-h-96 overflow-y-auto"},Re={key:0,class:"text-center py-8"},Ae={key:1,class:"text-center py-8 text-gray-500"},He={key:2,class:"space-y-3"},De={class:"flex items-start space-x-3"},Te={class:"flex-1 min-w-0"},Ne={class:"flex items-center justify-between"},ze={class:"text-sm font-medium text-gray-900 truncate"},Pe={class:"flex items-center space-x-2 text-xs text-gray-500"},Ke={class:"bg-gray-100 px-2 py-1 rounded"},Ge={class:"bg-blue-100 text-blue-800 px-2 py-1 rounded"},Je={class:"text-sm text-gray-600 mt-1"},We={key:0,class:"ml-2"},Xe={key:0,class:"text-xs text-gray-500 mt-2 line-clamp-2"},Ye={key:1,class:"mt-3"},Ze={key:0,class:"bg-blue-50 p-4 rounded-lg"},et={class:"text-sm font-medium text-blue-900 mb-2"},tt={class:"text-sm text-blue-700"},st={class:"flex justify-between items-center pt-6 border-t border-gray-200"},at={class:"text-sm text-gray-600"},ot={key:0},nt={class:"flex space-x-3"},lt={key:0},it={__name:"QuestionBankImport",props:{visibility:{type:Boolean,default:!1},onlineExam:{type:Object,required:!0}},emits:["close","imported"],setup(y,{emit:R}){const C=Z(),a=ee("$trans"),q=R,O=y,j=M(!1),v=M(!1),h=M([]),e=M([]),$=E({}),Q=M(!1),r=E({subject:"",type:"",batches:[],title:""}),x=E({subjects:[],types:[]}),F=E({batches:[],isLoaded:!1}),b=async()=>{j.value=!0;try{const t={subject:r.subject,type:r.type,title:r.title,batches:Array.isArray(r.batches)?r.batches.join(","):r.batches};Object.keys(t).forEach(g=>{(!t[g]||Array.isArray(t[g])&&t[g].length===0)&&delete t[g]});const n=await C.dispatch("exam/questionBank/list",{params:t});h.value=n.data||[]}catch(t){console.error("Failed to search questions:",t),h.value=[]}finally{j.value=!1}},o=ie.debounce(b,500);z(()=>[r.subject,r.type,r.batches,r.title],()=>{o()},{deep:!0}),z(()=>e.value,t=>{},{deep:!0});const I=t=>{const n=e.value.indexOf(t);if(n>-1)e.value.splice(n,1),delete $[t];else{e.value.push(t);const g=h.value.find(S=>S.uuid===t);g&&($[t]=g.mark)}},L=(t,n)=>{$[t]=parseFloat(n)||0},A=async()=>{v.value=!0;try{const t=await C.dispatch("exam/questionBank/importToExam",{examUuid:O.onlineExam.uuid,form:{question_ids:e.value,marks:$}});q("imported",t.result),q("close")}catch(t){console.error("Failed to import questions:",t)}finally{v.value=!1}};return J(async()=>{try{const t=await C.dispatch("exam/questionBank/preRequisiteForFilter",{});x.subjects=t.subjects||[],x.types=t.types||[],F.isLoaded=!0}catch(t){console.error("Failed to load prerequisites:",t)}}),z(()=>O.visibility,t=>{var n;t&&(e.value=[],Object.keys($).forEach(g=>delete $[g]),((n=O.onlineExam.type)==null?void 0:n.value)==="mcq"?(r.type="mcq",Q.value=!0):(r.type="",Q.value=!1),r.subject="",r.batches=[],r.title="")}),(t,n)=>{const g=f("BaseSelect"),S=f("BaseSelectSearch"),D=f("BaseInput"),H=f("BaseCheckbox"),m=f("BaseButton"),w=f("BaseModal");return c(),U(w,{visibility:y.visibility,onClose:n[5]||(n[5]=s=>q("close")),size:"6xl"},{header:d(()=>[l("h3",we,i(u(a)("exam.question_bank.import_from_question_bank")),1)]),default:d(()=>[l("div",Ee,[l("div",Qe,[l("h4",Ue,i(u(a)("exam.question_bank.search_questions")),1),l("div",Oe,[l("div",Fe,[p(g,{modelValue:r.subject,"onUpdate:modelValue":n[0]||(n[0]=s=>r.subject=s),name:"subject",label:u(a)("exam.question_bank.props.subject"),options:x.subjects,"label-prop":"name","value-prop":"uuid"},null,8,["modelValue","label","options"])]),l("div",Me,[p(g,{modelValue:r.type,"onUpdate:modelValue":n[1]||(n[1]=s=>r.type=s),name:"type",label:u(a)("exam.question_bank.props.type"),options:x.types,"label-prop":"label","value-prop":"value",disabled:Q.value},null,8,["modelValue","label","options","disabled"])]),l("div",Ie,[F.isLoaded?(c(),U(S,{key:0,multiple:"",modelValue:r.batches,"onUpdate:modelValue":n[2]||(n[2]=s=>r.batches=s),name:"batches",label:u(a)("academic.batch.batch"),"value-prop":"uuid","init-search":F.batches,"search-key":"course_batch","search-action":"academic/batch/list","close-on-select":!1},{selectedOption:d(s=>[k(i(s.value.course.name)+" - "+i(s.value.name),1)]),listOption:d(s=>[k(i(s.option.course.name)+" - "+i(s.option.name),1)]),_:1},8,["modelValue","label","init-search"])):V("",!0)])]),l("div",Se,[p(D,{type:"text",modelValue:r.title,"onUpdate:modelValue":n[3]||(n[3]=s=>r.title=s),name:"title",label:u(a)("exam.question_bank.props.title"),placeholder:u(a)("general.search")},null,8,["modelValue","label","placeholder"])])]),l("div",Le,[j.value?(c(),_("div",Re,n[6]||(n[6]=[l("i",{class:"fas fa-spinner fa-spin text-2xl text-gray-400"},null,-1)]))):h.value.length===0?(c(),_("div",Ae,i(u(a)("exam.question_bank.no_questions_found")),1)):(c(),_("div",He,[(c(!0),_(P,null,G(h.value,s=>{var B,T;return c(),_("div",{key:s.uuid,class:se(["border rounded-lg p-4 hover:bg-gray-50",{"bg-blue-50 border-blue-200":e.value.includes(s.uuid)}])},[l("div",De,[p(H,{"model-value":e.value.includes(s.uuid),"onUpdate:modelValue":N=>I(s.uuid),class:"mt-1"},null,8,["model-value","onUpdate:modelValue"]),l("div",Te,[l("div",Ne,[l("h5",ze,i(s.title),1),l("div",Pe,[l("span",Ke,i((B=s.type)==null?void 0:B.label),1),l("span",Ge,i(s.mark)+" "+i(u(a)("exam.question_bank.props.mark")),1)])]),l("p",Je,[k(i((T=s.subject)==null?void 0:T.name)+" ",1),s.batches&&s.batches.length>0?(c(),_("span",We," • "+i(s.batches.map(N=>`${N.course.name} - ${N.name}`).join(", ")),1)):V("",!0)]),s.header?(c(),_("div",Xe,i(s.header),1)):V("",!0),e.value.includes(s.uuid)?(c(),_("div",Ye,[p(D,{type:"number","model-value":$[s.uuid]||s.mark,"onUpdate:modelValue":N=>L(s.uuid,N),label:u(a)("exam.question_bank.props.mark"),step:"0.01",min:"0.01",class:"w-24"},null,8,["model-value","onUpdate:modelValue","label"])])):V("",!0)])])],2)}),128))]))]),e.value.length>0?(c(),_("div",Ze,[l("h4",et,i(u(a)("exam.question_bank.select_questions")),1),l("p",tt,i(e.value.length)+" "+i(u(a)("exam.online_exam.question.questions"))+" "+i(u(a)("general.selected")),1)])):V("",!0),l("div",st,[l("div",at,[e.value.length>0?(c(),_("span",ot,i(e.value.length)+" "+i(u(a)("exam.online_exam.question.questions"))+" "+i(u(a)("general.selected")),1)):V("",!0)]),l("div",nt,[p(m,{variant:"soft",onClick:n[4]||(n[4]=s=>q("close"))},{default:d(()=>[k(i(u(a)("general.cancel")),1)]),_:1}),p(m,{variant:"primary",onClick:A,disabled:e.value.length===0||v.value,loading:v.value},{default:d(()=>[k(i(u(a)("general.import"))+" ",1),e.value.length>0?(c(),_("span",lt," ("+i(e.value.length)+") ",1)):V("",!0)]),_:1},8,["disabled","loading"])])])])]),_:1},8,["visibility"])}}},rt=["innerHTML"],ut={name:"OnlineExamQuestionList"},pt=Object.assign(ut,{props:{onlineExam:{type:Object,default(){return{}}}},emits:["refresh"],setup(y,{emit:R}){const C=K();ae();const a=ee("emitter"),q=R;oe("online-exam:edit");const O="exam/onlineExam/question/",j=M("create"),v=M(!1),h=M(!1),e=M(!1),$=E({}),Q=E({uuid:null,title:"",type:"",mark:0}),r=E({}),x=t=>{Object.assign($,t)},F=t=>{Object.assign(r,{...t}),j.value="update",v.value=!0},b=t=>{Object.assign(r,{...t}),j.value="create",v.value=!0},o=()=>{q("refresh")},I=()=>{Object.assign(r,Q),o(),a.emit("listItems")},L=()=>{Object.assign(r,Q),v.value=!1},A=t=>{o(),a.emit("listItems")};return J(async()=>{a.on("addQuestion",()=>{Object.assign(r,Q),j.value="create",v.value=!0}),a.on("reorderQuestion",()=>{h.value=!0}),a.on("importFromQuestionBank",()=>{e.value=!0}),a.on("actionPerformed",()=>{o()})}),ne(()=>{a.all.delete("addQuestion"),a.all.delete("reorderQuestion"),a.all.delete("importFromQuestionBank"),a.all.delete("actionPerformed")}),(t,n)=>{const g=f("DataCell"),S=f("FloatingMenuItem"),D=f("FloatingMenu"),H=f("DataRow"),m=f("BaseButton"),w=f("DataTable"),s=f("ListItem");return c(),_(P,null,[p(s,{"init-url":O,uuid:u(C).params.uuid,onSetItems:x},{default:d(()=>[p(w,{header:$.headers,meta:$.meta,module:"exam.online_exam.question",onRefresh:n[1]||(n[1]=B=>u(a).emit("listItems"))},{actionButton:d(()=>[y.onlineExam.canManageQuestion?V("",!0):(c(),U(m,{key:0,onClick:n[0]||(n[0]=B=>v.value=!0)},{default:d(()=>[k(i(t.$trans("global.add",{attribute:t.$trans("exam.online_exam.question.question")})),1)]),_:1}))]),default:d(()=>[(c(!0),_(P,null,G($.data,B=>(c(),U(H,{key:B.uuid},{default:d(()=>[p(g,{name:"title"},{default:d(()=>[l("div",{innerHTML:B.title},null,8,rt)]),_:2},1024),p(g,{name:"type"},{default:d(()=>[k(i(B.type.label),1)]),_:2},1024),p(g,{name:"mark"},{default:d(()=>[k(i(B.mark),1)]),_:2},1024),p(g,{name:"createdAt"},{default:d(()=>[k(i(B.createdAt.formatted),1)]),_:2},1024),p(g,{name:"action"},{default:d(()=>[y.onlineExam.publishedAt.value?V("",!0):(c(),U(D,{key:0},{default:d(()=>[p(S,{icon:"fas fa-edit",onClick:T=>F(B)},{default:d(()=>[k(i(t.$trans("general.edit")),1)]),_:2},1032,["onClick"]),p(S,{icon:"fas fa-copy",onClick:T=>b(B)},{default:d(()=>[k(i(t.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),p(S,{icon:"fas fa-trash",onClick:T=>u(a).emit("deleteItem",{uuid:y.onlineExam.uuid,moduleUuid:B.uuid})},{default:d(()=>[k(i(t.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024))]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","meta"])]),_:1},8,["uuid"]),h.value?(c(),U(je,{key:0,visibility:h.value,onClose:n[2]||(n[2]=B=>h.value=!1),onRefresh:n[3]||(n[3]=B=>u(a).emit("listItems"))},null,8,["visibility"])):V("",!0),y.onlineExam?(c(),U(xe,{key:1,"online-exam":y.onlineExam,action:j.value,visibility:v.value,"selected-question":r,onCompleted:I,onClose:L},null,8,["online-exam","action","visibility","selected-question"])):V("",!0),y.onlineExam?(c(),U(it,{key:2,visibility:e.value,"online-exam":y.onlineExam,onClose:n[4]||(n[4]=B=>e.value=!1),onImported:A},null,8,["visibility","online-exam"])):V("",!0)],64)}}});export{pt as default};
