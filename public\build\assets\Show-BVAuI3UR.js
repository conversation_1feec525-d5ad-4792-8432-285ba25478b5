import{u as O,j as R,I as N,l as I,r as m,q as S,o as b,w as n,d as $,e as s,f as r,a as h,b as V,s as p,t as u,i as M,h as L,y as z,F as J}from"./app-DvIo72ZO.js";const K={class:"grid grid-cols-3 gap-6"},Q={class:"col-span-3 sm:col-span-1"},W={key:0,class:"font-bold"},X={key:0,class:"font-bold"},Y={class:"flex-row space-y-2 col-span-3 sm:col-span-1"},Z={class:"flex-row space-y-2 col-span-3 sm:col-span-1"},x={class:"flex-row space-y-2 col-span-3 sm:col-span-1"},ee={name:"AcademicPeriodImportForm"},te=Object.assign(ee,{props:{},emits:["completed"],setup(T,{emit:C}){const j=O(),k=C,w=R("emitter"),B={period:"",division:!1,course:!1,batch:!1,subject:!1,feeGroup:!1,feeHead:!1,feeConcession:!1,feeStructure:!1,transportCircle:!1,transportFee:!1},i="academic/period/",l=N(i),d=I({}),e=I({...B}),A=o=>{Object.assign(d,o)},H=o=>{o||(e.course=!1,e.batch=!1,e.subject=!1)},U=o=>{o?e.division=!0:(e.batch=!1,e.subject=!1)},c=o=>{o?(e.division=!0,e.course=!0):e.subject=!1},P=o=>{o&&(e.division=!0,e.course=!0,e.batch=!0)},_=o=>{o||(e.feeHead=!1,e.feeConcession=!1,e.feeStructure=!1)},y=o=>{o?e.feeGroup=!0:(e.feeConcession=!1,e.feeStructure=!1)},G=o=>{o?(e.feeGroup=!0,e.feeHead=!0):e.feeStructure=!1},F=o=>{o||(e.transportFee=!1)},g=o=>{o&&(e.transportCircle=!0)},q=()=>{k("completed"),w.emit("listItems")};return(o,a)=>{const D=m("BaseSelectSearch"),f=m("BaseSwitch"),E=m("FormAction");return b(),S(E,{"no-card":"","pre-requisites":!1,onSetPreRequisites:A,"init-url":i,uuid:r(j).params.uuid,"no-data-fetch":"",action:"import","init-form":B,form:e,"keep-adding":!1,"after-submit":q},{default:n(()=>[$("div",K,[$("div",Q,[s(D,{name:"period",label:o.$trans("academic.period.period"),modelValue:e.period,"onUpdate:modelValue":a[0]||(a[0]=t=>e.period=t),error:r(l).period,"onUpdate:error":a[1]||(a[1]=t=>r(l).period=t),"value-prop":"uuid","search-action":"academic/period/list"},{selectedOption:n(t=>{var v;return[t.value.session?(b(),h("span",W,u((v=t.value.session)==null?void 0:v.name)+" - ",1)):V("",!0),p(" "+u(t.value.name),1)]}),listOption:n(t=>{var v;return[t.option.session?(b(),h("span",X,u((v=t.option.session)==null?void 0:v.name)+" - ",1)):V("",!0),p(" "+u(t.option.name),1)]}),_:1},8,["label","modelValue","error"])]),$("div",Y,[s(f,{modelValue:e.division,"onUpdate:modelValue":a[2]||(a[2]=t=>e.division=t),name:"division",label:o.$trans("global.import",{attribute:o.$trans("academic.division.division")}),onChange:H,error:r(l).division,"onUpdate:error":a[3]||(a[3]=t=>r(l).division=t)},null,8,["modelValue","label","error"]),s(f,{modelValue:e.course,"onUpdate:modelValue":a[4]||(a[4]=t=>e.course=t),name:"course",label:o.$trans("global.import",{attribute:o.$trans("academic.course.course")}),onChange:U,error:r(l).course,"onUpdate:error":a[5]||(a[5]=t=>r(l).course=t)},null,8,["modelValue","label","error"]),s(f,{modelValue:e.batch,"onUpdate:modelValue":a[6]||(a[6]=t=>e.batch=t),name:"batch",label:o.$trans("global.import",{attribute:o.$trans("academic.batch.batch")}),onChange:c,error:r(l).batch,"onUpdate:error":a[7]||(a[7]=t=>r(l).batch=t)},null,8,["modelValue","label","error"]),s(f,{modelValue:e.subject,"onUpdate:modelValue":a[8]||(a[8]=t=>e.subject=t),name:"subject",label:o.$trans("global.import",{attribute:o.$trans("academic.subject.subject")}),onChange:P,error:r(l).subject,"onUpdate:error":a[9]||(a[9]=t=>r(l).subject=t)},null,8,["modelValue","label","error"])]),$("div",Z,[s(f,{modelValue:e.feeGroup,"onUpdate:modelValue":a[10]||(a[10]=t=>e.feeGroup=t),name:"feeGroup",label:o.$trans("global.import",{attribute:o.$trans("finance.fee_group.fee_group")}),onChange:_,error:r(l).feeGroup,"onUpdate:error":a[11]||(a[11]=t=>r(l).feeGroup=t)},null,8,["modelValue","label","error"]),s(f,{modelValue:e.feeHead,"onUpdate:modelValue":a[12]||(a[12]=t=>e.feeHead=t),name:"feeHead",label:o.$trans("global.import",{attribute:o.$trans("finance.fee_head.fee_head")}),onChange:y,error:r(l).feeHead,"onUpdate:error":a[13]||(a[13]=t=>r(l).feeHead=t)},null,8,["modelValue","label","error"]),s(f,{modelValue:e.feeConcession,"onUpdate:modelValue":a[14]||(a[14]=t=>e.feeConcession=t),name:"feeConcession",label:o.$trans("global.import",{attribute:o.$trans("finance.fee_concession.fee_concession")}),onChange:G,error:r(l).feeConcession,"onUpdate:error":a[15]||(a[15]=t=>r(l).feeConcession=t)},null,8,["modelValue","label","error"])]),$("div",x,[s(f,{modelValue:e.transportCircle,"onUpdate:modelValue":a[16]||(a[16]=t=>e.transportCircle=t),name:"transportCircle",label:o.$trans("global.import",{attribute:o.$trans("transport.circle.circle")}),onChange:F,error:r(l).transportCircle,"onUpdate:error":a[17]||(a[17]=t=>r(l).transportCircle=t)},null,8,["modelValue","label","error"]),s(f,{modelValue:e.transportFee,"onUpdate:modelValue":a[18]||(a[18]=t=>e.transportFee=t),name:"transportFee",label:o.$trans("global.import",{attribute:o.$trans("transport.fee.fee")}),onChange:g,error:r(l).transportFee,"onUpdate:error":a[19]||(a[19]=t=>r(l).transportFee=t)},null,8,["modelValue","label","error"])])])]),_:1},8,["uuid","form"])}}}),ae={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},oe={name:"AcademicPeriodShow"},se=Object.assign(oe,{setup(T){M();const C=O(),j=L(),k=R("emitter"),w={},B="academic/period/",i=I({...w}),l=d=>{Object.assign(i,d)};return(d,e)=>{const A=m("PageHeaderAction"),H=m("PageHeader"),U=m("TextMuted"),c=m("BaseDataView"),P=m("BaseButton"),_=m("ShowButton"),y=m("BaseCard"),G=m("ShowItem"),F=m("ParentTransition");return b(),h(J,null,[s(H,{title:d.$trans(r(C).meta.trans,{attribute:d.$trans(r(C).meta.label)}),navs:[{label:d.$trans("academic.academic"),path:"Academic"},{label:d.$trans("academic.period.period"),path:"AcademicPeriodList"}]},{default:n(()=>[s(A,{name:"AcademicPeriod",title:d.$trans("academic.period.period"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),s(F,{appear:"",visibility:!0},{default:n(()=>[s(G,{"init-url":B,uuid:r(C).params.uuid,onSetItem:l,onRedirectTo:e[1]||(e[1]=g=>r(j).push({name:"AcademicPeriod"}))},{default:n(()=>[i.uuid?(b(),S(y,{key:0},{title:n(()=>[p(u(i.name),1)]),footer:n(()=>[s(_,null,{default:n(()=>[r(z)("period:edit")?(b(),S(P,{key:0,design:"primary",onClick:e[0]||(e[0]=g=>r(j).push({name:"AcademicPeriodEdit",params:{uuid:i.uuid}}))},{default:n(()=>[p(u(d.$trans("general.edit")),1)]),_:1})):V("",!0)]),_:1})]),default:n(()=>[$("dl",ae,[s(c,{label:d.$trans("academic.period.props.name")},{default:n(()=>[p(u(i.name)+" ",1),i.session?(b(),S(U,{key:0,block:""},{default:n(()=>{var g;return[p(u(((g=i.session)==null?void 0:g.name)||"-"),1)]}),_:1})):V("",!0),s(U,{block:""},{default:n(()=>[p(u(i.alias),1)]),_:1})]),_:1},8,["label"]),s(c,{label:d.$trans("academic.period.props.code")},{default:n(()=>[p(u(i.code)+" ",1),s(U,{block:""},{default:n(()=>[p(u(i.shortcode),1)]),_:1})]),_:1},8,["label"]),s(c,{label:d.$trans("academic.period.props.start_date")},{default:n(()=>[p(u(i.startDate.formatted),1)]),_:1},8,["label"]),s(c,{label:d.$trans("academic.period.props.end_date")},{default:n(()=>[p(u(i.endDate.formatted),1)]),_:1},8,["label"]),s(c,{class:"col-span-1 sm:col-span-2",label:d.$trans("academic.period.props.description")},{default:n(()=>[p(u(i.description),1)]),_:1},8,["label"]),s(c,{label:d.$trans("general.created_at")},{default:n(()=>[p(u(i.createdAt.formatted),1)]),_:1},8,["label"]),s(c,{label:d.$trans("general.updated_at")},{default:n(()=>[p(u(i.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):V("",!0)]),_:1},8,["uuid"])]),_:1}),s(F,{appear:"",visibility:!0},{default:n(()=>[i.uuid?(b(),S(y,{key:0},{title:n(()=>[p(u(d.$trans("general.import")),1)]),default:n(()=>[s(te,{onCompleted:e[2]||(e[2]=g=>r(k).emit("refreshItem"))})]),_:1})):V("",!0)]),_:1})],64)}}});export{se as default};
