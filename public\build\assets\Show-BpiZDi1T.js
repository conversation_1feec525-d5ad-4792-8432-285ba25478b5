import{i as h,u as w,h as P,l as V,r as n,a as A,o as m,e as o,w as e,f as r,q as y,b as g,d as H,s,t as i,y as N,F as T}from"./app-DvIo72ZO.js";const D={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},R={name:"InventoryStockCategoryShow"},F=Object.assign(R,{setup(j){h();const l=w(),p=P(),_={},v="inventory/stockCategory/",a=V({..._}),f=t=>{Object.assign(a,t)};return(t,u)=>{const k=n("PageHeaderAction"),b=n("PageHeader"),c=n("BaseDataView"),B=n("BaseButton"),C=n("ShowButton"),S=n("BaseCard"),$=n("ShowItem"),I=n("ParentTransition");return m(),A(T,null,[o(b,{title:t.$trans(r(l).meta.trans,{attribute:t.$trans(r(l).meta.label)}),navs:[{label:t.$trans("inventory.inventory"),path:"Inventory"},{label:t.$trans("inventory.stock_category.stock_category"),path:"InventoryStockCategory"}]},{default:e(()=>[o(k,{name:"InventoryStockCategory",title:t.$trans("inventory.stock_category.stock_category"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),o(I,{appear:"",visibility:!0},{default:e(()=>[o($,{"init-url":v,uuid:r(l).params.uuid,"module-uuid":r(l).params.muuid,onSetItem:f,onRedirectTo:u[1]||(u[1]=d=>r(p).push({name:"InventoryStockCategory",params:{uuid:a.uuid}}))},{default:e(()=>[a.uuid?(m(),y(S,{key:0},{title:e(()=>[s(i(a.name),1)]),footer:e(()=>[o(C,null,{default:e(()=>[r(N)("stock-category:edit")?(m(),y(B,{key:0,design:"primary",onClick:u[0]||(u[0]=d=>r(p).push({name:"InventoryStockCategoryEdit",params:{uuid:a.uuid}}))},{default:e(()=>[s(i(t.$trans("general.edit")),1)]),_:1})):g("",!0)]),_:1})]),default:e(()=>[H("dl",D,[o(c,{label:t.$trans("inventory.inventory")},{default:e(()=>{var d;return[s(i(((d=a.inventory)==null?void 0:d.name)||"-"),1)]}),_:1},8,["label"]),o(c,{class:"col-span-1 sm:col-span-2",label:t.$trans("inventory.stock_category.props.description")},{default:e(()=>[s(i(a.description),1)]),_:1},8,["label"]),o(c,{label:t.$trans("general.created_at")},{default:e(()=>[s(i(a.createdAt.formatted),1)]),_:1},8,["label"]),o(c,{label:t.$trans("general.updated_at")},{default:e(()=>[s(i(a.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):g("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{F as default};
