import{i as x,u as O,h as z,j as L,I as M,l as V,m as q,r as s,a as G,o as p,e as o,w as a,f as i,q as f,b as y,d as b,s as r,t as l,y as h,F as J}from"./app-DvIo72ZO.js";const K={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},Q={class:"text-danger"},W={class:"grid grid-cols-3 gap-6"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3"},Z={name:"EmployeeExperienceShow"},te=Object.assign(Z,{props:{employee:{type:Object,default(){return{}}}},setup(d){x();const g=O(),B=z();L("emitter");const j={},S={status:"",comment:""},_="employee/experience/",v=M(_),c=V({...S}),$=q(!1),e=V({...j}),k=t=>{Object.assign(e,t)},w=()=>{$.value=!0};return(t,n)=>{const U=s("PageHeaderAction"),P=s("PageHeader"),T=s("BaseBadge"),u=s("BaseDataView"),A=s("ListMedia"),C=s("BaseButton"),F=s("ShowButton"),E=s("BaseCard"),I=s("BaseSelect"),D=s("BaseTextarea"),N=s("FormAction"),H=s("ShowItem"),R=s("ParentTransition");return p(),G(J,null,[o(P,{title:t.$trans(i(g).meta.trans,{attribute:t.$trans(i(g).meta.label)}),navs:[{label:t.$trans("employee.employee"),path:"Employee"},{label:d.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:d.employee.uuid}}}]},{default:a(()=>[o(U,{name:"EmployeeExperience",title:t.$trans("employee.experience.experience"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),o(R,{appear:"",visibility:!0},{default:a(()=>[o(H,{"init-url":_,uuid:i(g).params.uuid,"module-uuid":i(g).params.muuid,onSetItem:k,onRedirectTo:n[5]||(n[5]=m=>i(B).push({name:"EmployeeExperience",params:{uuid:d.employee.uuid}})),refresh:$.value,onRefreshed:n[6]||(n[6]=m=>$.value=!1)},{default:a(()=>[e.uuid?(p(),f(E,{key:0},{title:a(()=>[r(l(e.headline),1)]),footer:a(()=>[o(F,null,{default:a(()=>[i(h)("employee:edit")?(p(),f(C,{key:0,design:"primary",onClick:n[0]||(n[0]=m=>i(B).push({name:"EmployeeExperienceEdit",params:{uuid:d.employee.uuid,muuid:e.uuid}}))},{default:a(()=>[r(l(t.$trans("general.edit")),1)]),_:1})):y("",!0)]),_:1})]),default:a(()=>[b("dl",K,[o(u,{label:t.$trans("employee.employment_type.employment_type")},{default:a(()=>[r(l(e.employmentType.name)+" ",1),e.selfUpload?(p(),f(T,{key:0,design:e.verificationStatus.color},{default:a(()=>[r(l(e.verificationStatus.label),1)]),_:1},8,["design"])):y("",!0)]),_:1},8,["label"]),o(u,{label:t.$trans("employee.experience.props.title")},{default:a(()=>[r(l(e.title),1)]),_:1},8,["label"]),o(u,{label:t.$trans("employee.experience.props.organization_name")},{default:a(()=>[r(l(e.organizationName),1)]),_:1},8,["label"]),o(u,{label:t.$trans("employee.experience.props.location")},{default:a(()=>[r(l(e.location),1)]),_:1},8,["label"]),o(u,{label:t.$trans("employee.experience.props.start_date")},{default:a(()=>[r(l(e.startDate.formatted),1)]),_:1},8,["label"]),o(u,{label:t.$trans("employee.experience.props.end_date")},{default:a(()=>[r(l(e.endDate.formatted),1)]),_:1},8,["label"]),o(u,{class:"col-span-1 sm:col-span-2",label:t.$trans("employee.experience.props.job_profile")},{default:a(()=>[r(l(e.jobProfile),1)]),_:1},8,["label"]),e.selfUpload&&e.verificationStatus.value=="rejected"?(p(),f(u,{key:0,class:"col-span-1 sm:col-span-2",label:t.$trans("contact.verification.props.comment")},{default:a(()=>[b("span",Q,l(e.comment),1)]),_:1},8,["label"])):y("",!0),o(u,{class:"col-span-1 sm:col-span-2"},{default:a(()=>[o(A,{media:e.media,url:`/app/employees/${d.employee.uuid}/experiences/${e.uuid}/`},null,8,["media","url"])]),_:1}),o(u,{label:t.$trans("general.created_at")},{default:a(()=>[r(l(e.createdAt.formatted),1)]),_:1},8,["label"]),o(u,{label:t.$trans("general.updated_at")},{default:a(()=>[r(l(e.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):y("",!0),i(h)("employee:self-service-action")&&e.uuid&&e.selfUpload&&e.verificationStatus.value=="pending"?(p(),f(E,{key:1},{title:a(()=>[r(l(t.$trans("contact.verification.props.action")),1)]),default:a(()=>[o(N,{"no-card":"","keep-adding":!1,"init-url":_,uuid:d.employee.uuid,"module-uuid":e.uuid,"no-data-fetch":!0,action:"action","init-form":S,form:c,"after-submit":w},{default:a(()=>[b("div",W,[b("div",X,[o(I,{name:"status",label:t.$trans("global.select",{attribute:t.$trans("contact.verification.props.status")}),modelValue:c.status,"onUpdate:modelValue":n[1]||(n[1]=m=>c.status=m),options:[{value:"verify",label:t.$trans("contact.verification.action.verify")},{value:"reject",label:t.$trans("contact.verification.action.reject")}],error:i(v).status,"onUpdate:error":n[2]||(n[2]=m=>i(v).status=m)},null,8,["label","modelValue","options","error"])]),b("div",Y,[o(D,{modelValue:c.comment,"onUpdate:modelValue":n[3]||(n[3]=m=>c.comment=m),name:"comment",label:t.$trans("contact.comment"),error:i(v).comment,"onUpdate:error":n[4]||(n[4]=m=>i(v).comment=m)},null,8,["modelValue","label","error"])])])]),_:1},8,["uuid","module-uuid","form"])]),_:1})):y("",!0)]),_:1},8,["uuid","module-uuid","refresh"])]),_:1})],64)}}});export{te as default};
