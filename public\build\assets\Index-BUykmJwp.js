import{l as F,r as o,q as k,o as _,w as e,d as T,e as t,h as H,j as M,m as N,f as i,a as P,F as R,v as j,s as r,b as L,t as m}from"./app-DvIo72ZO.js";const S={class:"grid grid-cols-3 gap-6"},q={class:"col-span-3 sm:col-span-1"},O={__name:"Filter",emits:["hide"],setup(h,{emit:u}){const p=u,v={name:""},f=F({...v});return(C,l)=>{const c=o("BaseInput"),b=o("FilterForm");return _(),k(b,{"init-form":v,form:f,onHide:l[1]||(l[1]=n=>p("hide"))},{default:e(()=>[T("div",S,[T("div",q,[t(c,{type:"text",modelValue:f.name,"onUpdate:modelValue":l[0]||(l[0]=n=>f.name=n),name:"name",label:C.$trans("exam.term.props.name")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},U={name:"ExamTermList"},G=Object.assign(U,{setup(h){const u=H(),p=M("emitter");let v=["create","filter"],f=["print","pdf","excel"];const C="exam/term/",l=N(!1),c=F({}),b=n=>{Object.assign(c,n)};return(n,s)=>{const w=o("PageHeaderAction"),y=o("PageHeader"),x=o("ParentTransition"),B=o("TextMuted"),g=o("DataCell"),$=o("FloatingMenuItem"),I=o("FloatingMenu"),D=o("DataRow"),E=o("BaseButton"),V=o("DataTable"),A=o("ListItem");return _(),k(A,{"init-url":C,"additional-query":{details:!0},onSetItems:b},{header:e(()=>[t(y,{title:n.$trans("exam.term.term"),navs:[{label:n.$trans("exam.exam"),path:"Exam"}]},{default:e(()=>[t(w,{url:"exam/terms/",name:"ExamTerm",title:n.$trans("exam.term.term"),actions:i(v),"dropdown-actions":i(f),onToggleFilter:s[0]||(s[0]=a=>l.value=!l.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(x,{appear:"",visibility:l.value},{default:e(()=>[t(O,{onRefresh:s[1]||(s[1]=a=>i(p).emit("listItems")),onHide:s[2]||(s[2]=a=>l.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(x,{appear:"",visibility:!0},{default:e(()=>[t(V,{header:c.headers,meta:c.meta,module:"exam.term",onRefresh:s[4]||(s[4]=a=>i(p).emit("listItems"))},{actionButton:e(()=>[t(E,{onClick:s[3]||(s[3]=a=>i(u).push({name:"ExamTermCreate"}))},{default:e(()=>[r(m(n.$trans("global.add",{attribute:n.$trans("exam.term.term")})),1)]),_:1})]),default:e(()=>[(_(!0),P(R,null,j(c.data,a=>(_(),k(D,{key:a.uuid,onDoubleClick:d=>i(u).push({name:"ExamTermShow",params:{uuid:a.uuid}})},{default:e(()=>[t(g,{name:"name"},{default:e(()=>[r(m(a.name)+" ",1),a.displayName?(_(),k(B,{key:0,block:""},{default:e(()=>[r(m(a.displayName),1)]),_:2},1024)):L("",!0)]),_:2},1024),t(g,{name:"division"},{default:e(()=>{var d;return[r(m(((d=a.division)==null?void 0:d.name)||"-"),1)]}),_:2},1024),t(g,{name:"createdAt"},{default:e(()=>[r(m(a.createdAt.formatted),1)]),_:2},1024),t(g,{name:"action"},{default:e(()=>[t(I,null,{default:e(()=>[t($,{icon:"fas fa-arrow-circle-right",onClick:d=>i(u).push({name:"ExamTermShow",params:{uuid:a.uuid}})},{default:e(()=>[r(m(n.$trans("general.show")),1)]),_:2},1032,["onClick"]),t($,{icon:"fas fa-edit",onClick:d=>i(u).push({name:"ExamTermEdit",params:{uuid:a.uuid}})},{default:e(()=>[r(m(n.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t($,{icon:"fas fa-copy",onClick:d=>i(u).push({name:"ExamTermDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[r(m(n.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t($,{icon:"fas fa-trash",onClick:d=>i(p).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[r(m(n.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{G as default};
