import{i as w,u as E,h as L,l as S,r as o,a as C,o as d,e as t,w as a,f as p,q as P,b as V,d as k,s as n,t as s,F as A}from"./app-DvIo72ZO.js";const H={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},I={name:"EmployeeLeaveTypeShow"},R=Object.assign(I,{setup(N){w();const m=E(),u=L(),y={},c="employee/leave/type/",l=S({...y}),_=e=>{Object.assign(l,e)};return(e,i)=>{const v=o("PageHeaderAction"),b=o("PageHeader"),r=o("BaseDataView"),f=o("BaseButton"),g=o("ShowButton"),$=o("BaseCard"),B=o("ShowItem"),T=o("ParentTransition");return d(),C(A,null,[t(b,{title:e.$trans(p(m).meta.trans,{attribute:e.$trans(p(m).meta.label)}),navs:[{label:e.$trans("employee.employee"),path:"Employee"},{label:e.$trans("employee.leave.leave"),path:"EmployeeLeave"},{label:e.$trans("employee.leave.type.type"),path:"EmployeeLeaveTypeList"}]},{default:a(()=>[t(v,{name:"EmployeeLeaveType",title:e.$trans("employee.leave.type.type"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(T,{appear:"",visibility:!0},{default:a(()=>[t(B,{"init-url":c,uuid:p(m).params.uuid,onSetItem:_,onRedirectTo:i[1]||(i[1]=h=>p(u).push({name:"EmployeeLeaveType"}))},{default:a(()=>[l.uuid?(d(),P($,{key:0},{title:a(()=>[n(s(l.name),1)]),footer:a(()=>[t(g,null,{default:a(()=>[t(f,{design:"primary",onClick:i[0]||(i[0]=h=>p(u).push({name:"EmployeeLeaveTypeEdit",params:{uuid:l.uuid}}))},{default:a(()=>[n(s(e.$trans("general.edit")),1)]),_:1})]),_:1})]),default:a(()=>[k("dl",H,[t(r,{label:e.$trans("employee.leave.type.props.name")},{default:a(()=>[n(s(l.name),1)]),_:1},8,["label"]),t(r,{label:e.$trans("employee.leave.type.props.code")},{default:a(()=>[n(s(l.code),1)]),_:1},8,["label"]),t(r,{label:e.$trans("employee.leave.type.props.alias")},{default:a(()=>[n(s(l.alias),1)]),_:1},8,["label"]),t(r,{class:"col-span-1 sm:col-span-2",label:e.$trans("employee.leave.type.props.description")},{default:a(()=>[n(s(l.description),1)]),_:1},8,["label"]),t(r,{label:e.$trans("general.created_at")},{default:a(()=>[n(s(l.createdAt.formatted),1)]),_:1},8,["label"]),t(r,{label:e.$trans("general.updated_at")},{default:a(()=>[n(s(l.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):V("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{R as default};
