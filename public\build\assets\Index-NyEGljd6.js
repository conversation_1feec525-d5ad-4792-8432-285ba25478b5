import{u as W,l as C,n as z,r as o,q as y,o as u,w as e,d as _,b as g,s as d,t as a,e as n,h as G,j as J,y as $,m as K,f as l,B as Q,a as j,F as I,v as L}from"./app-DvIo72ZO.js";const X={class:"grid grid-cols-3 gap-6"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3 sm:col-span-1"},te={class:"col-span-3 sm:col-span-1"},se={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(P,{emit:h}){const m=W(),w=h,R=P,q={employees:[],batches:[],subjects:[],startDate:"",endDate:""},c=C({...q}),B=C({subjects:R.preRequisites.subjects}),i=C({employees:[],batches:[],subjects:[],isLoaded:!(m.query.employees||m.query.batches||m.query.subjects)});return z(async()=>{i.employees=m.query.employees?m.query.employees.split(","):[],i.batches=m.query.batches?m.query.batches.split(","):[],i.subjects=m.query.subjects?m.query.subjects.split(","):[],i.isLoaded=!0}),(v,p)=>{const r=o("BaseSelectSearch"),b=o("BaseSelect"),F=o("DatePicker"),S=o("FilterForm");return u(),y(S,{"init-form":q,form:c,multiple:["employees","batches","subjects"],onHide:p[5]||(p[5]=t=>w("hide"))},{default:e(()=>[_("div",X,[_("div",Y,[i.isLoaded?(u(),y(r,{key:0,multiple:"",name:"employees",label:v.$trans("global.select",{attribute:v.$trans("employee.employee")}),modelValue:c.employees,"onUpdate:modelValue":p[0]||(p[0]=t=>c.employees=t),"value-prop":"uuid","init-search":i.employees,"search-key":"name","search-action":"employee/list"},{selectedOption:e(t=>[d(a(t.value.name)+" ("+a(t.value.codeNumber)+") ",1)]),listOption:e(t=>[d(a(t.option.name)+" ("+a(t.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):g("",!0)]),_("div",Z,[i.isLoaded?(u(),y(r,{key:0,multiple:"",name:"batches",label:v.$trans("global.select",{attribute:v.$trans("academic.batch.batch")}),modelValue:c.batches,"onUpdate:modelValue":p[1]||(p[1]=t=>c.batches=t),"value-prop":"uuid","init-search":i.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:e(t=>[d(a(t.value.course.name)+" "+a(t.value.name),1)]),listOption:e(t=>[d(a(t.option.course.nameWithTerm)+" "+a(t.option.name),1)]),_:1},8,["label","modelValue","init-search"])):g("",!0)]),_("div",ee,[n(b,{multiple:"",modelValue:c.subjects,"onUpdate:modelValue":p[2]||(p[2]=t=>c.subjects=t),name:"subjects",label:v.$trans("academic.subject.subject"),"label-prop":"name","value-prop":"uuid",options:B.subjects},null,8,["modelValue","label","options"])]),_("div",te,[n(F,{start:c.startDate,"onUpdate:start":p[3]||(p[3]=t=>c.startDate=t),end:c.endDate,"onUpdate:end":p[4]||(p[4]=t=>c.endDate=t),name:"startDateBetween",as:"range",label:v.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},ae={class:"grid grid-cols-1 gap-4 px-4 pt-4 md:grid-cols-2 lg:grid-cols-3"},ne=["onClick"],oe={class:"px-2 py-2 text-gray-800 dark:text-gray-400"},ie={class:"flex items-center justify-between"},re={class:"font-medium"},le={class:"mt-2 flex justify-between items-center"},ue={class:"text-sm font-medium"},ce={class:"text-xl font-semibold"},de={name:"ResourceDiaryList"},pe=Object.assign(de,{setup(P){const h=G(),m=J("emitter");let w=["filter"];$("student-diary:create")&&w.unshift("create");let R=[];$("student-diary:export")&&(R=["print","pdf","excel"]);const q="resource/diary/",c=K(!1),B=C({subjects:[]}),i=C({}),v=r=>{Object.assign(B,r)},p=r=>{Object.assign(i,r)};return(r,b)=>{const F=o("PageHeaderAction"),S=o("PageHeader"),t=o("ParentTransition"),O=o("CardView"),A=o("Pagination"),M=o("CardList"),k=o("DataCell"),T=o("TextMuted"),V=o("FloatingMenuItem"),N=o("FloatingMenu"),U=o("DataRow"),H=o("BaseButton"),E=o("DataTable"),x=o("ListItem");return u(),y(x,{"init-url":q,"pre-requisites":!0,onSetPreRequisites:v,"additional-query":{},onSetItems:p},{header:e(()=>[n(S,{title:r.$trans("resource.diary.diary"),navs:[{label:r.$trans("resource.resource"),path:"Resource"}]},{default:e(()=>[n(F,{url:"resource/diaries/",name:"ResourceDiary",title:r.$trans("resource.diary.diary"),actions:l(w),"dropdown-actions":l(R),onToggleFilter:b[0]||(b[0]=s=>c.value=!c.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[n(t,{appear:"",visibility:c.value},{default:e(()=>[n(se,{onRefresh:b[1]||(b[1]=s=>l(m).emit("listItems")),"pre-requisites":B,onHide:b[2]||(b[2]=s=>c.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:e(()=>[l(Q)(["student","guardian"],"any")?(u(),y(t,{key:0,appear:"",visibility:!0},{default:e(()=>[n(M,{header:i.headers,meta:i.meta},{content:e(()=>[_("div",ce,a(r.$trans("dashboard.nothing_to_show")),1)]),default:e(()=>[_("div",ae,[(u(!0),j(I,null,L(i.data,s=>(u(),j("div",{key:s.uuid,class:"cursor-pointer",onClick:f=>l(h).push({name:"ResourceDiaryPreview",query:{batch:s.batchUuid,date:s.date.value}})},[n(O,{"no-padding":""},{default:e(()=>[_("div",oe,[_("div",ie,[_("span",re,a(s.date.formatted),1)]),_("div",le,[_("span",ue,a(s.courseBatch),1)])])]),_:2},1024)],8,ne))),128))]),_("div",null,[n(A,{"card-view":"",meta:i.meta,onRefresh:b[3]||(b[3]=s=>l(m).emit("listItems"))},null,8,["meta"])])]),_:1},8,["header","meta"])]),_:1})):(u(),y(t,{key:1,appear:"",visibility:!0},{default:e(()=>[n(E,{header:i.headers,meta:i.meta,module:"resource.diary",onRefresh:b[5]||(b[5]=s=>l(m).emit("listItems"))},{actionButton:e(()=>[l($)("student-diary:create")?(u(),y(H,{key:0,onClick:b[4]||(b[4]=s=>l(h).push({name:"ResourceDiaryCreate"}))},{default:e(()=>[d(a(r.$trans("global.add",{attribute:r.$trans("resource.diary.diary")})),1)]),_:1})):g("",!0)]),default:e(()=>[(u(!0),j(I,null,L(i.data,s=>(u(),y(U,{key:s.uuid,onDoubleClick:f=>l(h).push({name:"ResourceDiaryShow",params:{uuid:s.uuid}})},{default:e(()=>[n(k,{name:"date"},{default:e(()=>[d(a(s.date.formatted),1)]),_:2},1024),n(k,{name:"records"},{default:e(()=>[(u(!0),j(I,null,L(s.records,f=>{var D;return u(),j("div",null,[d(a(((D=f.batch.course)==null?void 0:D.name)+" "+f.batch.name)+" ",1),f.subject?(u(),y(T,{key:0},{default:e(()=>[d(a(f.subject.name),1)]),_:2},1024)):g("",!0)])}),256))]),_:2},1024),n(k,{name:"employee"},{default:e(()=>{var f;return[d(a(((f=s.employee)==null?void 0:f.name)||"-")+" ",1),n(T,{block:""},{default:e(()=>{var D;return[d(a((D=s.employee)==null?void 0:D.codeNumber),1)]}),_:2},1024)]}),_:2},1024),n(k,{name:"createdAt"},{default:e(()=>[d(a(s.createdAt.formatted),1)]),_:2},1024),n(k,{name:"action"},{default:e(()=>[n(N,null,{default:e(()=>[n(V,{icon:"fas fa-arrow-circle-right",onClick:f=>l(h).push({name:"ResourceDiaryShow",params:{uuid:s.uuid}})},{default:e(()=>[d(a(r.$trans("general.show")),1)]),_:2},1032,["onClick"]),l($)("student-diary:edit")&&s.isEditable?(u(),y(V,{key:0,icon:"fas fa-edit",onClick:f=>l(h).push({name:"ResourceDiaryEdit",params:{uuid:s.uuid}})},{default:e(()=>[d(a(r.$trans("general.edit")),1)]),_:2},1032,["onClick"])):g("",!0),l($)("student-diary:create")?(u(),y(V,{key:1,icon:"fas fa-copy",onClick:f=>l(h).push({name:"ResourceDiaryDuplicate",params:{uuid:s.uuid}})},{default:e(()=>[d(a(r.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):g("",!0),l($)("student-diary:delete")&&s.isDeletable?(u(),y(V,{key:2,icon:"fas fa-trash",onClick:f=>l(m).emit("deleteItem",{uuid:s.uuid})},{default:e(()=>[d(a(r.$trans("general.delete")),1)]),_:2},1032,["onClick"])):g("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1}))]),_:1})}}});export{pe as default};
