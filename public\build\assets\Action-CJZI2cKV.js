import{u as $,h as I,i as N,I as P,m as T,l as g,r as c,q as v,o as _,w as m,d as u,e as i,f as o,b as S,s as y,t as l,K as q,a as L,F as R}from"./app-DvIo72ZO.js";const C={class:"grid grid-cols-3 gap-6"},H={class:"col-span-3 sm:col-span-1"},w={class:"col-span-3 sm:col-span-1"},E={class:"col-span-2 sm:col-span-1"},K={class:"col-span-2 sm:col-span-1"},W={class:"col-span-2 sm:col-span-1"},z={class:"col-span-3"},G={name:"AcademicSubjectInchargeForm"},J=Object.assign(G,{setup(B){const b=$();I(),N();const n={batch:"",subject:"",employee:"",startDate:"",endDate:"",remarks:""},V="academic/subjectIncharge/",s=P(V);T(!1);const h=g({subjects:[]}),p=g({selectedBatch:null,subjects:[]}),r=g({...n}),d=g({batch:"",subject:"",employee:"",isLoaded:!b.params.uuid}),U=a=>{Object.assign(h,a)},D=async a=>{r.batch=(a==null?void 0:a.uuid)||""},A=async a=>{var t,j;await D(a.batch),Object.assign(n,{...a,startDate:a.startDate.value,endDate:a.endDate.value,batch:((t=a.batch)==null?void 0:t.uuid)||"",subject:((j=a.subject)==null?void 0:j.uuid)||"",employee:a.employee.uuid}),Object.assign(r,q(n)),d.employee=a.employee.uuid,p.selectedBatch=a.batch,d.isLoaded=!0};return(a,t)=>{const j=c("BaseSelect"),f=c("BaseSelectSearch"),k=c("DatePicker"),F=c("BaseTextarea"),O=c("FormAction");return _(),v(O,{"pre-requisites":!0,onSetPreRequisites:U,"init-url":V,"init-form":n,form:r,setForm:A,redirect:"AcademicSubjectIncharge"},{default:m(()=>[u("div",C,[u("div",H,[i(j,{modelValue:r.subject,"onUpdate:modelValue":t[0]||(t[0]=e=>r.subject=e),name:"subject",label:a.$trans("academic.subject.subject"),"label-prop":"name","value-prop":"uuid",options:h.subjects,error:o(s).subject,"onUpdate:error":t[1]||(t[1]=e=>o(s).subject=e)},null,8,["modelValue","label","options","error"])]),u("div",w,[d.isLoaded?(_(),v(f,{key:0,name:"batch",label:a.$trans("academic.batch.batch"),modelValue:p.selectedBatch,"onUpdate:modelValue":t[2]||(t[2]=e=>p.selectedBatch=e),error:o(s).batch,"onUpdate:error":t[3]||(t[3]=e=>o(s).batch=e),"value-prop":"uuid","object-prop":!0,"init-search":d.batch,"search-key":"course_batch","search-action":"academic/batch/list",onChange:D},{selectedOption:m(e=>[y(l(e.value.course.name)+" - "+l(e.value.name),1)]),listOption:m(e=>[y(l(e.option.course.nameWithTerm)+" - "+l(e.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):S("",!0)]),u("div",E,[d.isLoaded?(_(),v(f,{key:0,name:"employee",label:a.$trans("global.select",{attribute:a.$trans("employee.employee")}),modelValue:r.employee,"onUpdate:modelValue":t[4]||(t[4]=e=>r.employee=e),error:o(s).employee,"onUpdate:error":t[5]||(t[5]=e=>o(s).employee=e),"value-prop":"uuid","init-search":d.employee,"search-key":"name","search-action":"employee/list"},{selectedOption:m(e=>[y(l(e.value.name)+" ("+l(e.value.codeNumber)+") ",1)]),listOption:m(e=>[y(l(e.option.name)+" ("+l(e.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","error","init-search"])):S("",!0)]),u("div",K,[i(k,{modelValue:r.startDate,"onUpdate:modelValue":t[6]||(t[6]=e=>r.startDate=e),name:"startDate",label:a.$trans("employee.incharge.props.start_date"),"no-clear":"",error:o(s).startDate,"onUpdate:error":t[7]||(t[7]=e=>o(s).startDate=e)},null,8,["modelValue","label","error"])]),u("div",W,[i(k,{modelValue:r.endDate,"onUpdate:modelValue":t[8]||(t[8]=e=>r.endDate=e),name:"endDate",label:a.$trans("employee.incharge.props.end_date"),"no-clear":"",error:o(s).endDate,"onUpdate:error":t[9]||(t[9]=e=>o(s).endDate=e)},null,8,["modelValue","label","error"])]),u("div",z,[i(F,{modelValue:r.remarks,"onUpdate:modelValue":t[10]||(t[10]=e=>r.remarks=e),name:"remarks",label:a.$trans("employee.incharge.props.remarks"),error:o(s).remarks,"onUpdate:error":t[11]||(t[11]=e=>o(s).remarks=e)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),M={name:"AcademicSubjectInchargeAction"},X=Object.assign(M,{setup(B){const b=$();return(n,V)=>{const s=c("PageHeaderAction"),h=c("PageHeader"),p=c("ParentTransition");return _(),L(R,null,[i(h,{title:n.$trans(o(b).meta.trans,{attribute:n.$trans(o(b).meta.label)}),navs:[{label:n.$trans("academic.academic"),path:"Academic"},{label:n.$trans("academic.subject.subject"),path:"AcademicSubject"},{label:n.$trans("academic.subject_incharge.subject_incharge"),path:"AcademicSubjectInchargeList"}]},{default:m(()=>[i(s,{name:"AcademicSubjectIncharge",title:n.$trans("academic.subject_incharge.subject_incharge"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),i(p,{appear:"",visibility:!0},{default:m(()=>[i(J)]),_:1})],64)}}});export{X as default};
