import{u as M,l as F,n as H,r as a,q as f,o as c,w as e,d as C,e as n,b as k,h as U,j as q,y as $,m as j,f as s,a as E,F as O,v as L,s as r,t as i}from"./app-DvIo72ZO.js";const z={class:"grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},W={__name:"Filter",emits:["hide"],setup(V,{emit:b}){const v=M(),D=b,y={incomingNumber:"",outgointNumber:"",purposes:[],startDate:"",endDate:""},u=F({...y}),_=F({purposes:[],isLoaded:!v.query.purposes});return H(async()=>{_.purposes=v.query.purposes?v.query.purposes.split(","):[],_.isLoaded=!0}),(m,o)=>{const l=a("BaseInput"),d=a("BaseSelectSearch"),w=a("DatePicker"),B=a("FilterForm");return c(),f(B,{"init-form":y,form:u,multiple:["purposes"],onHide:o[5]||(o[5]=p=>D("hide"))},{default:e(()=>[C("div",z,[C("div",G,[n(l,{type:"text",modelValue:u.incomingNumber,"onUpdate:modelValue":o[0]||(o[0]=p=>u.incomingNumber=p),name:"incomingNumber",label:m.$trans("reception.call_log.props.incoming_number")},null,8,["modelValue","label"])]),C("div",J,[n(l,{type:"text",modelValue:u.outgoingNumber,"onUpdate:modelValue":o[1]||(o[1]=p=>u.outgoingNumber=p),name:"outgoingNumber",label:m.$trans("reception.call_log.props.outgoing_number")},null,8,["modelValue","label"])]),C("div",K,[_.isLoaded?(c(),f(d,{key:0,multiple:"",name:"purposes",label:m.$trans("global.select",{attribute:m.$trans("reception.call_log.purpose.purpose")}),modelValue:u.purposes,"onUpdate:modelValue":o[2]||(o[2]=p=>u.purposes=p),"value-prop":"uuid","init-search":_.purposes,"search-action":"option/list","additional-search-query":{type:"calling_purpose"}},null,8,["label","modelValue","init-search"])):k("",!0)]),C("div",Q,[n(w,{start:u.startDate,"onUpdate:start":o[3]||(o[3]=p=>u.startDate=p),end:u.endDate,"onUpdate:end":o[4]||(o[4]=p=>u.endDate=p),name:"dateBetween",as:"range",label:m.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},X={name:"ReceptionCallLogList"},Z=Object.assign(X,{setup(V){const b=U(),v=q("emitter");let D=["filter"];$("call-log:create")&&D.unshift("create");let y=[];$("call-log:export")&&(y=["print","pdf","excel"]);const u="reception/callLog/",_=j(!1),m=F({}),o=l=>{Object.assign(m,l)};return(l,d)=>{const w=a("PageHeaderAction"),B=a("PageHeader"),p=a("ParentTransition"),g=a("DataCell"),R=a("TextMuted"),N=a("FloatingMenuItem"),I=a("FloatingMenu"),A=a("DataRow"),P=a("BaseButton"),S=a("DataTable"),T=a("ListItem");return c(),f(T,{"init-url":u,onSetItems:o},{header:e(()=>[n(B,{title:l.$trans("reception.call_log.call_log"),navs:[{label:l.$trans("reception.reception"),path:"Reception"}]},{default:e(()=>[n(w,{url:"reception/call-logs/",name:"ReceptionCallLog",title:l.$trans("reception.call_log.call_log"),actions:s(D),"dropdown-actions":s(y),onToggleFilter:d[0]||(d[0]=t=>_.value=!_.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[n(p,{appear:"",visibility:_.value},{default:e(()=>[n(W,{onRefresh:d[1]||(d[1]=t=>s(v).emit("listItems")),onHide:d[2]||(d[2]=t=>_.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[n(p,{appear:"",visibility:!0},{default:e(()=>[n(S,{header:m.headers,meta:m.meta,module:"reception.call_log",onRefresh:d[4]||(d[4]=t=>s(v).emit("listItems"))},{actionButton:e(()=>[s($)("call-log:create")?(c(),f(P,{key:0,onClick:d[3]||(d[3]=t=>s(b).push({name:"ReceptionCallLogCreate"}))},{default:e(()=>[r(i(l.$trans("global.add",{attribute:l.$trans("reception.call_log.call_log")})),1)]),_:1})):k("",!0)]),default:e(()=>[(c(!0),E(O,null,L(m.data,t=>(c(),f(A,{key:t.uuid,onDoubleClick:h=>s(b).push({name:"ReceptionCallLogShow",params:{uuid:t.uuid}})},{default:e(()=>[n(g,{name:"type"},{default:e(()=>[r(i(t.type.label),1)]),_:2},1024),n(g,{name:"purpose"},{default:e(()=>[r(i(t.purpose.name),1)]),_:2},1024),n(g,{name:"name"},{default:e(()=>[r(i(t.name)+" ",1),t.companyName?(c(),f(R,{key:0,block:""},{default:e(()=>[r(i(t.companyName),1)]),_:2},1024)):k("",!0)]),_:2},1024),n(g,{name:"incomingNumber"},{default:e(()=>[r(i(t.incomingNumber),1)]),_:2},1024),n(g,{name:"outgoingNumber"},{default:e(()=>[r(i(t.outgoingNumber),1)]),_:2},1024),n(g,{name:"callAt"},{default:e(()=>[r(i(t.callAt.formatted)+" ",1),n(R,{block:""},{default:e(()=>[r(i(t.duration.label),1)]),_:2},1024)]),_:2},1024),n(g,{name:"createdAt"},{default:e(()=>[r(i(t.createdAt.formatted),1)]),_:2},1024),n(g,{name:"action"},{default:e(()=>[n(I,null,{default:e(()=>[n(N,{icon:"fas fa-arrow-circle-right",onClick:h=>s(b).push({name:"ReceptionCallLogShow",params:{uuid:t.uuid}})},{default:e(()=>[r(i(l.$trans("general.show")),1)]),_:2},1032,["onClick"]),s($)("call-log:edit")?(c(),f(N,{key:0,icon:"fas fa-edit",onClick:h=>s(b).push({name:"ReceptionCallLogEdit",params:{uuid:t.uuid}})},{default:e(()=>[r(i(l.$trans("general.edit")),1)]),_:2},1032,["onClick"])):k("",!0),s($)("call-log:create")?(c(),f(N,{key:1,icon:"fas fa-copy",onClick:h=>s(b).push({name:"ReceptionCallLogDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[r(i(l.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):k("",!0),s($)("call-log:delete")?(c(),f(N,{key:2,icon:"fas fa-trash",onClick:h=>s(v).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[r(i(l.$trans("general.delete")),1)]),_:2},1032,["onClick"])):k("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Z as default};
