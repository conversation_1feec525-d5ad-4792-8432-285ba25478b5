import{u as E,l as R,n as P,r as i,q as _,o as p,w as e,d as j,b as k,s as o,t as s,e as n,h as W,j as z,y as g,m as G,f as c,a as V,F as T,v as O}from"./app-DvIo72ZO.js";const J={class:"grid grid-cols-3 gap-6"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(S,{emit:h}){const d=E(),$=h,q=S,C={employees:[],batches:[],subjects:[],startDate:"",endDate:""},l=R({...C}),L=R({subjects:q.preRequisites.subjects}),m=R({employees:[],batches:[],subjects:[],isLoaded:!(d.query.employees||d.query.batches||d.query.subjects)});return P(async()=>{m.employees=d.query.employees?d.query.employees.split(","):[],m.batches=d.query.batches?d.query.batches.split(","):[],m.subjects=d.query.subjects?d.query.subjects.split(","):[],m.isLoaded=!0}),(y,u)=>{const r=i("BaseSelectSearch"),b=i("BaseSelect"),B=i("DatePicker"),F=i("FilterForm");return p(),_(F,{"init-form":C,form:l,multiple:["employees","types","batches","subjects"],onHide:u[5]||(u[5]=t=>$("hide"))},{default:e(()=>[j("div",J,[j("div",K,[m.isLoaded?(p(),_(r,{key:0,multiple:"",name:"employees",label:y.$trans("global.select",{attribute:y.$trans("employee.employee")}),modelValue:l.employees,"onUpdate:modelValue":u[0]||(u[0]=t=>l.employees=t),"value-prop":"uuid","init-search":m.employees,"search-key":"name","search-action":"employee/list"},{selectedOption:e(t=>[o(s(t.value.name)+" ("+s(t.value.codeNumber)+") ",1)]),listOption:e(t=>[o(s(t.option.name)+" ("+s(t.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):k("",!0)]),j("div",Q,[m.isLoaded?(p(),_(r,{key:0,multiple:"",name:"batches",label:y.$trans("global.select",{attribute:y.$trans("academic.batch.batch")}),modelValue:l.batches,"onUpdate:modelValue":u[1]||(u[1]=t=>l.batches=t),"value-prop":"uuid","init-search":m.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:e(t=>[o(s(t.value.course.name)+" "+s(t.value.name),1)]),listOption:e(t=>[o(s(t.option.course.nameWithTerm)+" "+s(t.option.name),1)]),_:1},8,["label","modelValue","init-search"])):k("",!0)]),j("div",X,[n(b,{multiple:"",modelValue:l.subjects,"onUpdate:modelValue":u[2]||(u[2]=t=>l.subjects=t),name:"subjects",label:y.$trans("academic.subject.subject"),"label-prop":"name","value-prop":"uuid",options:L.subjects},null,8,["modelValue","label","options"])]),j("div",Y,[n(B,{start:l.startDate,"onUpdate:start":u[3]||(u[3]=t=>l.startDate=t),end:l.endDate,"onUpdate:end":u[4]||(u[4]=t=>l.endDate=t),name:"startDateBetween",as:"range",label:y.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},x={name:"ResourceLessonPlanList"},te=Object.assign(x,{setup(S){const h=W(),d=z("emitter");let $=["filter"];g("resource:config")&&$.push("config"),g("lesson-plan:create")&&$.unshift("create");let q=[];g("lesson-plan:export")&&(q=["print","pdf","excel"]);const C="resource/lessonPlan/",l=G(!1),L=R({subjects:[]}),m=R({}),y=r=>{Object.assign(L,r)},u=r=>{Object.assign(m,r)};return(r,b)=>{const B=i("PageHeaderAction"),F=i("PageHeader"),t=i("ParentTransition"),v=i("DataCell"),I=i("TextMuted"),w=i("FloatingMenuItem"),M=i("FloatingMenu"),N=i("DataRow"),A=i("BaseButton"),H=i("DataTable"),U=i("ListItem");return p(),_(U,{"init-url":C,"pre-requisites":!0,onSetPreRequisites:y,"additional-query":{},onSetItems:u},{header:e(()=>[n(F,{title:r.$trans("resource.lesson_plan.lesson_plan"),navs:[{label:r.$trans("resource.resource"),path:"Resource"}]},{default:e(()=>[n(B,{url:"resource/lesson-plans/",name:"ResourceLessonPlan",title:r.$trans("resource.lesson_plan.lesson_plan"),actions:c($),"dropdown-actions":c(q),"config-path":"ResourceConfig",onToggleFilter:b[0]||(b[0]=a=>l.value=!l.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[n(t,{appear:"",visibility:l.value},{default:e(()=>[n(Z,{onRefresh:b[1]||(b[1]=a=>c(d).emit("listItems")),"pre-requisites":L,onHide:b[2]||(b[2]=a=>l.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:e(()=>[n(t,{appear:"",visibility:!0},{default:e(()=>[n(H,{header:m.headers,meta:m.meta,module:"resource.lesson_plan",onRefresh:b[4]||(b[4]=a=>c(d).emit("listItems"))},{actionButton:e(()=>[c(g)("lesson-plan:create")?(p(),_(A,{key:0,onClick:b[3]||(b[3]=a=>c(h).push({name:"ResourceLessonPlanCreate"}))},{default:e(()=>[o(s(r.$trans("global.add",{attribute:r.$trans("resource.lesson_plan.lesson_plan")})),1)]),_:1})):k("",!0)]),default:e(()=>[(p(!0),V(T,null,O(m.data,a=>(p(),_(N,{key:a.uuid,onDoubleClick:f=>c(h).push({name:"ResourceLessonPlanShow",params:{uuid:a.uuid}})},{default:e(()=>[n(v,{name:"topic"},{default:e(()=>[o(s(a.topicExcerpt),1)]),_:2},1024),n(v,{name:"records"},{default:e(()=>[(p(!0),V(T,null,O(a.records,f=>{var D;return p(),V("div",null,[o(s(((D=f.batch.course)==null?void 0:D.name)+" "+f.batch.name)+" ",1),f.subject?(p(),_(I,{key:0},{default:e(()=>[o(s(f.subject.name),1)]),_:2},1024)):k("",!0)])}),256))]),_:2},1024),n(v,{name:"employee"},{default:e(()=>{var f;return[o(s(((f=a.employee)==null?void 0:f.name)||"-")+" ",1),n(I,{block:""},{default:e(()=>{var D;return[o(s((D=a.employee)==null?void 0:D.codeNumber),1)]}),_:2},1024)]}),_:2},1024),n(v,{name:"startDate"},{default:e(()=>[o(s(a.startDate.formatted),1)]),_:2},1024),n(v,{name:"endDate"},{default:e(()=>[o(s(a.endDate.formatted),1)]),_:2},1024),n(v,{name:"createdAt"},{default:e(()=>[o(s(a.createdAt.formatted),1)]),_:2},1024),n(v,{name:"action"},{default:e(()=>[n(M,null,{default:e(()=>[n(w,{icon:"fas fa-arrow-circle-right",onClick:f=>c(h).push({name:"ResourceLessonPlanShow",params:{uuid:a.uuid}})},{default:e(()=>[o(s(r.$trans("general.show")),1)]),_:2},1032,["onClick"]),c(g)("lesson-plan:edit")&&a.isEditable?(p(),_(w,{key:0,icon:"fas fa-edit",onClick:f=>c(h).push({name:"ResourceLessonPlanEdit",params:{uuid:a.uuid}})},{default:e(()=>[o(s(r.$trans("general.edit")),1)]),_:2},1032,["onClick"])):k("",!0),c(g)("lesson-plan:create")?(p(),_(w,{key:1,icon:"fas fa-copy",onClick:f=>c(h).push({name:"ResourceLessonPlanDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[o(s(r.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):k("",!0),c(g)("lesson-plan:delete")&&a.isDeletable?(p(),_(w,{key:2,icon:"fas fa-trash",onClick:f=>c(d).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[o(s(r.$trans("general.delete")),1)]),_:2},1032,["onClick"])):k("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{te as default};
