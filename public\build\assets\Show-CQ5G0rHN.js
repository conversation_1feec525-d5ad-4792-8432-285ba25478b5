import{i as P,u as V,h as N,l as A,r,a as m,o as c,e as a,w as e,f as d,q as I,b as L,d as f,s as o,t as s,F as k,v as M}from"./app-DvIo72ZO.js";const D={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},E=["innerHTML"],R={name:"HostelBlockShow"},O=Object.assign(R,{setup(j){P();const p=V(),b=N(),B={},$="hostel/block/",l=A({...B}),h=t=>{Object.assign(l,t)};return(t,i)=>{const H=r("PageHeaderAction"),g=r("PageHeader"),n=r("BaseDataView"),v=r("TextMuted"),w=r("BaseButton"),S=r("ShowButton"),T=r("BaseCard"),y=r("ShowItem"),C=r("ParentTransition");return c(),m(k,null,[a(g,{title:t.$trans(d(p).meta.trans,{attribute:t.$trans(d(p).meta.label)}),navs:[{label:t.$trans("hostel.hostel"),path:"Hostel"},{label:t.$trans("hostel.block.block"),path:"HostelBlockList"}]},{default:e(()=>[a(H,{name:"HostelBlock",title:t.$trans("hostel.block.block"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(C,{appear:"",visibility:!0},{default:e(()=>[a(y,{"init-url":$,uuid:d(p).params.uuid,onSetItem:h,onRedirectTo:i[1]||(i[1]=u=>d(b).push({name:"HostelBlock"}))},{default:e(()=>[l.uuid?(c(),I(T,{key:0},{title:e(()=>[o(s(l.name),1)]),footer:e(()=>[a(S,null,{default:e(()=>[a(w,{design:"primary",onClick:i[0]||(i[0]=u=>d(b).push({name:"HostelBlockEdit",params:{uuid:l.uuid}}))},{default:e(()=>[o(s(t.$trans("general.edit")),1)]),_:1})]),_:1})]),default:e(()=>[f("dl",D,[a(n,{label:t.$trans("hostel.block.props.name")},{default:e(()=>[o(s(l.name),1)]),_:1},8,["label"]),a(n,{label:t.$trans("hostel.block.props.alias")},{default:e(()=>[o(s(l.alias),1)]),_:1},8,["label"]),a(n,{label:t.$trans("hostel.block.props.contact_number")},{default:e(()=>[o(s(l.contactNumber),1)]),_:1},8,["label"]),a(n,{label:t.$trans("hostel.block.props.contact_email")},{default:e(()=>[o(s(l.contactEmail),1)]),_:1},8,["label"]),a(n,{label:t.$trans("hostel.block.props.address")},{default:e(()=>[f("span",{innerHTML:l.address},null,8,E)]),_:1},8,["label"]),a(n,{label:t.$trans("hostel.block_incharge.block_incharge")},{default:e(()=>[(c(!0),m(k,null,M(l.incharges,u=>{var _;return c(),m("div",null,[o(s(((_=u==null?void 0:u.employee)==null?void 0:_.name)||"-")+" ",1),a(v,null,{default:e(()=>[o(s(u==null?void 0:u.period),1)]),_:2},1024)])}),256))]),_:1},8,["label"]),a(n,{class:"col-span-1 sm:col-span-2",label:t.$trans("hostel.block.props.description")},{default:e(()=>[o(s(l.description),1)]),_:1},8,["label"]),a(n,{label:t.$trans("general.created_at")},{default:e(()=>[o(s(l.createdAt.formatted),1)]),_:1},8,["label"]),a(n,{label:t.$trans("general.updated_at")},{default:e(()=>[o(s(l.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):L("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{O as default};
