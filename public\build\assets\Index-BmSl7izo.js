import{u as E,i as O,m as F,I,l as B,n as W,ad as D,r as c,q as T,o as y,w as d,d as u,e as m,f as r,s as j,a as z,b as S,t as b,F as C}from"./app-DvIo72ZO.js";import"./lodash-BPUmB9Gy.js";const M={class:"grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},J={key:0,class:"ml-1"},K={key:0,class:"ml-1"},Q={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"flex items-center gap-2"},ee={class:"grid grid-cols-3 gap-6"},te={class:"col-span-3 sm:col-span-1"},ae={class:"col-span-3 sm:col-span-1"},se={class:"mt-4 grid grid-cols-4 gap-6"},re={class:"col-span-3 sm:col-span-1"},oe={class:"col-span-3 sm:col-span-1"},le={class:"mt-4 grid grid-cols-4 gap-6"},ne={class:"col-span-4 sm:col-span-1"},ie={class:"col-span-4 sm:col-span-1"},me={class:"col-span-4 sm:col-span-1"},ue={class:"col-span-4 sm:col-span-1"},pe={__name:"Filter",props:{initUrl:{type:String,default:""},preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(L,{emit:w}){const l=E(),h=O(),g=w,p=L,x=[{label:"A4 Portrait",value:"A4"},{label:"A4 Landscape",value:"A4-L"},{label:"A3 Portrait",value:"A3"},{label:"A3 Landscape",value:"A3-L"},{label:"Legal Portrait",value:"Legal"},{label:"Legal Landscape",value:"Legal-L"}],A={exam:"",attempt:"first",batch:"",subjects:[],title:"",showPrintDateTime:!1,signatory1:"",signatory2:"",signatory3:"",signatory4:"",showWatermark:!1,paper_size:"A4-L"},V=F(!1),q=F(!1),o=I(p.initUrl),a=B({...A}),i=B({exams:p.preRequisites.exams,subjects:[]}),n=B({exam:"",batch:"",subjects:[],isLoaded:!(l.query.exam&&l.query.batch&&l.query.subjects)}),v=async s=>{if(!s){a.batch="",a.subjects=[],i.subjects=[];return}a.batch=s,i.subjects=[],a.subjects=[],V.value=!0,await h.dispatch("academic/batch/listSubjects",{uuid:s}).then(e=>{i.subjects=e,V.value=!1}).catch(e=>{V.value=!1})};return W(async()=>{if(_.isEmpty(l.query)){n.isLoaded=!0;return}n.exam=l.query.exam,a.exam=l.query.exam,a.attempt=l.query.attempt,n.batch=l.query.batch,a.batch=l.query.batch,a.showWatermark=D(l.query.showWatermark||""),a.showPrintDateTime=D(l.query.showPrintDateTime||""),a.paper_size=l.query.paper_size||"A4-L",l.query.batch&&(await v(l.query.batch),a.subjects=l.query.subjects?l.query.subjects.split(","):[],n.subjects=l.query.subjects?l.query.subjects.split(","):[]),n.isLoaded=!0}),(s,e)=>{const f=c("BaseSelect"),P=c("BaseSelectSearch"),R=c("BaseSwitch"),k=c("BaseInput"),H=c("BaseFieldset"),N=c("FilterForm");return y(),T(N,{"init-form":A,multiple:["subjects"],form:a,onHide:e[24]||(e[24]=t=>g("hide"))},{default:d(()=>[u("div",M,[u("div",G,[m(f,{modelValue:a.exam,"onUpdate:modelValue":e[0]||(e[0]=t=>a.exam=t),name:"exam",label:s.$trans("exam.exam"),"value-prop":"uuid",options:L.preRequisites.exams,error:r(o).exam,"onUpdate:error":e[1]||(e[1]=t=>r(o).exam=t)},{selectedOption:d(t=>{var $,U;return[j(b(t.value.name)+" ",1),t.value.term?(y(),z("span",J,"("+b(((U=($=t.value.term)==null?void 0:$.division)==null?void 0:U.name)||s.$trans("general.all"))+")",1)):S("",!0)]}),listOption:d(t=>{var $,U;return[j(b(t.option.name)+" ",1),t.option.term?(y(),z("span",K,"("+b(((U=($=t.option.term)==null?void 0:$.division)==null?void 0:U.name)||s.$trans("general.all"))+")",1)):S("",!0)]}),_:1},8,["modelValue","label","options","error"])]),u("div",Q,[n.isLoaded?(y(),T(f,{key:0,modelValue:a.attempt,"onUpdate:modelValue":e[2]||(e[2]=t=>a.attempt=t),name:"attempt",label:s.$trans("exam.schedule.props.attempt"),options:L.preRequisites.attempts,error:r(o).attempt,"onUpdate:error":e[3]||(e[3]=t=>r(o).attempt=t)},null,8,["modelValue","label","options","error"])):S("",!0)]),u("div",X,[n.isLoaded?(y(),T(P,{key:0,name:"batch",label:s.$trans("global.select",{attribute:s.$trans("academic.batch.batch")}),modelValue:a.batch,"onUpdate:modelValue":e[4]||(e[4]=t=>a.batch=t),error:r(o).batch,"onUpdate:error":e[5]||(e[5]=t=>r(o).batch=t),"value-prop":"uuid","init-search":n.batch,"search-key":"course_batch","search-action":"academic/batch/list",onChange:v},{selectedOption:d(t=>[j(b(t.value.course.name)+" "+b(t.value.name),1)]),listOption:d(t=>[j(b(t.option.course.nameWithTerm)+" "+b(t.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):S("",!0)]),u("div",Y,[m(f,{multiple:"",modelValue:a.subjects,"onUpdate:modelValue":e[6]||(e[6]=t=>a.subjects=t),name:"subjects",label:s.$trans("academic.subject.subject"),"label-prop":"name","value-prop":"uuid",options:i.subjects},null,8,["modelValue","label","options"])])]),m(H,{class:"mt-4"},{legend:d(()=>[u("div",Z,[j(b(s.$trans("global.show",{attribute:s.$trans("general.options")}))+" ",1),m(R,{reverse:"",modelValue:q.value,"onUpdate:modelValue":e[7]||(e[7]=t=>q.value=t),name:"showOptions"},null,8,["modelValue"])])]),default:d(()=>[q.value?(y(),z(C,{key:0},[u("div",ee,[u("div",te,[m(R,{vertical:"",modelValue:a.showWatermark,"onUpdate:modelValue":e[8]||(e[8]=t=>a.showWatermark=t),name:"showWatermark",label:s.$trans("global.show",{attribute:s.$trans("print.watermark")}),error:r(o).showWatermark,"onUpdate:error":e[9]||(e[9]=t=>r(o).showWatermark=t)},null,8,["modelValue","label","error"])]),u("div",ae,[m(R,{vertical:"",modelValue:a.showPrintDateTime,"onUpdate:modelValue":e[10]||(e[10]=t=>a.showPrintDateTime=t),name:"showPrintDateTime",label:s.$trans("global.show",{attribute:s.$trans("general.print_date_time")}),error:r(o).showPrintDateTime,"onUpdate:error":e[11]||(e[11]=t=>r(o).showPrintDateTime=t)},null,8,["modelValue","label","error"])])]),u("div",se,[u("div",re,[m(k,{type:"text",modelValue:a.title,"onUpdate:modelValue":e[12]||(e[12]=t=>a.title=t),name:"title",label:s.$trans("print.title"),error:r(o).title,"onUpdate:error":e[13]||(e[13]=t=>r(o).title=t)},null,8,["modelValue","label","error"])]),u("div",oe,[m(f,{modelValue:a.paper_size,"onUpdate:modelValue":e[14]||(e[14]=t=>a.paper_size=t),name:"paper_size",label:s.$trans("print.paper_size"),options:x,error:r(o).paper_size,"onUpdate:error":e[15]||(e[15]=t=>r(o).paper_size=t)},null,8,["modelValue","label","error"])])]),u("div",le,[u("div",ne,[m(k,{type:"text",modelValue:a.signatory1,"onUpdate:modelValue":e[16]||(e[16]=t=>a.signatory1=t),name:"signatory1",label:s.$trans("print.signatory1"),error:r(o).signatory1,"onUpdate:error":e[17]||(e[17]=t=>r(o).signatory1=t)},null,8,["modelValue","label","error"])]),u("div",ie,[m(k,{type:"text",modelValue:a.signatory2,"onUpdate:modelValue":e[18]||(e[18]=t=>a.signatory2=t),name:"signatory2",label:s.$trans("print.signatory2"),error:r(o).signatory2,"onUpdate:error":e[19]||(e[19]=t=>r(o).signatory2=t)},null,8,["modelValue","label","error"])]),u("div",me,[m(k,{type:"text",modelValue:a.signatory3,"onUpdate:modelValue":e[20]||(e[20]=t=>a.signatory3=t),name:"signatory3",label:s.$trans("print.signatory3"),error:r(o).signatory3,"onUpdate:error":e[21]||(e[21]=t=>r(o).signatory3=t)},null,8,["modelValue","label","error"])]),u("div",ue,[m(k,{type:"text",modelValue:a.signatory4,"onUpdate:modelValue":e[22]||(e[22]=t=>a.signatory4=t),name:"signatory4",label:s.$trans("print.signatory4"),error:r(o).signatory4,"onUpdate:error":e[23]||(e[23]=t=>r(o).signatory4=t)},null,8,["modelValue","label","error"])])])],64)):S("",!0)]),_:1})]),_:1},8,["form"])}}},de={name:"ExamReportMarkSummary"},ye=Object.assign(de,{setup(L){const w=E(),l=O(),h="exam/report/",g=F(!0),p=F(!1),x=B({exams:[],attempts:[]}),A=async()=>{p.value=!0;try{const i="/exam/reports/mark-summary/download-pdf",n={...w.query};!n.paper_size&&n.paperSize&&(n.paper_size=n.paperSize,delete n.paperSize);const v=new URLSearchParams(n).toString(),s=`${i}?${v}`,e=document.createElement("a");e.href=s,e.setAttribute("download","mark-summary.pdf"),e.setAttribute("target","_blank"),document.body.appendChild(e),e.click(),setTimeout(()=>{document.body.removeChild(e)},100)}catch(i){console.error("Download failed:",i)}finally{p.value=!1}};let V=["filter"],q=[{name:"download_pdf",label:"Download PDF",icon:"fas fa-file-pdf",action:A}];const o=async()=>{p.value=!0,await l.dispatch(h+"preRequisite",{name:"mark-summary"}).then(i=>{p.value=!1,Object.assign(x,i)}).catch(i=>{p.value=!1})},a=async()=>{p.value=!0,await l.dispatch(h+"fetchReport",{name:"mark-summary",params:w.query}).then(i=>{p.value=!1,window.open("/print").document.write(i)}).catch(i=>{p.value=!1})};return W(async()=>{await o()}),(i,n)=>{const v=c("PageHeaderAction"),s=c("PageHeader"),e=c("ParentTransition"),f=c("BaseCard");return y(),z(C,null,[m(s,{title:i.$trans(r(w).meta.label),navs:[{label:i.$trans("exam.exam"),path:"Exam"},{label:i.$trans("exam.report.report"),path:"ExamReport"}]},{default:d(()=>[m(v,{url:"exam/reports/mark-summary/",name:"ExamReportMarkSummary",title:i.$trans("exam.report.mark_summary.mark_summary"),actions:r(V),"dropdown-actions":r(q),onToggleFilter:n[0]||(n[0]=P=>g.value=!g.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"]),m(e,{appear:"",visibility:g.value},{default:d(()=>[m(pe,{onAfterFilter:a,"init-url":h,"pre-requisites":x,onHide:n[1]||(n[1]=P=>g.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"]),m(e,{appear:"",visibility:!0},{default:d(()=>[m(f,{"no-padding":"","no-content-padding":"","is-loading":p.value},null,8,["is-loading"])]),_:1})],64)}}});export{ye as default};
