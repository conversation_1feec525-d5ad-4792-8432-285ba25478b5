import{I as $,l as g,r as m,q,o as y,w as v,d as i,a as V,b as B,e as s,f as n,F as _,K as F,u as P}from"./app-DvIo72ZO.js";const S={class:"grid grid-cols-3 gap-6"},j={class:"col-span-3 sm:col-span-1"},A={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},C={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3 sm:col-span-1"},N={class:"col-span-3"},R={name:"InventoryStockItemForm"},T=Object.assign(R,{setup(k){const p={name:"",code:"",unit:"",category:"",isQuantityEditable:!0,place:"",quantity:0,description:""},l="inventory/stockItem/",r=$(l),u=g({stockCategories:[]}),o=g({...p}),c=a=>{Object.assign(u,a)},f=a=>{var e;Object.assign(p,{...a,category:a.category.uuid,place:(e=a.place)==null?void 0:e.uuid,quantity:a.editableQuantity||0}),Object.assign(o,F(p))};return(a,e)=>{const d=m("BaseInput"),b=m("BaseSelect"),I=m("BaseTextarea"),U=m("FormAction");return y(),q(U,{"pre-requisites":!0,onSetPreRequisites:c,"init-url":l,"init-form":p,form:o,"set-form":f,redirect:"InventoryStockItem"},{default:v(()=>[i("div",S,[i("div",j,[s(d,{type:"text",modelValue:o.name,"onUpdate:modelValue":e[0]||(e[0]=t=>o.name=t),name:"name",label:a.$trans("inventory.stock_item.props.name"),error:n(r).name,"onUpdate:error":e[1]||(e[1]=t=>n(r).name=t)},null,8,["modelValue","label","error"])]),i("div",A,[s(d,{type:"text",modelValue:o.code,"onUpdate:modelValue":e[2]||(e[2]=t=>o.code=t),name:"code",label:a.$trans("inventory.stock_item.props.code"),error:n(r).code,"onUpdate:error":e[3]||(e[3]=t=>n(r).code=t)},null,8,["modelValue","label","error"])]),i("div",E,[s(d,{type:"text",modelValue:o.unit,"onUpdate:modelValue":e[4]||(e[4]=t=>o.unit=t),name:"unit",label:a.$trans("inventory.stock_item.props.unit"),error:n(r).unit,"onUpdate:error":e[5]||(e[5]=t=>n(r).unit=t)},null,8,["modelValue","label","error"])]),i("div",O,[s(b,{modelValue:o.category,"onUpdate:modelValue":e[6]||(e[6]=t=>o.category=t),name:"category",label:a.$trans("inventory.stock_category.stock_category"),"label-prop":"name","value-prop":"uuid",options:u.categories,error:n(r).category,"onUpdate:error":e[7]||(e[7]=t=>n(r).category=t)},null,8,["modelValue","label","options","error"])]),o.isQuantityEditable?(y(),V(_,{key:0},[i("div",C,[s(b,{modelValue:o.place,"onUpdate:modelValue":e[8]||(e[8]=t=>o.place=t),name:"place",label:a.$trans("inventory.place"),"label-prop":"fullName","value-prop":"uuid",options:u.places,error:n(r).place,"onUpdate:error":e[9]||(e[9]=t=>n(r).place=t)},null,8,["modelValue","label","options","error"])]),i("div",H,[s(d,{step:"any",type:"number",modelValue:o.quantity,"onUpdate:modelValue":e[10]||(e[10]=t=>o.quantity=t),name:"quantity",label:a.$trans("inventory.stock_item.props.quantity"),error:n(r).quantity,"onUpdate:error":e[11]||(e[11]=t=>n(r).quantity=t)},null,8,["modelValue","label","error"])])],64)):B("",!0),i("div",N,[s(I,{modelValue:o.description,"onUpdate:modelValue":e[12]||(e[12]=t=>o.description=t),name:"description",label:a.$trans("inventory.stock_item.props.description"),error:n(r).description,"onUpdate:error":e[13]||(e[13]=t=>n(r).description=t)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),Q={name:"InventoryStockItemAction"},D=Object.assign(Q,{setup(k){const p=P();return(l,r)=>{const u=m("PageHeaderAction"),o=m("PageHeader"),c=m("ParentTransition");return y(),V(_,null,[s(o,{title:l.$trans(n(p).meta.trans,{attribute:l.$trans(n(p).meta.label)}),navs:[{label:l.$trans("inventory.inventory"),path:"Inventory"},{label:l.$trans("inventory.stock_item.stock_item"),path:"InventoryStockItemList"}]},{default:v(()=>[s(u,{name:"InventoryStockItem",title:l.$trans("inventory.stock_item.stock_item"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),s(c,{appear:"",visibility:!0},{default:v(()=>[s(T)]),_:1})],64)}}});export{D as default};
