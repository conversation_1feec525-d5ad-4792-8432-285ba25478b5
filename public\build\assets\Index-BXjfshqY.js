import{u as M,l as B,n as j,r,q as p,o as m,w as e,d as F,e as i,h as N,j as S,y as _,m as V,f as n,a as U,F as E,v as O,s as l,t as u,b as $}from"./app-DvIo72ZO.js";const q={class:"grid grid-cols-3 gap-6"},z={class:"col-span-3 sm:col-span-1"},G={__name:"Filter",emits:["hide"],setup(L,{emit:c}){M();const b=c,k={startDate:"",endDate:""},d=B({...k}),D=B({isLoaded:!1});return j(async()=>{D.isLoaded=!0}),(f,s)=>{const C=r("DatePicker"),o=r("FilterForm");return m(),p(o,{"init-form":k,form:d,multiple:[],onHide:s[2]||(s[2]=t=>b("hide"))},{default:e(()=>[F("div",q,[F("div",z,[i(C,{start:d.startDate,"onUpdate:start":s[0]||(s[0]=t=>d.startDate=t),end:d.endDate,"onUpdate:end":s[1]||(s[1]=t=>d.endDate=t),name:"dateBetween",as:"range",label:f.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},J={name:"LibraryBookAdditionList"},Q=Object.assign(J,{setup(L){const c=N(),b=S("emitter");let k=["filter"];_("book-addition:create")&&k.unshift("create");let d=[];_("book-addition:export")&&(d=["print","pdf","excel"]);const D="library/bookAddition/",f=V(!1),s=B({}),C=o=>{Object.assign(s,o)};return(o,t)=>{const h=r("PageHeaderAction"),I=r("PageHeader"),w=r("ParentTransition"),y=r("DataCell"),g=r("FloatingMenuItem"),P=r("FloatingMenu"),A=r("DataRow"),H=r("BaseButton"),R=r("DataTable"),T=r("ListItem");return m(),p(T,{"init-url":D,onSetItems:C},{header:e(()=>[i(I,{title:o.$trans("library.book_addition.book_addition"),navs:[{label:o.$trans("library.library"),path:"Library"}]},{default:e(()=>[i(h,{url:"library/book-additions/",name:"LibraryBookAddition",title:o.$trans("library.book_addition.book_addition"),actions:n(k),"dropdown-actions":n(d),onToggleFilter:t[0]||(t[0]=a=>f.value=!f.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[i(w,{appear:"",visibility:f.value},{default:e(()=>[i(G,{onRefresh:t[1]||(t[1]=a=>n(b).emit("listItems")),onHide:t[2]||(t[2]=a=>f.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[i(w,{appear:"",visibility:!0},{default:e(()=>[i(R,{header:s.headers,meta:s.meta,module:"library.book_addition",onRefresh:t[4]||(t[4]=a=>n(b).emit("listItems"))},{actionButton:e(()=>[n(_)("book-addition:create")?(m(),p(H,{key:0,onClick:t[3]||(t[3]=a=>n(c).push({name:"LibraryBookAdditionCreate"}))},{default:e(()=>[l(u(o.$trans("global.add",{attribute:o.$trans("library.book_addition.book_addition")})),1)]),_:1})):$("",!0)]),default:e(()=>[(m(!0),U(E,null,O(s.data,a=>(m(),p(A,{key:a.uuid,onDoubleClick:v=>n(c).push({name:"LibraryBookAdditionShow",params:{uuid:a.uuid}})},{default:e(()=>[i(y,{name:"date"},{default:e(()=>[l(u(a.date.formatted),1)]),_:2},1024),i(y,{name:"copies"},{default:e(()=>[l(u(a.copiesCount),1)]),_:2},1024),i(y,{name:"createdAt"},{default:e(()=>[l(u(a.createdAt.formatted),1)]),_:2},1024),i(y,{name:"action"},{default:e(()=>[i(P,null,{default:e(()=>[i(g,{icon:"fas fa-arrow-circle-right",onClick:v=>n(c).push({name:"LibraryBookAdditionShow",params:{uuid:a.uuid}})},{default:e(()=>[l(u(o.$trans("general.show")),1)]),_:2},1032,["onClick"]),n(_)("book-addition:edit")?(m(),p(g,{key:0,icon:"fas fa-edit",onClick:v=>n(c).push({name:"LibraryBookAdditionEdit",params:{uuid:a.uuid}})},{default:e(()=>[l(u(o.$trans("general.edit")),1)]),_:2},1032,["onClick"])):$("",!0),n(_)("book-addition:create")?(m(),p(g,{key:1,icon:"fas fa-copy",onClick:v=>n(c).push({name:"LibraryBookAdditionDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[l(u(o.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):$("",!0),n(_)("book-addition:delete")?(m(),p(g,{key:2,icon:"fas fa-trash",onClick:v=>n(b).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[l(u(o.$trans("general.delete")),1)]),_:2},1032,["onClick"])):$("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Q as default};
