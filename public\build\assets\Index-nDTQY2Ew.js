import{u as N,l as w,n as H,r as l,q as _,o as f,w as e,d as I,b as h,s as d,t as a,e as o,h as O,j as P,y,m as R,f as s,a as U,F as j,v as E}from"./app-DvIo72ZO.js";const z={class:"grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},Q={__name:"Filter",emits:["hide"],setup(B,{emit:g}){const p=N(),$=g,b={divisions:[],employees:[],startDate:"",endDate:""},u=w({...b}),r=w({divisions:[],employees:[],isLoaded:!(p.query.divisions||p.query.employees)});return H(async()=>{r.divisions=p.query.divisions?p.query.divisions.split(","):[],r.employees=p.query.employees?p.query.employees.split(","):[],r.isLoaded=!0}),(v,c)=>{const n=l("BaseSelectSearch"),m=l("DatePicker"),A=l("FilterForm");return f(),_(A,{"init-form":b,form:u,multiple:["divisions","employees"],onHide:c[4]||(c[4]=i=>$("hide"))},{default:e(()=>[I("div",z,[I("div",G,[r.isLoaded?(f(),_(n,{key:0,multiple:"",name:"divisions",label:v.$trans("global.select",{attribute:v.$trans("academic.division.division")}),modelValue:u.divisions,"onUpdate:modelValue":c[0]||(c[0]=i=>u.divisions=i),"value-prop":"uuid","init-search":r.divisions,"search-key":"course_division","search-action":"academic/division/list"},{selectedOption:e(i=>[d(a(i.value.name),1)]),listOption:e(i=>[d(a(i.option.name),1)]),_:1},8,["label","modelValue","init-search"])):h("",!0)]),I("div",J,[r.isLoaded?(f(),_(n,{key:0,multiple:"",name:"employees",label:v.$trans("global.select",{attribute:v.$trans("employee.employee")}),modelValue:u.employees,"onUpdate:modelValue":c[1]||(c[1]=i=>u.employees=i),"value-prop":"uuid","init-search":r.employees,"search-key":"name","search-action":"employee/list"},{selectedOption:e(i=>[d(a(i.value.name)+" ("+a(i.value.codeNumber)+") ",1)]),listOption:e(i=>[d(a(i.option.name)+" ("+a(i.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):h("",!0)]),I("div",K,[o(m,{start:u.startDate,"onUpdate:start":c[2]||(c[2]=i=>u.startDate=i),end:u.endDate,"onUpdate:end":c[3]||(c[3]=i=>u.endDate=i),name:"dateBetween",as:"range",label:v.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},W={name:"AcademicDivisionInchargeList"},Y=Object.assign(W,{setup(B){const g=O(),p=P("emitter");let $=["filter"];y("division-incharge:create")&&$.unshift("create");let b=[];y("division-incharge:export")&&(b=["print","pdf","excel"]);const u="academic/divisionIncharge/",r=R(!1),v=w({}),c=n=>{Object.assign(v,n)};return(n,m)=>{const A=l("PageHeaderAction"),i=l("PageHeader"),F=l("ParentTransition"),D=l("DataCell"),V=l("TextMuted"),k=l("FloatingMenuItem"),L=l("FloatingMenu"),S=l("DataRow"),T=l("BaseButton"),q=l("DataTable"),M=l("ListItem");return f(),_(M,{"init-url":u,onSetItems:c},{header:e(()=>[o(i,{title:n.$trans("academic.division_incharge.division_incharge"),navs:[{label:n.$trans("academic.academic"),path:"Academic"},{label:n.$trans("academic.division.division"),path:"AcademicDivision"}]},{default:e(()=>[o(A,{url:"academic/division-incharges/",name:"AcademicDivisionIncharge",title:n.$trans("academic.division_incharge.division_incharge"),actions:s($),"dropdown-actions":s(b),onToggleFilter:m[0]||(m[0]=t=>r.value=!r.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[o(F,{appear:"",visibility:r.value},{default:e(()=>[o(Q,{onRefresh:m[1]||(m[1]=t=>s(p).emit("listItems")),onHide:m[2]||(m[2]=t=>r.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[o(F,{appear:"",visibility:!0},{default:e(()=>[o(q,{header:v.headers,meta:v.meta,module:"academic.division_incharge",onRefresh:m[4]||(m[4]=t=>s(p).emit("listItems"))},{actionButton:e(()=>[s(y)("division-incharge:create")?(f(),_(T,{key:0,onClick:m[3]||(m[3]=t=>s(g).push({name:"AcademicDivisionInchargeCreate"}))},{default:e(()=>[d(a(n.$trans("global.add",{attribute:n.$trans("academic.division_incharge.division_incharge")})),1)]),_:1})):h("",!0)]),default:e(()=>[(f(!0),U(j,null,E(v.data,t=>(f(),_(S,{key:t.uuid,onDoubleClick:C=>s(g).push({name:"AcademicDivisionInchargeShow",params:{uuid:t.uuid}})},{default:e(()=>[o(D,{name:"division"},{default:e(()=>[d(a(t.division.name),1)]),_:2},1024),o(D,{name:"employee"},{default:e(()=>[d(a(t.employee.name)+" ",1),o(V,{block:""},{default:e(()=>[d(a(t.employee.codeNumber),1)]),_:2},1024)]),_:2},1024),o(D,{name:"period"},{default:e(()=>[d(a(t.period),1)]),_:2},1024),o(D,{name:"createdAt"},{default:e(()=>[d(a(t.createdAt.formatted),1)]),_:2},1024),o(D,{name:"action"},{default:e(()=>[o(L,null,{default:e(()=>[o(k,{icon:"fas fa-arrow-circle-right",onClick:C=>s(g).push({name:"AcademicDivisionInchargeShow",params:{uuid:t.uuid}})},{default:e(()=>[d(a(n.$trans("general.show")),1)]),_:2},1032,["onClick"]),s(y)("division-incharge:edit")?(f(),_(k,{key:0,icon:"fas fa-edit",onClick:C=>s(g).push({name:"AcademicDivisionInchargeEdit",params:{uuid:t.uuid}})},{default:e(()=>[d(a(n.$trans("general.edit")),1)]),_:2},1032,["onClick"])):h("",!0),s(y)("division-incharge:create")?(f(),_(k,{key:1,icon:"fas fa-copy",onClick:C=>s(g).push({name:"AcademicDivisionInchargeDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[d(a(n.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):h("",!0),s(y)("division-incharge:delete")?(f(),_(k,{key:2,icon:"fas fa-trash",onClick:C=>s(p).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[d(a(n.$trans("general.delete")),1)]),_:2},1032,["onClick"])):h("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Y as default};
