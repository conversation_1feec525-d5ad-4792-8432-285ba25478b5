import{l as h,r as o,q as b,o as v,w as e,d as T,e as t,h as P,j as V,m as R,f as r,a as j,F as L,v as M,s as p,t as u}from"./app-DvIo72ZO.js";const N={class:"grid grid-cols-3 gap-6"},E={class:"col-span-3 sm:col-span-1"},O={__name:"Filter",emits:["hide"],setup(w,{emit:l}){const m=l,f={name:""},c=h({...f});return($,i)=>{const d=o("BaseInput"),C=o("FilterForm");return v(),b(C,{"init-form":f,form:c,onHide:i[1]||(i[1]=a=>m("hide"))},{default:e(()=>[T("div",N,[T("div",E,[t(d,{type:"text",modelValue:c.name,"onUpdate:modelValue":i[0]||(i[0]=a=>c.name=a),name:"name",label:$.$trans("transport.stoppage.props.name")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},U={name:"TransportStoppageList"},z=Object.assign(U,{setup(w){const l=P(),m=V("emitter");let f=["create","filter"],c=["print","pdf","excel"];const $="transport/stoppage/",i=R(!1),d=h({}),C=a=>{Object.assign(d,a)};return(a,s)=>{const B=o("PageHeaderAction"),I=o("PageHeader"),F=o("ParentTransition"),k=o("DataCell"),_=o("FloatingMenuItem"),S=o("FloatingMenu"),D=o("DataRow"),y=o("BaseButton"),A=o("DataTable"),H=o("ListItem");return v(),b(H,{"init-url":$,onSetItems:C},{header:e(()=>[t(I,{title:a.$trans("transport.stoppage.stoppage"),navs:[{label:a.$trans("transport.transport"),path:"Transport"}]},{default:e(()=>[t(B,{url:"transport/stoppages/",name:"TransportStoppage",title:a.$trans("transport.stoppage.stoppage"),actions:r(f),"dropdown-actions":r(c),onToggleFilter:s[0]||(s[0]=n=>i.value=!i.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(F,{appear:"",visibility:i.value},{default:e(()=>[t(O,{onRefresh:s[1]||(s[1]=n=>r(m).emit("listItems")),onHide:s[2]||(s[2]=n=>i.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(F,{appear:"",visibility:!0},{default:e(()=>[t(A,{header:d.headers,meta:d.meta,module:"transport.stoppage",onRefresh:s[4]||(s[4]=n=>r(m).emit("listItems"))},{actionButton:e(()=>[t(y,{onClick:s[3]||(s[3]=n=>r(l).push({name:"TransportStoppageCreate"}))},{default:e(()=>[p(u(a.$trans("global.add",{attribute:a.$trans("transport.stoppage.stoppage")})),1)]),_:1})]),default:e(()=>[(v(!0),j(L,null,M(d.data,n=>(v(),b(D,{key:n.uuid,onDoubleClick:g=>r(l).push({name:"TransportStoppageShow",params:{uuid:n.uuid}})},{default:e(()=>[t(k,{name:"name"},{default:e(()=>[p(u(n.name),1)]),_:2},1024),t(k,{name:"createdAt"},{default:e(()=>[p(u(n.createdAt.formatted),1)]),_:2},1024),t(k,{name:"action"},{default:e(()=>[t(S,null,{default:e(()=>[t(_,{icon:"fas fa-arrow-circle-right",onClick:g=>r(l).push({name:"TransportStoppageShow",params:{uuid:n.uuid}})},{default:e(()=>[p(u(a.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(_,{icon:"fas fa-edit",onClick:g=>r(l).push({name:"TransportStoppageEdit",params:{uuid:n.uuid}})},{default:e(()=>[p(u(a.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t(_,{icon:"fas fa-copy",onClick:g=>r(l).push({name:"TransportStoppageDuplicate",params:{uuid:n.uuid}})},{default:e(()=>[p(u(a.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t(_,{icon:"fas fa-trash",onClick:g=>r(m).emit("deleteItem",{uuid:n.uuid})},{default:e(()=>[p(u(a.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{z as default};
