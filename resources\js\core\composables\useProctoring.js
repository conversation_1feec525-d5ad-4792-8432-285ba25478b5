import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import * as Api from '@core/apis'
import * as faceapi from 'face-api.js'
import RecordRTC from 'recordrtc'

export function useProctoring(examUuid, submissionUuid, proctorConfig) {
    const store = useStore()

    // State
    const isInitialized = ref(false)
    const isActive = ref(false)
    const errors = ref([])

    // Reactive submission UUID to allow updates
    const currentSubmissionUuid = ref(submissionUuid)

    // Media streams
    const videoStream = ref(null)
    const audioStream = ref(null)
    const screenStream = ref(null)
    const screenRecorder = ref(null)
    const audioRecorder = ref(null)
    
    // Face detection
    const faceDetectionInterval = ref(null)
    const lastFaceDetection = ref(null)
    const consecutiveNoFaceCount = ref(0)
    
    // Monitoring intervals
    const webcamCaptureInterval = ref(null)
    const audioMonitoringInterval = ref(null)
    
    // Audio monitoring
    const audioContext = ref(null)
    const audioAnalyser = ref(null)
    const audioDataArray = ref(null)

    // Intelligent audio conversation detection
    const audioLevelHistory = ref([])
    const isRecordingConversation = ref(false)
    const conversationRecorder = ref(null)
    const conversationStartTime = ref(null)
    const speechDetectionBuffer = ref([])
    const silenceTimeout = ref(null)
    const lastAudioActivity = ref(0)

    // Advanced audio analysis
    const frequencyAnalysisBuffer = ref([])
    const speechFrequencyRanges = {
        fundamental: { min: 85, max: 300 },    // Human fundamental frequency
        formants: { min: 300, max: 3400 },     // Speech formants
        consonants: { min: 2000, max: 8000 }   // Consonant energy
    }
    const noisePatterns = ref([])
    const voiceActivityHistory = ref([])
    const spectralCentroid = ref([])
    const zeroCrossingRate = ref([])

    // Audio intelligence configuration (will be updated from proctorConfig)
    const speechThreshold = computed(() => proctorConfig.audioThresholdDb || -40)
    const speechConfidenceThreshold = computed(() => (proctorConfig.speechConfidenceThreshold || 60) / 100)
    const conversationMinDuration = ref(10000) // Minimum 10 seconds
    const conversationMaxDuration = computed(() => (proctorConfig.audioRecordingDurationSeconds || 30) * 1000)
    const maxSilenceDuration = ref(3000) // 3 seconds of silence ends conversation
    const speechDetectionWindowSize = ref(5) // Analyze last 5 audio samples

    // Event tracking
    const eventCounts = reactive({
        webcamCaptures: 0,
        audioAlerts: 0,
        tabSwitches: 0,
        fullscreenExits: 0,
        copyPasteAttempts: 0,
        faceDetectionFailures: 0
    })

    /**
     * Initialize proctoring system
     */
    const initialize = async () => {
        try {
            errors.value = []
            
            // Load face-api models
            if (proctorConfig.faceDetection) {
                await loadFaceApiModels()
            }

            // Initialize media streams
            if (proctorConfig.webcamMonitoring) {
                await initializeWebcam()
            }

            if (proctorConfig.microphoneMonitoring) {
                await initializeMicrophone()
            }

            if (proctorConfig.screenRecording) {
                await initializeScreenRecording()
            }
            
            // Set up event listeners
            setupEventListeners()
            
            isInitialized.value = true
            
        } catch (error) {
            console.error('Failed to initialize proctoring:', error)
            errors.value.push(`Initialization failed: ${error.message}`)
            throw error
        }
    }

    /**
     * Start proctoring monitoring
     */
    const startMonitoring = () => {
        if (!isInitialized.value) {
            throw new Error('Proctoring not initialized')
        }
        
        isActive.value = true
        
        // Start webcam monitoring
        if (proctorConfig.webcamMonitoring && videoStream.value) {
            startWebcamCapture()
        }

        // Start audio monitoring
        if (proctorConfig.microphoneMonitoring && audioStream.value) {
            startAudioMonitoring()
        }

        // Start face detection
        if (proctorConfig.faceDetection && videoStream.value) {
            startFaceDetection()
        }

        // Start screen recording
        if (proctorConfig.screenRecording && screenRecorder.value) {
            screenRecorder.value.startRecording()
        }

        // Enforce fullscreen if required
        if (proctorConfig.fullscreenEnforcement) {
            enforceFullscreen()
        }

        // Block copy/paste if required
        if (proctorConfig.copyPasteBlocking) {
            blockCopyPaste()
        }
    }

    /**
     * Stop proctoring monitoring
     */
    const stopMonitoring = () => {
        isActive.value = false
        
        // Clear intervals
        if (webcamCaptureInterval.value) {
            clearInterval(webcamCaptureInterval.value)
        }
        
        if (audioMonitoringInterval.value) {
            clearInterval(audioMonitoringInterval.value)
        }

        // Stop any ongoing conversation recording
        if (isRecordingConversation.value) {
            stopConversationRecording('monitoring_stopped')
        }

        // Clear silence timeout
        if (silenceTimeout.value) {
            clearTimeout(silenceTimeout.value)
            silenceTimeout.value = null
        }
        
        if (faceDetectionInterval.value) {
            clearInterval(faceDetectionInterval.value)
        }
        
        // Stop recordings
        if (screenRecorder.value) {
            screenRecorder.value.stopRecording()
        }

        if (audioRecorder.value) {
            audioRecorder.value.stopRecording()
        }

        // Stop media streams
        if (videoStream.value) {
            videoStream.value.getTracks().forEach(track => track.stop())
        }

        if (audioStream.value) {
            audioStream.value.getTracks().forEach(track => track.stop())
        }

        // Stop screen sharing stream (this was missing!)
        if (screenStream.value) {
            screenStream.value.getTracks().forEach(track => track.stop())
        }
        
        // Remove event listeners
        removeEventListeners()
    }

    /**
     * Load face-api.js models
     */
    const loadFaceApiModels = async () => {
        const modelUrl = '/models'
        
        await Promise.all([
            faceapi.nets.tinyFaceDetector.loadFromUri(modelUrl),
            faceapi.nets.faceLandmark68Net.loadFromUri(modelUrl),
            faceapi.nets.faceRecognitionNet.loadFromUri(modelUrl)
        ])
    }

    /**
     * Initialize webcam
     */
    const initializeWebcam = async () => {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    width: { ideal: 640 },
                    height: { ideal: 480 },
                    facingMode: 'user'
                }
            })
            
            videoStream.value = stream
            
        } catch (error) {
            throw new Error(`Webcam access denied: ${error.message}`)
        }
    }

    /**
     * Initialize microphone
     */
    const initializeMicrophone = async () => {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true
                }
            })
            
            audioStream.value = stream
            
            // Set up audio analysis
            audioContext.value = new (window.AudioContext || window.webkitAudioContext)()
            const source = audioContext.value.createMediaStreamSource(stream)
            audioAnalyser.value = audioContext.value.createAnalyser()
            audioAnalyser.value.fftSize = 256
            
            source.connect(audioAnalyser.value)
            
            const bufferLength = audioAnalyser.value.frequencyBinCount
            audioDataArray.value = new Uint8Array(bufferLength)
            
        } catch (error) {
            throw new Error(`Microphone access denied: ${error.message}`)
        }
    }

    /**
     * Initialize screen recording
     */
    const initializeScreenRecording = async () => {
        try {
            const stream = await navigator.mediaDevices.getDisplayMedia({
                video: true,
                audio: true
            })

            screenStream.value = stream

            let chunkCounter = 0

            screenRecorder.value = new RecordRTC(stream, {
                type: 'video',
                mimeType: 'video/webm;codecs=vp8,opus',
                timeSlice: 15000, // 15 second chunks for more frequent evidence
                ondataavailable: (blob) => {
                    chunkCounter++
                    console.log(`Screen recording chunk ${chunkCounter} captured: ${blob.size} bytes`)
                    // Upload screen recording chunk with chunk info
                    uploadScreenRecording(blob, chunkCounter, null) // totalChunks unknown during recording
                }
            })

        } catch (error) {
            throw new Error(`Screen recording access denied: ${error.message}`)
        }
    }

    /**
     * Start webcam capture at intervals - more frequent for better evidence
     */
    const startWebcamCapture = () => {
        // Use more frequent captures: half the configured interval, minimum 10 seconds
        const baseInterval = proctorConfig.captureIntervalSeconds * 1000
        const intelligentInterval = Math.max(baseInterval / 2, 10000) // Minimum 10 seconds

        console.log(`Starting webcam capture every ${intelligentInterval/1000} seconds`)

        webcamCaptureInterval.value = setInterval(async () => {
            await captureWebcamImage()
        }, intelligentInterval)

        // Capture immediately
        captureWebcamImage()
    }

    /**
     * Trigger immediate capture during suspicious activities
     */
    const triggerSuspiciousActivityCapture = async (reason) => {
        console.log(`Triggering immediate capture due to: ${reason}`)

        // Immediate webcam capture
        if (proctorConfig.webcamMonitoring && videoStream.value) {
            await captureWebcamImage()
        }

        // Trigger additional screen recording chunk if possible
        if (proctorConfig.screenRecording && screenRecorder.value) {
            // Note: RecordRTC doesn't allow manual chunk triggers, but we log the event
            console.log('Screen recording is active during suspicious activity')
        }
    }

    /**
     * Capture webcam image
     */
    const captureWebcamImage = async () => {
        if (!videoStream.value) return
        
        try {
            const video = document.createElement('video')
            video.srcObject = videoStream.value
            video.play()
            
            await new Promise(resolve => {
                video.onloadedmetadata = resolve
            })
            
            const canvas = document.createElement('canvas')
            canvas.width = video.videoWidth
            canvas.height = video.videoHeight
            
            const ctx = canvas.getContext('2d')
            ctx.drawImage(video, 0, 0)
            
            // Convert to blob
            canvas.toBlob(async (blob) => {
                let faceData = null
                
                // Detect faces if enabled
                if (proctorConfig.faceDetection) {
                    faceData = await detectFaces(canvas)
                }
                
                // Upload capture
                await uploadWebcamCapture(blob, faceData)
                eventCounts.webcamCaptures++
                
            }, 'image/jpeg', 0.8)
            
        } catch (error) {
            console.error('Failed to capture webcam image:', error)
        }
    }

    /**
     * Detect faces in image
     */
    const detectFaces = async (canvas) => {
        try {
            const detections = await faceapi
                .detectAllFaces(canvas, new faceapi.TinyFaceDetectorOptions())
                .withFaceLandmarks()
                .withFaceDescriptors()
            
            lastFaceDetection.value = Date.now()
            
            if (detections.length === 0) {
                consecutiveNoFaceCount.value++
                
                if (consecutiveNoFaceCount.value >= proctorConfig.maxFaceDetectionFailures) {
                    await logFaceDetectionFailure('No face detected for extended period')
                    eventCounts.faceDetectionFailures++
                }
            } else {
                consecutiveNoFaceCount.value = 0
                
                if (detections.length > 1) {
                    await logSuspiciousActivity('Multiple faces detected', {
                        face_count: detections.length
                    })
                }
            }
            
            return detections.map(detection => ({
                confidence: detection.detection.score,
                box: detection.detection.box
            }))
            
        } catch (error) {
            console.error('Face detection failed:', error)
            await logFaceDetectionFailure(`Face detection error: ${error.message}`)
            return null
        }
    }

    // API calls for logging events
    const uploadWebcamCapture = async (blob, faceData) => {
        if (!currentSubmissionUuid.value) {
            console.error('Cannot upload webcam capture: submission UUID not set')
            return
        }

        // Don't upload if monitoring has been stopped
        if (!isActive.value) {
            console.log('Skipping webcam capture upload - monitoring stopped')
            return
        }

        const formData = new FormData()
        formData.append('submission_uuid', currentSubmissionUuid.value)
        formData.append('image', blob, 'webcam_capture.jpg')

        if (faceData && Array.isArray(faceData)) {
            // Send face data as individual array elements for Laravel validation
            faceData.forEach((face, index) => {
                formData.append(`face_data[${index}][confidence]`, face.confidence)
                formData.append(`face_data[${index}][box][x]`, face.box.x)
                formData.append(`face_data[${index}][box][y]`, face.box.y)
                formData.append(`face_data[${index}][box][width]`, face.box.width)
                formData.append(`face_data[${index}][box][height]`, face.box.height)
            })
        }

        try {
            await Api.custom({
                url: `app/online-exams/${examUuid}/proctoring/webcam-capture`,
                method: 'POST',
                data: formData,
                upload: true
            })
        } catch (error) {
            console.error('Failed to upload webcam capture:', error)
        }
    }

    const logFaceDetectionFailure = async (reason) => {
        if (!currentSubmissionUuid.value) {
            console.error('Cannot log face detection failure: submission UUID not set')
            return
        }

        // Trigger immediate capture for suspicious activity
        await triggerSuspiciousActivityCapture('face_detection_failure')

        try {
            await Api.custom({
                url: `app/online-exams/${examUuid}/proctoring/face-detection-failure`,
                method: 'POST',
                data: {
                    submission_uuid: currentSubmissionUuid.value,
                    reason
                }
            })
        } catch (error) {
            console.error('Failed to log face detection failure:', error)
        }
    }

    const logSuspiciousActivity = async (activity, details = {}) => {
        // This would be implemented as a separate endpoint
        console.warn('Suspicious activity detected:', activity, details)
    }

    const uploadScreenRecording = async (blob, chunkNumber = null, totalChunks = null) => {
        if (!currentSubmissionUuid.value) {
            console.error('Cannot upload screen recording: submission UUID not set')
            return
        }

        // Don't upload if monitoring has been stopped
        if (!isActive.value) {
            console.log('Skipping screen recording upload - monitoring stopped')
            return
        }

        try {
            // Ensure the blob has the correct MIME type
            const videoBlob = new Blob([blob], { type: 'video/webm' })

            console.log('Uploading screen recording chunk:', {
                size: videoBlob.size,
                type: videoBlob.type,
                chunkNumber,
                totalChunks,
                submissionUuid: currentSubmissionUuid.value
            })

            const formData = new FormData()
            formData.append('submission_uuid', currentSubmissionUuid.value)
            formData.append('recording_file', videoBlob, `screen_recording_${Date.now()}.webm`)

            if (chunkNumber && totalChunks) {
                formData.append('chunk_number', chunkNumber)
                formData.append('total_chunks', totalChunks)
            }

            await Api.custom({
                url: `app/online-exams/${examUuid}/proctoring/screen-recording`,
                method: 'POST',
                data: formData,
                upload: true
            })

            console.log('Screen recording uploaded successfully:', videoBlob.size, 'bytes')
        } catch (error) {
            console.error('Failed to upload screen recording:', error)
            console.error('Error details:', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status
            })
        }
    }

    const startAudioMonitoring = () => {
        if (!audioAnalyser.value || !audioStream.value) return

        const interval = 500 // Check every 500ms for more responsive detection
        audioMonitoringInterval.value = setInterval(() => {
            analyzeAudioForConversation()
        }, interval)
    }

    const analyzeAudioForConversation = () => {
        // Get both frequency and time domain data for comprehensive analysis
        const frequencyData = new Uint8Array(audioAnalyser.value.frequencyBinCount)
        const timeData = new Uint8Array(audioAnalyser.value.fftSize)

        audioAnalyser.value.getByteFrequencyData(frequencyData)
        audioAnalyser.value.getByteTimeDomainData(timeData)

        // Perform advanced audio analysis
        const audioFeatures = performAdvancedAudioAnalysis(frequencyData, timeData)

        // Smart speech detection using multiple features
        const speechDetection = detectSpeechPatterns(audioFeatures)

        // Add to speech detection buffer with rich data
        speechDetectionBuffer.value.push({
            timestamp: Date.now(),
            level: audioFeatures.volume,
            isSpeech: speechDetection.isSpeech,
            speechConfidence: speechDetection.confidence,
            voiceActivity: speechDetection.voiceActivity,
            isNoise: speechDetection.isNoise,
            features: audioFeatures
        })

        // Keep only recent samples (last 10 seconds at 500ms intervals = 20 samples)
        const maxSamples = 20
        if (speechDetectionBuffer.value.length > maxSamples) {
            speechDetectionBuffer.value.shift()
        }

        // Analyze conversation patterns over time window
        const conversationAnalysis = analyzeConversationPatterns()

        console.log(`Smart Audio Analysis: ${audioFeatures.volume.toFixed(1)}dB, Speech: ${(speechDetection.confidence * 100).toFixed(1)}%, Conversation: ${(conversationAnalysis.conversationProbability * 100).toFixed(1)}%`)

        // Determine if this is a meaningful conversation
        if (conversationAnalysis.isConversation) {
            lastAudioActivity.value = Date.now()

            // Start conversation recording if not already recording
            if (!isRecordingConversation.value) {
                startConversationRecording()
            }

            // Clear any pending silence timeout
            if (silenceTimeout.value) {
                clearTimeout(silenceTimeout.value)
                silenceTimeout.value = null
            }
        } else if (isRecordingConversation.value) {
            // Check for silence timeout
            const silenceDuration = Date.now() - lastAudioActivity.value

            if (silenceDuration > maxSilenceDuration.value && !silenceTimeout.value) {
                // Set timeout to stop recording after sustained silence
                silenceTimeout.value = setTimeout(() => {
                    stopConversationRecording('silence_detected')
                }, maxSilenceDuration.value)
            }
        }

        // Check for maximum conversation duration
        if (isRecordingConversation.value && conversationStartTime.value) {
            const recordingDuration = Date.now() - conversationStartTime.value
            if (recordingDuration >= conversationMaxDuration.value) {
                stopConversationRecording('max_duration_reached')
            }
        }
    }

    /**
     * Advanced audio analysis using multiple acoustic features
     */
    const performAdvancedAudioAnalysis = (frequencyData, timeData) => {
        // Calculate volume (RMS)
        let sum = 0
        for (let i = 0; i < frequencyData.length; i++) {
            sum += frequencyData[i] * frequencyData[i]
        }
        const rms = Math.sqrt(sum / frequencyData.length)
        const volume = 20 * Math.log10(rms / 255)

        // Calculate spectral centroid (brightness of sound)
        let weightedSum = 0
        let magnitudeSum = 0
        for (let i = 0; i < frequencyData.length; i++) {
            const frequency = (i * audioContext.value.sampleRate) / (2 * frequencyData.length)
            weightedSum += frequency * frequencyData[i]
            magnitudeSum += frequencyData[i]
        }
        const spectralCentroidValue = magnitudeSum > 0 ? weightedSum / magnitudeSum : 0

        // Calculate zero crossing rate (indicates speech vs. noise)
        let zeroCrossings = 0
        for (let i = 1; i < timeData.length; i++) {
            if ((timeData[i] - 128) * (timeData[i - 1] - 128) < 0) {
                zeroCrossings++
            }
        }
        const zcr = zeroCrossings / timeData.length

        // Analyze frequency distribution for speech characteristics
        const speechEnergy = calculateSpeechEnergy(frequencyData)
        const noiseEnergy = calculateNoiseEnergy(frequencyData)

        // Calculate spectral rolloff (90% of energy below this frequency)
        const spectralRolloff = calculateSpectralRolloff(frequencyData)

        return {
            volume,
            spectralCentroid: spectralCentroidValue,
            zeroCrossingRate: zcr,
            speechEnergy,
            noiseEnergy,
            spectralRolloff,
            frequencyData: Array.from(frequencyData),
            timeData: Array.from(timeData)
        }
    }

    /**
     * Calculate energy in speech frequency ranges
     */
    const calculateSpeechEnergy = (frequencyData) => {
        const sampleRate = audioContext.value.sampleRate
        const binSize = sampleRate / (2 * frequencyData.length)

        let fundamentalEnergy = 0
        let formantEnergy = 0
        let consonantEnergy = 0

        for (let i = 0; i < frequencyData.length; i++) {
            const frequency = i * binSize
            const energy = frequencyData[i]

            if (frequency >= speechFrequencyRanges.fundamental.min && frequency <= speechFrequencyRanges.fundamental.max) {
                fundamentalEnergy += energy
            }
            if (frequency >= speechFrequencyRanges.formants.min && frequency <= speechFrequencyRanges.formants.max) {
                formantEnergy += energy
            }
            if (frequency >= speechFrequencyRanges.consonants.min && frequency <= speechFrequencyRanges.consonants.max) {
                consonantEnergy += energy
            }
        }

        return {
            fundamental: fundamentalEnergy,
            formants: formantEnergy,
            consonants: consonantEnergy,
            total: fundamentalEnergy + formantEnergy + consonantEnergy
        }
    }

    /**
     * Calculate energy in typical noise frequency ranges
     */
    const calculateNoiseEnergy = (frequencyData) => {
        const sampleRate = audioContext.value.sampleRate
        const binSize = sampleRate / (2 * frequencyData.length)

        let lowFreqNoise = 0  // 0-85 Hz (electrical hum, etc.)
        let highFreqNoise = 0 // 8000+ Hz (electronic noise)

        for (let i = 0; i < frequencyData.length; i++) {
            const frequency = i * binSize
            const energy = frequencyData[i]

            if (frequency < 85) {
                lowFreqNoise += energy
            }
            if (frequency > 8000) {
                highFreqNoise += energy
            }
        }

        return {
            lowFreq: lowFreqNoise,
            highFreq: highFreqNoise,
            total: lowFreqNoise + highFreqNoise
        }
    }

    /**
     * Calculate spectral rolloff (frequency below which 90% of energy is contained)
     */
    const calculateSpectralRolloff = (frequencyData) => {
        const totalEnergy = frequencyData.reduce((sum, val) => sum + val, 0)
        const threshold = totalEnergy * 0.9

        let cumulativeEnergy = 0
        for (let i = 0; i < frequencyData.length; i++) {
            cumulativeEnergy += frequencyData[i]
            if (cumulativeEnergy >= threshold) {
                const sampleRate = audioContext.value.sampleRate
                return (i * sampleRate) / (2 * frequencyData.length)
            }
        }
        return 0
    }

    /**
     * Intelligent speech pattern detection using multiple acoustic features
     */
    const detectSpeechPatterns = (audioFeatures) => {
        const { volume, spectralCentroid, zeroCrossingRate, speechEnergy, noiseEnergy, spectralRolloff } = audioFeatures

        // Basic volume threshold check
        const volumeThreshold = speechThreshold.value
        const hasVolume = volume > volumeThreshold

        if (!hasVolume) {
            return {
                isSpeech: false,
                confidence: 0,
                voiceActivity: false,
                isNoise: false,
                reason: 'below_volume_threshold'
            }
        }

        // Speech characteristics analysis
        let speechScore = 0
        let noiseScore = 0

        // 1. Spectral centroid analysis (speech typically 1000-3000 Hz)
        if (spectralCentroid >= 1000 && spectralCentroid <= 3000) {
            speechScore += 0.25
        } else if (spectralCentroid > 5000) {
            noiseScore += 0.3 // High frequency noise
        }

        // 2. Zero crossing rate analysis (speech: 0.1-0.35, noise: varies widely)
        if (zeroCrossingRate >= 0.1 && zeroCrossingRate <= 0.35) {
            speechScore += 0.25
        } else if (zeroCrossingRate > 0.5) {
            noiseScore += 0.2 // Likely electronic noise
        }

        // 3. Speech energy distribution
        const totalSpeechEnergy = speechEnergy.total
        const totalNoiseEnergy = noiseEnergy.total
        const speechToNoiseRatio = totalNoiseEnergy > 0 ? totalSpeechEnergy / totalNoiseEnergy : totalSpeechEnergy

        if (speechToNoiseRatio > 2) {
            speechScore += 0.3 // Strong speech energy
        } else if (speechToNoiseRatio < 0.5) {
            noiseScore += 0.25 // More noise than speech
        }

        // 4. Formant energy analysis (key indicator of human speech)
        if (speechEnergy.formants > speechEnergy.fundamental * 0.5) {
            speechScore += 0.2 // Good formant structure
        }

        // 5. Spectral rolloff analysis (speech typically rolls off around 4-6 kHz)
        if (spectralRolloff >= 3000 && spectralRolloff <= 7000) {
            speechScore += 0.15
        }

        // Advanced noise detection using common noise source patterns
        const commonNoiseDetection = detectCommonNoiseSources(audioFeatures)

        // Determine if this is noise (keyboard, mouse, electronic)
        const isNoise = noiseScore > 0.4 ||
                       commonNoiseDetection.isAnyNoise || (
                           spectralCentroid > 6000 && zeroCrossingRate > 0.4
                       ) || (
                           noiseEnergy.highFreq > speechEnergy.total * 2
                       )

        // Calculate final confidence
        const confidence = Math.max(0, Math.min(1, speechScore - noiseScore))
        const isSpeech = confidence > 0.4 && !isNoise
        const voiceActivity = confidence > 0.3 && !isNoise

        return {
            isSpeech,
            confidence,
            voiceActivity,
            isNoise,
            speechScore,
            noiseScore,
            features: {
                spectralCentroid,
                zeroCrossingRate,
                speechToNoiseRatio,
                spectralRolloff
            }
        }
    }

    /**
     * Analyze conversation patterns over time to detect meaningful conversations
     */
    const analyzeConversationPatterns = () => {
        if (speechDetectionBuffer.value.length < 5) {
            return {
                isConversation: false,
                conversationProbability: 0,
                reason: 'insufficient_data'
            }
        }

        const recentSamples = speechDetectionBuffer.value.slice(-10) // Last 5 seconds

        // Calculate various conversation indicators
        const speechSamples = recentSamples.filter(s => s.isSpeech)
        const voiceActivitySamples = recentSamples.filter(s => s.voiceActivity)
        const noiseSamples = recentSamples.filter(s => s.isNoise)

        // 1. Speech consistency (sustained speech over time)
        const speechConsistency = speechSamples.length / recentSamples.length

        // 2. Voice activity pattern (natural speech has pauses)
        const voiceActivityPattern = voiceActivitySamples.length / recentSamples.length

        // 3. Noise filtering (too much noise indicates non-speech)
        const noiseRatio = noiseSamples.length / recentSamples.length

        // 4. Confidence stability (real speech has consistent confidence)
        const confidences = speechSamples.map(s => s.speechConfidence)
        const avgConfidence = confidences.length > 0 ? confidences.reduce((a, b) => a + b, 0) / confidences.length : 0

        // 5. Volume variation (conversation has natural volume changes)
        const volumes = recentSamples.map(s => s.level).filter(v => v > -60)
        const volumeVariation = volumes.length > 1 ? calculateVariation(volumes) : 0

        // Calculate conversation probability
        let conversationScore = 0

        // Speech consistency should be moderate (not 100% - that's suspicious)
        if (speechConsistency >= 0.4 && speechConsistency <= 0.8) {
            conversationScore += 0.3
        }

        // Voice activity should be present but not constant
        if (voiceActivityPattern >= 0.3 && voiceActivityPattern <= 0.9) {
            conversationScore += 0.25
        }

        // Low noise ratio is good
        if (noiseRatio < 0.3) {
            conversationScore += 0.2
        }

        // Good average confidence
        if (avgConfidence > 0.5) {
            conversationScore += 0.15
        }

        // Natural volume variation
        if (volumeVariation > 2 && volumeVariation < 15) {
            conversationScore += 0.1
        }

        const conversationProbability = Math.min(1, conversationScore)
        const configuredThreshold = speechConfidenceThreshold.value
        const isConversation = conversationProbability >= configuredThreshold

        return {
            isConversation,
            conversationProbability,
            speechConsistency,
            voiceActivityPattern,
            noiseRatio,
            avgConfidence,
            volumeVariation,
            threshold: configuredThreshold
        }
    }

    /**
     * Calculate variation in a series of values
     */
    const calculateVariation = (values) => {
        if (values.length < 2) return 0

        const mean = values.reduce((a, b) => a + b, 0) / values.length
        const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
        return Math.sqrt(variance)
    }

    /**
     * Detect common non-speech sounds (keyboard, mouse, etc.)
     */
    const detectCommonNoiseSources = (audioFeatures) => {
        const { spectralCentroid, zeroCrossingRate, speechEnergy, volume, spectralRolloff } = audioFeatures

        // Keyboard typing detection
        const isKeyboardTyping = (
            spectralCentroid > 4000 &&           // High frequency clicks
            zeroCrossingRate > 0.4 &&            // Sharp transients
            spectralRolloff > 6000 &&            // Energy concentrated in high frequencies
            volume > -50 && volume < -20         // Moderate volume
        )

        // Mouse clicking detection
        const isMouseClick = (
            spectralCentroid > 5000 &&           // Very high frequency
            zeroCrossingRate > 0.5 &&            // Very sharp transients
            speechEnergy.total < 50              // Low speech-like energy
        )

        // Electronic/fan noise detection
        const isElectronicNoise = (
            spectralCentroid < 500 ||            // Very low frequency hum
            (spectralCentroid > 8000 && speechEnergy.formants < 10) // High freq without formants
        )

        // Paper rustling/movement detection
        const isPaperRustling = (
            spectralCentroid > 3000 &&           // High frequency
            zeroCrossingRate > 0.35 &&           // Irregular pattern
            speechEnergy.formants < speechEnergy.consonants // More consonant-like than speech
        )

        return {
            isKeyboardTyping,
            isMouseClick,
            isElectronicNoise,
            isPaperRustling,
            isAnyNoise: isKeyboardTyping || isMouseClick || isElectronicNoise || isPaperRustling
        }
    }

    const startConversationRecording = () => {
        if (!audioStream.value || isRecordingConversation.value) return

        try {
            console.log('Starting intelligent conversation recording...')

            isRecordingConversation.value = true
            conversationStartTime.value = Date.now()

            // Create a new recorder for conversation
            conversationRecorder.value = new RecordRTC(audioStream.value, {
                type: 'audio',
                mimeType: 'audio/wav',
                recorderType: RecordRTC.StereoAudioRecorder,
                numberOfAudioChannels: 1,
                desiredSampRate: 16000
            })

            conversationRecorder.value.startRecording()

        } catch (error) {
            console.error('Failed to start conversation recording:', error)
            isRecordingConversation.value = false
            conversationStartTime.value = null
        }
    }

    const stopConversationRecording = async (reason = 'manual') => {
        if (!isRecordingConversation.value || !conversationRecorder.value) return

        try {
            const recordingDuration = Date.now() - conversationStartTime.value

            console.log(`Stopping conversation recording. Duration: ${recordingDuration}ms, Reason: ${reason}`)

            // Only save if recording meets minimum duration
            if (recordingDuration >= conversationMinDuration.value) {
                conversationRecorder.value.stopRecording(async () => {
                    const blob = conversationRecorder.value.getBlob()
                    const audioBlob = new Blob([blob], { type: 'audio/wav' })

                    console.log(`Conversation recording completed: ${audioBlob.size} bytes, duration: ${recordingDuration}ms`)

                    // Log as conversation instead of simple audio alert
                    await logConversationRecording(audioBlob, recordingDuration, reason)

                    // Clean up recorder
                    conversationRecorder.value.destroy()
                    conversationRecorder.value = null
                })
            } else {
                console.log(`Conversation too short (${recordingDuration}ms), discarding...`)
                conversationRecorder.value.stopRecording(() => {
                    conversationRecorder.value.destroy()
                    conversationRecorder.value = null
                })
            }

        } catch (error) {
            console.error('Failed to stop conversation recording:', error)
        } finally {
            isRecordingConversation.value = false
            conversationStartTime.value = null

            // Clear silence timeout
            if (silenceTimeout.value) {
                clearTimeout(silenceTimeout.value)
                silenceTimeout.value = null
            }
        }
    }

    const logConversationRecording = async (audioBlob, duration, reason) => {
        if (!currentSubmissionUuid.value) {
            console.error('Cannot log conversation: submission UUID not set')
            return
        }

        // Don't log if monitoring has been stopped
        if (!isActive.value) {
            console.log('Skipping conversation logging - monitoring stopped')
            return
        }

        try {
            // Calculate conversation analysis summary
            const recentSamples = speechDetectionBuffer.value.slice(-10)
            const speechSamples = recentSamples.filter(s => s.isSpeech)
            const avgConfidence = speechSamples.length > 0 ?
                speechSamples.reduce((sum, s) => sum + s.speechConfidence, 0) / speechSamples.length : 0

            const formData = new FormData()
            formData.append('submission_uuid', currentSubmissionUuid.value)
            formData.append('audio_file', audioBlob, `conversation_${Date.now()}.wav`)
            formData.append('duration_ms', duration)
            formData.append('detection_reason', reason)
            formData.append('event_type', 'intelligent_conversation_detected')
            formData.append('speech_confidence', (avgConfidence * 100).toFixed(1))
            formData.append('analysis_method', 'smart_audio_detection')

            await Api.custom({
                url: `app/online-exams/${examUuid}/proctoring/audio-alert`,
                method: 'POST',
                data: formData,
                upload: true
            })

            eventCounts.audioAlerts++
            console.log(`Intelligent conversation logged: ${duration}ms, confidence: ${(avgConfidence * 100).toFixed(1)}%`)
        } catch (error) {
            console.error('Failed to log conversation recording:', error)
        }
    }

    const captureAudioClip = async (audioLevel) => {
        if (!audioStream.value) return

        try {
            // Get configurable recording duration (default to 15 seconds if not set)
            const recordingDuration = (proctorConfig.audioRecordingDurationSeconds || 15) * 1000

            // Create a new recorder for each audio clip
            const recorder = new RecordRTC(audioStream.value, {
                type: 'audio',
                mimeType: 'audio/wav',
                recorderType: RecordRTC.StereoAudioRecorder,
                numberOfAudioChannels: 1,
                desiredSampRate: 16000,
                timeSlice: recordingDuration
            })

            console.log(`Starting audio recording for ${recordingDuration/1000} seconds`)

            // Start recording
            recorder.startRecording()

            setTimeout(async () => {
                recorder.stopRecording(async () => {
                    const blob = recorder.getBlob()

                    // Ensure the blob has the correct type
                    const audioBlob = new Blob([blob], { type: 'audio/wav' })

                    console.log(`Audio recording completed: ${audioBlob.size} bytes, duration: ${recordingDuration/1000}s`)
                    await logAudioAlert(audioLevel, audioBlob)

                    // Clean up recorder
                    recorder.destroy()
                })
            }, recordingDuration)

        } catch (error) {
            console.error('Failed to capture audio clip:', error)
            // Fallback to just logging the level without audio file
            await logAudioAlert(audioLevel, null)
        }
    }

    const logAudioAlert = async (audioLevel, audioBlob = null) => {
        if (!currentSubmissionUuid.value) {
            console.error('Cannot log audio alert: submission UUID not set')
            return
        }

        // Don't log if monitoring has been stopped
        if (!isActive.value) {
            console.log('Skipping audio alert logging - monitoring stopped')
            return
        }

        try {
            const formData = new FormData()
            formData.append('submission_uuid', currentSubmissionUuid.value)
            formData.append('audio_level', audioLevel)

            if (audioBlob) {
                formData.append('audio_file', audioBlob, 'audio_alert.wav')
            }

            await Api.custom({
                url: `app/online-exams/${examUuid}/proctoring/audio-alert`,
                method: 'POST',
                data: formData,
                upload: true
            })
        } catch (error) {
            console.error('Failed to log audio alert:', error)
        }
    }

    const startFaceDetection = () => {
        // Face detection is handled in webcam capture
        // This function is called but the actual detection happens in captureWebcamImage
    }

    const enforceFullscreen = () => {
        if (document.documentElement.requestFullscreen) {
            document.documentElement.requestFullscreen()
        }

        // Listen for fullscreen changes
        fullscreenChangeHandler = () => {
            if (!document.fullscreenElement) {
                logFullscreenExit()
                eventCounts.fullscreenExits++
            }
        }
        document.addEventListener('fullscreenchange', fullscreenChangeHandler)
    }

    const logFullscreenExit = async () => {
        if (!currentSubmissionUuid.value) {
            console.error('Cannot log fullscreen exit: submission UUID not set')
            return
        }

        // Trigger immediate capture for suspicious activity
        await triggerSuspiciousActivityCapture('fullscreen_exit')

        try {
            await Api.custom({
                url: `app/online-exams/${examUuid}/proctoring/fullscreen-exit`,
                method: 'POST',
                data: {
                    submission_uuid: currentSubmissionUuid.value
                }
            })
        } catch (error) {
            console.error('Failed to log fullscreen exit:', error)
        }
    }

    const blockCopyPaste = () => {
        copyHandler = (e) => {
            e.preventDefault()
            logCopyPasteAttempt('copy')
            eventCounts.copyPasteAttempts++
        }

        pasteHandler = (e) => {
            e.preventDefault()
            logCopyPasteAttempt('paste')
            eventCounts.copyPasteAttempts++
        }

        cutHandler = (e) => {
            e.preventDefault()
            logCopyPasteAttempt('cut')
            eventCounts.copyPasteAttempts++
        }

        document.addEventListener('copy', copyHandler)
        document.addEventListener('paste', pasteHandler)
        document.addEventListener('cut', cutHandler)
    }

    const logCopyPasteAttempt = async (action) => {
        if (!currentSubmissionUuid.value) {
            console.error('Cannot log copy/paste attempt: submission UUID not set')
            return
        }

        // Trigger immediate capture for suspicious activity
        await triggerSuspiciousActivityCapture(`copy_paste_${action}`)

        try {
            await Api.custom({
                url: `app/online-exams/${examUuid}/proctoring/copy-paste-attempt`,
                method: 'POST',
                data: {
                    submission_uuid: currentSubmissionUuid.value,
                    action: action
                }
            })
        } catch (error) {
            console.error('Failed to log copy/paste attempt:', error)
        }
    }

    // Store event listener functions for proper cleanup
    let visibilityChangeHandler = null
    let fullscreenChangeHandler = null
    let copyHandler = null
    let pasteHandler = null
    let cutHandler = null

    const setupEventListeners = () => {
        // Tab switch detection
        visibilityChangeHandler = () => {
            if (document.hidden) {
                logTabSwitch()
                eventCounts.tabSwitches++
            }
        }
        document.addEventListener('visibilitychange', visibilityChangeHandler)
    }

    const logTabSwitch = async () => {
        if (!currentSubmissionUuid.value) {
            console.error('Cannot log tab switch: submission UUID not set')
            return
        }

        // Don't log if monitoring has been stopped
        if (!isActive.value) {
            console.log('Skipping tab switch logging - monitoring stopped')
            return
        }

        // Trigger immediate capture for suspicious activity
        await triggerSuspiciousActivityCapture('tab_switch')

        try {
            await Api.custom({
                url: `app/online-exams/${examUuid}/proctoring/tab-switch`,
                method: 'POST',
                data: {
                    submission_uuid: currentSubmissionUuid.value
                }
            })
        } catch (error) {
            console.error('Failed to log tab switch:', error)
        }
    }

    const removeEventListeners = () => {
        // Remove all event listeners when stopping monitoring
        if (visibilityChangeHandler) {
            document.removeEventListener('visibilitychange', visibilityChangeHandler)
        }
        if (fullscreenChangeHandler) {
            document.removeEventListener('fullscreenchange', fullscreenChangeHandler)
        }
        if (copyHandler) {
            document.removeEventListener('copy', copyHandler)
        }
        if (pasteHandler) {
            document.removeEventListener('paste', pasteHandler)
        }
        if (cutHandler) {
            document.removeEventListener('cut', cutHandler)
        }
    }

    /**
     * Update submission UUID after initialization
     */
    const setSubmissionUuid = (newSubmissionUuid) => {
        currentSubmissionUuid.value = newSubmissionUuid
    }

    return {
        // State
        isInitialized,
        isActive,
        errors,
        eventCounts,

        // Methods
        initialize,
        startMonitoring,
        stopMonitoring,
        setSubmissionUuid,

        // Media streams (for external access if needed)
        videoStream,
        audioStream
    }
}
