import{u as P,l as E,r as l,q as f,o as p,w as e,d as C,e as n,h as S,j as T,y as _,m as j,f as o,a as L,F as M,v as N,s as r,t as d,b as w}from"./app-DvIo72ZO.js";import{_ as O}from"./ModuleDropdown-DaP494Fu.js";const U={class:"grid grid-cols-3 gap-6"},q={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},G={__name:"Filter",emits:["hide"],setup(B,{emit:m}){P();const k=m,y={name:"",code:""},u=E({...y});return(h,i)=>{const c=l("BaseInput"),b=l("FilterForm");return p(),f(b,{"init-form":y,form:u,multiple:[],onHide:i[2]||(i[2]=t=>k("hide"))},{default:e(()=>[C("div",U,[C("div",q,[n(c,{type:"text",modelValue:u.name,"onUpdate:modelValue":i[0]||(i[0]=t=>u.name=t),name:"name",label:h.$trans("employee.attendance.work_shift.props.name")},null,8,["modelValue","label"])]),C("div",z,[n(c,{type:"text",modelValue:u.code,"onUpdate:modelValue":i[1]||(i[1]=t=>u.code=t),name:"code",label:h.$trans("employee.attendance.work_shift.props.code")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},J={name:"EmployeeAttendanceWorkShiftList"},X=Object.assign(J,{setup(B){const m=S(),k=T("emitter");let y=["filter"];_("work-shift:create")&&y.unshift("create");let u=[];_("work-shift:export")&&(u=["print","pdf","excel"]);const h="employee/attendance/workShift/",i=j(!1),c=E({}),b=t=>{Object.assign(c,t)};return(t,s)=>{const A=l("BaseButton"),I=l("PageHeaderAction"),D=l("PageHeader"),F=l("ParentTransition"),$=l("DataCell"),v=l("FloatingMenuItem"),V=l("FloatingMenu"),W=l("DataRow"),R=l("DataTable"),H=l("ListItem");return p(),f(H,{"init-url":h,onSetItems:b},{header:e(()=>[n(D,{title:t.$trans("employee.attendance.work_shift.work_shift"),navs:[{label:t.$trans("employee.employee"),path:"Employee"},{label:t.$trans("employee.attendance.attendance"),path:"EmployeeAttendance"}]},{default:e(()=>[n(I,{url:"employee/attendance/work-shifts/",name:"EmployeeAttendanceWorkShift",title:t.$trans("employee.attendance.work_shift.work_shift"),actions:o(y),"dropdown-actions":o(u),onToggleFilter:s[1]||(s[1]=a=>i.value=!i.value)},{moduleOption:e(()=>[n(O)]),default:e(()=>[n(A,{design:"white",onClick:s[0]||(s[0]=a=>o(m).push({name:"EmployeeAttendanceWorkShiftReport"}))},{default:e(()=>[r(d(t.$trans("global.report",{attribute:t.$trans("employee.attendance.work_shift.work_shift")})),1)]),_:1})]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[n(F,{appear:"",visibility:i.value},{default:e(()=>[n(G,{onRefresh:s[2]||(s[2]=a=>o(k).emit("listItems")),onHide:s[3]||(s[3]=a=>i.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[n(F,{appear:"",visibility:!0},{default:e(()=>[n(R,{header:c.headers,meta:c.meta,module:"employee.attendance.work_shift",onRefresh:s[5]||(s[5]=a=>o(k).emit("listItems"))},{actionButton:e(()=>[o(_)("work-shift:create")?(p(),f(A,{key:0,onClick:s[4]||(s[4]=a=>o(m).push({name:"EmployeeAttendanceWorkShiftCreate"}))},{default:e(()=>[r(d(t.$trans("global.add",{attribute:t.$trans("employee.attendance.work_shift.work_shift")})),1)]),_:1})):w("",!0)]),default:e(()=>[(p(!0),L(M,null,N(c.data,a=>(p(),f(W,{key:a.uuid,onDoubleClick:g=>o(m).push({name:"EmployeeAttendanceWorkShiftShow",params:{uuid:a.uuid}})},{default:e(()=>[n($,{name:"name"},{default:e(()=>[r(d(a.name),1)]),_:2},1024),n($,{name:"code"},{default:e(()=>[r(d(a.code),1)]),_:2},1024),n($,{name:"createdAt"},{default:e(()=>[r(d(a.createdAt.formatted),1)]),_:2},1024),n($,{name:"action"},{default:e(()=>[n(V,null,{default:e(()=>[n(v,{icon:"fas fa-arrow-circle-right",onClick:g=>o(m).push({name:"EmployeeAttendanceWorkShiftShow",params:{uuid:a.uuid}})},{default:e(()=>[r(d(t.$trans("general.show")),1)]),_:2},1032,["onClick"]),o(_)("work-shift:edit")?(p(),f(v,{key:0,icon:"fas fa-edit",onClick:g=>o(m).push({name:"EmployeeAttendanceWorkShiftEdit",params:{uuid:a.uuid}})},{default:e(()=>[r(d(t.$trans("general.edit")),1)]),_:2},1032,["onClick"])):w("",!0),o(_)("work-shift:create")?(p(),f(v,{key:1,icon:"fas fa-copy",onClick:g=>o(m).push({name:"EmployeeAttendanceWorkShiftDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[r(d(t.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):w("",!0),o(_)("work-shift:delete")?(p(),f(v,{key:2,icon:"fas fa-trash",onClick:g=>o(k).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[r(d(t.$trans("general.delete")),1)]),_:2},1032,["onClick"])):w("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{X as default};
