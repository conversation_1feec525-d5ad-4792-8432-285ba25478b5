import{I as B,l as g,r as c,q as F,o as _,w as u,d,e as n,f as i,K as P,u as V,a as D,F as h}from"./app-DvIo72ZO.js";const j={class:"grid grid-cols-3 gap-6"},q={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},S={name:"AcademicDivisionForm"},U=Object.assign(S,{setup(v){const t={name:"",code:"",shortcode:"",program:"",position:"",pgAccount:"",description:""},o="academic/division/",m=B(o),l=g({programs:[]}),a=g({...t}),p=r=>{Object.assign(l,r)},f=r=>{var e;Object.assign(t,{...r,program:((e=r.program)==null?void 0:e.uuid)||""}),Object.assign(a,P(t))};return(r,e)=>{const b=c("BaseInput"),A=c("BaseSelect"),$=c("FormAction");return _(),F($,{"has-setup-wizard":!0,"pre-requisites":!0,onSetPreRequisites:p,"init-url":o,"init-form":t,form:a,"set-form":f,redirect:"AcademicDivision"},{default:u(()=>[d("div",j,[d("div",q,[n(b,{type:"text",modelValue:a.name,"onUpdate:modelValue":e[0]||(e[0]=s=>a.name=s),name:"name",label:r.$trans("academic.division.props.name"),error:i(m).name,"onUpdate:error":e[1]||(e[1]=s=>i(m).name=s),placeholder:"Junior Secondary School",autofocus:""},null,8,["modelValue","label","error"])]),d("div",O,[n(A,{modelValue:a.program,"onUpdate:modelValue":e[2]||(e[2]=s=>a.program=s),name:"program",label:r.$trans("academic.program.program"),"label-prop":"nameWithDepartment","value-prop":"uuid",options:l.programs,error:i(m).program,"onUpdate:error":e[3]||(e[3]=s=>i(m).program=s)},null,8,["modelValue","label","options","error"])])])]),_:1},8,["form"])}}}),H={name:"AcademicDivisionAction"},k=Object.assign(H,{setup(v){const t=V();return(o,m)=>{const l=c("PageHeaderAction"),a=c("PageHeader"),p=c("ParentTransition");return _(),D(h,null,[n(a,{title:o.$trans(i(t).meta.trans,{attribute:o.$trans(i(t).meta.label)}),navs:[{label:o.$trans("academic.academic"),path:"Academic"},{label:o.$trans("academic.division.division"),path:"AcademicDivisionList"}]},{default:u(()=>[n(l,{name:"AcademicDivision",title:o.$trans("academic.division.division"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(p,{appear:"",visibility:!0},{default:u(()=>[n(U)]),_:1})],64)}}});export{k as default};
