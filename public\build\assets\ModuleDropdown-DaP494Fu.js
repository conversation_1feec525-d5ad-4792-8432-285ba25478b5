import{u as c,h as f,r as k,a as E,o as n,q as m,b as p,f as e,y as l,w as d,s as u,t as i,F as h}from"./app-DvIo72ZO.js";const A={name:"EmployeeAttendanceModuleDropdown"},C=Object.assign(A,{setup(w){const a=c(),o=f();return(s,t)=>{const r=k("DropdownItem");return n(),E(h,null,[e(a).name!="EmployeeAttendanceTypeList"&&e(l)("attendance:config")?(n(),m(r,{key:0,onClick:t[0]||(t[0]=y=>e(o).push({name:"EmployeeAttendanceType"}))},{default:d(()=>[u(i(s.$trans("employee.attendance.type.type")),1)]),_:1})):p("",!0),e(a).name!="EmployeeAttendanceTimesheetList"&&e(l)("timesheet:read")?(n(),m(r,{key:1,onClick:t[1]||(t[1]=y=>e(o).push({name:"EmployeeAttendanceTimesheet"}))},{default:d(()=>[u(i(s.$trans("employee.attendance.timesheet.timesheet")),1)]),_:1})):p("",!0),e(a).name!="EmployeeAttendanceWorkShiftList"&&e(l)("work-shift:read")?(n(),m(r,{key:2,onClick:t[2]||(t[2]=y=>e(o).push({name:"EmployeeAttendanceWorkShift"}))},{default:d(()=>[u(i(s.$trans("employee.attendance.work_shift.work_shift")),1)]),_:1})):p("",!0),e(a).name!="EmployeeAttendanceList"&&e(l)("attendance:read")?(n(),m(r,{key:3,onClick:t[3]||(t[3]=y=>e(o).push({name:"EmployeeAttendance"}))},{default:d(()=>[u(i(s.$trans("employee.attendance.attendance")),1)]),_:1})):p("",!0)],64)}}});export{C as _};
