import{u as C,j as B,l as w,I as D,n as R,r as n,q as F,o as $,w as i,d as b,e as r,b as U,s as S,t as _,f as d,i as q,m as k,a as H,F as P,aS as T}from"./app-DvIo72ZO.js";const A={class:"grid grid-cols-3 gap-6"},L={class:"col-span-3 sm:col-span-1"},N={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3 sm:col-span-1"},j={__name:"Filter",props:{initUrl:{type:String,default:""}},emits:["hide"],setup(V,{emit:c}){const u=C();B("moment");const f=c,h=V,l={date:"",batches:[],status:"all"},o=w({...l}),p=D(h.initUrl),s=w({isLoaded:!u.query.batches});return R(async()=>{s.batches=u.query.batches?u.query.batches.split(","):[],s.isLoaded=!0}),(e,a)=>{const v=n("DatePicker"),m=n("BaseSelectSearch"),g=n("CustomCheckbox"),y=n("FilterForm");return $(),F(y,{"init-form":l,multiple:["batches"],form:o,onHide:a[4]||(a[4]=t=>f("hide"))},{default:i(()=>[b("div",A,[b("div",L,[r(v,{modelValue:o.date,"onUpdate:modelValue":a[0]||(a[0]=t=>o.date=t),name:"date",as:"date",label:e.$trans("general.date")},null,8,["modelValue","label"])]),b("div",N,[s.isLoaded?($(),F(m,{key:0,multiple:"",name:"batches",label:e.$trans("global.select",{attribute:e.$trans("academic.batch.batch")}),modelValue:o.batches,"onUpdate:modelValue":a[1]||(a[1]=t=>o.batches=t),"value-prop":"uuid","init-search":s.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:i(t=>[S(_(t.value.course.nameWithTerm)+" "+_(t.value.name),1)]),listOption:i(t=>[S(_(t.option.course.nameWithTerm)+" "+_(t.option.name),1)]),_:1},8,["label","modelValue","init-search"])):U("",!0)]),b("div",W,[r(g,{label:e.$trans("resource.status"),options:[{label:e.$trans("general.all"),value:"all"},{label:e.$trans("resource.submitted"),value:"submitted"},{label:e.$trans("resource.not_submitted"),value:"not_submitted"}],modelValue:o.status,"onUpdate:modelValue":a[2]||(a[2]=t=>o.status=t),error:d(p).status,"onUpdate:error":a[3]||(a[3]=t=>d(p).status=t)},null,8,["label","options","modelValue","error"])])])]),_:1},8,["form"])}}},E={name:"ResourceReportDateWiseStudentDiary"},I=Object.assign(E,{setup(V){const c=C();q();let u=["filter"],f=[];const h="resource/report/",l=k(!0),o=k(!1),p=async()=>{let s="/app/resource/reports/date-wise-student-diary/export",e=c.query;window.open(T(s,e),"_blank").focus()};return R(async()=>{}),(s,e)=>{const a=n("PageHeaderAction"),v=n("PageHeader"),m=n("ParentTransition"),g=n("BaseCard");return $(),H(P,null,[r(v,{title:s.$trans(d(c).meta.label),navs:[{label:s.$trans("resource.resource"),path:"Resource"},{label:s.$trans("resource.report.report"),path:"ResourceReport"}]},{default:i(()=>[r(a,{url:"resource/reports/date-wise-student-diary/",name:"ResourceReportDateWiseStudentDiary",title:s.$trans("resource.report.date_wise_student_diary.date_wise_student_diary"),actions:d(u),"dropdown-actions":d(f),onToggleFilter:e[0]||(e[0]=y=>l.value=!0)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"]),r(m,{appear:"",visibility:l.value},{default:i(()=>[r(j,{onAfterFilter:p,"init-url":h,onHide:e[1]||(e[1]=y=>l.value=!1)})]),_:1},8,["visibility"]),r(m,{appear:"",visibility:!0},{default:i(()=>[r(g,{"no-padding":"","no-content-padding":"","is-loading":o.value},null,8,["is-loading"])]),_:1})],64)}}});export{I as default};
