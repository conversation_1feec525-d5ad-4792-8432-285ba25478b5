import{u as q,H as U,I as F,l as b,r as u,q as P,o as g,w as y,d as r,e as n,f as o,K as H,a as B,F as j}from"./app-DvIo72ZO.js";const O={class:"grid grid-cols-3 gap-6"},A={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},S={class:"col-span-3 sm:col-span-1"},I={class:"col-span-2 sm:col-span-1"},M={class:"col-span-2 sm:col-span-1"},w={class:"col-span-3 sm:col-span-1"},L={class:"grid grid-cols-1"},C={class:"col"},N={name:"EmployeeQualificationForm"},K=Object.assign(N,{setup(p){const d=q(),i={level:"",course:"",institute:"",affiliatedTo:"",startDate:"",endDate:"",result:"",media:[],mediaUpdated:!1,mediaToken:U(),mediaHash:[]},v="employee/qualification/",l=F(v),c=b({levels:[]}),a=b({...i}),V=b({level:"",isLoaded:!d.params.muuid}),$=s=>{Object.assign(c,s)},_=()=>{a.mediaToken=U(),a.mediaHash=[]},T=s=>{var e,f,m;Object.assign(i,{...s,level:(e=s.level)==null?void 0:e.uuid,startDate:((f=s.startDate)==null?void 0:f.value)||"",endDate:((m=s.endDate)==null?void 0:m.value)||""}),Object.assign(a,H(i)),V.level=s.level.name,V.isLoaded=!0};return(s,e)=>{const f=u("BaseSelect"),m=u("BaseInput"),D=u("DatePicker"),k=u("MediaUpload"),E=u("FormAction");return g(),P(E,{"no-data-fetch":"","pre-requisites":!0,onSetPreRequisites:$,"init-url":v,uuid:o(d).params.uuid,"module-uuid":o(d).params.muuid,"init-form":i,form:a,"set-form":T,redirect:{name:"EmployeeQualification",params:{uuid:o(d).params.uuid}},onResetMediaFiles:_},{default:y(()=>[r("div",O,[r("div",A,[n(f,{modelValue:a.level,"onUpdate:modelValue":e[0]||(e[0]=t=>a.level=t),name:"level",label:s.$trans("employee.qualification_level.qualification_level"),"label-prop":"name","value-prop":"uuid",options:c.levels,error:o(l).level,"onUpdate:error":e[1]||(e[1]=t=>o(l).level=t)},null,8,["modelValue","label","options","error"])]),r("div",Q,[n(m,{type:"text",modelValue:a.course,"onUpdate:modelValue":e[2]||(e[2]=t=>a.course=t),name:"course",label:s.$trans("employee.qualification.props.course"),error:o(l).course,"onUpdate:error":e[3]||(e[3]=t=>o(l).course=t),autofocus:""},null,8,["modelValue","label","error"])]),r("div",R,[n(m,{type:"text",modelValue:a.institute,"onUpdate:modelValue":e[4]||(e[4]=t=>a.institute=t),name:"institute",label:s.$trans("employee.qualification.props.institute"),error:o(l).institute,"onUpdate:error":e[5]||(e[5]=t=>o(l).institute=t),autofocus:""},null,8,["modelValue","label","error"])]),r("div",S,[n(m,{type:"text",modelValue:a.affiliatedTo,"onUpdate:modelValue":e[6]||(e[6]=t=>a.affiliatedTo=t),name:"affiliatedTo",label:s.$trans("employee.qualification.props.affiliated_to"),error:o(l).affiliatedTo,"onUpdate:error":e[7]||(e[7]=t=>o(l).affiliatedTo=t),autofocus:""},null,8,["modelValue","label","error"])]),r("div",I,[n(D,{modelValue:a.startDate,"onUpdate:modelValue":e[8]||(e[8]=t=>a.startDate=t),name:"startDate",label:s.$trans("employee.qualification.props.start_date"),error:o(l).startDate,"onUpdate:error":e[9]||(e[9]=t=>o(l).startDate=t)},null,8,["modelValue","label","error"])]),r("div",M,[n(D,{modelValue:a.endDate,"onUpdate:modelValue":e[10]||(e[10]=t=>a.endDate=t),name:"endDate",label:s.$trans("employee.qualification.props.end_date"),error:o(l).endDate,"onUpdate:error":e[11]||(e[11]=t=>o(l).endDate=t)},null,8,["modelValue","label","error"])]),r("div",w,[n(m,{type:"text",modelValue:a.result,"onUpdate:modelValue":e[12]||(e[12]=t=>a.result=t),name:"result",label:s.$trans("employee.qualification.props.result"),error:o(l).result,"onUpdate:error":e[13]||(e[13]=t=>o(l).result=t),autofocus:""},null,8,["modelValue","label","error"])])]),r("div",L,[r("div",C,[n(k,{multiple:"",label:s.$trans("general.file"),module:"qualification",media:a.media,"media-token":a.mediaToken,onIsUpdated:e[14]||(e[14]=t=>a.mediaUpdated=!0),onSetHash:e[15]||(e[15]=t=>a.mediaHash.push(t))},null,8,["label","media","media-token"])])])]),_:1},8,["uuid","module-uuid","form","redirect"])}}}),z={name:"EmployeeQualificationAction"},J=Object.assign(z,{props:{employee:{type:Object,default(){return{}}}},setup(p){const d=q();return(i,v)=>{const l=u("PageHeaderAction"),c=u("PageHeader"),a=u("ParentTransition");return g(),B(j,null,[n(c,{title:i.$trans(o(d).meta.trans,{attribute:i.$trans(o(d).meta.label)}),navs:[{label:i.$trans("employee.employee"),path:"EmployeeList"},{label:p.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:p.employee.uuid}}},{label:i.$trans("employee.qualification.qualification"),path:{name:"EmployeeQualification",params:{uuid:p.employee.uuid}}}]},{default:y(()=>[n(l,{name:"EmployeeQualification",title:i.$trans("employee.qualification.qualification"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(a,{appear:"",visibility:!0},{default:y(()=>[n(K)]),_:1})],64)}}});export{J as default};
