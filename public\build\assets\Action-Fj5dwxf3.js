import{u as D,H as v,I as k,l as f,r as p,q as B,o as T,w as V,d as s,e as r,f as a,K as F,a as H,F as N}from"./app-DvIo72ZO.js";const z={class:"grid grid-cols-3 gap-6"},O={class:"col-span-3 sm:col-span-1"},q={class:"col-span-3 sm:col-span-2"},A={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},S={class:"col-span-3 sm:col-span-1"},I={class:"col-span-2 sm:col-span-1"},M={class:"col-span-2 sm:col-span-1"},w={class:"col-span-3"},L={class:"grid grid-cols-1"},C={class:"col"},K={name:"EmployeeExperienceForm"},G=Object.assign(K,{setup(u){const m=D(),i={employmentType:"",headline:"",title:"",organizationName:"",location:"",startDate:"",endDate:"",jobProfile:"",media:[],mediaUpdated:!1,mediaToken:v(),mediaHash:[]},b="employee/experience/",l=k(b),y=f({employmentTypes:[]}),t=f({...i}),g=f({employmentType:"",isLoaded:!m.params.muuid}),$=n=>{Object.assign(y,n)},P=()=>{t.mediaToken=v(),t.mediaHash=[]},E=n=>{var e,c,d;Object.assign(i,{...n,employmentType:(e=n.employmentType)==null?void 0:e.uuid,startDate:((c=n.startDate)==null?void 0:c.value)||"",endDate:((d=n.endDate)==null?void 0:d.value)||""}),Object.assign(t,F(i)),g.employmentType=n.employmentType.name,g.isLoaded=!0};return(n,e)=>{const c=p("BaseSelect"),d=p("BaseInput"),U=p("DatePicker"),x=p("BaseTextarea"),j=p("MediaUpload"),_=p("FormAction");return T(),B(_,{"no-data-fetch":"","pre-requisites":!0,onSetPreRequisites:$,"init-url":b,uuid:a(m).params.uuid,"module-uuid":a(m).params.muuid,"init-form":i,form:t,"set-form":E,redirect:{name:"EmployeeExperience",params:{uuid:a(m).params.uuid}},onResetMediaFiles:P},{default:V(()=>[s("div",z,[s("div",O,[r(c,{modelValue:t.employmentType,"onUpdate:modelValue":e[0]||(e[0]=o=>t.employmentType=o),name:"employmentType",label:n.$trans("employee.employment_type.employment_type"),"label-prop":"name","value-prop":"uuid",options:y.employmentTypes,error:a(l).employmentType,"onUpdate:error":e[1]||(e[1]=o=>a(l).employmentType=o)},null,8,["modelValue","label","options","error"])]),s("div",q,[r(d,{type:"text",modelValue:t.headline,"onUpdate:modelValue":e[2]||(e[2]=o=>t.headline=o),name:"headline",label:n.$trans("employee.experience.props.headline"),error:a(l).headline,"onUpdate:error":e[3]||(e[3]=o=>a(l).headline=o),autofocus:""},null,8,["modelValue","label","error"])]),s("div",A,[r(d,{type:"text",modelValue:t.title,"onUpdate:modelValue":e[4]||(e[4]=o=>t.title=o),name:"title",label:n.$trans("employee.experience.props.title"),error:a(l).title,"onUpdate:error":e[5]||(e[5]=o=>a(l).title=o),autofocus:""},null,8,["modelValue","label","error"])]),s("div",R,[r(d,{type:"text",modelValue:t.organizationName,"onUpdate:modelValue":e[6]||(e[6]=o=>t.organizationName=o),name:"organizationName",label:n.$trans("employee.experience.props.organization_name"),error:a(l).organizationName,"onUpdate:error":e[7]||(e[7]=o=>a(l).organizationName=o)},null,8,["modelValue","label","error"])]),s("div",S,[r(d,{type:"text",modelValue:t.location,"onUpdate:modelValue":e[8]||(e[8]=o=>t.location=o),name:"location",label:n.$trans("employee.experience.props.location"),error:a(l).location,"onUpdate:error":e[9]||(e[9]=o=>a(l).location=o)},null,8,["modelValue","label","error"])]),s("div",I,[r(U,{modelValue:t.startDate,"onUpdate:modelValue":e[10]||(e[10]=o=>t.startDate=o),name:"startDate",label:n.$trans("employee.experience.props.start_date"),error:a(l).startDate,"onUpdate:error":e[11]||(e[11]=o=>a(l).startDate=o)},null,8,["modelValue","label","error"])]),s("div",M,[r(U,{modelValue:t.endDate,"onUpdate:modelValue":e[12]||(e[12]=o=>t.endDate=o),name:"endDate",label:n.$trans("employee.experience.props.end_date"),error:a(l).endDate,"onUpdate:error":e[13]||(e[13]=o=>a(l).endDate=o)},null,8,["modelValue","label","error"])]),s("div",w,[r(x,{modelValue:t.jobProfile,"onUpdate:modelValue":e[14]||(e[14]=o=>t.jobProfile=o),name:"jobProfile",label:n.$trans("employee.experience.props.job_profile"),error:a(l).jobProfile,"onUpdate:error":e[15]||(e[15]=o=>a(l).jobProfile=o)},null,8,["modelValue","label","error"])])]),s("div",L,[s("div",C,[r(j,{multiple:"",label:n.$trans("general.file"),module:"experience",media:t.media,"media-token":t.mediaToken,onIsUpdated:e[16]||(e[16]=o=>t.mediaUpdated=!0),onSetHash:e[17]||(e[17]=o=>t.mediaHash.push(o))},null,8,["label","media","media-token"])])])]),_:1},8,["uuid","module-uuid","form","redirect"])}}}),J={name:"EmployeeExperienceAction"},W=Object.assign(J,{props:{employee:{type:Object,default(){return{}}}},setup(u){const m=D();return(i,b)=>{const l=p("PageHeaderAction"),y=p("PageHeader"),t=p("ParentTransition");return T(),H(N,null,[r(y,{title:i.$trans(a(m).meta.trans,{attribute:i.$trans(a(m).meta.label)}),navs:[{label:i.$trans("employee.employee"),path:"EmployeeList"},{label:u.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:u.employee.uuid}}},{label:i.$trans("employee.experience.experience"),path:{name:"EmployeeExperience",params:{uuid:u.employee.uuid}}}]},{default:V(()=>[r(l,{name:"EmployeeExperience",title:i.$trans("employee.experience.experience"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),r(t,{appear:"",visibility:!0},{default:V(()=>[r(G)]),_:1})],64)}}});export{W as default};
