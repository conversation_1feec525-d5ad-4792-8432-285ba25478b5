import{u as N,l as S,n as V,r as o,q as g,o as l,w as e,d as y,e as n,h as U,j as q,y as D,m as E,f as r,a as w,F as h,v as M,s as p,t as m,b as B}from"./app-DvIo72ZO.js";const O={class:"grid grid-cols-3 gap-6"},z={class:"col-span-3 sm:col-span-1"},G={__name:"Filter",emits:["hide"],setup(I,{emit:c}){N();const v=c,f={startDate:"",endDate:""},u=S({...f}),b=S({isLoaded:!0});return V(async()=>{b.isLoaded=!0}),(d,s)=>{const $=o("DatePicker"),i=o("FilterForm");return l(),g(i,{"init-form":f,form:u,multiple:[],onHide:s[2]||(s[2]=t=>v("hide"))},{default:e(()=>[y("div",O,[y("div",z,[n($,{start:u.startDate,"onUpdate:start":s[0]||(s[0]=t=>u.startDate=t),end:u.endDate,"onUpdate:end":s[1]||(s[1]=t=>u.endDate=t),name:"dateBetween",as:"range",label:d.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},J={name:"CommunicationSMSList"},Q=Object.assign(J,{setup(I){const c=U(),v=q("emitter");let f=["filter"];D("sms:send")&&f.unshift("new");let u=[];D("sms:read")&&(u=["print","pdf","excel"]);const b="communication/sms/",d=E(!1),s=S({}),$=i=>{Object.assign(s,i)};return(i,t)=>{const P=o("PageHeaderAction"),L=o("PageHeader"),k=o("ParentTransition"),_=o("DataCell"),F=o("FloatingMenuItem"),T=o("FloatingMenu"),j=o("DataRow"),A=o("BaseButton"),H=o("DataTable"),R=o("ListItem");return l(),g(R,{"init-url":b,"additional-query":{},onSetItems:$},{header:e(()=>[n(L,{title:i.$trans("communication.sms.sms"),navs:[{label:i.$trans("communication.communication"),path:"Communication"}]},{default:e(()=>[n(P,{url:"communication/sms/",name:"CommunicationSMS",title:i.$trans("communication.sms.sms"),actions:r(f),"dropdown-actions":r(u),"config-path":"CommunicationConfig",onToggleFilter:t[0]||(t[0]=a=>d.value=!d.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[n(k,{appear:"",visibility:d.value},{default:e(()=>[n(G,{onRefresh:t[1]||(t[1]=a=>r(v).emit("listItems")),onHide:t[2]||(t[2]=a=>d.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[n(k,{appear:"",visibility:!0},{default:e(()=>[n(H,{header:s.headers,meta:s.meta,module:"communication.sms",onRefresh:t[4]||(t[4]=a=>r(v).emit("listItems"))},{actionButton:e(()=>[r(D)("sms:send")?(l(),g(A,{key:0,onClick:t[3]||(t[3]=a=>r(c).push({name:"CommunicationSMSNew"}))},{default:e(()=>[p(m(i.$trans("global.new",{attribute:i.$trans("communication.sms.sms")})),1)]),_:1})):B("",!0)]),default:e(()=>[(l(!0),w(h,null,M(s.data,a=>(l(),g(j,{key:a.uuid,onDoubleClick:C=>r(c).push({name:"CommunicationSMSShow",params:{uuid:a.uuid}})},{default:e(()=>[n(_,{name:"subject"},{default:e(()=>[p(m(a.subjectExcerpt),1)]),_:2},1024),n(_,{name:"audience"},{default:e(()=>[(l(!0),w(h,null,M(a.audienceTypes,C=>(l(),w("div",null,m(C),1))),256))]),_:2},1024),n(_,{name:"recipientCount"},{default:e(()=>[p(m(a.recipientCount),1)]),_:2},1024),n(_,{name:"createdAt"},{default:e(()=>[p(m(a.createdAt.formatted),1)]),_:2},1024),n(_,{name:"action"},{default:e(()=>[n(T,null,{default:e(()=>[n(F,{icon:"fas fa-arrow-circle-right",onClick:C=>r(c).push({name:"CommunicationSMSShow",params:{uuid:a.uuid}})},{default:e(()=>[p(m(i.$trans("general.show")),1)]),_:2},1032,["onClick"]),r(D)("sms:send")?(l(),g(F,{key:0,icon:"fas fa-copy",onClick:C=>r(c).push({name:"CommunicationSMSDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[p(m(i.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):B("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Q as default};
