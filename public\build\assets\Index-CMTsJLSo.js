import{u as E,l as g,n as P,r as d,q as V,o as N,w as n,d as p,e,b as j,s as i,t as r,h as L,j as M,m as H,f,a as O,F as A,v as W}from"./app-DvIo72ZO.js";const z={class:"grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={class:"mt-4 grid grid-cols-3 gap-6"},Q={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3 sm:col-span-1"},te={class:"col-span-3 sm:col-span-1"},ae={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(C,{emit:h}){const c=E(),S=h,$={codeNumber:"",firstName:"",lastName:"",fatherName:"",motherName:"",batches:[],reasons:[],admissionStartDate:"",admissionEndDate:"",transferStartDate:"",transferEndDate:""},s=g({...$});g({});const b=g({isLoaded:!(c.query.batches||c.query.reasons)});return P(async()=>{b.batches=c.query.batches?c.query.batches.split(","):[],b.reasons=c.query.reasons?c.query.reasons.split(","):[],b.isLoaded=!0}),(l,a)=>{const v=d("BaseInput"),k=d("BaseSelectSearch"),m=d("BaseSelect"),u=d("DatePicker"),y=d("FilterForm");return N(),V(y,{"init-form":$,form:s,multiple:["batches","reasons"],onHide:a[11]||(a[11]=t=>S("hide"))},{default:n(()=>[p("div",z,[p("div",G,[e(v,{type:"text",modelValue:s.codeNumber,"onUpdate:modelValue":a[0]||(a[0]=t=>s.codeNumber=t),name:"codeNumber",label:l.$trans("student.admission.props.code_number")},null,8,["modelValue","label"])]),p("div",J,[b.isLoaded?(N(),V(k,{key:0,multiple:"",name:"batches",label:l.$trans("global.select",{attribute:l.$trans("academic.batch.batch")}),modelValue:s.batches,"onUpdate:modelValue":a[1]||(a[1]=t=>s.batches=t),"value-prop":"uuid","init-search":b.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:n(t=>[i(r(t.value.course.name)+" "+r(t.value.name),1)]),listOption:n(t=>[i(r(t.option.course.nameWithTerm)+" "+r(t.option.name),1)]),_:1},8,["label","modelValue","init-search"])):j("",!0)])]),p("div",K,[p("div",Q,[e(v,{type:"text",modelValue:s.firstName,"onUpdate:modelValue":a[2]||(a[2]=t=>s.firstName=t),name:"firstName",label:l.$trans("contact.props.first_name")},null,8,["modelValue","label"])]),p("div",X,[e(v,{type:"text",modelValue:s.lastName,"onUpdate:modelValue":a[3]||(a[3]=t=>s.lastName=t),name:"lastName",label:l.$trans("contact.props.last_name")},null,8,["modelValue","label"])]),p("div",Y,[e(v,{type:"text",modelValue:s.fatherName,"onUpdate:modelValue":a[4]||(a[4]=t=>s.fatherName=t),name:"fatherName",label:l.$trans("contact.props.father_name")},null,8,["modelValue","label"])]),p("div",Z,[e(v,{type:"text",modelValue:s.motherName,"onUpdate:modelValue":a[5]||(a[5]=t=>s.motherName=t),name:"motherName",label:l.$trans("contact.props.mother_name")},null,8,["modelValue","label"])]),p("div",x,[e(m,{multiple:"",name:"reasons",label:l.$trans("global.select",{attribute:l.$trans("student.transfer_reason.transfer_reason")}),modelValue:s.reasons,"onUpdate:modelValue":a[6]||(a[6]=t=>s.reasons=t),options:C.preRequisites.reasons,"label-prop":"name","value-prop":"uuid"},null,8,["label","modelValue","options"])]),p("div",ee,[e(u,{start:s.admissionStartDate,"onUpdate:start":a[7]||(a[7]=t=>s.admissionStartDate=t),end:s.admissionEndDate,"onUpdate:end":a[8]||(a[8]=t=>s.admissionEndDate=t),name:"admissionDateBetween",as:"range",label:l.$trans("global.date_between",{attribute:l.$trans("student.admission.props.date")})},null,8,["start","end","label"])]),p("div",te,[e(u,{start:s.transferStartDate,"onUpdate:start":a[9]||(a[9]=t=>s.transferStartDate=t),end:s.transferEndDate,"onUpdate:end":a[10]||(a[10]=t=>s.transferEndDate=t),name:"transferDateBetween",as:"range",label:l.$trans("global.date_between",{attribute:l.$trans("student.transfer.props.date")})},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},ne={name:"StudentTransferList"},oe=Object.assign(ne,{setup(C){const h=L(),c=M("emitter");let S=["create","filter"],$=["print","pdf","excel"];const s="student/transfer/",b=g({reasons:[]}),l=H(!1),a=g({}),v=m=>{Object.assign(b,m)},k=m=>{Object.assign(a,m)};return(m,u)=>{const y=d("PageHeaderAction"),t=d("PageHeader"),q=d("ParentTransition"),D=d("TextMuted"),_=d("DataCell"),B=d("FloatingMenuItem"),T=d("FloatingMenu"),F=d("DataRow"),U=d("BaseButton"),I=d("DataTable"),R=d("ListItem");return N(),V(R,{"init-url":s,"pre-requisites":!0,onSetPreRequisites:v,onSetItems:k},{header:n(()=>[e(t,{title:m.$trans("student.transfer.transfer"),navs:[{label:m.$trans("student.student"),path:"Student"}]},{default:n(()=>[e(y,{url:"student/transfers/",name:"StudentTransfer",title:m.$trans("student.transfer.transfer"),actions:f(S),"dropdown-actions":f($),onToggleFilter:u[0]||(u[0]=o=>l.value=!l.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:n(()=>[e(q,{appear:"",visibility:l.value},{default:n(()=>[e(ae,{onRefresh:u[1]||(u[1]=o=>f(c).emit("listItems")),"pre-requisites":b,onHide:u[2]||(u[2]=o=>l.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:n(()=>[e(q,{appear:"",visibility:!0},{default:n(()=>[e(I,{header:a.headers,meta:a.meta,module:"student.transfer",onRefresh:u[4]||(u[4]=o=>f(c).emit("listItems"))},{actionButton:n(()=>[e(U,{onClick:u[3]||(u[3]=o=>f(h).push({name:"StudentTransferCreate"}))},{default:n(()=>[i(r(m.$trans("global.add",{attribute:m.$trans("student.transfer.transfer")})),1)]),_:1})]),default:n(()=>[(N(!0),O(A,null,W(a.data,o=>(N(),V(F,{key:o.uuid,onDoubleClick:w=>f(h).push({name:"StudentTransferShow",params:{uuid:o.uuid}})},{default:n(()=>[e(_,{name:"codeNumber"},{default:n(()=>[i(r(o.codeNumber)+" ",1),e(D,{block:""},{default:n(()=>[i(r(o.transferCertificateNumber),1)]),_:2},1024)]),_:2},1024),e(_,{name:"name"},{default:n(()=>[i(r(o.name)+" ",1),e(D,{block:""},{default:n(()=>[i(r(o.contactNumber),1)]),_:2},1024)]),_:2},1024),e(_,{name:"parent"},{default:n(()=>[i(r(o.fatherName)+" ",1),e(D,{block:""},{default:n(()=>[i(r(o.motherName),1)]),_:2},1024)]),_:2},1024),e(_,{name:"admissionDate"},{default:n(()=>[i(r(o.joiningDate.formatted),1)]),_:2},1024),e(_,{name:"course"},{default:n(()=>[i(r(o.courseName)+" ",1),e(D,{block:""},{default:n(()=>[i(r(o.batchName),1)]),_:2},1024)]),_:2},1024),e(_,{name:"transferDate"},{default:n(()=>[i(r(o.leavingDate.formatted),1)]),_:2},1024),e(_,{name:"reason"},{default:n(()=>[i(r(o.reason),1)]),_:2},1024),e(_,{name:"action"},{default:n(()=>[e(T,null,{default:n(()=>[e(B,{icon:"fas fa-arrow-circle-right",onClick:w=>f(h).push({name:"StudentTransferShow",params:{uuid:o.uuid}})},{default:n(()=>[i(r(m.$trans("general.show")),1)]),_:2},1032,["onClick"]),e(B,{icon:"fas fa-edit",onClick:w=>f(h).push({name:"StudentTransferEdit",params:{uuid:o.uuid}})},{default:n(()=>[i(r(m.$trans("general.edit")),1)]),_:2},1032,["onClick"]),e(B,{icon:"fas fa-trash",onClick:w=>f(c).emit("deleteItem",{uuid:o.uuid})},{default:n(()=>[i(r(m.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{oe as default};
