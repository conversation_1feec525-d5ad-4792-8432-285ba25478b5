import{u as R,I as L,l as T,c as j,L as O,r as c,q as P,o as i,w as f,d as l,a as V,b as y,e as o,f as a,s as U,t as d,K as w,F as H}from"./app-DvIo72ZO.js";const C={class:"grid grid-cols-3 gap-6"},E={class:"col-span-3 sm:col-span-2"},K={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3"},z={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},X={key:0,class:"col-span-3 sm:col-span-1"},Y={key:1,class:"col-span-3 sm:col-span-1"},Z={key:2,class:"col-span-3 sm:col-span-1"},x={key:3,class:"col-span-3 sm:col-span-1"},ee={key:4,class:"col-span-3"},te={key:5,class:"col-span-3"},ne={class:"col-span-3 sm:col-span-1"},re={class:"col-span-3 sm:col-span-1"},se={class:"col-span-3 sm:col-span-1"},ae={class:"col-span-3 sm:col-span-1"},oe={class:"col-span-3"},le={name:"FinancePaymentRestrictionForm"},ie=Object.assign(le,{setup(M){const B=R(),p={name:"",description:"",type:"",scope:"",targetUsers:"",percentageThreshold:0,fixedAmount:0,feeStructureId:"",feeInstallmentId:"",applicableBatches:[],applicableStudents:[],isActive:!0,redirectToGuestPayment:!1,restrictionMessage:"",gracePeriodDays:0,effectiveFrom:"",effectiveUntil:""},A="finance/paymentRestriction/",s=L(A),m=T({types:[],scopes:[],targets:[],feeStructures:[],feeInstallments:[],batches:[]}),n=T({...p}),v=T({isLoaded:!B.params.uuid,batches:[],students:[]}),N=j(()=>{var t;return!n.feeStructureId||!((t=m.feeInstallments)!=null&&t.length)?[]:m.feeInstallments.filter(u=>u.structure&&u.structure.uuid===n.feeStructureId)}),q=r=>{Object.assign(m,r)},G=r=>{var u,I,_,b,S,$,F,e,h,k,D;const t={name:r.name||"",description:r.description||"",type:((u=r.type)==null?void 0:u.value)||"",scope:((I=r.scope)==null?void 0:I.value)||"",targetUsers:((_=r.targetUsers)==null?void 0:_.value)||"",feeStructureId:((b=r.feeStructure)==null?void 0:b.uuid)||"",feeInstallmentId:((S=r.feeInstallment)==null?void 0:S.uuid)||"",applicableBatches:(r.applicableBatches||[]).map(g=>g.uuid),applicableStudents:(r.applicableStudents||[]).map(g=>g.uuid),isActive:r.isActive??!0,redirectToGuestPayment:r.redirectToGuestPayment??!1,restrictionMessage:r.restrictionMessage||"",gracePeriodDays:r.gracePeriodDays||0,effectiveFrom:(($=r.effectiveFrom)==null?void 0:$.value)||"",effectiveUntil:((F=r.effectiveUntil)==null?void 0:F.value)||"",percentageThreshold:((e=r.percentageThreshold)==null?void 0:e.value)||0,fixedAmount:(h=r.fixedAmount)!=null&&h.value?parseFloat(r.fixedAmount.value):0};Object.assign(p,t),Object.assign(n,w(p)),(k=r.applicableBatches)!=null&&k.length&&(v.batches=r.applicableBatches.map(g=>g.uuid)),(D=r.applicableStudents)!=null&&D.length&&(v.students=r.applicableStudents.map(g=>g.uuid)),v.isLoaded=!0};return O(()=>n.feeStructureId,(r,t)=>{r!==t&&t!==void 0&&(n.feeInstallmentId="",s.feeInstallmentId&&(s.feeInstallmentId=""))}),O(()=>n.type,(r,t)=>{r!==t&&t!==void 0&&r!=="specific_fee"&&(n.feeStructureId="",n.feeInstallmentId="")}),(r,t)=>{const u=c("BaseInput"),I=c("BaseSwitch"),_=c("BaseTextarea"),b=c("BaseSelect"),S=c("BaseSelectSearch"),$=c("DatePicker"),F=c("FormAction");return i(),P(F,{"pre-requisites":!0,onSetPreRequisites:q,"init-url":A,"init-form":p,form:n,"set-form":G,redirect:"FinancePaymentRestriction"},{default:f(()=>[l("div",C,[l("div",E,[o(u,{type:"text",modelValue:n.name,"onUpdate:modelValue":t[0]||(t[0]=e=>n.name=e),name:"name",label:r.$trans("finance.payment_restriction.props.name"),error:a(s).name,"onUpdate:error":t[1]||(t[1]=e=>a(s).name=e)},null,8,["modelValue","label","error"])]),l("div",K,[o(I,{vertical:"",modelValue:n.isActive,"onUpdate:modelValue":t[2]||(t[2]=e=>n.isActive=e),name:"isActive",label:r.$trans("finance.payment_restriction.props.is_active")},null,8,["modelValue","label"])]),l("div",W,[o(_,{modelValue:n.description,"onUpdate:modelValue":t[3]||(t[3]=e=>n.description=e),name:"description",label:r.$trans("finance.payment_restriction.props.description"),error:a(s).description,"onUpdate:error":t[4]||(t[4]=e=>a(s).description=e)},null,8,["modelValue","label","error"])]),l("div",z,[o(b,{modelValue:n.type,"onUpdate:modelValue":t[5]||(t[5]=e=>n.type=e),name:"type",label:r.$trans("finance.payment_restriction.props.type"),options:m.types,"value-prop":"value","label-prop":"label",error:a(s).type,"onUpdate:error":t[6]||(t[6]=e=>a(s).type=e)},null,8,["modelValue","label","options","error"])]),l("div",J,[o(b,{modelValue:n.scope,"onUpdate:modelValue":t[7]||(t[7]=e=>n.scope=e),name:"scope",label:r.$trans("finance.payment_restriction.props.scope"),options:m.scopes,"value-prop":"value","label-prop":"label",error:a(s).scope,"onUpdate:error":t[8]||(t[8]=e=>a(s).scope=e)},null,8,["modelValue","label","options","error"])]),l("div",Q,[o(b,{modelValue:n.targetUsers,"onUpdate:modelValue":t[9]||(t[9]=e=>n.targetUsers=e),name:"targetUsers",label:r.$trans("finance.payment_restriction.props.target_users"),options:m.targets,"value-prop":"value","label-prop":"label",error:a(s).targetUsers,"onUpdate:error":t[10]||(t[10]=e=>a(s).targetUsers=e)},null,8,["modelValue","label","options","error"])]),n.type==="percentage"?(i(),V("div",X,[o(u,{modelValue:n.percentageThreshold,"onUpdate:modelValue":t[11]||(t[11]=e=>n.percentageThreshold=e),name:"percentageThreshold",label:r.$trans("finance.payment_restriction.props.percentage_threshold"),percentage:"",error:a(s).percentageThreshold,"onUpdate:error":t[12]||(t[12]=e=>a(s).percentageThreshold=e)},null,8,["modelValue","label","error"])])):y("",!0),n.type==="fixed_amount"?(i(),V("div",Y,[o(u,{modelValue:n.fixedAmount,"onUpdate:modelValue":t[13]||(t[13]=e=>n.fixedAmount=e),name:"fixedAmount",label:r.$trans("finance.payment_restriction.props.fixed_amount"),currency:"",error:a(s).fixedAmount,"onUpdate:error":t[14]||(t[14]=e=>a(s).fixedAmount=e)},null,8,["modelValue","label","error"])])):y("",!0),n.type==="specific_fee"?(i(),V("div",Z,[o(b,{modelValue:n.feeStructureId,"onUpdate:modelValue":t[15]||(t[15]=e=>n.feeStructureId=e),name:"feeStructureId",label:r.$trans("finance.payment_restriction.props.fee_structure"),options:m.feeStructures,"value-prop":"uuid","label-prop":"name",error:a(s).feeStructureId,"onUpdate:error":t[16]||(t[16]=e=>a(s).feeStructureId=e)},null,8,["modelValue","label","options","error"])])):y("",!0),n.type==="specific_fee"?(i(),V("div",x,[(i(),P(b,{modelValue:n.feeInstallmentId,"onUpdate:modelValue":t[17]||(t[17]=e=>n.feeInstallmentId=e),name:"feeInstallmentId",label:r.$trans("finance.payment_restriction.props.fee_installment"),options:N.value,"value-prop":"uuid","label-prop":"title",error:a(s).feeInstallmentId,"onUpdate:error":t[18]||(t[18]=e=>a(s).feeInstallmentId=e),key:`installment-${n.feeStructureId}`,placeholder:n.feeStructureId?r.$trans("general.select"):r.$trans("finance.payment_restriction.select_fee_structure_first"),disabled:!n.feeStructureId},{selectedOption:f(e=>[U(d(e.value.title),1)]),listOption:f(e=>[U(d(e.option.title),1)]),_:1},8,["modelValue","label","options","error","placeholder","disabled"]))])):y("",!0),n.scope==="batch"?(i(),V("div",ee,[v.isLoaded?(i(),P(S,{key:0,multiple:"",name:"applicableBatches",label:r.$trans("finance.payment_restriction.props.applicable_batches"),modelValue:n.applicableBatches,"onUpdate:modelValue":t[19]||(t[19]=e=>n.applicableBatches=e),"value-prop":"uuid","init-search":v.batches,"search-key":"course_batch","search-action":"academic/batch/list",error:a(s).applicableBatches,"onUpdate:error":t[20]||(t[20]=e=>a(s).applicableBatches=e)},{selectedOption:f(e=>[U(d(e.value.course.name)+" "+d(e.value.name),1)]),listOption:f(e=>[U(d(e.option.course.nameWithTerm)+" "+d(e.option.name),1)]),_:1},8,["label","modelValue","init-search","error"])):y("",!0)])):y("",!0),n.scope==="individual"?(i(),V("div",te,[v.isLoaded?(i(),P(S,{key:0,multiple:"",name:"applicableStudents",label:r.$trans("finance.payment_restriction.props.applicable_students"),modelValue:n.applicableStudents,"onUpdate:modelValue":t[21]||(t[21]=e=>n.applicableStudents=e),"value-prop":"uuid","init-search":v.students,"search-key":"name","search-action":"student/summary",error:a(s).applicableStudents,"onUpdate:error":t[22]||(t[22]=e=>a(s).applicableStudents=e),"additional-search-query":{status:"all"}},{selectedOption:f(e=>[U(d(e.value.name)+" ("+d(e.value.courseName+" "+e.value.batchName)+") ",1)]),listOption:f(e=>[U(d(e.option.name)+" ("+d(e.option.courseName+" "+e.option.batchName)+") ",1)]),_:1},8,["label","modelValue","init-search","error"])):y("",!0)])):y("",!0),l("div",ne,[o(u,{type:"number",modelValue:n.gracePeriodDays,"onUpdate:modelValue":t[23]||(t[23]=e=>n.gracePeriodDays=e),name:"gracePeriodDays",label:r.$trans("finance.payment_restriction.props.grace_period_days"),min:"0",max:"365",error:a(s).gracePeriodDays,"onUpdate:error":t[24]||(t[24]=e=>a(s).gracePeriodDays=e)},null,8,["modelValue","label","error"])]),l("div",re,[o($,{modelValue:n.effectiveFrom,"onUpdate:modelValue":t[25]||(t[25]=e=>n.effectiveFrom=e),name:"effectiveFrom",label:r.$trans("finance.payment_restriction.props.effective_from"),error:a(s).effectiveFrom,"onUpdate:error":t[26]||(t[26]=e=>a(s).effectiveFrom=e)},null,8,["modelValue","label","error"])]),l("div",se,[o($,{modelValue:n.effectiveUntil,"onUpdate:modelValue":t[27]||(t[27]=e=>n.effectiveUntil=e),name:"effectiveUntil",label:r.$trans("finance.payment_restriction.props.effective_until"),error:a(s).effectiveUntil,"onUpdate:error":t[28]||(t[28]=e=>a(s).effectiveUntil=e)},null,8,["modelValue","label","error"])]),l("div",ae,[o(I,{vertical:"",modelValue:n.redirectToGuestPayment,"onUpdate:modelValue":t[29]||(t[29]=e=>n.redirectToGuestPayment=e),name:"redirectToGuestPayment",label:r.$trans("finance.payment_restriction.props.redirect_to_guest_payment")},null,8,["modelValue","label"])]),l("div",oe,[o(_,{modelValue:n.restrictionMessage,"onUpdate:modelValue":t[30]||(t[30]=e=>n.restrictionMessage=e),name:"restrictionMessage",label:r.$trans("finance.payment_restriction.props.restriction_message"),error:a(s).restrictionMessage,"onUpdate:error":t[31]||(t[31]=e=>a(s).restrictionMessage=e)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),pe={name:"FinancePaymentRestrictionAction"},de=Object.assign(pe,{setup(M){const B=R();return(p,A)=>{const s=c("PageHeaderAction"),m=c("PageHeader"),n=c("ParentTransition");return i(),V(H,null,[o(m,{title:p.$trans(a(B).meta.trans,{attribute:p.$trans(a(B).meta.label)}),navs:[{label:p.$trans("finance.finance"),path:"Finance"},{label:p.$trans("finance.payment_restriction.payment_restrictions"),path:"FinancePaymentRestrictionList"}]},{default:f(()=>[o(s,{name:"FinancePaymentRestriction",title:p.$trans("finance.payment_restriction.payment_restriction"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),o(n,{appear:"",visibility:!0},{default:f(()=>[o(ie)]),_:1})],64)}}});export{de as default};
