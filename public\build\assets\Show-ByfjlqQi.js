import{i as k,u as C,h as P,l as N,r as o,a as A,o as c,e as n,w as e,f as i,q as m,b as _,d as H,s,t as r,y as I,F as M}from"./app-DvIo72ZO.js";const R={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},j={name:"TransportVehicleDocumentShow"},L=Object.assign(j,{setup(E){k();const u=C(),p=P(),h={},f="transport/vehicle/document/",a=N({...h}),b=t=>{Object.assign(a,t)};return(t,d)=>{const v=o("PageHeaderAction"),g=o("PageHeader"),$=o("TextMuted"),l=o("BaseDataView"),B=o("ListMedia"),T=o("BaseButton"),V=o("ShowButton"),D=o("BaseCard"),w=o("ShowItem"),y=o("ParentTransition");return c(),A(M,null,[n(g,{title:t.$trans(i(u).meta.trans,{attribute:t.$trans(i(u).meta.label)}),navs:[{label:t.$trans("transport.transport"),path:"Transport"},{label:t.$trans("transport.vehicle.vehicle"),path:"TransportVehicle"},{label:t.$trans("transport.vehicle.document.document"),path:"TransportVehicleDocument"}]},{default:e(()=>[n(v,{name:"TransportVehicleDocument",title:t.$trans("transport.vehicle.document.document"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(y,{appear:"",visibility:!0},{default:e(()=>[n(w,{"init-url":f,uuid:i(u).params.uuid,"module-uuid":i(u).params.muuid,onSetItem:b,onRedirectTo:d[1]||(d[1]=S=>i(p).push({name:"TransportVehicleDocument",params:{uuid:a.uuid}}))},{default:e(()=>[a.uuid?(c(),m(D,{key:0},{title:e(()=>[s(r(a.vehicle.name)+" ",1),n($,{block:""},{default:e(()=>[s(r(a.vehicle.registrationNumber),1)]),_:1})]),footer:e(()=>[n(V,null,{default:e(()=>[i(I)("vehicle-document:edit")?(c(),m(T,{key:0,design:"primary",onClick:d[0]||(d[0]=S=>i(p).push({name:"TransportVehicleDocumentEdit",params:{uuid:a.uuid}}))},{default:e(()=>[s(r(t.$trans("general.edit")),1)]),_:1})):_("",!0)]),_:1})]),default:e(()=>[H("dl",R,[n(l,{label:t.$trans("transport.vehicle.document.props.title")},{default:e(()=>[s(r(a.title),1)]),_:1},8,["label"]),n(l,{label:t.$trans("transport.vehicle.document.props.type")},{default:e(()=>[s(r(a.type.name),1)]),_:1},8,["label"]),n(l,{label:t.$trans("transport.vehicle.document.props.start_date")},{default:e(()=>[s(r(a.startDate.formatted),1)]),_:1},8,["label"]),n(l,{label:t.$trans("transport.vehicle.document.props.end_date")},{default:e(()=>[s(r(a.endDate.formatted),1)]),_:1},8,["label"]),n(l,{class:"col-span-1 sm:col-span-2",label:t.$trans("transport.vehicle.document.props.description")},{default:e(()=>[s(r(a.description),1)]),_:1},8,["label"]),n(l,{class:"col-span-1 sm:col-span-2"},{default:e(()=>[n(B,{media:a.media,url:`/app/transport/vehicle/documents/${a.uuid}/`},null,8,["media","url"])]),_:1}),n(l,{label:t.$trans("general.created_at")},{default:e(()=>[s(r(a.createdAt.formatted),1)]),_:1},8,["label"]),n(l,{label:t.$trans("general.updated_at")},{default:e(()=>[s(r(a.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):_("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{L as default};
