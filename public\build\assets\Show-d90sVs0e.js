import{i as w,u as S,h as v,l as P,r as a,a as V,o as u,e as n,w as e,f as s,q as k,b as y,d as H,s as d,t as r,F as N}from"./app-DvIo72ZO.js";const D={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},R={name:"AcademicIdCardTemplateShow"},F=Object.assign(R,{setup(j){w();const i=S(),c=v(),p={},_="academic/idCardTemplate/",o=P({...p}),f=t=>{Object.assign(o,t)};return(t,l)=>{const g=a("PageHeaderAction"),B=a("PageHeader"),C=a("TextMuted"),m=a("BaseDataView"),T=a("BaseButton"),b=a("ShowButton"),A=a("BaseCard"),I=a("ShowItem"),$=a("ParentTransition");return u(),V(N,null,[n(B,{title:t.$trans(s(i).meta.trans,{attribute:t.$trans(s(i).meta.label)}),navs:[{label:t.$trans("academic.academic"),path:"Academic"},{label:t.$trans("academic.id_card.template.template"),path:"AcademicIdCardTemplateList"}]},{default:e(()=>[n(g,{name:"AcademicIdCardTemplate",title:t.$trans("academic.id_card.template.template"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n($,{appear:"",visibility:!0},{default:e(()=>[n(I,{"init-url":_,uuid:s(i).params.uuid,onSetItem:f,onRedirectTo:l[1]||(l[1]=h=>s(c).push({name:"AcademicIdCardTemplate"}))},{default:e(()=>[o.uuid?(u(),k(A,{key:0},{title:e(()=>[d(r(o.name)+" ",1),n(C,null,{default:e(()=>[d(r(o.for.label),1)]),_:1})]),footer:e(()=>[n(b,null,{default:e(()=>[n(T,{design:"primary",onClick:l[0]||(l[0]=h=>s(c).push({name:"AcademicIdCardTemplateEdit",params:{uuid:o.uuid}}))},{default:e(()=>[d(r(t.$trans("general.edit")),1)]),_:1})]),_:1})]),default:e(()=>[H("dl",D,[n(m,{label:t.$trans("general.created_at")},{default:e(()=>[d(r(o.createdAt.formatted),1)]),_:1},8,["label"]),n(m,{label:t.$trans("general.updated_at")},{default:e(()=>[d(r(o.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):y("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{F as default};
