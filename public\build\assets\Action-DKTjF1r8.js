import{u as w,H as N,I as E,l as L,n as P,r as m,q as O,o as p,w as i,d as n,a as _,b as h,e as u,f as o,s as b,t as l,F,v as z,N as I,K}from"./app-DvIo72ZO.js";const M={class:"grid grid-cols-4 gap-6"},G={class:"col-span-4 sm:col-span-1"},J={key:0,class:"col-span-2 sm:col-span-1"},Q={key:1,class:"col-span-2 sm:col-span-1"},W={class:"mt-4 grid grid-cols-3 gap-6"},X={class:"col-span-2 sm:col-span-1"},Y={class:"col-span-2 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={class:"flex items-end justify-between space-x-4"},ee={class:"flex-1"},re={class:"mt-4"},te={class:"mt-4 grid grid-cols-3 gap-4"},ae={class:"col-span-3"},se={name:"LibraryTransactionForm"},oe=Object.assign(se,{setup(S){const v=w(),d={to:"",requester:"",issueDate:"",dueDate:"",records:[],remarks:""},B={uuid:N(),copy:{}},k="library/transaction/",s=E(k),V=L({to:[]}),y=L({requester:"",isLoaded:!v.params.uuid}),a=L({...d}),j=t=>{Object.assign(V,t)},T=()=>{a.records.push({...B,uuid:N()})},C=async t=>{await I()&&(a.records.length==1?a.records=[B]:a.records.splice(t,1))},R=t=>{var $,g,f,q;let r=[];t.records.forEach(U=>{r.push({...U})}),Object.assign(d,{...t,records:r,to:t.to.value,requester:(($=t.requester)==null?void 0:$.uuid)||"",issueDate:(g=t.issueDate)==null?void 0:g.value,dueDate:(f=t.dueDate)==null?void 0:f.value}),Object.assign(a,K(d)),y.requester=(q=t.requester)==null?void 0:q.uuid,y.isLoaded=!0};return P(async()=>{v.params.uuid||T()}),(t,r)=>{const $=m("BaseSelect"),g=m("BaseSelectSearch"),f=m("DatePicker"),q=m("BaseButton"),U=m("BaseBadge"),A=m("BaseTextarea"),H=m("FormAction");return p(),O(H,{"pre-requisites":!0,onSetPreRequisites:j,"init-url":k,"init-form":d,form:a,"set-form":R,redirect:"LibraryTransaction"},{default:i(()=>[n("div",M,[n("div",G,[u($,{modelValue:a.to,"onUpdate:modelValue":r[0]||(r[0]=e=>a.to=e),name:"to",label:t.$trans("library.transaction.props.to"),options:V.to,error:o(s).to,"onUpdate:error":r[1]||(r[1]=e=>o(s).to=e)},null,8,["modelValue","label","options","error"])]),a.to=="student"?(p(),_("div",J,[y.isLoaded?(p(),O(g,{key:0,name:"requester",label:t.$trans("student.student"),placeholder:t.$trans("global.select",{attribute:t.$trans("student.student")}),modelValue:a.requester,"onUpdate:modelValue":r[2]||(r[2]=e=>a.requester=e),error:o(s).requester,"onUpdate:error":r[3]||(r[3]=e=>o(s).requester=e),"value-prop":"uuid","init-search":y.requester,"search-key":"name","search-action":"student/summary"},{selectedOption:i(e=>[b(l(e.value.name)+" ("+l(e.value.courseName+" "+e.value.batchName)+") ",1)]),listOption:i(e=>[b(l(e.option.name)+" ("+l(e.option.courseName+" "+e.option.batchName)+") ",1)]),_:1},8,["label","placeholder","modelValue","error","init-search"])):h("",!0)])):h("",!0),a.to=="employee"?(p(),_("div",Q,[y.isLoaded?(p(),O(g,{key:0,name:"requester",label:t.$trans("employee.employee"),placeholder:t.$trans("global.select",{attribute:t.$trans("employee.employee")}),modelValue:a.requester,"onUpdate:modelValue":r[4]||(r[4]=e=>a.requester=e),error:o(s).requester,"onUpdate:error":r[5]||(r[5]=e=>o(s).requester=e),"value-prop":"uuid","init-search":y.requester,"search-key":"name","search-action":"employee/summary"},{selectedOption:i(e=>[b(l(e.value.name)+" ("+l(e.value.designation)+") ",1)]),listOption:i(e=>[b(l(e.option.name)+" ("+l(e.option.designation)+") ",1)]),_:1},8,["label","placeholder","modelValue","error","init-search"])):h("",!0)])):h("",!0)]),n("div",W,[n("div",X,[u(f,{modelValue:a.issueDate,"onUpdate:modelValue":r[6]||(r[6]=e=>a.issueDate=e),name:"issueDate",label:t.$trans("library.transaction.props.issue_date"),"no-clear":"",error:o(s).issueDate,"onUpdate:error":r[7]||(r[7]=e=>o(s).issueDate=e)},null,8,["modelValue","label","error"])]),n("div",Y,[u(f,{modelValue:a.dueDate,"onUpdate:modelValue":r[8]||(r[8]=e=>a.dueDate=e),name:"dueDate",label:t.$trans("library.transaction.props.due_date"),"no-clear":"",error:o(s).dueDate,"onUpdate:error":r[9]||(r[9]=e=>o(s).dueDate=e)},null,8,["modelValue","label","error"])])]),(p(!0),_(F,null,z(a.records,(e,D)=>(p(),_("div",{class:"mt-4 grid grid-cols-3 gap-4",key:e.uuid},[n("div",Z,[n("div",x,[n("div",null,[u(q,{size:"xs",design:"danger",onClick:c=>C(D)},{default:i(()=>r[12]||(r[12]=[n("i",{class:"fas fa-trash"},null,-1)])),_:2},1032,["onClick"])]),n("div",ee,[u(g,{name:`records.${D}.copy`,label:t.$trans("global.select",{attribute:t.$trans("library.book.props.number")}),modelValue:e.copy,"onUpdate:modelValue":c=>e.copy=c,error:o(s)[`records.${D}.copy`],"onUpdate:error":c=>o(s)[`records.${D}.copy`]=c,"object-prop":!0,"search-action":"library/bookCopy/list","init-search":e.copy.number,"search-key":"number","init-search-key":"number"},{selectedOption:i(c=>[b(l(c.value.book.title)+" ("+l(c.value.number)+") ",1)]),listOption:i(c=>[b(l(c.option.book.title)+" ("+l(c.option.number)+") ",1)]),_:2},1032,["name","label","modelValue","onUpdate:modelValue","error","onUpdate:error","init-search"])])])])]))),128)),n("div",re,[u(U,{design:"primary",onClick:T,class:"cursor-pointer"},{default:i(()=>[b(l(t.$trans("global.add",{attribute:t.$trans("general.row")})),1)]),_:1})]),n("div",te,[n("div",ae,[u(A,{rows:1,modelValue:a.remarks,"onUpdate:modelValue":r[10]||(r[10]=e=>a.remarks=e),name:"remarks",label:t.$trans("library.transaction.props.remarks"),error:o(s).remarks,"onUpdate:error":r[11]||(r[11]=e=>o(s).remarks=e)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),ne={name:"LibraryTransactionAction"},ie=Object.assign(ne,{setup(S){const v=w();return(d,B)=>{const k=m("PageHeaderAction"),s=m("PageHeader"),V=m("ParentTransition");return p(),_(F,null,[u(s,{title:d.$trans(o(v).meta.trans,{attribute:d.$trans(o(v).meta.label)}),navs:[{label:d.$trans("library.library"),path:"Library"},{label:d.$trans("library.transaction.transaction"),path:"LibraryTransactionList"}]},{default:i(()=>[u(k,{name:"LibraryTransaction",title:d.$trans("library.transaction.transaction"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),u(V,{appear:"",visibility:!0},{default:i(()=>[u(oe)]),_:1})],64)}}});export{ie as default};
