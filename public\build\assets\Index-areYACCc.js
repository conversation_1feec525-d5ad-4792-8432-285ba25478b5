import{u as D,j as P,l as q,I as E,n as C,r as d,q as B,o as v,w as a,d as F,a as w,b as A,e,s as i,t as o,i as I,y as M,m as S,f as y,F as T,v as x}from"./app-DvIo72ZO.js";const z={class:"grid grid-cols-3 gap-6"},W={key:0,class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},Q={__name:"Filter",props:{initUrl:{type:String,default:""},preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(g,{emit:p}){const b=D();P("moment");const N=p,H=g,_={codeNumber:"",name:"",batches:[],feeHead:""},s=q({..._});E(H.initUrl);const c=q({isLoaded:!b.query.batches});return C(async()=>{c.batches=b.query.batches?b.query.batches.split(","):[],c.isLoaded=!0}),(u,n)=>{const V=d("BaseSelect"),h=d("BaseInput"),l=d("BaseSelectSearch"),m=d("FilterForm");return v(),B(m,{"init-form":_,multiple:["batches"],form:s,onHide:n[4]||(n[4]=t=>N("hide"))},{default:a(()=>[F("div",z,[g.preRequisites.feeHeads.length?(v(),w("div",W,[e(V,{name:"feeHead",label:u.$trans("global.select",{attribute:u.$trans("finance.fee_head.fee_head")}),"label-prop":"name","value-prop":"uuid",modelValue:s.feeHead,"onUpdate:modelValue":n[0]||(n[0]=t=>s.feeHead=t),options:g.preRequisites.feeHeads},null,8,["label","modelValue","options"])])):A("",!0),F("div",G,[e(h,{type:"text",modelValue:s.codeNumber,"onUpdate:modelValue":n[1]||(n[1]=t=>s.codeNumber=t),name:"codeNumber",label:u.$trans("student.admission.props.code_number")},null,8,["modelValue","label"])]),F("div",J,[e(h,{type:"text",modelValue:s.name,"onUpdate:modelValue":n[2]||(n[2]=t=>s.name=t),name:"name",label:u.$trans("contact.props.name")},null,8,["modelValue","label"])]),F("div",K,[c.isLoaded?(v(),B(l,{key:0,multiple:"",name:"batches",label:u.$trans("global.select",{attribute:u.$trans("academic.batch.batch")}),modelValue:s.batches,"onUpdate:modelValue":n[3]||(n[3]=t=>s.batches=t),"value-prop":"uuid","init-search":c.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:a(t=>[i(o(t.value.course.name)+" "+o(t.value.name),1)]),listOption:a(t=>[i(o(t.option.course.nameWithTerm)+" "+o(t.option.name),1)]),_:1},8,["label","modelValue","init-search"])):A("",!0)])])]),_:1},8,["form"])}}},X={name:"FinanceReportFeeHead"},Z=Object.assign(X,{setup(g){const p=D(),b=I();let N=["filter"],H=[];M("finance:export")&&(H=["print","pdf","excel"]);const _="finance/report/",s=S(!0),c=S(!1),u=q({feeHeads:[]}),n=q({headers:[],data:[],meta:{total:0}}),V=async()=>{c.value=!0,await b.dispatch(_+"preRequisite",{name:"fee-head",params:p.query}).then(l=>{c.value=!1;let m=l.feeHeads;l.defaultFeeHeads.forEach(t=>{m.push(t)}),u.feeHeads=m}).catch(l=>{c.value=!1})},h=async()=>{c.value=!0,await b.dispatch(_+"fetchReport",{name:"fee-head",params:p.query}).then(l=>{c.value=!1,Object.assign(n,l)}).catch(l=>{c.value=!1})};return C(async()=>{await V(),p.query.feeHead&&await h()}),(l,m)=>{const t=d("PageHeaderAction"),U=d("PageHeader"),R=d("ParentTransition"),j=d("BaseAlert"),k=d("BaseCard"),f=d("DataCell"),$=d("TextMuted"),L=d("DataRow"),O=d("DataTable");return v(),w(T,null,[e(U,{title:l.$trans(y(p).meta.label),navs:[{label:l.$trans("finance.finance"),path:"Finance"},{label:l.$trans("finance.report.report"),path:"FinanceReport"}]},{default:a(()=>[e(t,{url:"finance/reports/fee-head/",name:"FinanceReportFeeHead",title:l.$trans("finance.report.fee_head.fee_head"),actions:y(N),"dropdown-actions":y(H),headers:n.headers,onToggleFilter:m[0]||(m[0]=r=>s.value=!s.value)},null,8,["title","actions","dropdown-actions","headers"])]),_:1},8,["title","navs"]),e(R,{appear:"",visibility:s.value},{default:a(()=>[e(Q,{onAfterFilter:h,"init-url":_,"pre-requisites":u,onHide:m[1]||(m[1]=r=>s.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"]),e(R,{appear:"",visibility:!y(p).query.feeHead},{default:a(()=>[e(k,null,{default:a(()=>[e(j,{size:"xs",design:"info"},{default:a(()=>[i(o(l.$trans("finance.report.fee_head.select_fee_head")),1)]),_:1})]),_:1})]),_:1},8,["visibility"]),e(R,{appear:"",visibility:!!y(p).query.feeHead},{default:a(()=>[e(k,{"no-padding":"","no-content-padding":"","is-loading":c.value},{default:a(()=>[e(O,{header:n.headers,footer:n.footers,meta:n.meta,module:"finance.report.fee_head",onRefresh:h},{default:a(()=>[(v(!0),w(T,null,x(n.data,r=>(v(),B(L,{key:r.uuid},{default:a(()=>[e(f,{name:"codeNumber"},{default:a(()=>[i(o(r.codeNumber),1)]),_:2},1024),e(f,{name:"name"},{default:a(()=>[i(o(r.name)+" ",1),e($,{block:""},{default:a(()=>[i(o(r.rollNumber),1)]),_:2},1024)]),_:2},1024),e(f,{name:"fatherName"},{default:a(()=>[i(o(r.fatherName)+" ",1),e($,{block:""},{default:a(()=>[i(o(r.contactNumber),1)]),_:2},1024)]),_:2},1024),e(f,{name:"course"},{default:a(()=>[i(o(r.courseName)+" ",1),e($,{block:""},{default:a(()=>[i(o(r.batchName),1)]),_:2},1024)]),_:2},1024),e(f,{name:"totalAmount"},{default:a(()=>[i(o(r.total.formatted),1)]),_:2},1024),e(f,{name:"concessionAmount"},{default:a(()=>[i(o(r.concession.formatted),1)]),_:2},1024),e(f,{name:"paidAmount"},{default:a(()=>[i(o(r.paid.formatted),1)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","footer","meta"])]),_:1},8,["is-loading"])]),_:1},8,["visibility"])],64)}}});export{Z as default};
