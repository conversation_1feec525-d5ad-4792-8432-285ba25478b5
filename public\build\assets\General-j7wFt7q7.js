import{u as w,j as D,I as E,c as L,l as f,r as c,a as u,o as p,e as r,f as t,w as g,d,b as m,s as P,t as y,F as T}from"./app-DvIo72ZO.js";const R={class:"grid grid-cols-3 gap-4"},I={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},A={class:"mt-4 grid grid-cols-3 gap-4"},q={class:"col-span-3 sm:col-span-1"},B={key:0,class:"col-span-3 sm:col-span-1"},F={class:"mt-4 grid grid-cols-3 gap-4"},j={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3 sm:col-span-1"},N={class:"col-span-3 sm:col-span-1"},$={class:"flex items-center gap-2"},z={key:0,class:"grid grid-cols-3 gap-6"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},M={class:"col-span-3 sm:col-span-1"},W={class:"flex items-center gap-2"},X={class:"grid grid-cols-3 gap-4"},Y={class:"col-span-3 sm:col-span-1"},Z={key:0,class:"col-span-3 sm:col-span-1"},h={key:0,class:"mt-6"},ee={class:"grid grid-cols-3 gap-4"},oe={class:"col-span-3 sm:col-span-1"},te={class:"col-span-3 sm:col-span-1"},le={key:0,class:"col-span-3 sm:col-span-1"},ne={key:1,class:"col-span-3 sm:col-span-1"},ae={class:"col-span-3 sm:col-span-1"},re={class:"mt-6"},de={class:"grid grid-cols-3 gap-4"},se={class:"col-span-3 sm:col-span-1"},ie={key:0,class:"col-span-3 sm:col-span-1"},ue={key:1,class:"col-span-3 sm:col-span-1"},pe={key:2,class:"col-span-3 sm:col-span-1"},me={name:"EmployeeAttendanceConfigGeneral"},ye=Object.assign(me,{setup(ce){const S=w(),a=D("$trans"),b="config/",n=E(b);L(()=>a("global.placeholder_info",{attribute:_.datePlaceholders}));const _=f({datePlaceholders:""}),V={allowEmployeeClockInOut:!1,allowEmployeeClockInOutViaDevice:!1,enableQrCodeAttendance:!1,useDynamicQrCode:!1,lateGracePeriod:"",earlyLeavingGracePeriod:"",presentGracePeriod:"",enableGeolocationTimesheet:!1,geolocationLatitude:"",geolocationLongitude:"",geolocationRadius:"",enableStudentTimesheet:!1,useEmployeeSettingsForStudent:!0,allowStudentClockInOut:!1,enableStudentGeolocationTimesheet:!1,studentGeolocationLatitude:"",studentGeolocationLongitude:"",studentGeolocationRadius:"",enableStudentQrCodeAttendance:!1,useStudentDynamicQrCode:!1,studentQrCodeExpiryDuration:"300",studentDurationBetweenClockRequest:"5",type:"employee"},l=f({...V}),G=v=>{Object.assign(_,{datePlaceholders:v.datePlaceholders.map(e=>e.value).join(", ")})};return(v,e)=>{const U=c("PageHeader"),s=c("BaseSwitch"),k=c("HelperText"),i=c("BaseInput"),C=c("BaseFieldset"),x=c("FormAction"),Q=c("ParentTransition");return p(),u(T,null,[r(U,{title:t(a)(t(S).meta.label),navs:[{label:t(a)("employee.employee"),path:"Employee"},{label:t(a)("employee.attendance.attendance"),path:"EmployeeAttendance"}]},null,8,["title","navs"]),r(Q,{appear:"",visibility:!0},{default:g(()=>[r(x,{"pre-requisites":{data:["datePlaceholders"]},onSetPreRequisites:G,"init-url":b,"data-fetch":"employee",action:"store","init-form":V,form:l,"stay-on":"",redirect:"EmployeeAttendance"},{default:g(()=>[d("div",R,[d("div",I,[r(s,{vertical:"",modelValue:l.allowEmployeeClockInOut,"onUpdate:modelValue":e[0]||(e[0]=o=>l.allowEmployeeClockInOut=o),name:"allowEmployeeClockInOut",label:t(a)("employee.attendance.config.props.allow_employee_clock_in_out"),error:t(n).allowEmployeeClockInOut,"onUpdate:error":e[1]||(e[1]=o=>t(n).allowEmployeeClockInOut=o)},null,8,["modelValue","label","error"])]),d("div",O,[r(s,{vertical:"",modelValue:l.allowEmployeeClockInOutViaDevice,"onUpdate:modelValue":e[2]||(e[2]=o=>l.allowEmployeeClockInOutViaDevice=o),name:"allowEmployeeClockInOutViaDevice",label:t(a)("employee.attendance.config.props.allow_employee_clock_in_out_via_device"),error:t(n).allowEmployeeClockInOutViaDevice,"onUpdate:error":e[3]||(e[3]=o=>t(n).allowEmployeeClockInOutViaDevice=o)},null,8,["modelValue","label","error"])])]),d("div",A,[d("div",q,[r(s,{vertical:"",modelValue:l.enableQrCodeAttendance,"onUpdate:modelValue":e[4]||(e[4]=o=>l.enableQrCodeAttendance=o),name:"enableQrCodeAttendance",label:t(a)("employee.attendance.config.props.enable_qr_code_attendance"),error:t(n).enableQrCodeAttendance,"onUpdate:error":e[5]||(e[5]=o=>t(n).enableQrCodeAttendance=o)},null,8,["modelValue","label","error"])]),l.enableQrCodeAttendance?(p(),u("div",B,[r(s,{vertical:"",modelValue:l.useDynamicQrCode,"onUpdate:modelValue":e[6]||(e[6]=o=>l.useDynamicQrCode=o),name:"useDynamicQrCode",label:t(a)("employee.attendance.config.props.use_dynamic_qr_code"),error:t(n).useDynamicQrCode,"onUpdate:error":e[7]||(e[7]=o=>t(n).useDynamicQrCode=o)},null,8,["modelValue","label","error"]),r(k,null,{default:g(()=>[P(y(t(a)("employee.attendance.config.props.dynamic_qr_code_tip")),1)]),_:1})])):m("",!0)]),d("div",F,[d("div",j,[r(i,{type:"text",modelValue:l.lateGracePeriod,"onUpdate:modelValue":e[8]||(e[8]=o=>l.lateGracePeriod=o),name:"lateGracePeriod",label:t(a)("employee.attendance.config.props.late_grace_period"),"label-hint":t(a)("employee.attendance.config.props.late_grace_period_tip"),error:t(n).lateGracePeriod,"onUpdate:error":e[9]||(e[9]=o=>t(n).lateGracePeriod=o)},null,8,["modelValue","label","label-hint","error"])]),d("div",H,[r(i,{type:"text",modelValue:l.earlyLeavingGracePeriod,"onUpdate:modelValue":e[10]||(e[10]=o=>l.earlyLeavingGracePeriod=o),name:"earlyLeavingGracePeriod",label:t(a)("employee.attendance.config.props.early_leaving_grace_period"),"label-hint":t(a)("employee.attendance.config.props.early_leaving_grace_period_tip"),error:t(n).earlyLeavingGracePeriod,"onUpdate:error":e[11]||(e[11]=o=>t(n).earlyLeavingGracePeriod=o)},null,8,["modelValue","label","label-hint","error"])]),d("div",N,[r(i,{type:"text",modelValue:l.presentGracePeriod,"onUpdate:modelValue":e[12]||(e[12]=o=>l.presentGracePeriod=o),name:"presentGracePeriod",label:t(a)("employee.attendance.config.props.present_grace_period"),"label-hint":t(a)("employee.attendance.config.props.present_grace_period_tip"),error:t(n).presentGracePeriod,"onUpdate:error":e[13]||(e[13]=o=>t(n).presentGracePeriod=o)},null,8,["modelValue","label","label-hint","error"])])]),r(C,{class:"mt-4"},{legend:g(()=>[d("div",$,[d("span",null,y(t(a)("employee.attendance.config.props.enable_geolocation_timesheet")),1),r(s,{modelValue:l.enableGeolocationTimesheet,"onUpdate:modelValue":e[14]||(e[14]=o=>l.enableGeolocationTimesheet=o),name:"enableGeolocationTimesheet",error:t(n).enableGeolocationTimesheet,"onUpdate:error":e[15]||(e[15]=o=>t(n).enableGeolocationTimesheet=o)},null,8,["modelValue","error"])])]),default:g(()=>[l.enableGeolocationTimesheet?(p(),u("div",z,[d("div",J,[r(i,{type:"text",modelValue:l.geolocationLatitude,"onUpdate:modelValue":e[16]||(e[16]=o=>l.geolocationLatitude=o),name:"geolocationLatitude",label:t(a)("employee.attendance.config.props.geolocation_latitude"),error:t(n).geolocationLatitude,"onUpdate:error":e[17]||(e[17]=o=>t(n).geolocationLatitude=o)},null,8,["modelValue","label","error"])]),d("div",K,[r(i,{type:"text",modelValue:l.geolocationLongitude,"onUpdate:modelValue":e[18]||(e[18]=o=>l.geolocationLongitude=o),name:"geolocationLongitude",label:t(a)("employee.attendance.config.props.geolocation_longitude"),error:t(n).geolocationLongitude,"onUpdate:error":e[19]||(e[19]=o=>t(n).geolocationLongitude=o)},null,8,["modelValue","label","error"])]),d("div",M,[r(i,{type:"text",modelValue:l.geolocationRadius,"onUpdate:modelValue":e[20]||(e[20]=o=>l.geolocationRadius=o),name:"geolocationRadius","trailing-text":t(a)("list.distances.mtr"),label:t(a)("employee.attendance.config.props.geolocation_radius"),error:t(n).geolocationRadius,"onUpdate:error":e[21]||(e[21]=o=>t(n).geolocationRadius=o)},null,8,["modelValue","trailing-text","label","error"])])])):m("",!0)]),_:1}),r(C,{class:"mt-4"},{legend:g(()=>[d("div",W,[d("span",null,y(t(a)("employee.attendance.timesheet.student_timesheet_config")),1)])]),default:g(()=>[d("div",X,[d("div",Y,[r(s,{vertical:"",modelValue:l.enableStudentTimesheet,"onUpdate:modelValue":e[22]||(e[22]=o=>l.enableStudentTimesheet=o),name:"enableStudentTimesheet",label:t(a)("employee.attendance.config.props.enable_student_timesheet"),error:t(n).enableStudentTimesheet,"onUpdate:error":e[23]||(e[23]=o=>t(n).enableStudentTimesheet=o)},null,8,["modelValue","label","error"])]),l.enableStudentTimesheet?(p(),u("div",Z,[r(s,{vertical:"",modelValue:l.useEmployeeSettingsForStudent,"onUpdate:modelValue":e[24]||(e[24]=o=>l.useEmployeeSettingsForStudent=o),name:"useEmployeeSettingsForStudent",label:t(a)("employee.attendance.config.props.use_employee_settings_for_student"),error:t(n).useEmployeeSettingsForStudent,"onUpdate:error":e[25]||(e[25]=o=>t(n).useEmployeeSettingsForStudent=o)},null,8,["modelValue","label","error"])])):m("",!0)]),l.enableStudentTimesheet&&!l.useEmployeeSettingsForStudent?(p(),u("div",h,[d("div",ee,[d("div",oe,[r(s,{vertical:"",modelValue:l.allowStudentClockInOut,"onUpdate:modelValue":e[26]||(e[26]=o=>l.allowStudentClockInOut=o),name:"allowStudentClockInOut",label:t(a)("employee.attendance.config.props.allow_student_clock_in_out"),error:t(n).allowStudentClockInOut,"onUpdate:error":e[27]||(e[27]=o=>t(n).allowStudentClockInOut=o)},null,8,["modelValue","label","error"])]),d("div",te,[r(s,{vertical:"",modelValue:l.enableStudentQrCodeAttendance,"onUpdate:modelValue":e[28]||(e[28]=o=>l.enableStudentQrCodeAttendance=o),name:"enableStudentQrCodeAttendance",label:t(a)("employee.attendance.config.props.enable_student_qr_code_attendance"),error:t(n).enableStudentQrCodeAttendance,"onUpdate:error":e[29]||(e[29]=o=>t(n).enableStudentQrCodeAttendance=o)},null,8,["modelValue","label","error"])]),l.enableStudentQrCodeAttendance?(p(),u("div",le,[r(s,{vertical:"",modelValue:l.useStudentDynamicQrCode,"onUpdate:modelValue":e[30]||(e[30]=o=>l.useStudentDynamicQrCode=o),name:"useStudentDynamicQrCode",label:t(a)("employee.attendance.config.props.use_student_dynamic_qr_code"),error:t(n).useStudentDynamicQrCode,"onUpdate:error":e[31]||(e[31]=o=>t(n).useStudentDynamicQrCode=o)},null,8,["modelValue","label","error"])])):m("",!0),l.useStudentDynamicQrCode?(p(),u("div",ne,[r(i,{type:"text",name:"studentQrCodeExpiryDuration","trailing-text":t(a)("list.durations.sec"),label:t(a)("employee.attendance.config.props.student_qr_code_expiry_duration"),modelValue:l.studentQrCodeExpiryDuration,"onUpdate:modelValue":e[32]||(e[32]=o=>l.studentQrCodeExpiryDuration=o),error:t(n).studentQrCodeExpiryDuration,"onUpdate:error":e[33]||(e[33]=o=>t(n).studentQrCodeExpiryDuration=o)},null,8,["trailing-text","label","modelValue","error"])])):m("",!0),d("div",ae,[r(i,{type:"text",name:"studentDurationBetweenClockRequest","trailing-text":t(a)("list.durations.min"),label:t(a)("employee.attendance.config.props.student_duration_between_clock_request"),modelValue:l.studentDurationBetweenClockRequest,"onUpdate:modelValue":e[34]||(e[34]=o=>l.studentDurationBetweenClockRequest=o),error:t(n).studentDurationBetweenClockRequest,"onUpdate:error":e[35]||(e[35]=o=>t(n).studentDurationBetweenClockRequest=o)},null,8,["trailing-text","label","modelValue","error"])])]),d("div",re,[d("div",de,[d("div",se,[r(s,{vertical:"",modelValue:l.enableStudentGeolocationTimesheet,"onUpdate:modelValue":e[36]||(e[36]=o=>l.enableStudentGeolocationTimesheet=o),name:"enableStudentGeolocationTimesheet",label:t(a)("employee.attendance.config.props.enable_student_geolocation_timesheet"),error:t(n).enableStudentGeolocationTimesheet,"onUpdate:error":e[37]||(e[37]=o=>t(n).enableStudentGeolocationTimesheet=o)},null,8,["modelValue","label","error"])]),l.enableStudentGeolocationTimesheet?(p(),u("div",ie,[r(i,{type:"text",step:"any",name:"studentGeolocationLatitude",label:t(a)("employee.attendance.config.props.student_geolocation_latitude"),modelValue:l.studentGeolocationLatitude,"onUpdate:modelValue":e[38]||(e[38]=o=>l.studentGeolocationLatitude=o),error:t(n).studentGeolocationLatitude,"onUpdate:error":e[39]||(e[39]=o=>t(n).studentGeolocationLatitude=o)},null,8,["label","modelValue","error"])])):m("",!0),l.enableStudentGeolocationTimesheet?(p(),u("div",ue,[r(i,{type:"text",step:"any",name:"studentGeolocationLongitude",label:t(a)("employee.attendance.config.props.student_geolocation_longitude"),modelValue:l.studentGeolocationLongitude,"onUpdate:modelValue":e[40]||(e[40]=o=>l.studentGeolocationLongitude=o),error:t(n).studentGeolocationLongitude,"onUpdate:error":e[41]||(e[41]=o=>t(n).studentGeolocationLongitude=o)},null,8,["label","modelValue","error"])])):m("",!0),l.enableStudentGeolocationTimesheet?(p(),u("div",pe,[r(i,{type:"text",name:"studentGeolocationRadius","trailing-text":t(a)("list.distances.mtr"),label:t(a)("employee.attendance.config.props.student_geolocation_radius"),modelValue:l.studentGeolocationRadius,"onUpdate:modelValue":e[42]||(e[42]=o=>l.studentGeolocationRadius=o),error:t(n).studentGeolocationRadius,"onUpdate:error":e[43]||(e[43]=o=>t(n).studentGeolocationRadius=o)},null,8,["trailing-text","label","modelValue","error"])])):m("",!0)])])])):m("",!0)]),_:1})]),_:1},8,["form"])]),_:1})],64)}}});export{ye as default};
