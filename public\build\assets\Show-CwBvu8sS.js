import{u as R,h as D,i as E,O as F,j as C,l as L,m as x,r as p,a as i,o as n,e as u,w as l,f as e,b as c,d as t,q as k,s as v,t as o,am as O,x as f,F as I,v as q}from"./app-DvIo72ZO.js";const G={key:0,class:"grid grid-cols-1 gap-6 lg:grid-cols-3"},J={class:"lg:col-span-1"},K={class:"grid grid-cols-1 gap-x-4 gap-y-6"},Q={class:"flex items-center"},W={class:"badge bg-secondary"},X={class:"text-sm"},Y={class:"lg:col-span-2"},Z={key:0,class:"text-center text-muted py-8"},ee={class:"card-body p-3"},te={class:"d-flex align-items-start"},se={key:0,class:"me-2"},ae={class:"flex-fill"},oe=["innerHTML"],ne={key:0,class:"mt-2"},le={class:"text-danger"},re={key:1,class:"mt-2"},ie={key:0},ce={key:1,class:"ms-2"},de={key:2,class:"ms-2"},ue={key:1,class:"ms-2"},_e={class:"text-end mt-1"},ve={key:1,class:"text-center py-4"},me={class:"spinner-border spinner-border-sm",role:"status"},pe={class:"visually-hidden"},fe={name:"AIConversationShow"},ye=Object.assign(fe,{setup(ge){const g=R(),A=D(),U=E(),T=F(),s=C("$trans"),y=C("$cal"),B="ai/conversations/",r=L({}),h=x([]),b=x(!1),M=x(null),$=d=>{Object.assign(r,d),S()},S=async()=>{b.value=!0;try{const d=await U.dispatch("ai/conversation/fetchMessages",{conversationUuid:g.params.uuid});h.value=d.data}catch(d){T.error(d.message||"Failed to fetch messages")}finally{b.value=!1}},j=d=>d.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/\n/g,"<br>"),H=d=>({user:"fas fa-user",assistant:"fas fa-robot",system:"fas fa-cog"})[d]||"fas fa-comment";return(d,_)=>{const P=p("PageHeaderAction"),V=p("PageHeader"),m=p("BaseDataView"),w=p("BaseCard"),z=p("ShowItem"),N=p("ParentTransition");return n(),i(I,null,[u(V,{title:e(s)(e(g).meta.trans,{attribute:e(s)(e(g).meta.label)}),navs:[{label:e(s)("ai.ai"),path:"AIIndex"},{label:e(s)("ai.conversation.conversations"),path:"AIConversations"}]},{default:l(()=>[u(P,{name:"AIConversation",title:e(s)("ai.conversation.conversation"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),u(N,{appear:"",visibility:!0},{default:l(()=>[u(z,{"init-url":B,uuid:e(g).params.uuid,onSetItem:$,onRedirectTo:_[0]||(_[0]=a=>e(A).push({name:"AIConversations"}))},{default:l(()=>[r.uuid?(n(),i("div",G,[t("div",J,[u(w,null,{title:l(()=>[v(o(e(s)("ai.conversation.conversation")),1)]),default:l(()=>[t("dl",K,[u(m,{label:e(s)("ai.conversation.props.title")},{default:l(()=>[v(o(r.title),1)]),_:1},8,["label"]),u(m,{label:e(s)("ai.conversation.props.type")},{default:l(()=>[t("div",Q,[t("i",{class:f([r.typeIcon,"me-2"]),style:O({color:r.typeColor})},null,6),v(" "+o(r.typeName),1)])]),_:1},8,["label"]),r.context?(n(),k(m,{key:0,label:e(s)("ai.conversation.props.context")},{default:l(()=>[t("span",W,o(r.context),1)]),_:1},8,["label"])):c("",!0),r.user?(n(),k(m,{key:1,label:e(s)("general.user")},{default:l(()=>[v(o(r.user.name),1)]),_:1},8,["label"])):c("",!0),u(m,{label:e(s)("general.created_at")},{default:l(()=>[v(o(e(y).toUserTimezone(r.createdAt.value,"datetime")),1)]),_:1},8,["label"]),r.lastMessageAt?(n(),k(m,{key:2,label:e(s)("ai.conversation.props.last_message")},{default:l(()=>[v(o(e(y).toUserTimezone(r.lastMessageAt.value,"datetime")),1)]),_:1},8,["label"])):c("",!0),u(m,{label:e(s)("general.statistics")},{default:l(()=>[t("div",X,[t("div",null,o(e(s)("ai.message.messages"))+": "+o(h.value.length),1)])]),_:1},8,["label"])])]),_:1})]),t("div",Y,[u(w,null,{title:l(()=>[v(o(e(s)("ai.message.messages")),1)]),default:l(()=>[t("div",{class:"space-y-4",style:{"max-height":"600px","overflow-y":"auto"},ref_key:"messagesContainer",ref:M},[h.value.length===0?(n(),i("div",Z,[_[1]||(_[1]=t("i",{class:"fas fa-comments fa-3x mb-3"},null,-1)),t("h4",null,o(e(s)("general.no_messages")),1),t("p",null,o(e(s)("ai.conversation.no_messages_description")),1)])):c("",!0),(n(!0),i(I,null,q(h.value,a=>(n(),i("div",{key:a.uuid,class:"mb-4"},[t("div",{class:f(["d-flex",a.role==="user"?"justify-content-end":"justify-content-start"])},[t("div",{class:f(["card border-0 shadow-sm",a.role==="user"?"bg-primary text-white":"bg-light",a.isError?"border-danger":""]),style:{"max-width":"85%"}},[t("div",ee,[t("div",te,[a.role!=="user"?(n(),i("div",se,[t("i",{class:f(H(a.role))},null,2)])):c("",!0),t("div",ae,[t("div",{innerHTML:j(a.content)},null,8,oe),a.isError?(n(),i("div",ne,[t("small",le,[_[2]||(_[2]=t("i",{class:"fas fa-exclamation-triangle me-1"},null,-1)),v(" "+o(e(s)("general.error")),1)])])):c("",!0),a.providerUsed||a.tokensUsed?(n(),i("div",re,[t("small",{class:f(a.role==="user"?"text-white-50":"text-muted")},[a.providerUsed?(n(),i("span",ie,o(e(s)("ai.provider.provider"))+": "+o(a.providerUsed),1)):c("",!0),a.tokensUsed?(n(),i("span",ce,o(e(s)("ai.tokens"))+": "+o(a.tokensUsed),1)):c("",!0),a.cost?(n(),i("span",de,o(e(s)("ai.cost"))+": $"+o(a.cost),1)):c("",!0)],2)])):c("",!0)]),a.role==="user"?(n(),i("div",ue,_[3]||(_[3]=[t("i",{class:"fas fa-user"},null,-1)]))):c("",!0)]),t("div",_e,[t("small",{class:f(a.role==="user"?"text-white-50":"text-muted")},o(e(y).toUserTimezone(a.createdAt.value,"datetime")),3)])])],2)],2)]))),128)),b.value?(n(),i("div",ve,[t("div",me,[t("span",pe,o(e(s)("general.loading")),1)])])):c("",!0)],512)]),_:1})])])):c("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{ye as default};
