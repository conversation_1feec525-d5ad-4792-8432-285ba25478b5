import{i as te,u as ne,h as le,g as y,l as se,r as s,a as j,o as w,e as a,w as l,f as r,q as h,b as f,d as re,s as d,t as u,F as de}from"./app-DvIo72ZO.js";const ue={key:0,class:"mb-4"},oe={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},ie={name:"StudentProfileEditRequestShow"},pe=Object.assign(ie,{props:{student:{type:Object,default(){return{}}}},setup($){te();const g=ne(),F=le(),D={},O="student/profileEditRequest/",M=y("contact.uniqueIdNumber1Label"),G=y("contact.uniqueIdNumber2Label"),U=y("contact.uniqueIdNumber3Label"),e=se({...D}),J=t=>{Object.assign(e,t)};return(t,q)=>{const K=s("PageHeaderAction"),Q=s("PageHeader"),W=s("BaseAlert"),X=s("BaseBadge"),o=s("BaseDataView"),n=s("HorizontalListItem"),A=s("HorizontalList"),L=s("BaseFieldset"),Y=s("ListMedia"),Z=s("ShowButton"),x=s("BaseCard"),ee=s("ShowItem"),ae=s("ParentTransition");return w(),j(de,null,[a(Q,{title:t.$trans(r(g).meta.trans,{attribute:t.$trans(r(g).meta.label)}),navs:[{label:t.$trans("student.student"),path:"Student"},{label:$.student.contact.name,path:{name:"StudentShow",params:{uuid:$.student.uuid}}}]},{default:l(()=>[a(K,{name:"StudentProfileEditRequest",title:t.$trans("student.edit_request.edit_request"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(ae,{appear:"",visibility:!0},{default:l(()=>[a(ee,{"init-url":O,uuid:r(g).params.uuid,"module-uuid":r(g).params.muuid,onSetItem:J,onRedirectTo:q[0]||(q[0]=_=>r(F).push({name:"StudentProfileEditRequest",params:{uuid:$.student.uuid}}))},{default:l(()=>[e.uuid?(w(),h(x,{key:0},{title:l(()=>[d(u(t.$trans("student.edit_request.request_by_name",{attribute:e.user.profile.name})),1)]),footer:l(()=>[a(Z)]),default:l(()=>[e.isRejected&&e.comment?(w(),j("div",ue,[a(W,{design:"error",size:"xs"},{default:l(()=>[d(u(e.comment),1)]),_:1})])):f("",!0),re("dl",oe,[a(o,{label:t.$trans("student.edit_request.props.status")},{default:l(()=>[a(X,{design:e.status.color},{default:l(()=>[d(u(e.status.label),1)]),_:1},8,["design"])]),_:1},8,["label"]),a(o,{class:"col-span-1 sm:col-span-2"},{default:l(()=>{var _,B,N,I,S,P,z,R,C,E,H,k,V;return[a(A,null,{default:l(()=>[a(n,{label:t.$trans("contact.props.contact_number"),value:e.data.new.contactNumber},null,8,["label","value"]),a(n,{label:t.$trans("contact.props.alternate_contact_number"),value:e.data.new.alternateContactNumber},null,8,["label","value"]),a(n,{label:t.$trans("contact.props.email"),value:e.data.new.email},null,8,["label","value"]),a(n,{label:t.$trans("contact.props.father_contact_number"),value:e.data.new.fatherContactNumber},null,8,["label","value"]),a(n,{label:t.$trans("contact.props.father_email"),value:e.data.new.fatherEmail},null,8,["label","value"]),a(n,{label:t.$trans("contact.props.mother_contact_number"),value:e.data.new.motherContactNumber},null,8,["label","value"]),a(n,{label:t.$trans("contact.props.mother_email"),value:e.data.new.motherEmail},null,8,["label","value"]),a(n,{label:r(M),value:e.data.new.uniqueIdNumber1},null,8,["label","value"]),a(n,{label:r(G),value:e.data.new.uniqueIdNumber2},null,8,["label","value"]),a(n,{label:r(U),value:e.data.new.uniqueIdNumber3},null,8,["label","value"]),a(n,{label:t.$trans("contact.props.birth_place"),value:e.data.new.birthPlace},null,8,["label","value"]),a(n,{label:t.$trans("contact.props.nationality"),value:e.data.new.nationality},null,8,["label","value"]),a(n,{label:t.$trans("contact.props.mother_tongue"),value:e.data.new.motherTongue},null,8,["label","value"]),a(n,{label:t.$trans("contact.props.blood_group"),value:t.$trans("list.blood_groups."+e.data.new.bloodGroup)},null,8,["label","value"]),a(n,{label:t.$trans("contact.religion.religion"),value:e.data.new.religion},null,8,["label","value"]),a(n,{label:t.$trans("contact.category.category"),value:e.data.new.category},null,8,["label","value"]),a(n,{label:t.$trans("contact.caste.caste"),value:e.data.new.caste},null,8,["label","value"])]),_:1}),(_=e.data.new.presentAddress)!=null&&_.addressLine1||(B=e.data.new.presentAddress)!=null&&B.addressLine2||(N=e.data.new.presentAddress)!=null&&N.city||(I=e.data.new.presentAddress)!=null&&I.state||(S=e.data.new.presentAddress)!=null&&S.zipcode||(P=e.data.new.presentAddress)!=null&&P.country?(w(),h(L,{key:0,class:"mt-4"},{legend:l(()=>[d(u(t.$trans("contact.props.present_address")),1)]),default:l(()=>[a(A,null,{default:l(()=>{var i,c,p,b,m,v;return[a(n,{label:t.$trans("contact.props.address.address_line1"),value:(i=e.data.new.presentAddress)==null?void 0:i.addressLine1},null,8,["label","value"]),a(n,{label:t.$trans("contact.props.address.address_line2"),value:(c=e.data.new.presentAddress)==null?void 0:c.addressLine2},null,8,["label","value"]),a(n,{label:t.$trans("contact.props.address.city"),value:(p=e.data.new.presentAddress)==null?void 0:p.city},null,8,["label","value"]),a(n,{label:t.$trans("contact.props.address.state"),value:(b=e.data.new.presentAddress)==null?void 0:b.state},null,8,["label","value"]),a(n,{label:t.$trans("contact.props.address.zipcode"),value:(m=e.data.new.presentAddress)==null?void 0:m.zipcode},null,8,["label","value"]),a(n,{label:t.$trans("contact.props.address.country"),value:(v=e.data.new.presentAddress)==null?void 0:v.country},null,8,["label","value"])]}),_:1})]),_:1})):f("",!0),(z=e.data.new.permanentAddress)!=null&&z.sameAsPresentAddress||(R=e.data.new.permanentAddress)!=null&&R.addressLine1||(C=e.data.new.permanentAddress)!=null&&C.addressLine2||(E=e.data.new.permanentAddress)!=null&&E.city||(H=e.data.new.permanentAddress)!=null&&H.state||(k=e.data.new.permanentAddress)!=null&&k.zipcode||(V=e.data.new.permanentAddress)!=null&&V.country?(w(),h(L,{key:1,class:"mt-4"},{legend:l(()=>[d(u(t.$trans("contact.props.permanent_address")),1)]),default:l(()=>[a(A,null,{default:l(()=>{var i,c,p,b,m,v,T;return[a(n,{label:t.$trans("contact.props.same_as_present_address"),value:(i=e.data.new.permanentAddress)!=null&&i.sameAsPresentAddress?t.$trans("general.yes"):t.$trans("general.no")},null,8,["label","value"]),a(n,{label:t.$trans("contact.props.address.address_line1"),value:(c=e.data.new.permanentAddress)==null?void 0:c.addressLine1},null,8,["label","value"]),a(n,{label:t.$trans("contact.props.address.address_line2"),value:(p=e.data.new.permanentAddress)==null?void 0:p.addressLine2},null,8,["label","value"]),a(n,{label:t.$trans("contact.props.address.city"),value:(b=e.data.new.permanentAddress)==null?void 0:b.city},null,8,["label","value"]),a(n,{label:t.$trans("contact.props.address.state"),value:(m=e.data.new.permanentAddress)==null?void 0:m.state},null,8,["label","value"]),a(n,{label:t.$trans("contact.props.address.zipcode"),value:(v=e.data.new.permanentAddress)==null?void 0:v.zipcode},null,8,["label","value"]),a(n,{label:t.$trans("contact.props.address.country"),value:(T=e.data.new.permanentAddress)==null?void 0:T.country},null,8,["label","value"])]}),_:1})]),_:1})):f("",!0)]}),_:1}),a(o,{class:"col-span-1 sm:col-span-2"},{default:l(()=>[a(Y,{media:e.media,url:`/app/students/${$.student.uuid}/edit-requests/${e.uuid}/`},null,8,["media","url"])]),_:1}),a(o,{label:t.$trans("general.created_at")},{default:l(()=>[d(u(e.createdAt.formatted),1)]),_:1},8,["label"]),a(o,{label:t.$trans("general.updated_at")},{default:l(()=>[d(u(e.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):f("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{pe as default};
