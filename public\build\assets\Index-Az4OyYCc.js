import{l as B,r as l,q as v,o as r,w as e,d as h,u as N,h as V,j as A,y as F,m as O,e as a,f as o,a as w,F as E,v as U,s as m,t as u,b as R}from"./app-DvIo72ZO.js";const q={__name:"Filter",emits:["hide"],setup(n,{emit:k}){const d=k,c={},$=B({...c});return(b,p)=>{const f=l("FilterForm");return r(),v(f,{"init-form":c,form:$,onHide:p[0]||(p[0]=y=>d("hide"))},{default:e(()=>p[1]||(p[1]=[h("div",{class:"grid grid-cols-3 gap-6"},[h("div",{class:"col-span-3 sm:col-span-1"})],-1)])),_:1},8,["form"])}}},x={class:"text-xs"},z={name:"EmployeeRecordList"},J=Object.assign(z,{props:{employee:{type:Object,default(){return{}}}},setup(n){const k=N(),d=V(),c=A("emitter"),$=n;let b=[];F("employment-record:manage")&&!$.employee.lastRecord.endDate.value&&!$.employee.isDefault&&b.unshift("create");const p="employee/record/",f=O(!1),y=B({}),I=i=>{Object.assign(y,i)};return(i,s)=>{const S=l("PageHeaderAction"),H=l("PageHeader"),_=l("ParentTransition"),g=l("DataCell"),C=l("FloatingMenuItem"),P=l("FloatingMenu"),T=l("DataRow"),j=l("BaseButton"),L=l("DataTable"),M=l("ListItem");return r(),v(M,{"init-url":p,uuid:o(k).params.uuid,onSetItems:I},{header:e(()=>[n.employee.uuid?(r(),v(H,{key:0,title:i.$trans("employee.record.record"),navs:[{label:i.$trans("employee.employee"),path:"Employee"},{label:n.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:n.employee.uuid}}}]},{default:e(()=>[a(S,{url:`employees/${n.employee.uuid}/records/`,name:"EmployeeRecord",title:i.$trans("employee.record.record"),actions:o(b),"dropdown-actions":["print","pdf","excel"],onToggleFilter:s[0]||(s[0]=t=>f.value=!f.value)},null,8,["url","title","actions"])]),_:1},8,["title","navs"])):R("",!0)]),filter:e(()=>[a(_,{appear:"",visibility:f.value},{default:e(()=>[a(q,{onRefresh:s[1]||(s[1]=t=>o(c).emit("listItems")),onHide:s[2]||(s[2]=t=>f.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[a(_,{appear:"",visibility:!0},{default:e(()=>[a(L,{header:y.headers,meta:y.meta,module:"employee.record",onRefresh:s[4]||(s[4]=t=>o(c).emit("listItems"))},{actionButton:e(()=>[o(F)("employment-record:manage")&&!n.employee.lastRecord.endDate&&!n.employee.isDefault?(r(),v(j,{key:0,onClick:s[3]||(s[3]=t=>o(d).push({name:"EmployeeRecordCreate"}))},{default:e(()=>[m(u(i.$trans("global.add",{attribute:i.$trans("employee.record.record")})),1)]),_:1})):R("",!0)]),default:e(()=>[(r(!0),w(E,null,U(y.data,t=>(r(),v(T,{key:t.uuid,onDoubleClick:D=>o(d).push({name:"EmployeeRecordShow",params:{uuid:n.employee.uuid,muuid:t.uuid}})},{default:e(()=>[a(g,{name:"startDate"},{default:e(()=>[m(u(t.period)+" ",1),h("div",x,u(t.duration),1)]),_:2},1024),a(g,{name:"department"},{default:e(()=>[m(u(t.department.name),1)]),_:2},1024),a(g,{name:"designation"},{default:e(()=>[m(u(t.designation.name),1)]),_:2},1024),a(g,{name:"employmentStatus"},{default:e(()=>[m(u(t.employmentStatus.name),1)]),_:2},1024),a(g,{name:"action"},{default:e(()=>[a(P,null,{default:e(()=>[a(C,{icon:"fas fa-arrow-circle-right",onClick:D=>o(d).push({name:"EmployeeRecordShow",params:{uuid:n.employee.uuid,muuid:t.uuid}})},{default:e(()=>[m(u(i.$trans("general.show")),1)]),_:2},1032,["onClick"]),o(F)("employment-record:manage")&&!n.employee.isDefault?(r(),w(E,{key:0},[a(C,{icon:"fas fa-edit",onClick:D=>o(d).push({name:"EmployeeRecordEdit",params:{uuid:n.employee.uuid,muuid:t.uuid}})},{default:e(()=>[m(u(i.$trans("general.edit")),1)]),_:2},1032,["onClick"]),a(C,{icon:"fas fa-trash",onClick:D=>o(c).emit("deleteItem",{uuid:n.employee.uuid,moduleUuid:t.uuid})},{default:e(()=>[m(u(i.$trans("general.delete")),1)]),_:2},1032,["onClick"])],64)):R("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1},8,["uuid"])}}});export{J as default};
