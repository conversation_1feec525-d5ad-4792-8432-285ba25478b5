import{u as S,j as A,l as B,I,n as U,r as u,q as R,o as y,w as a,d as _,e,b as L,s as i,t as s,h as M,i as W,y as E,m as q,a as O,f as w,F as T,v as z}from"./app-DvIo72ZO.js";const J={class:"grid grid-cols-3 gap-6"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3 sm:col-span-1"},ee={__name:"Filter",props:{initUrl:{type:String,default:""},preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup($,{emit:b}){const h=S();A("moment");const F=b,D=$,v={installment:"",codeNumber:"",name:"",batches:[],feeGroup:"",dueOn:""},n=B({...v});I(D.initUrl);const m=B({isLoaded:!h.query.batches});return U(async()=>{m.batches=h.query.batches?h.query.batches.split(","):[],m.isLoaded=!0}),(l,o)=>{const c=u("BaseInput"),N=u("BaseSelectSearch"),g=u("BaseSelect"),d=u("DatePicker"),p=u("FilterForm");return y(),R(p,{"init-form":v,multiple:["batches"],form:n,onHide:o[6]||(o[6]=t=>F("hide"))},{default:a(()=>[_("div",J,[_("div",K,[e(c,{type:"text",modelValue:n.installment,"onUpdate:modelValue":o[0]||(o[0]=t=>n.installment=t),name:"installment",label:l.$trans("finance.fee_structure.installment")},null,8,["modelValue","label"])]),_("div",Q,[e(c,{type:"text",modelValue:n.codeNumber,"onUpdate:modelValue":o[1]||(o[1]=t=>n.codeNumber=t),name:"codeNumber",label:l.$trans("student.admission.props.code_number")},null,8,["modelValue","label"])]),_("div",X,[e(c,{type:"text",modelValue:n.name,"onUpdate:modelValue":o[2]||(o[2]=t=>n.name=t),name:"name",label:l.$trans("contact.props.name")},null,8,["modelValue","label"])]),_("div",Y,[m.isLoaded?(y(),R(N,{key:0,multiple:"",name:"batches",label:l.$trans("global.select",{attribute:l.$trans("academic.batch.batch")}),modelValue:n.batches,"onUpdate:modelValue":o[3]||(o[3]=t=>n.batches=t),"value-prop":"uuid","init-search":m.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:a(t=>[i(s(t.value.course.name)+" "+s(t.value.name),1)]),listOption:a(t=>[i(s(t.option.course.nameWithTerm)+" "+s(t.option.name),1)]),_:1},8,["label","modelValue","init-search"])):L("",!0)]),_("div",Z,[e(g,{name:"feeGroup",label:l.$trans("global.select",{attribute:l.$trans("finance.fee_group.fee_group")}),modelValue:n.feeGroup,"onUpdate:modelValue":o[4]||(o[4]=t=>n.feeGroup=t),options:$.preRequisites.feeGroups,"value-prop":"uuid","label-prop":"name"},null,8,["label","modelValue","options"])]),_("div",x,[e(d,{modelValue:n.dueOn,"onUpdate:modelValue":o[5]||(o[5]=t=>n.dueOn=t),name:"dueOn",as:"date",label:l.$trans("finance.report.fee_due.props.due_on")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},ae={name:"FinanceReportInstallmentWiseFeeDue"},ne=Object.assign(ae,{setup($){const b=S(),h=M(),F=W();let D=["filter"],v=[];E("finance:export")&&(v=["print","pdf","excel"]);const n="finance/report/",m=q(!1),l=q(!1),o=B({feeGroups:[]}),c=B({headers:[],data:[],meta:{total:0}}),N=async()=>{l.value=!0,await F.dispatch(n+"preRequisite",{name:"installment-wise-fee-due",params:b.query}).then(d=>{l.value=!1,Object.assign(o,d)}).catch(d=>{l.value=!1})},g=async()=>{l.value=!0,await F.dispatch(n+"fetchReport",{name:"installment-wise-fee-due",params:b.query}).then(d=>{l.value=!1,Object.assign(c,d)}).catch(d=>{l.value=!1})};return U(async()=>{await N(),await g()}),(d,p)=>{const t=u("BaseButton"),C=u("PageHeaderAction"),G=u("PageHeader"),k=u("ParentTransition"),f=u("DataCell"),V=u("TextMuted"),j=u("DataRow"),H=u("DataTable"),P=u("BaseCard");return y(),O(T,null,[e(G,{title:d.$trans(w(b).meta.label),navs:[{label:d.$trans("finance.finance"),path:"Finance"},{label:d.$trans("finance.report.report"),path:"FinanceReport"}]},{default:a(()=>[e(C,{url:"finance/reports/installment-wise-fee-due/",name:"FinanceReportInstallmentWiseFeeDue",title:d.$trans("finance.report.installment_wise_fee_due.installment_wise_fee_due"),actions:w(D),"dropdown-actions":w(v),headers:c.headers,onToggleFilter:p[1]||(p[1]=r=>m.value=!m.value)},{default:a(()=>[e(t,{design:"white",onClick:p[0]||(p[0]=r=>w(h).push({name:"FinanceReportFeeDue"}))},{default:a(()=>[i(s(d.$trans("finance.report.fee_due.fee_due")),1)]),_:1})]),_:1},8,["title","actions","dropdown-actions","headers"])]),_:1},8,["title","navs"]),e(k,{appear:"",visibility:m.value},{default:a(()=>[e(ee,{onAfterFilter:g,"init-url":n,"pre-requisites":o,onHide:p[2]||(p[2]=r=>m.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"]),e(k,{appear:"",visibility:!0},{default:a(()=>[e(P,{"no-padding":"","no-content-padding":"","is-loading":l.value},{default:a(()=>[e(H,{header:c.headers,footer:c.footers,meta:c.meta,module:"finance.report.installment_wise_fee_due",onRefresh:g},{default:a(()=>[(y(!0),O(T,null,z(c.data,r=>(y(),R(j,{key:r.uuid},{default:a(()=>[e(f,{name:"codeNumber"},{default:a(()=>[i(s(r.codeNumber),1)]),_:2},1024),e(f,{name:"name"},{default:a(()=>[i(s(r.name)+" ",1),e(V,{block:""},{default:a(()=>[i(s(r.rollNumber),1)]),_:2},1024)]),_:2},1024),e(f,{name:"fatherName"},{default:a(()=>[i(s(r.fatherName)+" ",1),e(V,{block:""},{default:a(()=>[i(s(r.contactNumber),1)]),_:2},1024)]),_:2},1024),e(f,{name:"course"},{default:a(()=>[i(s(r.courseName)+" ",1),e(V,{block:""},{default:a(()=>[i(s(r.batchName),1)]),_:2},1024)]),_:2},1024),e(f,{name:"installment"},{default:a(()=>[i(s(r.installmentTitle)+" ",1),e(V,{block:""},{default:a(()=>[i(s(r.feeGroupName),1)]),_:2},1024)]),_:2},1024),e(f,{name:"dueFee"},{default:a(()=>[i(s(r.dueFee.formatted),1)]),_:2},1024),e(f,{name:"finalDueDate"},{default:a(()=>[i(s(r.finalDueDate.formatted),1)]),_:2},1024),e(f,{name:"overdueBy"},{default:a(()=>[i(s(r.overdueBy),1)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","footer","meta"])]),_:1},8,["is-loading"])]),_:1})],64)}}});export{ne as default};
