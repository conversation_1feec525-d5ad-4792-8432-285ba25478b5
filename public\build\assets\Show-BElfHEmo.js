import{i as y,u as C,h as P,l as N,r as o,a as A,o as u,e as r,w as e,f as n,q as m,b as _,d as H,s,t as l,y as I,F as M}from"./app-DvIo72ZO.js";const D={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},j={name:"TransportVehicleTravelRecordShow"},L=Object.assign(j,{setup(E){y();const d=C(),p=P(),v={},h="transport/vehicle/travelRecord/",a=N({...v}),f=t=>{Object.assign(a,t)};return(t,c)=>{const b=o("PageHeaderAction"),g=o("PageHeader"),T=o("TextMuted"),i=o("BaseDataView"),$=o("ListMedia"),B=o("BaseButton"),V=o("ShowButton"),R=o("BaseCard"),k=o("ShowItem"),w=o("ParentTransition");return u(),A(M,null,[r(g,{title:t.$trans(n(d).meta.trans,{attribute:t.$trans(n(d).meta.label)}),navs:[{label:t.$trans("transport.transport"),path:"Transport"},{label:t.$trans("transport.vehicle.vehicle"),path:"TransportVehicle"},{label:t.$trans("transport.vehicle.travel_record.travel_record"),path:"TransportVehicleTravelRecord"}]},{default:e(()=>[r(b,{name:"TransportVehicleTravelRecord",title:t.$trans("transport.vehicle.travel_record.travel_record"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),r(w,{appear:"",visibility:!0},{default:e(()=>[r(k,{"init-url":h,uuid:n(d).params.uuid,"module-uuid":n(d).params.muuid,onSetItem:f,onRedirectTo:c[1]||(c[1]=S=>n(p).push({name:"TransportVehicleTravelRecord",params:{uuid:a.uuid}}))},{default:e(()=>[a.uuid?(u(),m(R,{key:0},{title:e(()=>[s(l(a.vehicle.name)+" ",1),r(T,{block:""},{default:e(()=>[s(l(a.vehicle.registrationNumber),1)]),_:1})]),footer:e(()=>[r(V,null,{default:e(()=>[n(I)("vehicle-travel-record:edit")?(u(),m(B,{key:0,design:"primary",onClick:c[0]||(c[0]=S=>n(p).push({name:"TransportVehicleTravelRecordEdit",params:{uuid:a.uuid}}))},{default:e(()=>[s(l(t.$trans("general.edit")),1)]),_:1})):_("",!0)]),_:1})]),default:e(()=>[H("dl",D,[r(i,{label:t.$trans("transport.vehicle.travel_record.props.date")},{default:e(()=>[s(l(a.date.formatted),1)]),_:1},8,["label"]),r(i,{label:t.$trans("transport.vehicle.travel_record.props.log")},{default:e(()=>[s(l(a.log||"-"),1)]),_:1},8,["label"]),r(i,{class:"col-span-1 sm:col-span-2",label:t.$trans("transport.vehicle.travel_record.props.remarks")},{default:e(()=>[s(l(a.remarks),1)]),_:1},8,["label"]),r(i,{class:"col-span-1 sm:col-span-2"},{default:e(()=>[r($,{media:a.media,url:`/app/transport/vehicle/travel-records/${a.uuid}/`},null,8,["media","url"])]),_:1}),r(i,{label:t.$trans("general.created_at")},{default:e(()=>[s(l(a.createdAt.formatted),1)]),_:1},8,["label"]),r(i,{label:t.$trans("general.updated_at")},{default:e(()=>[s(l(a.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):_("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{L as default};
