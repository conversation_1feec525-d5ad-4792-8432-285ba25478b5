import{u as N,l as E,n as V,r as i,q as f,o as d,w as e,d as k,b,s as r,t as a,e as o,h as A,j as R,y as $,m as O,f as s,a as j,F as q,v as U}from"./app-DvIo72ZO.js";import{_ as z}from"./ModuleDropdown-Cjwc1GLq.js";const G={class:"grid grid-cols-3 gap-6"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},Q={__name:"Filter",emits:["hide"],setup(I,{emit:g}){const v=N(),D=g,h={employees:[],startDate:"",endDate:""},p=E({...h}),c=E({employees:[],isLoaded:!v.query.employees});return V(async()=>{c.employees=v.query.employees?v.query.employees.split(","):[],c.isLoaded=!0}),(_,m)=>{const n=i("BaseSelectSearch"),u=i("DatePicker"),w=i("FilterForm");return d(),f(w,{"init-form":h,form:p,multiple:["employees"],onHide:m[3]||(m[3]=l=>D("hide"))},{default:e(()=>[k("div",G,[k("div",J,[c.isLoaded?(d(),f(n,{key:0,multiple:"",name:"employees",label:_.$trans("global.select",{attribute:_.$trans("employee.employee")}),modelValue:p.employees,"onUpdate:modelValue":m[0]||(m[0]=l=>p.employees=l),"value-prop":"uuid","init-search":c.employees,"search-key":"name","search-action":"employee/list"},{selectedOption:e(l=>[r(a(l.value.name)+" ("+a(l.value.codeNumber)+") ",1)]),listOption:e(l=>[r(a(l.option.name)+" ("+a(l.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):b("",!0)]),k("div",K,[o(u,{start:p.startDate,"onUpdate:start":m[1]||(m[1]=l=>p.startDate=l),end:p.endDate,"onUpdate:end":m[2]||(m[2]=l=>p.endDate=l),name:"dateBetween",as:"range",label:_.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},W={class:"text-success"},X={class:"text-danger"},Y={class:"text-success"},Z={name:"EmployeePayrollSalaryStructureList"},te=Object.assign(Z,{setup(I){const g=A(),v=R("emitter");let D=["filter"];$("salary-structure:create")&&D.unshift("create");let h=[];$("salary-structure:export")&&(h=["print","pdf","excel"]);const p="employee/payroll/salaryStructure/",c=O(!1),_=E({}),m=n=>{Object.assign(_,n)};return(n,u)=>{const w=i("PageHeaderAction"),l=i("PageHeader"),F=i("ParentTransition"),y=i("DataCell"),B=i("TextMuted"),C=i("FloatingMenuItem"),S=i("FloatingMenu"),T=i("DataRow"),H=i("BaseButton"),L=i("DataTable"),M=i("ListItem");return d(),f(M,{"init-url":p,onSetItems:m},{header:e(()=>[o(l,{title:n.$trans("employee.payroll.salary_structure.salary_structure"),navs:[{label:n.$trans("employee.employee"),path:"Employee"},{label:n.$trans("employee.payroll.payroll"),path:"EmployeePayroll"}]},{default:e(()=>[o(w,{url:"employee/payroll/salary-structures/",name:"EmployeePayrollSalaryStructure",title:n.$trans("employee.payroll.salary_structure.salary_structure"),actions:s(D),"dropdown-actions":s(h),onToggleFilter:u[0]||(u[0]=t=>c.value=!c.value)},{moduleOption:e(()=>[o(z)]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[o(F,{appear:"",visibility:c.value},{default:e(()=>[o(Q,{onRefresh:u[1]||(u[1]=t=>s(v).emit("listItems")),onHide:u[2]||(u[2]=t=>c.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[o(F,{appear:"",visibility:!0},{default:e(()=>[o(L,{header:_.headers,meta:_.meta,module:"employee.payroll.salary_structure",onRefresh:u[4]||(u[4]=t=>s(v).emit("listItems"))},{actionButton:e(()=>[s($)("salary-structure:create")?(d(),f(H,{key:0,onClick:u[3]||(u[3]=t=>s(g).push({name:"EmployeePayrollSalaryStructureCreate"}))},{default:e(()=>[r(a(n.$trans("global.add",{attribute:n.$trans("employee.payroll.salary_structure.salary_structure")})),1)]),_:1})):b("",!0)]),default:e(()=>[(d(!0),j(q,null,U(_.data,t=>(d(),f(T,{key:t.uuid,onDoubleClick:P=>s(g).push({name:"EmployeePayrollSalaryStructureShow",params:{uuid:t.uuid}})},{default:e(()=>[o(y,{name:"employee"},{default:e(()=>[r(a(t.employee.name)+" ("+a(t.employee.codeNumber)+") ",1)]),_:2},1024),o(y,{name:"designation"},{default:e(()=>[r(a(t.employee.designation),1)]),_:2},1024),o(y,{name:"template"},{default:e(()=>[r(a(t.template.name)+" ",1),t.template.hasHourlyPayroll?(d(),f(B,{key:0,block:""},{default:e(()=>[r(a(n.$trans("employee.payroll.salary_structure.hourly_pay_amount",{attribute:t.hourlyPay.formatted})),1)]),_:2},1024)):b("",!0)]),_:2},1024),o(y,{name:"effectiveDate"},{default:e(()=>[r(a(t.effectiveDate.formatted),1)]),_:2},1024),o(y,{name:"netEarning"},{default:e(()=>[k("span",W,a(t.netEarning.formatted),1)]),_:2},1024),o(y,{name:"netDeduction"},{default:e(()=>[k("span",X,a(t.netDeduction.formatted),1),o(B,{block:"",class:"text-danger"},{default:e(()=>[r(a(n.$trans("employee.payroll.salary_structure.props.net_employee_contribution"))+" "+a(t.netEmployeeContribution.formatted),1)]),_:2},1024)]),_:2},1024),o(y,{name:"netSalary"},{default:e(()=>[k("span",Y,a(t.netSalary.formatted),1)]),_:2},1024),o(y,{name:"createdAt"},{default:e(()=>[r(a(t.createdAt.formatted),1)]),_:2},1024),o(y,{name:"action"},{default:e(()=>[o(S,null,{default:e(()=>[o(C,{icon:"fas fa-arrow-circle-right",onClick:P=>s(g).push({name:"EmployeePayrollSalaryStructureShow",params:{uuid:t.uuid}})},{default:e(()=>[r(a(n.$trans("general.show")),1)]),_:2},1032,["onClick"]),s($)("salary-structure:edit")?(d(),f(C,{key:0,icon:"fas fa-edit",onClick:P=>s(g).push({name:"EmployeePayrollSalaryStructureEdit",params:{uuid:t.uuid}})},{default:e(()=>[r(a(n.$trans("general.edit")),1)]),_:2},1032,["onClick"])):b("",!0),s($)("salary-structure:create")?(d(),f(C,{key:1,icon:"fas fa-copy",onClick:P=>s(g).push({name:"EmployeePayrollSalaryStructureDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[r(a(n.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):b("",!0),s($)("salary-structure:delete")?(d(),f(C,{key:2,icon:"fas fa-trash",onClick:P=>s(v).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[r(a(n.$trans("general.delete")),1)]),_:2},1032,["onClick"])):b("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{te as default};
