import{u as E,l as N,n as O,r as a,q as y,o as i,w as e,d as u,e as n,b as D,h as z,j as G,y as C,m as J,f as o,B as K,a as w,F as x,v as I,t as s,am as Q,s as f}from"./app-DvIo72ZO.js";const W={class:"grid grid-cols-3 gap-6"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},ee={__name:"Filter",emits:["hide"],setup(P,{emit:b}){const v=E(),$=b,k={codeNumber:"",types:[],startDate:"",endDate:""},_=N({...k}),g=N({types:[],isLoaded:!v.query.types});return O(async()=>{g.types=v.query.types?v.query.types.split(","):[],g.isLoaded=!0}),(d,c)=>{const l=a("BaseInput"),r=a("BaseSelectSearch"),B=a("DatePicker"),F=a("FilterForm");return i(),y(F,{"init-form":k,form:_,multiple:["types","batches"],onHide:c[4]||(c[4]=p=>$("hide"))},{default:e(()=>[u("div",W,[u("div",X,[n(l,{type:"text",modelValue:_.codeNumber,"onUpdate:modelValue":c[0]||(c[0]=p=>_.codeNumber=p),name:"codeNumber",label:d.$trans("communication.announcement.props.code_number")},null,8,["modelValue","label"])]),u("div",Y,[g.isLoaded?(i(),y(r,{key:0,multiple:"",name:"types",label:d.$trans("global.select",{attribute:d.$trans("communication.announcement.type.type")}),modelValue:_.types,"onUpdate:modelValue":c[1]||(c[1]=p=>_.types=p),"value-prop":"uuid","init-search":g.types,"search-action":"option/list","additional-search-query":{type:"announcement_type"}},null,8,["label","modelValue","init-search"])):D("",!0)]),u("div",Z,[n(B,{start:_.startDate,"onUpdate:start":c[2]||(c[2]=p=>_.startDate=p),end:_.endDate,"onUpdate:end":c[3]||(c[3]=p=>_.endDate=p),name:"dateBetween",as:"range",label:d.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},te={class:"grid grid-cols-1 gap-4 px-4 pt-4 md:grid-cols-2 lg:grid-cols-3"},ne=["onClick"],ae={class:"px-2 py-2 text-gray-800 dark:text-gray-400"},oe={class:"flex items-center justify-between"},se={class:"font-medium"},ie={class:"mt-2 flex justify-between"},le={class:"text-sm"},re={class:"mt-2 text-xs"},ue={class:"text-xl font-semibold"},de={name:"CommunicationAnnouncementList"},ce=Object.assign(de,{setup(P){const b=z(),v=G("emitter");let $=["filter"];C("communication:config")&&$.push("config"),C("announcement:create")&&$.unshift("create");let k=[];C("announcement:export")&&(k=["print","pdf","excel"]);const _="communication/announcement/",g=J(!1),d=N({}),c=l=>{Object.assign(d,l)};return(l,r)=>{const B=a("PageHeaderAction"),F=a("PageHeader"),p=a("ParentTransition"),S=a("CardView"),L=a("Pagination"),T=a("CardList"),h=a("DataCell"),M=a("TextMuted"),A=a("FloatingMenuItem"),R=a("FloatingMenu"),j=a("DataRow"),q=a("BaseButton"),H=a("DataTable"),U=a("ListItem");return i(),y(U,{"init-url":_,"additional-query":{},onSetItems:c},{header:e(()=>[n(F,{title:l.$trans("communication.announcement.announcement"),navs:[{label:l.$trans("communication.communication"),path:"Communication"}]},{default:e(()=>[n(B,{url:"communication/announcements/",name:"CommunicationAnnouncement",title:l.$trans("communication.announcement.announcement"),actions:o($),"dropdown-actions":o(k),"config-path":"CommunicationConfig",onToggleFilter:r[0]||(r[0]=t=>g.value=!g.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[n(p,{appear:"",visibility:g.value},{default:e(()=>[n(ee,{onRefresh:r[1]||(r[1]=t=>o(v).emit("listItems")),onHide:r[2]||(r[2]=t=>g.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[o(K)(["student","guardian"],"any")?(i(),y(p,{key:0,appear:"",visibility:!0},{default:e(()=>[n(T,{header:d.headers,meta:d.meta},{content:e(()=>[u("div",ue,s(l.$trans("dashboard.nothing_to_show")),1)]),default:e(()=>[u("div",te,[(i(!0),w(x,null,I(d.data,t=>(i(),w("div",{key:t.uuid,class:"cursor-pointer",onClick:m=>o(b).push({name:"CommunicationAnnouncementShow",params:{uuid:t.uuid}})},[n(S,{"no-padding":""},{default:e(()=>[u("div",ae,[u("div",oe,[u("span",se,s(t.title),1)]),u("div",ie,[u("span",le,s(t.codeNumber),1),u("span",{class:"px-2 py-1 text-gray-50 text-xs rounded-lg",style:Q(`background-color: ${t.type.color}`)},s(t.type.name),5)]),u("p",re,s(t.publishedAt.formatted),1)])]),_:2},1024)],8,ne))),128))]),u("div",null,[n(L,{"card-view":"",meta:d.meta,onRefresh:r[3]||(r[3]=t=>o(v).emit("listItems"))},null,8,["meta"])])]),_:1},8,["header","meta"])]),_:1})):(i(),y(p,{key:1,appear:"",visibility:!0},{default:e(()=>[n(H,{header:d.headers,meta:d.meta,module:"communication.announcement",onRefresh:r[5]||(r[5]=t=>o(v).emit("listItems"))},{actionButton:e(()=>[o(C)("announcement:create")?(i(),y(q,{key:0,onClick:r[4]||(r[4]=t=>o(b).push({name:"CommunicationAnnouncementCreate"}))},{default:e(()=>[f(s(l.$trans("global.add",{attribute:l.$trans("communication.announcement.announcement")})),1)]),_:1})):D("",!0)]),default:e(()=>[(i(!0),w(x,null,I(d.data,t=>(i(),y(j,{key:t.uuid,onDoubleClick:m=>o(b).push({name:"CommunicationAnnouncementShow",params:{uuid:t.uuid}})},{default:e(()=>[n(h,{name:"codeNumber"},{default:e(()=>[f(s(t.codeNumber),1)]),_:2},1024),n(h,{name:"title"},{default:e(()=>[f(s(t.titleExcerpt),1)]),_:2},1024),n(h,{name:"type"},{default:e(()=>{var m;return[f(s(((m=t.type)==null?void 0:m.name)||"-"),1)]}),_:2},1024),n(h,{name:"employee"},{default:e(()=>{var m;return[f(s(((m=t.employee)==null?void 0:m.name)||"-")+" ",1),n(M,{block:""},{default:e(()=>{var V;return[f(s((V=t.employee)==null?void 0:V.codeNumber),1)]}),_:2},1024)]}),_:2},1024),n(h,{name:"audience"},{default:e(()=>[(i(!0),w(x,null,I(t.audienceTypes,m=>(i(),w("div",null,s(m),1))),256))]),_:2},1024),n(h,{name:"createdAt"},{default:e(()=>[f(s(t.createdAt.formatted),1)]),_:2},1024),n(h,{name:"action"},{default:e(()=>[n(R,null,{default:e(()=>[n(A,{icon:"fas fa-arrow-circle-right",onClick:m=>o(b).push({name:"CommunicationAnnouncementShow",params:{uuid:t.uuid}})},{default:e(()=>[f(s(l.$trans("general.show")),1)]),_:2},1032,["onClick"]),o(C)("announcement:edit")?(i(),y(A,{key:0,icon:"fas fa-edit",onClick:m=>o(b).push({name:"CommunicationAnnouncementEdit",params:{uuid:t.uuid}})},{default:e(()=>[f(s(l.$trans("general.edit")),1)]),_:2},1032,["onClick"])):D("",!0),o(C)("announcement:create")?(i(),y(A,{key:1,icon:"fas fa-copy",onClick:m=>o(b).push({name:"CommunicationAnnouncementDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[f(s(l.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):D("",!0),o(C)("announcement:delete")?(i(),y(A,{key:2,icon:"fas fa-trash",onClick:m=>o(v).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[f(s(l.$trans("general.delete")),1)]),_:2},1032,["onClick"])):D("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1}))]),_:1})}}});export{ce as default};
