import{u as A,h as D,j as h,l as C,r as p,a as g,o as n,e as r,w as s,f as e,q as c,b as _,d,s as o,t as l,am as S,x as u,F as k,v as V}from"./app-DvIo72ZO.js";const j={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},N={class:"flex items-center"},T={class:"d-flex flex-wrap gap-1"},R={name:"AIProviderShow"},F=Object.assign(R,{setup($){const b=A(),x=D(),t=h("$trans"),v=h("moment"),I="ai/providers/",a=C({}),P=f=>{Object.assign(a,f)};return(f,y)=>{const w=p("PageHeaderAction"),B=p("PageHeader"),i=p("BaseDataView"),H=p("BaseCard"),M=p("ShowItem"),Y=p("ParentTransition");return n(),g(k,null,[r(B,{title:e(t)(e(b).meta.trans,{attribute:e(t)(e(b).meta.label)}),navs:[{label:e(t)("ai.ai"),path:"AIIndex"},{label:e(t)("ai.provider.providers"),path:"AIProviders"}]},{default:s(()=>[r(w,{name:"AIProvider",title:e(t)("ai.provider.provider"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),r(Y,{appear:"",visibility:!0},{default:s(()=>[r(M,{"init-url":I,uuid:e(b).params.uuid,onSetItem:P,onRedirectTo:y[0]||(y[0]=m=>e(x).push({name:"AIProviders"}))},{default:s(()=>[a.uuid?(n(),c(H,{key:0},{title:s(()=>[o(l(a.name),1)]),default:s(()=>[d("dl",j,[r(i,{label:e(t)("ai.provider.props.name")},{default:s(()=>[o(l(a.name),1)]),_:1},8,["label"]),r(i,{label:e(t)("ai.provider.props.type")},{default:s(()=>[d("div",N,[d("i",{class:u([a.type_icon,"me-2"]),style:S({color:a.type_color})},null,6),o(" "+l(a.type_name),1)])]),_:1},8,["label"]),a.description?(n(),c(i,{key:0,label:e(t)("ai.provider.props.description")},{default:s(()=>[o(l(a.description),1)]),_:1},8,["label"])):_("",!0),r(i,{label:e(t)("ai.provider.props.api_endpoint")},{default:s(()=>[o(l(a.api_endpoint),1)]),_:1},8,["label"]),r(i,{label:e(t)("ai.provider.props.priority")},{default:s(()=>[o(l(a.priority),1)]),_:1},8,["label"]),r(i,{label:e(t)("ai.provider.props.status")},{default:s(()=>[d("span",{class:u(["badge",a.is_active?"bg-success":"bg-secondary"])},l(a.is_active?e(t)("general.active"):e(t)("general.inactive")),3)]),_:1},8,["label"]),r(i,{label:e(t)("general.scope")},{default:s(()=>[d("span",{class:u(["badge",a.is_global?"bg-primary":"bg-info"])},l(a.is_global?e(t)("general.global"):e(t)("general.team")),3)]),_:1},8,["label"]),r(i,{label:e(t)("ai.provider.props.api_key")},{default:s(()=>[d("span",{class:u(["badge",a.has_api_key?"bg-success":"bg-warning"])},l(a.has_api_key?e(t)("general.configured"):e(t)("general.not_configured")),3)]),_:1},8,["label"]),a.capabilities&&a.capabilities.length>0?(n(),c(i,{key:1,label:e(t)("ai.provider.props.capabilities")},{default:s(()=>[d("div",T,[(n(!0),g(k,null,V(a.capabilities,m=>(n(),g("span",{key:m,class:"badge bg-secondary"},l(m),1))),128))])]),_:1},8,["label"])):_("",!0),r(i,{label:e(t)("general.created_at")},{default:s(()=>[o(l(e(v)(a.created_at).format("MMMM DD, YYYY [at] HH:mm")),1)]),_:1},8,["label"]),a.updated_at?(n(),c(i,{key:2,label:e(t)("general.updated_at")},{default:s(()=>[o(l(e(v)(a.updated_at).format("MMMM DD, YYYY [at] HH:mm")),1)]),_:1},8,["label"])):_("",!0)])]),_:1})):_("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{F as default};
