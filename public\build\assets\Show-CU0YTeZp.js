import{i as C,u as P,h as T,l as V,r as n,a as c,o as u,e as a,w as e,f as m,q as f,b,d as H,s as l,t as o,F as g,v as I,y as M}from"./app-DvIo72ZO.js";const N={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},D={name:"ExamAssessmentShow"},F=Object.assign(D,{setup(R){C();const p=P(),_=T(),B={},$="exam/assessment/",t=V({...B}),x=s=>{Object.assign(t,s)};return(s,d)=>{const A=n("PageHeaderAction"),h=n("PageHeader"),i=n("BaseDataView"),v=n("TextMuted"),w=n("BaseButton"),k=n("ShowButton"),E=n("BaseCard"),S=n("ShowItem"),y=n("ParentTransition");return u(),c(g,null,[a(h,{title:s.$trans(m(p).meta.trans,{attribute:s.$trans(m(p).meta.label)}),navs:[{label:s.$trans("exam.exam"),path:"Exam"},{label:s.$trans("exam.assessment.assessment"),path:"ExamAssessmentList"}]},{default:e(()=>[a(A,{name:"ExamAssessment",title:s.$trans("exam.assessment.assessment"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(y,{appear:"",visibility:!0},{default:e(()=>[a(S,{"init-url":$,uuid:m(p).params.uuid,onSetItem:x,onRedirectTo:d[1]||(d[1]=r=>m(_).push({name:"ExamAssessment"}))},{default:e(()=>[t.uuid?(u(),f(E,{key:0},{title:e(()=>[l(o(t.name),1)]),footer:e(()=>[a(k,null,{default:e(()=>[m(M)("exam-assessment:edit")?(u(),f(w,{key:0,design:"primary",onClick:d[0]||(d[0]=r=>m(_).push({name:"ExamAssessmentEdit",params:{uuid:t.uuid}}))},{default:e(()=>[l(o(s.$trans("general.edit")),1)]),_:1})):b("",!0)]),_:1})]),default:e(()=>[H("dl",N,[a(i,{label:s.$trans("exam.assessment.props.name")},{default:e(()=>[l(o(t.name),1)]),_:1},8,["label"]),a(i,{class:"col-span-1 sm:col-span-2",label:s.$trans("exam.assessment.props.records")},{default:e(()=>[(u(!0),c(g,null,I(t.records,r=>(u(),c("div",null,[l(o(r.name)+" "+o(r.code)+" : ",1),a(v,null,{default:e(()=>[l(o(r.maxMark+"("+r.passingMark+")"),1)]),_:2},1024)]))),256))]),_:1},8,["label"]),a(i,{class:"col-span-1 sm:col-span-2",label:s.$trans("exam.assessment.props.description")},{default:e(()=>[l(o(t.description),1)]),_:1},8,["label"]),a(i,{label:s.$trans("general.created_at")},{default:e(()=>[l(o(t.createdAt.formatted),1)]),_:1},8,["label"]),a(i,{label:s.$trans("general.updated_at")},{default:e(()=>[l(o(t.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):b("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{F as default};
