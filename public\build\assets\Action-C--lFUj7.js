import{I as f,l as _,r as i,q as B,o as b,w as c,d,e as s,f as a,u as U,a as $,F as v}from"./app-DvIo72ZO.js";const g={class:"grid grid-cols-3 gap-6"},H={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},N={class:"col-span-3 sm:col-span-1"},F={class:"col-span-3 sm:col-span-1"},P={class:"col-span-3 sm:col-span-1"},y={class:"col-span-3 sm:col-span-1"},A={name:"HostelBlockForm"},T=Object.assign(A,{setup(V){const m={name:"",alias:"",contactNumber:"",contactEmail:"",address:"",description:""},r="hostel/block/",l=f(r),t=_({...m});return(n,e)=>{const p=i("BaseInput"),u=i("BaseTextarea"),k=i("FormAction");return b(),B(k,{"init-url":r,"init-form":m,form:t,redirect:"HostelBlock"},{default:c(()=>[d("div",g,[d("div",H,[s(p,{type:"text",modelValue:t.name,"onUpdate:modelValue":e[0]||(e[0]=o=>t.name=o),name:"name",label:n.$trans("hostel.block.props.name"),error:a(l).name,"onUpdate:error":e[1]||(e[1]=o=>a(l).name=o),autofocus:""},null,8,["modelValue","label","error"])]),d("div",E,[s(p,{type:"text",modelValue:t.alias,"onUpdate:modelValue":e[2]||(e[2]=o=>t.alias=o),name:"alias",label:n.$trans("hostel.block.props.alias"),error:a(l).alias,"onUpdate:error":e[3]||(e[3]=o=>a(l).alias=o)},null,8,["modelValue","label","error"])]),d("div",N,[s(p,{type:"text",modelValue:t.contactNumber,"onUpdate:modelValue":e[4]||(e[4]=o=>t.contactNumber=o),name:"contactNumber",label:n.$trans("hostel.block.props.contact_number"),error:a(l).contactNumber,"onUpdate:error":e[5]||(e[5]=o=>a(l).contactNumber=o)},null,8,["modelValue","label","error"])]),d("div",F,[s(p,{type:"text",modelValue:t.contactEmail,"onUpdate:modelValue":e[6]||(e[6]=o=>t.contactEmail=o),name:"contactEmail",label:n.$trans("hostel.block.props.contact_email"),error:a(l).contactEmail,"onUpdate:error":e[7]||(e[7]=o=>a(l).contactEmail=o)},null,8,["modelValue","label","error"])]),d("div",P,[s(u,{modelValue:t.address,"onUpdate:modelValue":e[8]||(e[8]=o=>t.address=o),name:"address",label:n.$trans("hostel.block.props.address"),error:a(l).address,"onUpdate:error":e[9]||(e[9]=o=>a(l).address=o)},null,8,["modelValue","label","error"])]),d("div",y,[s(u,{modelValue:t.description,"onUpdate:modelValue":e[10]||(e[10]=o=>t.description=o),name:"description",label:n.$trans("hostel.block.props.description"),error:a(l).description,"onUpdate:error":e[11]||(e[11]=o=>a(l).description=o)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),I={name:"HostelBlockAction"},w=Object.assign(I,{setup(V){const m=U();return(r,l)=>{const t=i("PageHeaderAction"),n=i("PageHeader"),e=i("ParentTransition");return b(),$(v,null,[s(n,{title:r.$trans(a(m).meta.trans,{attribute:r.$trans(a(m).meta.label)}),navs:[{label:r.$trans("hostel.hostel"),path:"Hostel"},{label:r.$trans("hostel.block.block"),path:"HostelBlockList"}]},{default:c(()=>[s(t,{name:"HostelBlock",title:r.$trans("hostel.block.block"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),s(e,{appear:"",visibility:!0},{default:c(()=>[s(T)]),_:1})],64)}}});export{w as default};
