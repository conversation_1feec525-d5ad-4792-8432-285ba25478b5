import{i as U,u as A,h as E,c as V,l as M,m as j,r as u,z as q,a as _,o as i,d as a,e as p,w as l,s as b,t as m,F as x,v as L,b as g,am as G,x as J,A as K,N as Q,an as D,ao as P,ap as W,j as z,q as v,f as n}from"./app-DvIo72ZO.js";import{_ as X}from"./Asset-BZY-RXy4.js";const Y={class:"flex justify-center space-x-4"},Z={class:"mt-4 grid grid-cols-1 gap-4"},ee={class:"col-span-1"},te={class:"h-24 flex border border-gray-200 dark:border-gray-700 shadow-lg items-center justify-center"},se={key:0,class:"h-1 w-full bg-gray-200"},ae={class:"mt-4 grid grid-cols-1 gap-4"},oe={class:"col-span-1"},le={class:"relative h-72 flex border border-gray-200 dark:border-gray-700 shadow-lg items-center justify-center"},ne={class:"absolute z-50 right-2 top-2"},ie={class:"flex gap-2"},re=["onClick"],ce=["src"],de={name:"SiteBlockSliderImageForm"},ue=Object.assign(de,{props:{block:{type:Object,default:()=>({})}},setup(T){const y=U();A(),E();const k=T,r=V(()=>y.getters["config/config"]("system.postMaxSize")),$=V(()=>c.media.filter(e=>e.status==="waiting")),f=j(null);j(!1);const c=M({media:[],images:k.block.sliderImages}),s=async e=>{var o;if(typeof((o=f.value)==null?void 0:o.files)!="object"){toast.error($trans("general.errors.invalid_action"));return}for(let d=0;d<f.value.files.length;d++)f.value.files[d].size>r.value?toast.error($trans("general.errors.file_too_large")):c.media.push({uuid:null,file:f.value.files[d],status:"waiting",msg:"",progress:0});S()},S=async()=>{const e=W.CancelToken;c.media.forEach((o,d)=>{if(o.status==="waiting"){let h=new FormData;h.append("file",o.file),D({url:"/app/site/blocks/"+k.block.uuid+"/slider-images",method:"POST",data:h,upload:!0,onUploadProgress:t=>{o.progress=Math.round(t.loaded*100/t.total)},cancelToken:new e(t=>{o.cancel=t})}).then(t=>{o.status="uploaded",c.images.push(t.image)}).catch(t=>{if(t===void 0)c.media.splice(d,1);else{let w=P(t);o.status="error",o.msg=w.message}})}})},B=async e=>{await Q()&&await D({url:"/app/site/blocks/"+k.block.uuid+"/slider-images/"+e.uuid,method:"DELETE"}).then(o=>{c.images=c.images.filter(d=>d.uuid!==e.uuid)}).catch(o=>{P(o)})};return(e,o)=>{const d=u("BaseButton"),h=q("tooltip");return i(),_(x,null,[a("div",Y,[p(d,{as:"label"},{default:l(()=>[o[0]||(o[0]=a("i",{class:"fas fa-upload mr-2"},null,-1)),b(" "+m(e.$trans("global.upload",{attribute:e.$trans("site.block.props.slider_image")}))+" ",1),a("input",{multiple:"",accept:"image/jpeg, image/png, image/webp",ref_key:"file",ref:f,type:"file",onChange:s,name:"images",id:"sliderImage",class:"hidden"},null,544)]),_:1})]),a("div",Z,[(i(!0),_(x,null,L($.value,t=>(i(),_("div",ee,[a("div",te,[a("div",null,[b(m(t.file.name)+" ",1),t.status=="waiting"?(i(),_("div",se,[a("div",{class:J(["h-1",{"bg-info":t.progress<100,"bg-success":t.progress==100}]),style:G({width:t.progress+"%"})},null,6)])):g("",!0)])])]))),256))]),a("div",ae,[(i(!0),_(x,null,L(c.images,t=>(i(),_("div",oe,[a("div",le,[a("div",ne,[a("div",ie,[K(a("i",{class:"far fa-circle-xmark cursor-pointer text-gray-500",onClick:w=>B(t)},null,8,re),[[h,e.$trans("global.delete",{attribute:e.$trans("gallery.props.image")})]])])]),a("img",{src:t.url,class:"px-4 py-2 max-h-full object-cover"},null,8,ce)])]))),256))])],64)}}}),pe={class:"space-y-2"},me={class:"flex items-center"},ge={class:"space-y-4"},_e={class:"px-4 py-4"},fe={class:"text-2xl font-bold text-gray-900 dark:text-gray-400"},be={key:0,class:"mt-2 font-semibold text-gray-800 dark:text-gray-500"},ke={name:"SiteBlockShow"},ye=Object.assign(ke,{setup(T){const y=A(),k=E(),r=z("$trans");z("emitter");const $={uuid:""},f="site/block/",c=j(!1),s=M({...$}),S=B=>{Object.assign(s,B)};return(B,e)=>{const o=u("BaseButton"),d=u("PageHeaderAction"),h=u("PageHeader"),t=u("BaseBadge"),w=u("ListItemView"),H=u("ListContainerVertical"),C=u("BaseCard"),N=u("MarkdownContent"),O=u("DetailLayoutVertical"),R=u("ShowItem"),F=u("ParentTransition");return i(),_(x,null,[s.uuid?(i(),v(h,{key:0,title:n(r)(n(y).meta.trans,{attribute:n(r)(n(y).meta.label)}),navs:[{label:n(r)("site.site"),path:"Site"},{label:n(r)("site.block.block"),path:"SiteBlockList"}]},{default:l(()=>[p(d,{name:"SiteBlock",title:n(r)("site.block.block"),actions:["list"]},{default:l(()=>[p(o,{design:"white",onClick:e[0]||(e[0]=I=>n(k).push({name:"SiteBlockEdit",params:{uuid:s.uuid}}))},{default:l(()=>e[4]||(e[4]=[a("i",{class:"fas fa-pen-to-square"},null,-1)])),_:1})]),_:1},8,["title"])]),_:1},8,["title","navs"])):g("",!0),p(F,{appear:"",visibility:!0},{default:l(()=>[p(R,{"init-url":f,uuid:n(y).params.uuid,onSetItem:S,onRedirectTo:e[2]||(e[2]=I=>n(k).push({name:"SiteBlock"})),refresh:c.value,onRefreshed:e[3]||(e[3]=I=>c.value=!1)},{default:l(()=>[s.uuid?(i(),v(O,{key:0},{detail:l(()=>[a("div",pe,[p(C,{"no-padding":"","no-content-padding":""},{title:l(()=>[a("div",me,[b(m(s.name)+" ",1),s.isSlider?(i(),v(t,{key:0,design:"info",class:"ml-2"},{default:l(()=>[b(m(n(r)("site.block.props.slider")),1)]),_:1})):g("",!0)])]),action:l(()=>e[5]||(e[5]=[])),default:l(()=>[p(H,null,{default:l(()=>[p(w,{label:n(r)("general.created_at")},{default:l(()=>[b(m(s.createdAt.formatted),1)]),_:1},8,["label"]),p(w,{label:n(r)("general.updated_at")},{default:l(()=>[b(m(s.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})])]),default:l(()=>[a("div",ge,[s.isSlider?g("",!0):(i(),v(C,{key:0,"no-padding":"","no-content-padding":"","bottom-content-padding":""},{default:l(()=>[s.uuid?(i(),v(X,{key:0,disabled:"",block:s,onRefreshItem:e[1]||(e[1]=I=>c.value=!0)},null,8,["block"])):g("",!0),a("div",_e,[a("h1",fe,m(s.title),1),s.subTitle?(i(),_("h2",be,m(s.subTitle),1)):g("",!0),p(N,{class:"mt-2",content:s.contentHtml},null,8,["content"])])]),_:1})),s.isSlider?(i(),v(C,{key:1},{title:l(()=>[b(m(n(r)("global.add",{attribute:n(r)("site.block.props.slider_image")})),1)]),default:l(()=>[p(ue,{block:s},null,8,["block"])]),_:1})):g("",!0)])]),_:1})):g("",!0)]),_:1},8,["uuid","refresh"])]),_:1})],64)}}});export{ye as default};
