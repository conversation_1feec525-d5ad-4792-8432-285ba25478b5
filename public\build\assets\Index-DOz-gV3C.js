import{l as E,r as s,q as g,o as p,w as e,d as w,e as a,h as M,j as N,y as c,m as B,f as o,a as S,F as U,v as O,s as m,t as u,b as I}from"./app-DvIo72ZO.js";const q={class:"grid grid-cols-3 gap-6"},z={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},J={__name:"Filter",emits:["hide"],setup(V,{emit:f}){const v=f,$={name:"",alias:""},r=E({...$});return(k,i)=>{const d=s("BaseInput"),y=s("FilterForm");return p(),g(y,{"init-form":$,form:r,onHide:i[2]||(i[2]=_=>v("hide"))},{default:e(()=>[w("div",q,[w("div",z,[a(d,{type:"text",modelValue:r.name,"onUpdate:modelValue":i[0]||(i[0]=_=>r.name=_),name:"name",label:k.$trans("employee.designation.props.name")},null,8,["modelValue","label"])]),w("div",G,[a(d,{type:"text",modelValue:r.alias,"onUpdate:modelValue":i[1]||(i[1]=_=>r.alias=_),name:"alias",label:k.$trans("employee.designation.props.alias")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},K={name:"EmployeeDesignationList"},W=Object.assign(K,{setup(V){const f=M(),v=N("emitter");let $=["filter"];c("designation:create")&&$.unshift("create");let r=[];c("designation:export")&&(r=["print","pdf","excel"]),c("designation:create")&&r.unshift("import");const k="employee/designation/",i=B(!1),d=B(!1),y=E({}),_=l=>{Object.assign(y,l)};return(l,n)=>{const h=s("PageHeaderAction"),H=s("PageHeader"),T=s("BaseImport"),F=s("ParentTransition"),b=s("DataCell"),C=s("FloatingMenuItem"),A=s("FloatingMenu"),P=s("DataRow"),R=s("BaseButton"),j=s("DataTable"),L=s("ListItem");return p(),g(L,{"init-url":k,onSetItems:_},{header:e(()=>[a(H,{title:l.$trans("employee.designation.designation"),navs:[{label:l.$trans("employee.employee"),path:"Employee"}]},{default:e(()=>[a(h,{url:"employee/designations/",name:"EmployeeDesignation",title:l.$trans("employee.designation.designation"),actions:o($),"dropdown-actions":o(r),onToggleFilter:n[0]||(n[0]=t=>i.value=!i.value),onToggleImport:n[1]||(n[1]=t=>d.value=!d.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),import:e(()=>[a(F,{appear:"",visibility:d.value},{default:e(()=>[a(T,{path:"employee/designations/import",onCancelled:n[2]||(n[2]=t=>d.value=!1),onHide:n[3]||(n[3]=t=>d.value=!1),onCompleted:n[4]||(n[4]=t=>o(v).emit("listItems"))})]),_:1},8,["visibility"])]),filter:e(()=>[a(F,{appear:"",visibility:i.value},{default:e(()=>[a(J,{onRefresh:n[5]||(n[5]=t=>o(v).emit("listItems")),onHide:n[6]||(n[6]=t=>i.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[a(F,{appear:"",visibility:!0},{default:e(()=>[a(j,{header:y.headers,meta:y.meta,module:"employee.designation",onRefresh:n[8]||(n[8]=t=>o(v).emit("listItems"))},{actionButton:e(()=>[o(c)("designation:create")?(p(),g(R,{key:0,onClick:n[7]||(n[7]=t=>o(f).push({name:"EmployeeDesignationCreate"}))},{default:e(()=>[m(u(l.$trans("global.add",{attribute:l.$trans("employee.designation.designation")})),1)]),_:1})):I("",!0)]),default:e(()=>[(p(!0),S(U,null,O(y.data,t=>(p(),g(P,{key:t.uuid,onDoubleClick:D=>o(f).push({name:"EmployeeDesignationShow",params:{uuid:t.uuid}})},{default:e(()=>[a(b,{name:"name"},{default:e(()=>[m(u(t.name),1)]),_:2},1024),a(b,{name:"alias"},{default:e(()=>[m(u(t.alias),1)]),_:2},1024),a(b,{name:"parent"},{default:e(()=>[m(u(t.parent?t.parent.name:"-"),1)]),_:2},1024),a(b,{name:"createdAt"},{default:e(()=>[m(u(t.createdAt.formatted),1)]),_:2},1024),a(b,{name:"action"},{default:e(()=>[a(A,null,{default:e(()=>[a(C,{icon:"fas fa-arrow-circle-right",onClick:D=>o(f).push({name:"EmployeeDesignationShow",params:{uuid:t.uuid}})},{default:e(()=>[m(u(l.$trans("general.show")),1)]),_:2},1032,["onClick"]),o(c)("designation:edit")?(p(),g(C,{key:0,icon:"fas fa-edit",onClick:D=>o(f).push({name:"EmployeeDesignationEdit",params:{uuid:t.uuid}})},{default:e(()=>[m(u(l.$trans("general.edit")),1)]),_:2},1032,["onClick"])):I("",!0),o(c)("designation:create")?(p(),g(C,{key:1,icon:"fas fa-copy",onClick:D=>o(f).push({name:"EmployeeDesignationDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[m(u(l.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):I("",!0),o(c)("designation:delete")?(p(),g(C,{key:2,icon:"fas fa-trash",onClick:D=>o(v).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[m(u(l.$trans("general.delete")),1)]),_:2},1032,["onClick"])):I("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{W as default};
