import{u as A,l as S,n as H,r as l,q as _,o as v,w as e,d as $,e as t,h as L,j as O,y as g,m as E,f as d,a as Q,F as z,v as G,s as p,t as m,b as w,aS as J}from"./app-DvIo72ZO.js";const K={class:"grid grid-cols-3 gap-6"},W={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3 sm:col-span-1"},ee={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(F,{emit:y}){A();const q=y,b=F,C={codeNumber:"",startDate:"",endDate:"",inventory:"",vendors:[],places:[]},r=S({...C}),f=S({vendors:b.preRequisites.vendors,inventories:b.preRequisites.inventories,places:b.preRequisites.places}),V=S({isLoaded:!0});return H(async()=>{V.isLoaded=!0}),(c,s)=>{const B=l("BaseInput"),I=l("BaseSelect"),o=l("DatePicker"),u=l("FilterForm");return v(),_(u,{"init-form":C,form:r,multiple:["vendors","places"],onHide:s[6]||(s[6]=i=>q("hide"))},{default:e(()=>[$("div",K,[$("div",W,[t(B,{type:"text",modelValue:r.codeNumber,"onUpdate:modelValue":s[0]||(s[0]=i=>r.codeNumber=i),name:"codeNumber",label:c.$trans("inventory.stock_requisition.props.code_number")},null,8,["modelValue","label"])]),$("div",X,[t(I,{modelValue:r.inventory,"onUpdate:modelValue":s[1]||(s[1]=i=>r.inventory=i),name:"inventory","label-prop":"name","value-prop":"uuid",label:c.$trans("inventory.inventory"),options:f.inventories},null,8,["modelValue","label","options"])]),$("div",Y,[t(I,{multiple:"",modelValue:r.vendors,"onUpdate:modelValue":s[2]||(s[2]=i=>r.vendors=i),name:"vendors","label-prop":"name","value-prop":"uuid",label:c.$trans("inventory.vendor"),options:f.vendors},null,8,["modelValue","label","options"])]),$("div",Z,[t(I,{multiple:"",modelValue:r.places,"onUpdate:modelValue":s[3]||(s[3]=i=>r.places=i),name:"places","label-prop":"fullName","value-prop":"uuid",label:c.$trans("inventory.place"),options:f.places},null,8,["modelValue","label","options"])]),$("div",x,[t(o,{start:r.startDate,"onUpdate:start":s[4]||(s[4]=i=>r.startDate=i),end:r.endDate,"onUpdate:end":s[5]||(s[5]=i=>r.endDate=i),name:"dateBetween",as:"range",label:c.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},te={name:"InventoryStockRequisitionList"},oe=Object.assign(te,{setup(F){const y=L(),q=O("emitter");let b=["filter"];g("inventory:config")&&b.push("config"),g("stock-requisition:create")&&b.unshift("create");let C=[];g("stock-requisition:export")&&(C=["print","pdf","excel"]);const r="inventory/stockRequisition/",f=E(!1),V=S({inventories:[],vendors:[],places:[]}),c=S({}),s=o=>{Object.assign(V,o)},B=o=>{Object.assign(c,o)},I=o=>{let u="/app/inventory/stock-requisitions/"+o.uuid+"/export",i={};window.open(J(u,i),"_blank").focus()};return(o,u)=>{const i=l("PageHeaderAction"),P=l("PageHeader"),N=l("ParentTransition"),h=l("TextMuted"),k=l("DataCell"),D=l("FloatingMenuItem"),R=l("FloatingMenu"),T=l("DataRow"),M=l("BaseButton"),U=l("DataTable"),j=l("ListItem");return v(),_(j,{"init-url":r,"pre-requisites":!0,onSetPreRequisites:s,onSetItems:B},{header:e(()=>[t(P,{title:o.$trans("inventory.stock_requisition.stock_requisition"),navs:[{label:o.$trans("inventory.inventory"),path:"Inventory"}]},{default:e(()=>[t(i,{url:"inventory/stock-requisitions/",name:"InventoryStockRequisition",title:o.$trans("inventory.stock_requisition.stock_requisition"),actions:d(b),"dropdown-actions":d(C),"config-path":"InventoryConfig",onToggleFilter:u[0]||(u[0]=n=>f.value=!f.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(N,{appear:"",visibility:f.value},{default:e(()=>[t(ee,{onRefresh:u[1]||(u[1]=n=>d(q).emit("listItems")),"pre-requisites":V,onHide:u[2]||(u[2]=n=>f.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:e(()=>[t(N,{appear:"",visibility:!0},{default:e(()=>[t(U,{header:c.headers,meta:c.meta,module:"inventory.stock_requisition",onRefresh:u[4]||(u[4]=n=>d(q).emit("listItems"))},{actionButton:e(()=>[d(g)("stock-requisition:create")?(v(),_(M,{key:0,onClick:u[3]||(u[3]=n=>d(y).push({name:"InventoryStockRequisitionCreate"}))},{default:e(()=>[p(m(o.$trans("global.add",{attribute:o.$trans("inventory.stock_requisition.stock_requisition")})),1)]),_:1})):w("",!0)]),default:e(()=>[(v(!0),Q(z,null,G(c.data,n=>(v(),_(T,{key:n.uuid,onDoubleClick:a=>d(y).push({name:"InventoryStockRequisitionShow",params:{uuid:n.uuid}})},{default:e(()=>[t(k,{name:"codeNumber"},{default:e(()=>[p(m(n.codeNumber)+" ",1),t(h,{block:""},{default:e(()=>{var a;return[p(m((a=n.inventory)==null?void 0:a.name),1)]}),_:2},1024)]),_:2},1024),t(k,{name:"vendor"},{default:e(()=>{var a;return[p(m(((a=n.vendor)==null?void 0:a.name)||"-"),1)]}),_:2},1024),t(k,{name:"date"},{default:e(()=>[p(m(n.date.formatted),1)]),_:2},1024),t(k,{name:"place"},{default:e(()=>{var a;return[p(m(((a=n.place)==null?void 0:a.fullName)||"-"),1)]}),_:2},1024),t(k,{name:"employee"},{default:e(()=>{var a;return[p(m(((a=n.employee)==null?void 0:a.name)||"-")+" ",1),n.employee?(v(),_(h,{key:0,block:""},{default:e(()=>[p(m(n.employee.designation),1)]),_:2},1024)):w("",!0)]}),_:2},1024),t(k,{name:"createdAt"},{default:e(()=>[p(m(n.createdAt.formatted),1)]),_:2},1024),t(k,{name:"action"},{default:e(()=>[t(R,null,{default:e(()=>[t(D,{icon:"fas fa-arrow-circle-right",onClick:a=>d(y).push({name:"InventoryStockRequisitionShow",params:{uuid:n.uuid}})},{default:e(()=>[p(m(o.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(D,{icon:"fas fa-arrow-circle-right",onClick:a=>I(n)},{default:e(()=>[p(m(o.$trans("global.print",{attribute:o.$trans("inventory.stock_requisition.slip")})),1)]),_:2},1032,["onClick"]),d(g)("stock-requisition:edit")?(v(),_(D,{key:0,icon:"fas fa-edit",onClick:a=>d(y).push({name:"InventoryStockRequisitionEdit",params:{uuid:n.uuid}})},{default:e(()=>[p(m(o.$trans("general.edit")),1)]),_:2},1032,["onClick"])):w("",!0),d(g)("stock-requisition:create")?(v(),_(D,{key:1,icon:"fas fa-copy",onClick:a=>d(y).push({name:"InventoryStockRequisitionDuplicate",params:{uuid:n.uuid}})},{default:e(()=>[p(m(o.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):w("",!0),d(g)("stock-requisition:delete")?(v(),_(D,{key:2,icon:"fas fa-trash",onClick:a=>d(q).emit("deleteItem",{uuid:n.uuid})},{default:e(()=>[p(m(o.$trans("general.delete")),1)]),_:2},1032,["onClick"])):w("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{oe as default};
