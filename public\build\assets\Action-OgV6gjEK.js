import{u as g,I as U,l as _,r as m,q as V,o as f,w as p,d,b as E,f as s,s as k,t as y,e as l,K as B,a as F,F as N}from"./app-DvIo72ZO.js";const O={class:"grid grid-cols-4 gap-6"},P={class:"col-span-4 sm:col-span-1"},D={class:"col-span-4 sm:col-span-1"},S={class:"col-span-4 sm:col-span-1"},j={class:"col-span-4 sm:col-span-1"},H={class:"col-span-4"},L={name:"EmployeeAttendanceTimesheetForm"},C=Object.assign(L,{setup(v){const u=g(),r={employee:"",date:"",inAt:"",outAt:"",remarks:""},A="employee/attendance/timesheet/",n=U(A),a=_({...r}),i=_({employee:"",isLoaded:!u.params.uuid}),h=o=>{var t,c;Object.assign(r,{employee:(t=o.employee)==null?void 0:t.uuid,date:o.date.value,inAt:o.inAtTime.at,outAt:o.outAtTime.at,remarks:o.remarks}),Object.assign(a,B(r)),i.employee=(c=o.employee)==null?void 0:c.uuid,i.isLoaded=!0};return(o,t)=>{const c=m("BaseSelectSearch"),b=m("DatePicker"),$=m("BaseTextarea"),T=m("FormAction");return f(),V(T,{"init-url":A,"init-form":r,form:a,"set-form":h,redirect:"EmployeeAttendanceTimesheet"},{default:p(()=>[d("div",O,[d("div",P,[i.isLoaded?(f(),V(c,{key:0,name:"employee",label:o.$trans("global.select",{attribute:o.$trans("employee.employee")}),modelValue:a.employee,"onUpdate:modelValue":t[0]||(t[0]=e=>a.employee=e),error:s(n).employee,"onUpdate:error":t[1]||(t[1]=e=>s(n).employee=e),"value-prop":"uuid","init-search":i.employee,"additional-search-query":{self:0},"search-key":"name","search-action":"employee/list"},{selectedOption:p(e=>[k(y(e.value.name)+" ("+y(e.value.codeNumber)+") ",1)]),listOption:p(e=>[k(y(e.option.name)+" ("+y(e.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","error","init-search"])):E("",!0)]),d("div",D,[l(b,{modelValue:a.date,"onUpdate:modelValue":t[2]||(t[2]=e=>a.date=e),name:"date",label:o.$trans("employee.attendance.timesheet.props.date"),"no-clear":"",error:s(n).date,"onUpdate:error":t[3]||(t[3]=e=>s(n).date=e)},null,8,["modelValue","label","error"])]),d("div",S,[l(b,{modelValue:a.inAt,"onUpdate:modelValue":t[4]||(t[4]=e=>a.inAt=e),name:"inAt",label:o.$trans("employee.attendance.timesheet.props.in_at"),as:"time",error:s(n).inAt,"onUpdate:error":t[5]||(t[5]=e=>s(n).inAt=e)},null,8,["modelValue","label","error"])]),d("div",j,[l(b,{modelValue:a.outAt,"onUpdate:modelValue":t[6]||(t[6]=e=>a.outAt=e),name:"outAt",label:o.$trans("employee.attendance.timesheet.props.out_at"),as:"time",error:s(n).outAt,"onUpdate:error":t[7]||(t[7]=e=>s(n).outAt=e)},null,8,["modelValue","label","error"])]),d("div",H,[l($,{modelValue:a.remarks,"onUpdate:modelValue":t[8]||(t[8]=e=>a.remarks=e),name:"remarks",label:o.$trans("employee.attendance.timesheet.props.remarks"),error:s(n).remarks,"onUpdate:error":t[9]||(t[9]=e=>s(n).remarks=e)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),q={name:"EmployeeAttendanceTimesheetAction"},I=Object.assign(q,{setup(v){const u=g();return(r,A)=>{const n=m("PageHeaderAction"),a=m("PageHeader"),i=m("ParentTransition");return f(),F(N,null,[l(a,{title:r.$trans(s(u).meta.trans,{attribute:r.$trans(s(u).meta.label)}),navs:[{label:r.$trans("employee.employee"),path:"Employee"},{label:r.$trans("employee.attendance.attendance"),path:"EmployeeAttendance"},{label:r.$trans("employee.attendance.timesheet.timesheet"),path:"EmployeeAttendanceTimesheetList"}]},{default:p(()=>[l(n,{name:"EmployeeAttendanceTimesheet",title:r.$trans("employee.attendance.timesheet.timesheet"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(i,{appear:"",visibility:!0},{default:p(()=>[l(C)]),_:1})],64)}}});export{I as default};
