import{u as T,j as q,l as V,I as L,n as A,r as o,q as F,o as b,w as a,d as k,e as t,b as j,s as c,t as r,f as h,i as N,y as O,m as S,a as B,F as R,v as E}from"./app-DvIo72ZO.js";const M={class:"grid grid-cols-3 gap-6"},W={class:"col-span-3 sm:col-span-1"},I={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},G={__name:"Filter",props:{initUrl:{type:String,default:""}},emits:["hide"],setup(C,{emit:v}){const _=T();q("moment");const w=v,g=C,f={date:"",batches:[],status:"all"},s=V({...f}),u=L(g.initUrl),l=V({isLoaded:!_.query.batches});return A(async()=>{l.batches=_.query.batches?_.query.batches.split(","):[],l.isLoaded=!0}),(d,e)=>{const p=o("DatePicker"),$=o("BaseSelectSearch"),D=o("CustomCheckbox"),y=o("FilterForm");return b(),F(y,{"init-form":f,multiple:["batches"],form:s,onHide:e[4]||(e[4]=n=>w("hide"))},{default:a(()=>[k("div",M,[k("div",W,[t(p,{modelValue:s.date,"onUpdate:modelValue":e[0]||(e[0]=n=>s.date=n),name:"date",as:"date",label:d.$trans("general.date")},null,8,["modelValue","label"])]),k("div",I,[l.isLoaded?(b(),F($,{key:0,multiple:"",name:"batches",label:d.$trans("global.select",{attribute:d.$trans("academic.batch.batch")}),modelValue:s.batches,"onUpdate:modelValue":e[1]||(e[1]=n=>s.batches=n),"value-prop":"uuid","init-search":l.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:a(n=>[c(r(n.value.course.name)+" "+r(n.value.name),1)]),listOption:a(n=>[c(r(n.option.course.nameWithTerm)+" "+r(n.option.name),1)]),_:1},8,["label","modelValue","init-search"])):j("",!0)]),k("div",z,[t(D,{label:d.$trans("student.attendance.status"),options:[{label:d.$trans("general.all"),value:"all"},{label:d.$trans("student.attendance.statuses.marked"),value:"marked"},{label:d.$trans("student.attendance.statuses.not_marked"),value:"not_marked"}],modelValue:s.status,"onUpdate:modelValue":e[2]||(e[2]=n=>s.status=n),error:h(u).status,"onUpdate:error":e[3]||(e[3]=n=>h(u).status=n)},null,8,["label","options","modelValue","error"])])])]),_:1},8,["form"])}}},J={name:"StudentReportDateWiseAttendance"},Q=Object.assign(J,{setup(C){const v=T(),_=N();let w=["filter"],g=[];O("student:list-attendance")&&(g=["print","pdf","excel"]);const f="student/report/",s=S(!0),u=S(!1),l=V({headers:[],data:[],meta:{total:0}}),d=async()=>{u.value=!0,await _.dispatch(f+"fetchReport",{name:"date-wise-attendance",params:v.query}).then(e=>{u.value=!1,Object.assign(l,e)}).catch(e=>{u.value=!1})};return A(async()=>{await d()}),(e,p)=>{const $=o("PageHeaderAction"),D=o("PageHeader"),y=o("ParentTransition"),n=o("TextMuted"),m=o("DataCell"),U=o("DataRow"),H=o("DataTable"),P=o("BaseCard");return b(),B(R,null,[t(D,{title:e.$trans(h(v).meta.label),navs:[{label:e.$trans("student.student"),path:"Student"},{label:e.$trans("student.report.report"),path:"StudentReport"}]},{default:a(()=>[t($,{url:"student/reports/date-wise-attendance/",name:"StudentReportDateWiseAttendance",title:e.$trans("student.report.date_wise_attendance.date_wise_attendance"),actions:h(w),"dropdown-actions":h(g),headers:l.headers,onToggleFilter:p[0]||(p[0]=i=>s.value=!s.value)},null,8,["title","actions","dropdown-actions","headers"])]),_:1},8,["title","navs"]),t(y,{appear:"",visibility:s.value},{default:a(()=>[t(G,{onAfterFilter:d,"init-url":f,onHide:p[1]||(p[1]=i=>s.value=!1)})]),_:1},8,["visibility"]),t(y,{appear:"",visibility:!0},{default:a(()=>[t(P,{"no-padding":"","no-content-padding":"","is-loading":u.value},{default:a(()=>[t(H,{header:l.headers,footer:l.footers,meta:l.meta,module:"student.report.date_wise_attendance",onRefresh:d},{default:a(()=>[(b(!0),B(R,null,E(l.data,i=>(b(),F(U,{key:i.uuid},{default:a(()=>[t(m,{name:"course_batch"},{default:a(()=>[c(r(i.courseBatch)+" ",1),t(n,{block:""},{default:a(()=>[c(r(i.incharge),1)]),_:2},1024)]),_:2},1024),t(m,{name:"strength"},{default:a(()=>[c(r(i.strength),1)]),_:2},1024),t(m,{name:"present"},{default:a(()=>[c(r(i.present),1)]),_:2},1024),t(m,{name:"absent"},{default:a(()=>[c(r(i.absent),1)]),_:2},1024),t(m,{name:"late"},{default:a(()=>[c(r(i.late),1)]),_:2},1024),t(m,{name:"halfDay"},{default:a(()=>[c(r(i.halfDay),1)]),_:2},1024),t(m,{name:"total"},{default:a(()=>[c(r(i.total),1)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","footer","meta"])]),_:1},8,["is-loading"])]),_:1})],64)}}});export{Q as default};
