import{u as A,l as D,n as H,r as l,q as h,o as _,w as e,d as k,e as t,h as L,j as P,y as g,m as O,f as i,a as E,F as z,v as G,s as u,t as d,b as w}from"./app-DvIo72ZO.js";const J={class:"grid grid-cols-3 gap-6"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(F,{emit:b}){A();const $=b,y=F,I={codeNumber:"",voucherNumber:"",startDate:"",endDate:"",inventory:"",vendors:[],places:[]},a=D({...I}),v=D({vendors:y.preRequisites.vendors,inventories:y.preRequisites.inventories,places:y.preRequisites.places}),V=D({isLoaded:!0});return H(async()=>{V.isLoaded=!0}),(c,o)=>{const C=l("BaseInput"),s=l("BaseSelect"),p=l("DatePicker"),B=l("FilterForm");return _(),h(B,{"init-form":I,form:a,multiple:["vendors","places"],onHide:o[7]||(o[7]=r=>$("hide"))},{default:e(()=>[k("div",J,[k("div",K,[t(C,{type:"text",modelValue:a.codeNumber,"onUpdate:modelValue":o[0]||(o[0]=r=>a.codeNumber=r),name:"codeNumber",label:c.$trans("inventory.stock_purchase.props.code_number")},null,8,["modelValue","label"])]),k("div",Q,[t(C,{type:"text",modelValue:a.voucherNumber,"onUpdate:modelValue":o[1]||(o[1]=r=>a.voucherNumber=r),name:"voucherNumber",label:c.$trans("inventory.stock_purchase.props.voucher_number")},null,8,["modelValue","label"])]),k("div",W,[t(s,{modelValue:a.inventory,"onUpdate:modelValue":o[2]||(o[2]=r=>a.inventory=r),name:"inventory","label-prop":"name","value-prop":"uuid",label:c.$trans("inventory.inventory"),options:v.inventories},null,8,["modelValue","label","options"])]),k("div",X,[t(s,{multiple:"",modelValue:a.vendors,"onUpdate:modelValue":o[3]||(o[3]=r=>a.vendors=r),name:"vendors","label-prop":"name","value-prop":"uuid",label:c.$trans("inventory.vendor"),options:v.vendors},null,8,["modelValue","label","options"])]),k("div",Y,[t(s,{multiple:"",modelValue:a.places,"onUpdate:modelValue":o[4]||(o[4]=r=>a.places=r),name:"places","label-prop":"fullname","value-prop":"uuid",label:c.$trans("inventory.place"),options:v.places},null,8,["modelValue","label","options"])]),k("div",Z,[t(p,{start:a.startDate,"onUpdate:start":o[5]||(o[5]=r=>a.startDate=r),end:a.endDate,"onUpdate:end":o[6]||(o[6]=r=>a.endDate=r),name:"dateBetween",as:"range",label:c.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},ee={name:"InventoryStockPurchaseList"},ne=Object.assign(ee,{setup(F){const b=L(),$=P("emitter");let y=["filter"];g("inventory:config")&&y.push("config"),g("stock-purchase:create")&&y.unshift("create");let I=[];g("stock-purchase:export")&&(I=["print","pdf","excel"]);const a="inventory/stockPurchase/",v=O(!1),V=D({inventories:[],vendors:[],places:[]}),c=D({}),o=s=>{Object.assign(V,s)},C=s=>{Object.assign(c,s)};return(s,p)=>{const B=l("PageHeaderAction"),r=l("PageHeader"),S=l("ParentTransition"),R=l("TextMuted"),f=l("DataCell"),N=l("FloatingMenuItem"),q=l("FloatingMenu"),T=l("DataRow"),U=l("BaseButton"),M=l("DataTable"),j=l("ListItem");return _(),h(j,{"init-url":a,"pre-requisites":!0,onSetPreRequisites:o,onSetItems:C},{header:e(()=>[t(r,{title:s.$trans("inventory.stock_purchase.stock_purchase"),navs:[{label:s.$trans("inventory.inventory"),path:"Inventory"}]},{default:e(()=>[t(B,{url:"inventory/stock-purchases/",name:"InventoryStockPurchase",title:s.$trans("inventory.stock_purchase.stock_purchase"),actions:i(y),"dropdown-actions":i(I),"config-path":"InventoryConfig",onToggleFilter:p[0]||(p[0]=n=>v.value=!v.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(S,{appear:"",visibility:v.value},{default:e(()=>[t(x,{onRefresh:p[1]||(p[1]=n=>i($).emit("listItems")),"pre-requisites":V,onHide:p[2]||(p[2]=n=>v.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:e(()=>[t(S,{appear:"",visibility:!0},{default:e(()=>[t(M,{header:c.headers,meta:c.meta,module:"inventory.stock_purchase",onRefresh:p[4]||(p[4]=n=>i($).emit("listItems"))},{actionButton:e(()=>[i(g)("stock-purchase:create")?(_(),h(U,{key:0,onClick:p[3]||(p[3]=n=>i(b).push({name:"InventoryStockPurchaseCreate"}))},{default:e(()=>[u(d(s.$trans("global.add",{attribute:s.$trans("inventory.stock_purchase.stock_purchase")})),1)]),_:1})):w("",!0)]),default:e(()=>[(_(!0),E(z,null,G(c.data,n=>(_(),h(T,{key:n.uuid,onDoubleClick:m=>i(b).push({name:"InventoryStockPurchaseShow",params:{uuid:n.uuid}})},{default:e(()=>[t(f,{name:"codeNumber"},{default:e(()=>[u(d(n.codeNumber)+" ",1),t(R,{block:""},{default:e(()=>{var m;return[u(d((m=n.inventory)==null?void 0:m.name),1)]}),_:2},1024)]),_:2},1024),t(f,{name:"voucherNumber"},{default:e(()=>[u(d(n.voucherNumber),1)]),_:2},1024),t(f,{name:"vendor"},{default:e(()=>[u(d(n.vendor.name),1)]),_:2},1024),t(f,{name:"date"},{default:e(()=>[u(d(n.date.formatted),1)]),_:2},1024),t(f,{name:"total"},{default:e(()=>[u(d(n.total.formatted),1)]),_:2},1024),t(f,{name:"place"},{default:e(()=>{var m;return[u(d(((m=n.place)==null?void 0:m.fullName)||"-"),1)]}),_:2},1024),t(f,{name:"createdAt"},{default:e(()=>[u(d(n.createdAt.formatted),1)]),_:2},1024),t(f,{name:"action"},{default:e(()=>[t(q,null,{default:e(()=>[t(N,{icon:"fas fa-arrow-circle-right",onClick:m=>i(b).push({name:"InventoryStockPurchaseShow",params:{uuid:n.uuid}})},{default:e(()=>[u(d(s.$trans("general.show")),1)]),_:2},1032,["onClick"]),i(g)("stock-purchase:edit")?(_(),h(N,{key:0,icon:"fas fa-edit",onClick:m=>i(b).push({name:"InventoryStockPurchaseEdit",params:{uuid:n.uuid}})},{default:e(()=>[u(d(s.$trans("general.edit")),1)]),_:2},1032,["onClick"])):w("",!0),i(g)("stock-purchase:create")?(_(),h(N,{key:1,icon:"fas fa-copy",onClick:m=>i(b).push({name:"InventoryStockPurchaseDuplicate",params:{uuid:n.uuid}})},{default:e(()=>[u(d(s.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):w("",!0),i(g)("stock-purchase:delete")?(_(),h(N,{key:2,icon:"fas fa-trash",onClick:m=>i($).emit("deleteItem",{uuid:n.uuid})},{default:e(()=>[u(d(s.$trans("general.delete")),1)]),_:2},1032,["onClick"])):w("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{ne as default};
