import{u as W,j as X,I as Y,g as U,l as k,n as Z,r as d,a as $,o as i,q as b,b as p,e as s,f as a,w as S,d as u,s as B,t as E,J as D,F as h,K as x}from"./app-DvIo72ZO.js";import{u as _}from"./useCustomFields-XeNAQP8g.js";const ee={class:"grid grid-cols-3 gap-6"},oe={class:"col-span-3 sm:col-span-2"},te={class:"flex"},ae={class:"col-span-3 sm:col-span-1"},le={class:"col-span-3 sm:col-span-1"},re={class:"col-span-3 sm:col-span-1"},ne={class:"col-span-3 sm:col-span-1"},se={class:"col-span-3 sm:col-span-1"},ue={class:"col-span-3 sm:col-span-1"},ie={class:"col-span-3 sm:col-span-1"},me={class:"col-span-3 sm:col-span-1"},de={class:"col-span-3 sm:col-span-1"},pe={class:"col-span-3 sm:col-span-1"},be={class:"col-span-3 sm:col-span-1"},ye={key:0,class:"col-span-3 sm:col-span-1"},ce={key:1,class:"col-span-3 sm:col-span-1"},ge={key:2,class:"col-span-3 sm:col-span-1"},Ne={name:"EmployeeEditBasic"},Ue=Object.assign(Ne,{props:{employee:{type:Object,default(){return{}}}},setup(y){const P=W(),C=X("emitter"),n=y,V={firstName:"",middleName:"",thirdName:"",lastName:"",gender:"",birthDate:"",uniqueIdNumber1:"",uniqueIdNumber2:"",uniqueIdNumber3:"",birthPlace:"",nationality:"",motherTongue:"",maritalStatus:"",bloodGroup:"",religion:"",category:"",caste:"",type:"",customFields:[]},L="employee/",l=Y(L),G=U("employee.uniqueIdNumber1Label"),T=U("employee.uniqueIdNumber2Label"),j=U("employee.uniqueIdNumber3Label"),R=U("contact.enableCategoryField"),O=U("contact.enableCasteField"),m=k({types:[],genders:[],bloodGroups:[],religions:[],categories:[],castes:[],maritalStatuses:[],customFields:[]}),t=k({...V}),c=k({isLoaded:!P.params.uuid}),{customFields:w,setCustomFields:A}=_(),H=r=>{Object.assign(m,r),z()},z=()=>{var r,e,q,f,I,v,F;Object.assign(V,{firstName:n.employee.contact.firstName,middleName:n.employee.contact.middleName,thirdName:n.employee.contact.thirdName,lastName:n.employee.contact.lastName,gender:(r=n.employee.contact.gender)==null?void 0:r.value,birthDate:(e=n.employee.contact.birthDate)==null?void 0:e.value,uniqueIdNumber1:n.employee.contact.uniqueIdNumber1,uniqueIdNumber2:n.employee.contact.uniqueIdNumber2,uniqueIdNumber3:n.employee.contact.uniqueIdNumber3,birthPlace:n.employee.contact.birthPlace,nationality:n.employee.contact.nationality,motherTongue:n.employee.contact.motherTongue,bloodGroup:((q=n.employee.contact.bloodGroup)==null?void 0:q.value)||"",maritalStatus:((f=n.employee.contact.maritalStatus)==null?void 0:f.value)||"",religion:((I=n.employee.contact.religion)==null?void 0:I.uuid)||"",category:((v=n.employee.contact.category)==null?void 0:v.uuid)||"",caste:((F=n.employee.contact.caste)==null?void 0:F.uuid)||"",type:n.employee.type.value}),A(m.customFields,n.employee.contact.customFields),V.customFields=w.value,Object.assign(t,x(V)),c.isLoaded=!0};Z(async()=>{});const J=()=>{C.emit("employeeUpdated")};return(r,e)=>{const q=d("PageHeader"),f=d("BaseLabel"),I=d("NameInput"),v=d("BaseRadioGroup"),F=d("DatePicker"),g=d("BaseInput"),N=d("BaseSelect"),K=d("CustomField"),M=d("FormAction"),Q=d("ParentTransition");return i(),$(h,null,[y.employee.uuid?(i(),b(q,{key:0,title:r.$trans(a(P).meta.trans,{attribute:r.$trans(a(P).meta.label)}),navs:[{label:r.$trans("employee.employee"),path:"Employee"},{label:y.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:y.employee.uuid}}}]},null,8,["title","navs"])):p("",!0),s(Q,{appear:"",visibility:!0},{default:S(()=>[y.employee.uuid?(i(),b(M,{key:0,"pre-requisites":!0,onSetPreRequisites:H,"init-url":L,"no-data-fetch":"","init-form":V,form:t,"stay-on":"","after-submit":J,redirect:{name:"EmployeeShowBasic",params:{uuid:y.employee.uuid}}},{default:S(()=>[u("div",ee,[u("div",oe,[s(f,null,{default:S(()=>[B(E(r.$trans("employee.props.name")),1)]),_:1}),u("div",te,[s(I,{firstName:t.firstName,"onUpdate:firstName":e[0]||(e[0]=o=>t.firstName=o),middleName:t.middleName,"onUpdate:middleName":e[1]||(e[1]=o=>t.middleName=o),thirdName:t.thirdName,"onUpdate:thirdName":e[2]||(e[2]=o=>t.thirdName=o),lastName:t.lastName,"onUpdate:lastName":e[3]||(e[3]=o=>t.lastName=o),formErrors:a(l),"onUpdate:formErrors":e[4]||(e[4]=o=>D(l)?l.value=o:null)},null,8,["firstName","middleName","thirdName","lastName","formErrors"])])]),u("div",ae,[s(f,null,{default:S(()=>[B(E(r.$trans("contact.props.gender")),1)]),_:1}),s(v,{"top-margin":"",options:m.genders,name:"gender",modelValue:t.gender,"onUpdate:modelValue":e[5]||(e[5]=o=>t.gender=o),error:a(l).gender,"onUpdate:error":e[6]||(e[6]=o=>a(l).gender=o),horizontal:""},null,8,["options","modelValue","error"])]),u("div",le,[s(F,{modelValue:t.birthDate,"onUpdate:modelValue":e[7]||(e[7]=o=>t.birthDate=o),name:"birthDate",label:r.$trans("contact.props.birth_date"),"no-clear":"",error:a(l).birthDate,"onUpdate:error":e[8]||(e[8]=o=>a(l).birthDate=o)},null,8,["modelValue","label","error"])]),u("div",re,[s(g,{type:"text",modelValue:t.uniqueIdNumber1,"onUpdate:modelValue":e[9]||(e[9]=o=>t.uniqueIdNumber1=o),name:"uniqueIdNumber1",label:a(G),error:a(l).uniqueIdNumber1,"onUpdate:error":e[10]||(e[10]=o=>a(l).uniqueIdNumber1=o)},null,8,["modelValue","label","error"])]),u("div",ne,[s(g,{type:"text",modelValue:t.uniqueIdNumber2,"onUpdate:modelValue":e[11]||(e[11]=o=>t.uniqueIdNumber2=o),name:"uniqueIdNumber2",label:a(T),error:a(l).uniqueIdNumber2,"onUpdate:error":e[12]||(e[12]=o=>a(l).uniqueIdNumber2=o)},null,8,["modelValue","label","error"])]),u("div",se,[s(g,{type:"text",modelValue:t.uniqueIdNumber3,"onUpdate:modelValue":e[13]||(e[13]=o=>t.uniqueIdNumber3=o),name:"uniqueIdNumber3",label:a(j),error:a(l).uniqueIdNumber3,"onUpdate:error":e[14]||(e[14]=o=>a(l).uniqueIdNumber3=o)},null,8,["modelValue","label","error"])]),u("div",ue,[s(g,{type:"text",modelValue:t.birthPlace,"onUpdate:modelValue":e[15]||(e[15]=o=>t.birthPlace=o),name:"birthPlace",label:r.$trans("contact.props.birth_place"),error:a(l).birthPlace,"onUpdate:error":e[16]||(e[16]=o=>a(l).birthPlace=o)},null,8,["modelValue","label","error"])]),u("div",ie,[s(g,{type:"text",modelValue:t.nationality,"onUpdate:modelValue":e[17]||(e[17]=o=>t.nationality=o),name:"nationality",label:r.$trans("contact.props.nationality"),error:a(l).nationality,"onUpdate:error":e[18]||(e[18]=o=>a(l).nationality=o)},null,8,["modelValue","label","error"])]),u("div",me,[s(g,{type:"text",modelValue:t.motherTongue,"onUpdate:modelValue":e[19]||(e[19]=o=>t.motherTongue=o),name:"motherTongue",label:r.$trans("contact.props.mother_tongue"),error:a(l).motherTongue,"onUpdate:error":e[20]||(e[20]=o=>a(l).motherTongue=o)},null,8,["modelValue","label","error"])]),u("div",de,[c.isLoaded?(i(),b(N,{key:0,modelValue:t.bloodGroup,"onUpdate:modelValue":e[21]||(e[21]=o=>t.bloodGroup=o),name:"bloodGroup",label:r.$trans("contact.props.blood_group"),options:m.bloodGroups,error:a(l).bloodGroup,"onUpdate:error":e[22]||(e[22]=o=>a(l).bloodGroup=o)},null,8,["modelValue","label","options","error"])):p("",!0)]),u("div",pe,[c.isLoaded?(i(),b(N,{key:0,modelValue:t.maritalStatus,"onUpdate:modelValue":e[23]||(e[23]=o=>t.maritalStatus=o),name:"maritalStatus",label:r.$trans("contact.props.marital_status"),options:m.maritalStatuses,error:a(l).maritalStatus,"onUpdate:error":e[24]||(e[24]=o=>a(l).maritalStatus=o)},null,8,["modelValue","label","options","error"])):p("",!0)]),u("div",be,[c.isLoaded?(i(),b(N,{key:0,name:"religion",label:r.$trans("global.select",{attribute:r.$trans("contact.religion.religion")}),modelValue:t.religion,"onUpdate:modelValue":e[25]||(e[25]=o=>t.religion=o),error:a(l).religion,"onUpdate:error":e[26]||(e[26]=o=>a(l).religion=o),options:m.religions,"label-prop":"name","value-prop":"uuid"},null,8,["label","modelValue","error","options"])):p("",!0)]),a(R)?(i(),$("div",ye,[c.isLoaded?(i(),b(N,{key:0,name:"category",label:r.$trans("global.select",{attribute:r.$trans("contact.category.category")}),modelValue:t.category,"onUpdate:modelValue":e[27]||(e[27]=o=>t.category=o),error:a(l).category,"onUpdate:error":e[28]||(e[28]=o=>a(l).category=o),options:m.categories,"label-prop":"name","value-prop":"uuid"},null,8,["label","modelValue","error","options"])):p("",!0)])):p("",!0),a(O)?(i(),$("div",ce,[c.isLoaded?(i(),b(N,{key:0,name:"caste",label:r.$trans("global.select",{attribute:r.$trans("contact.caste.caste")}),modelValue:t.caste,"onUpdate:modelValue":e[29]||(e[29]=o=>t.caste=o),error:a(l).caste,"onUpdate:error":e[30]||(e[30]=o=>a(l).caste=o),options:m.castes,"label-prop":"name","value-prop":"uuid"},null,8,["label","modelValue","error","options"])):p("",!0)])):p("",!0),m.types.length?(i(),$("div",ge,[s(N,{name:"type",label:r.$trans("global.select",{attribute:r.$trans("employee.type")}),options:m.types,modelValue:t.type,"onUpdate:modelValue":e[31]||(e[31]=o=>t.type=o),error:a(l).type,"onUpdate:error":e[32]||(e[32]=o=>a(l).type=o)},null,8,["label","options","modelValue","error"])])):p("",!0)]),s(K,{customFields:t.customFields,"onUpdate:customFields":e[33]||(e[33]=o=>t.customFields=o),formErrors:a(l),"onUpdate:formErrors":e[34]||(e[34]=o=>D(l)?l.value=o:null)},null,8,["customFields","formErrors"])]),_:1},8,["form","redirect"])):p("",!0)]),_:1})],64)}}});export{Ue as default};
