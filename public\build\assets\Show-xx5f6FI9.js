import{i as S,u as C,h as P,l as V,r as s,a as k,o as p,e as a,w as e,f as l,q as y,b as A,d as H,s as o,t as r,F as I}from"./app-DvIo72ZO.js";const N={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},T={name:"DeviceShow"},E=Object.assign(T,{setup(R){S();const c=C(),u=P(),m={},_="device/",n=V({...m}),v=t=>{Object.assign(n,t)};return(t,d)=>{const f=s("PageHeaderAction"),b=s("PageHeader"),i=s("BaseDataView"),g=s("BaseButton"),B=s("ShowButton"),$=s("BaseCard"),w=s("ShowItem"),D=s("ParentTransition");return p(),k(I,null,[a(b,{title:t.$trans(l(c).meta.trans,{attribute:t.$trans(l(c).meta.label)}),navs:[{label:t.$trans("device.device"),path:"Device"}]},{default:e(()=>[a(f,{name:"Device",title:t.$trans("device.device"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(D,{appear:"",visibility:!0},{default:e(()=>[a(w,{"init-url":_,uuid:l(c).params.uuid,onSetItem:v,onRedirectTo:d[1]||(d[1]=h=>l(u).push({name:"Device"}))},{default:e(()=>[n.uuid?(p(),y($,{key:0},{title:e(()=>[o(r(n.name),1)]),footer:e(()=>[a(B,null,{default:e(()=>[a(g,{design:"primary",onClick:d[0]||(d[0]=h=>l(u).push({name:"DeviceEdit",params:{uuid:n.uuid}}))},{default:e(()=>[o(r(t.$trans("general.edit")),1)]),_:1})]),_:1})]),default:e(()=>[H("dl",N,[a(i,{label:t.$trans("device.props.name")},{default:e(()=>[o(r(n.name),1)]),_:1},8,["label"]),a(i,{label:t.$trans("device.props.code")},{default:e(()=>[o(r(n.code),1)]),_:1},8,["label"]),a(i,{class:"col-span-1 sm:col-span-2",label:t.$trans("device.props.description")},{default:e(()=>[o(r(n.description),1)]),_:1},8,["label"]),a(i,{label:t.$trans("general.created_at")},{default:e(()=>[o(r(n.createdAt.formatted),1)]),_:1},8,["label"]),a(i,{label:t.$trans("general.updated_at")},{default:e(()=>[o(r(n.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):A("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{E as default};
