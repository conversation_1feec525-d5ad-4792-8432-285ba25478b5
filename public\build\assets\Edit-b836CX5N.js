import{u as C,h as P,I as O,l as I,L as q,n as A,r as a,q as S,o as d,w as p,d as u,a as j,b as B,e as i,f as s,F as E,K as H,j as R,m as D,z as M,A as N}from"./app-DvIo72ZO.js";import{_ as L}from"./Asset-BZY-RXy4.js";const z={class:"grid grid-cols-3 gap-4"},K={class:"col-span-2 sm:col-span-1"},x={class:"col-span-2 sm:col-span-1"},G={class:"col-span-3"},J={class:"col-span-3"},Q={class:"col-span-3"},W={key:0,class:"col-span-3"},X={class:"col-span-3"},Y={name:"SiteBlockEditContent"},Z=Object.assign(Y,{props:{block:{type:Object,default(){return{}}}},emits:["refresh"],setup(_,{emit:U}){C();const V=P(),c=U,v=_,b={name:"",isSlider:!1,title:"",subTitle:"",content:"",menu:null,url:""},m="site/block/",o=O(m),g=I({menus:[]}),l=I({...b}),r=n=>{Object.assign(g,n)},y=()=>{c("refresh")},$=n=>{var e;Object.assign(b,{name:n.name,isSlider:n.isSlider,title:n.title,subTitle:n.subTitle,content:n.content,menu:(e=n.menu)==null?void 0:e.uuid,url:n.url}),Object.assign(l,H(b))};return q(()=>v.block,n=>{n.uuid&&$(n)},{immediate:!0,deep:!0}),A(()=>{v.block.uuid&&$(v.block)}),(n,e)=>{const f=a("BaseInput"),w=a("BaseSwitch"),T=a("BaseSelect"),k=a("MdEditor"),F=a("FormAction");return d(),S(F,{"pre-requisites":!0,onSetPreRequisites:r,"no-card":"","no-data-fetch":"","button-padding":"",action:"update","keep-adding":!1,"stay-on":!0,"init-url":m,"init-form":b,form:l,"after-submit":y,onCancelled:e[14]||(e[14]=t=>s(V).push({name:"SiteBlockShow",params:{uuid:_.block.uuid}}))},{default:p(()=>[u("div",z,[u("div",K,[i(f,{type:"text",modelValue:l.name,"onUpdate:modelValue":e[0]||(e[0]=t=>l.name=t),name:"name",label:n.$trans("site.block.props.name"),error:s(o).name,"onUpdate:error":e[1]||(e[1]=t=>s(o).name=t)},null,8,["modelValue","label","error"])]),u("div",x,[i(w,{vertical:"",modelValue:l.isSlider,"onUpdate:modelValue":e[2]||(e[2]=t=>l.isSlider=t),name:"isSlider",label:n.$trans("global.is",{attribute:n.$trans("site.block.props.slider")}),error:s(o).isSlider,"onUpdate:error":e[3]||(e[3]=t=>s(o).isSlider=t)},null,8,["modelValue","label","error"])]),l.isSlider?B("",!0):(d(),j(E,{key:0},[u("div",G,[i(f,{type:"text",modelValue:l.title,"onUpdate:modelValue":e[4]||(e[4]=t=>l.title=t),name:"title",label:n.$trans("site.block.props.title"),error:s(o).title,"onUpdate:error":e[5]||(e[5]=t=>s(o).title=t)},null,8,["modelValue","label","error"])]),u("div",J,[i(f,{type:"text",modelValue:l.subTitle,"onUpdate:modelValue":e[6]||(e[6]=t=>l.subTitle=t),name:"subTitle",label:n.$trans("site.block.props.sub_title"),error:s(o).subTitle,"onUpdate:error":e[7]||(e[7]=t=>s(o).subTitle=t)},null,8,["modelValue","label","error"])]),u("div",Q,[i(T,{modelValue:l.menu,"onUpdate:modelValue":e[8]||(e[8]=t=>l.menu=t),name:"menu",label:n.$trans("global.select",{attribute:n.$trans("site.menu.menu")}),"label-prop":"name","value-prop":"uuid",options:g.menus,error:s(o).menu,"onUpdate:error":e[9]||(e[9]=t=>s(o).menu=t)},null,8,["modelValue","label","options","error"])]),l.menu?B("",!0):(d(),j("div",W,[i(f,{type:"text",modelValue:l.url,"onUpdate:modelValue":e[10]||(e[10]=t=>l.url=t),name:"url",label:n.$trans("site.block.props.url"),error:s(o).url,"onUpdate:error":e[11]||(e[11]=t=>s(o).url=t)},null,8,["modelValue","label","error"])])),u("div",X,[i(k,{placeholder:n.$trans("site.block.props.content"),modelValue:l.content,"onUpdate:modelValue":e[12]||(e[12]=t=>l.content=t),error:s(o).content,"onUpdate:error":e[13]||(e[13]=t=>s(o).content=t)},null,8,["placeholder","modelValue","error"])])],64))])]),_:1},8,["form"])}}}),h={class:"px-4 py-4"},ee={name:"SiteBlockEdit"},ne=Object.assign(ee,{setup(_){const U=C(),V=P(),c=R("$trans");R("emitter");const v={uuid:""},b="site/block/",m=D(!1),o=I({...v}),g=l=>{Object.assign(o,l)};return(l,r)=>{const y=a("BaseButton"),$=a("PageHeaderAction"),n=a("PageHeader"),e=a("BaseCard"),f=a("ShowItem"),w=a("ParentTransition"),T=M("tooltip");return d(),j(E,null,[o.uuid?(d(),S(n,{key:0,title:o.name,navs:[{label:s(c)("site.site"),path:"Site"},{label:s(c)("site.block.block"),path:"SiteBlockList"}]},{default:p(()=>[i($,{name:"SiteBlock",title:s(c)("site.block.block"),actions:["list"]},{default:p(()=>[N((d(),S(y,{design:"white",onClick:r[0]||(r[0]=k=>s(V).push({name:"SiteBlockShow",params:{uuid:o.uuid}}))},{default:p(()=>r[5]||(r[5]=[u("i",{class:"fas fa-eye"},null,-1)])),_:1})),[[T,s(c)("general.preview")]])]),_:1},8,["title"])]),_:1},8,["title","navs"])):B("",!0),i(w,{appear:"",visibility:!0},{default:p(()=>[i(f,{"init-url":b,uuid:s(U).params.uuid,onSetItem:g,onRedirectTo:r[3]||(r[3]=k=>s(V).push({name:"SiteBlock"})),refresh:m.value,onRefreshed:r[4]||(r[4]=k=>m.value=!1)},{default:p(()=>[i(e,{"no-padding":"","no-content-padding":""},{default:p(()=>[o.uuid&&!o.isSlider?(d(),S(L,{key:0,block:o,onRefreshItem:r[1]||(r[1]=k=>m.value=!0)},null,8,["block"])):B("",!0),u("div",h,[o.uuid?(d(),S(Z,{key:0,block:o,onRefreshItem:r[2]||(r[2]=k=>m.value=!0)},null,8,["block"])):B("",!0)])]),_:1})]),_:1},8,["uuid","refresh"])]),_:1})],64)}}});export{ne as default};
