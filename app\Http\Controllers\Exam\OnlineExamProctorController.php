<?php

namespace App\Http\Controllers\Exam;

use App\Http\Controllers\Controller;
use App\Models\Exam\OnlineExam;
use App\Models\Exam\OnlineExamSubmission;
use App\Services\Exam\OnlineExamProctorService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;

class OnlineExamProctorController extends Controller
{
    public function __construct()
    {
        $this->middleware('role:student')->only([
            'logWebcamCapture',
            'logAudioAlert',
            'logScreenRecording',
            'logTabSwitch',
            'logFullscreenExit',
            'logCopyPasteAttempt',
            'logFaceDetectionFailure',
            'validateRequirements'
        ]);
    }

    /**
     * Validate proctoring requirements before exam start
     */
    public function validateRequirements(Request $request, string $onlineExam, OnlineExamProctorService $service)
    {        
        try {
            $onlineExam = OnlineExam::findByUuidOrFail($onlineExam);

            $service->validateProctorRequirements($onlineExam, $request);
            
            return response()->success([
                'message' => 'Proctoring requirements validated successfully',
                'proctoring_config' => $onlineExam->proctor_config,
            ]);
        } catch (ValidationException $e) {
            return response()->error([
                'message' => 'Proctoring requirements not met',
                'errors' => $e->errors(),
            ], 422);
        }
    }

    /**
     * Log webcam capture event
     */
    public function logWebcamCapture(Request $request, string $onlineExam, OnlineExamProctorService $service)
    {
        $request->validate([
            'submission_uuid' => 'required|string',
            'image' => 'required|image|max:2048', // 2MB max
            'face_data' => 'nullable|array',
            'face_data.*.confidence' => 'numeric|min:0|max:1',
            'face_data.*.box' => 'array',
        ]);

        $submission = $this->getStudentSubmission($onlineExam, $request->submission_uuid);
        
        $log = $service->logWebcamCapture(
            $submission,
            $request->file('image'),
            $request->input('face_data')
        );

        return response()->success([
            'message' => 'Webcam capture logged successfully',
            'log_id' => $log->uuid,
        ]);
    }

    /**
     * Log audio alert event
     */
    public function logAudioAlert(Request $request, string $onlineExam, OnlineExamProctorService $service)
    {
        $request->validate([
            'submission_uuid' => 'required|string',
            'audio_level' => 'required|numeric',
            'audio_file' => 'nullable|file|mimes:wav,mp3,ogg|max:5120', // 5MB max
        ]);

        $submission = $this->getStudentSubmission($onlineExam, $request->submission_uuid);

        $log = $service->logAudioAlert(
            $submission,
            $request->input('audio_level'),
            $request->file('audio_file')
        );

        return response()->success([
            'message' => 'Audio alert logged successfully',
            'log_id' => $log->uuid,
        ]);
    }

    /**
     * Log screen recording event
     */
    public function logScreenRecording(Request $request, string $onlineExam, OnlineExamProctorService $service)
    {
        $request->validate([
            'submission_uuid' => 'required|string',
            'recording_file' => 'required|file|mimetypes:video/webm,video/mp4,video/avi,video/x-msvideo,application/octet-stream|max:51200', // 50MB max
            'chunk_number' => 'nullable|integer|min:1',
            'total_chunks' => 'nullable|integer|min:1',
        ]);

        $submission = $this->getStudentSubmission($onlineExam, $request->submission_uuid);

        $log = $service->logScreenRecording(
            $submission,
            $request->file('recording_file'),
            $request->input('chunk_number'),
            $request->input('total_chunks')
        );

        return response()->success([
            'message' => 'Screen recording logged successfully',
            'log_id' => $log->uuid,
        ]);
    }

    /**
     * Log tab switch event
     */
    public function logTabSwitch(Request $request, string $onlineExam, OnlineExamProctorService $service)
    {
        $request->validate([
            'submission_uuid' => 'required|string',
            'tab_title' => 'nullable|string|max:255',
        ]);

        $submission = $this->getStudentSubmission($onlineExam, $request->submission_uuid);
        
        $log = $service->logTabSwitch(
            $submission,
            $request->input('tab_title')
        );

        return response()->success([
            'message' => 'Tab switch logged successfully',
            'log_id' => $log->uuid,
        ]);
    }

    /**
     * Log fullscreen exit event
     */
    public function logFullscreenExit(Request $request, string $onlineExam, OnlineExamProctorService $service)
    {
        $request->validate([
            'submission_uuid' => 'required|string',
        ]);

        $submission = $this->getStudentSubmission($onlineExam, $request->submission_uuid);
        
        $log = $service->logFullscreenExit($submission);

        return response()->success([
            'message' => 'Fullscreen exit logged successfully',
            'log_id' => $log->uuid,
        ]);
    }

    /**
     * Log copy/paste attempt event
     */
    public function logCopyPasteAttempt(Request $request, string $onlineExam, OnlineExamProctorService $service)
    {
        $request->validate([
            'submission_uuid' => 'required|string',
            'action' => 'required|string|in:copy,paste,cut',
            'content' => 'nullable|string|max:1000',
        ]);

        $submission = $this->getStudentSubmission($onlineExam, $request->submission_uuid);
        
        $log = $service->logCopyPasteAttempt(
            $submission,
            $request->input('action'),
            $request->input('content')
        );

        return response()->success([
            'message' => 'Copy/paste attempt logged successfully',
            'log_id' => $log->uuid,
        ]);
    }

    /**
     * Log face detection failure event
     */
    public function logFaceDetectionFailure(Request $request, string $onlineExam, OnlineExamProctorService $service)
    {
        $request->validate([
            'submission_uuid' => 'required|string',
            'reason' => 'required|string|max:255',
        ]);

        $submission = $this->getStudentSubmission($onlineExam, $request->submission_uuid);
        
        $log = $service->logFaceDetectionFailure(
            $submission,
            $request->input('reason')
        );

        return response()->success([
            'message' => 'Face detection failure logged successfully',
            'log_id' => $log->uuid,
        ]);
    }

    /**
     * Get proctoring summary for examiner review
     */
    public function getProctoringSummary(Request $request, string $onlineExam, string $submission, OnlineExamProctorService $service)
    {
        $onlineExam = OnlineExam::findByUuidOrFail($onlineExam);
        $this->authorize('view', $onlineExam);

        $submission = OnlineExamSubmission::where('uuid', $submission)
            ->where('online_exam_id', $onlineExam->id)
            ->firstOrFail();

        $summary = $service->getProctoringSummary($submission);
        $violationCounts = $service->getViolationCounts($submission);

        return response()->success([
            'summary' => $summary,
            'violation_counts' => $violationCounts,
            'has_critical_violations' => $service->hasCriticalViolations($submission),
        ]);
    }

    /**
     * Get detailed proctoring logs for examiner review
     */
    public function getProctorLogs(Request $request, string $onlineExam, string $submission)
    {
        $onlineExam = OnlineExam::findByUuidOrFail($onlineExam);
        $this->authorize('view', $onlineExam);

        $submission = OnlineExamSubmission::where('uuid', $submission)
            ->where('online_exam_id', $onlineExam->id)
            ->firstOrFail();

        $logs = $submission->proctorLogs()
            ->when($request->event_type, fn($q) => $q->where('event_type', $request->event_type))
            ->when($request->severity, fn($q) => $q->where('severity', $request->severity))
            ->orderBy('detected_at')
            ->paginate(50);

        return response()->success($logs);
    }

    /**
     * Download media file for proctoring log
     */
    public function downloadMedia(Request $request, string $onlineExam, string $log)
    {
        $onlineExam = OnlineExam::findByUuidOrFail($onlineExam);
        $this->authorize('view', $onlineExam);

        $proctorLog = \App\Models\Exam\OnlineExamProctorLog::where('uuid', $log)
            ->whereHas('submission.exam', fn($q) => $q->where('id', $onlineExam->id))
            ->firstOrFail();

        if (!$proctorLog->hasMedia()) {
            abort(404, 'No media file found for this log entry');
        }

        // Determine the appropriate disk based on configuration
        $disk = config('filesystems.default', 'local');
        if (in_array($disk, ['local', 'vol'])) {
            $disk = 'local'; // Use local disk for private files
        }

        // Check if file exists on the configured disk
        if (!Storage::disk($disk)->exists($proctorLog->media_path)) {
            abort(404, 'Media file not found on storage disk');
        }

        $fileName = basename($proctorLog->media_path);

        // For S3 and other cloud storage, generate a temporary URL
        if (in_array($disk, ['s3', 'r2'])) {
            $url = Storage::disk($disk)->temporaryUrl($proctorLog->media_path, now()->addMinutes(5));
            return redirect($url);
        }

        // For local storage, stream the file directly
        $fileContent = Storage::disk($disk)->get($proctorLog->media_path);
        $mimeType = Storage::disk($disk)->mimeType($proctorLog->media_path);

        return response($fileContent, 200, [
            'Content-Type' => $mimeType,
            'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
        ]);
    }

    /**
     * Get student's own submission for proctoring
     */
    private function getStudentSubmission(string $examUuid, string $submissionUuid): OnlineExamSubmission
    {
        $student = auth()->user()->student;
        
        if (!$student) {
            throw ValidationException::withMessages(['message' => 'Student not found']);
        }

        $submission = OnlineExamSubmission::where('uuid', $submissionUuid)
            ->where('student_id', $student->id)
            ->whereHas('exam', fn($q) => $q->where('uuid', $examUuid))
            ->first();

        if (!$submission) {
            throw ValidationException::withMessages(['message' => 'Submission not found']);
        }

        if (!$submission->exam->enable_proctoring) {
            throw ValidationException::withMessages(['message' => 'Proctoring not enabled for this exam']);
        }

        return $submission;
    }
}
