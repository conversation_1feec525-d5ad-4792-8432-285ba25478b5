import{u as V,l as h,n as A,r as o,q as k,o as b,w as e,d as F,b as H,s as u,t as a,e as t,h as P,j,m as E,f,a as O,F as U,v as q}from"./app-DvIo72ZO.js";const x={class:"grid grid-cols-3 gap-6"},z={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},J={__name:"Filter",emits:["hide"],setup(y,{emit:g}){const p=V(),$=g,v={students:[],startDate:"",endDate:""},l=h({...v});h({});const i=h({students:[],isLoaded:!p.query.students});return A(async()=>{i.students=p.query.students?p.query.students.split(","):[],i.isLoaded=!0}),(c,d)=>{const m=o("BaseSelectSearch"),r=o("DatePicker"),w=o("FilterForm");return b(),k(w,{"init-form":v,form:l,multiple:["students"],onHide:d[3]||(d[3]=s=>$("hide"))},{default:e(()=>[F("div",x,[F("div",z,[i.isLoaded?(b(),k(m,{key:0,multiple:"",name:"students",label:c.$trans("global.select",{attribute:c.$trans("student.student")}),modelValue:l.students,"onUpdate:modelValue":d[0]||(d[0]=s=>l.students=s),"value-prop":"uuid","init-search":i.students,"search-key":"name","search-action":"student/summary"},{selectedOption:e(s=>[u(a(s.value.name)+" ("+a(s.value.codeNumber)+") ",1)]),listOption:e(s=>[u(a(s.option.name)+" ("+a(s.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):H("",!0)]),F("div",G,[t(r,{start:l.startDate,"onUpdate:start":d[1]||(d[1]=s=>l.startDate=s),end:l.endDate,"onUpdate:end":d[2]||(d[2]=s=>l.endDate=s),name:"dateBetween",as:"range",label:c.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},K={name:"StudentEditRequestList"},W=Object.assign(K,{setup(y){const g=P(),p=j("emitter");let $=["filter"],v=["print","pdf","excel"];const l="student/editRequest/",i=E(!1),c=h({}),d=m=>{Object.assign(c,m)};return(m,r)=>{const w=o("PageHeaderAction"),s=o("PageHeader"),S=o("ParentTransition"),D=o("TextMuted"),_=o("DataCell"),N=o("BaseBadge"),B=o("FloatingMenuItem"),C=o("FloatingMenu"),I=o("DataRow"),T=o("DataTable"),L=o("ListItem");return b(),k(L,{"init-url":l,onSetItems:d},{header:e(()=>[t(s,{title:m.$trans("student.edit_request.edit_request"),navs:[{label:m.$trans("student.student"),path:"Student"}]},{default:e(()=>[t(w,{url:"student/edit-requests/",name:"StudentEditRequest",title:m.$trans("student.edit_request.edit_request"),actions:f($),"dropdown-actions":f(v),onToggleFilter:r[0]||(r[0]=n=>i.value=!i.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(S,{appear:"",visibility:i.value},{default:e(()=>[t(J,{onRefresh:r[1]||(r[1]=n=>f(p).emit("listItems")),onHide:r[2]||(r[2]=n=>i.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(S,{appear:"",visibility:!0},{default:e(()=>[t(T,{header:c.headers,meta:c.meta,module:"student.edit_request",onRefresh:r[3]||(r[3]=n=>f(p).emit("listItems"))},{default:e(()=>[(b(!0),O(U,null,q(c.data,n=>(b(),k(I,{key:n.uuid,onDoubleClick:M=>f(g).push({name:"StudentEditRequestShow",params:{uuid:m.student.uuid,muuid:n.uuid}})},{default:e(()=>[t(_,{name:"name"},{default:e(()=>[u(a(n.student.name)+" ",1),t(D,{block:""},{default:e(()=>[u(a(n.student.contactNumber),1)]),_:2},1024)]),_:2},1024),t(_,{name:"parent"},{default:e(()=>[u(a(n.student.fatherName)+" ",1),t(D,{block:""},{default:e(()=>[u(a(n.student.motherName),1)]),_:2},1024)]),_:2},1024),t(_,{name:"admissionDate"},{default:e(()=>[u(a(n.student.joiningDate.formatted)+" ",1),t(D,{block:""},{default:e(()=>[u(a(n.student.codeNumber),1)]),_:2},1024)]),_:2},1024),t(_,{name:"course"},{default:e(()=>[u(a(n.student.courseName)+" ",1),t(D,{block:""},{default:e(()=>[u(a(n.student.batchName),1)]),_:2},1024)]),_:2},1024),t(_,{name:"status"},{default:e(()=>[t(N,{design:n.status.color},{default:e(()=>[u(a(n.status.label),1)]),_:2},1032,["design"])]),_:2},1024),t(_,{name:"createdAt"},{default:e(()=>[u(a(n.createdAt.formatted),1)]),_:2},1024),t(_,{name:"action"},{default:e(()=>[t(C,null,{default:e(()=>[t(B,{icon:"fas fa-arrow-circle-right",onClick:M=>f(g).push({name:"StudentEditRequestShow",params:{uuid:n.uuid}})},{default:e(()=>[u(a(m.$trans("general.show")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{W as default};
