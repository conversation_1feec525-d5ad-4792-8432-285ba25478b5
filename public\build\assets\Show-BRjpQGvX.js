import{i as P,u as C,h as T,l as V,r as c,a as N,o as d,e as n,w as e,f as l,q as m,b as p,d as H,s,t as o,y as I,F as D}from"./app-DvIo72ZO.js";const R={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},j={name:"AcademicBatchShow"},M=Object.assign(j,{setup(E){P();const u=C(),b=T(),_={},h="academic/batch/",t=V({..._}),f=a=>{Object.assign(t,a)};return(a,i)=>{const g=c("PageHeaderAction"),B=c("PageHeader"),$=c("TextMuted"),r=c("BaseDataView"),A=c("BaseButton"),w=c("ShowButton"),S=c("BaseCard"),k=c("ShowItem"),y=c("ParentTransition");return d(),N(D,null,[n(B,{title:a.$trans(l(u).meta.trans,{attribute:a.$trans(l(u).meta.label)}),navs:[{label:a.$trans("academic.academic"),path:"Academic"},{label:a.$trans("academic.batch.batch"),path:"AcademicBatchList"}]},{default:e(()=>[n(g,{name:"AcademicBatch",title:a.$trans("academic.batch.batch"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(y,{appear:"",visibility:!0},{default:e(()=>[n(k,{"init-url":h,uuid:l(u).params.uuid,onSetItem:f,onRedirectTo:i[1]||(i[1]=v=>l(b).push({name:"AcademicBatch"}))},{default:e(()=>[t.uuid?(d(),m(S,{key:0},{title:e(()=>[s(o(t.name),1)]),footer:e(()=>[n(w,null,{default:e(()=>[l(I)("batch:edit")?(d(),m(A,{key:0,design:"primary",onClick:i[0]||(i[0]=v=>l(b).push({name:"AcademicBatchEdit",params:{uuid:t.uuid}}))},{default:e(()=>[s(o(a.$trans("general.edit")),1)]),_:1})):p("",!0)]),_:1})]),default:e(()=>[H("dl",R,[n(r,{label:a.$trans("academic.batch.props.name")},{default:e(()=>[s(o(t.name)+" ",1),t.pgAccount?(d(),m($,{key:0,block:""},{default:e(()=>[s(o(t.pgAccount),1)]),_:1})):p("",!0)]),_:1},8,["label"]),n(r,{label:a.$trans("academic.course.course")},{default:e(()=>[s(o(t.course.name),1)]),_:1},8,["label"]),n(r,{label:a.$trans("academic.batch.props.max_strength")},{default:e(()=>[s(o(t.maxStrength),1)]),_:1},8,["label"]),n(r,{label:a.$trans("academic.batch.props.roll_number_prefix")},{default:e(()=>[s(o(t.rollNumberPrefix),1)]),_:1},8,["label"]),n(r,{class:"col-span-1 sm:col-span-2",label:a.$trans("academic.batch.props.description")},{default:e(()=>[s(o(t.description),1)]),_:1},8,["label"]),n(r,{label:a.$trans("general.created_at")},{default:e(()=>[s(o(t.createdAt.formatted),1)]),_:1},8,["label"]),n(r,{label:a.$trans("general.updated_at")},{default:e(()=>[s(o(t.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):p("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{M as default};
