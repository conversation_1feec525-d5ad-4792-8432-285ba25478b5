import{u as M,l as y,n as R,r as s,q as C,o as h,w as e,d as b,e as t,h as T,j as V,m as j,f as i,a as S,F as N,v as E,s as r,t as m}from"./app-DvIo72ZO.js";const O={class:"grid grid-cols-3 gap-6"},U={class:"col-span-3 sm:col-span-1"},q={__name:"Filter",emits:["hide"],setup(P,{emit:u}){M();const c=u,f={name:""},p=y({...f}),F=y({isLoaded:!0});return R(async()=>{F.isLoaded=!0}),(d,l)=>{const $=s("BaseInput"),a=s("FilterForm");return h(),C(a,{"init-form":f,form:p,multiple:[],onHide:l[1]||(l[1]=o=>c("hide"))},{default:e(()=>[b("div",O,[b("div",U,[t($,{type:"text",modelValue:p.name,"onUpdate:modelValue":l[0]||(l[0]=o=>p.name=o),name:"name",label:d.$trans("finance.payment_method.props.name")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},z={name:"FinancePaymentMethodList"},J=Object.assign(z,{setup(P){const u=T(),c=V("emitter");let f=["create","filter"],p=["print","pdf","excel"];const F="finance/paymentMethod/",d=j(!1),l=y({}),$=a=>{Object.assign(l,a)};return(a,o)=>{const w=s("PageHeaderAction"),B=s("PageHeader"),k=s("ParentTransition"),_=s("DataCell"),v=s("FloatingMenuItem"),D=s("FloatingMenu"),I=s("DataRow"),A=s("BaseButton"),H=s("DataTable"),L=s("ListItem");return h(),C(L,{"init-url":F,onSetItems:$},{header:e(()=>[t(B,{title:a.$trans("finance.payment_method.payment_method"),navs:[{label:a.$trans("finance.finance"),path:"Finance"}]},{default:e(()=>[t(w,{url:"finance/payment-methods/",name:"FinancePaymentMethod",title:a.$trans("finance.payment_method.payment_method"),actions:i(f),"dropdown-actions":i(p),onToggleFilter:o[0]||(o[0]=n=>d.value=!d.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(k,{appear:"",visibility:d.value},{default:e(()=>[t(q,{onRefresh:o[1]||(o[1]=n=>i(c).emit("listItems")),onHide:o[2]||(o[2]=n=>d.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(k,{appear:"",visibility:!0},{default:e(()=>[t(H,{header:l.headers,meta:l.meta,module:"finance.payment_method",onRefresh:o[4]||(o[4]=n=>i(c).emit("listItems"))},{actionButton:e(()=>[t(A,{onClick:o[3]||(o[3]=n=>i(u).push({name:"FinancePaymentMethodCreate"}))},{default:e(()=>[r(m(a.$trans("global.add",{attribute:a.$trans("finance.payment_method.payment_method")})),1)]),_:1})]),default:e(()=>[(h(!0),S(N,null,E(l.data,n=>(h(),C(I,{key:n.uuid,onDoubleClick:g=>i(u).push({name:"FinancePaymentMethodShow",params:{uuid:n.uuid}})},{default:e(()=>[t(_,{name:"name"},{default:e(()=>[r(m(n.name),1)]),_:2},1024),t(_,{name:"code"},{default:e(()=>[r(m(n.code),1)]),_:2},1024),t(_,{name:"createdAt"},{default:e(()=>[r(m(n.createdAt.formatted),1)]),_:2},1024),t(_,{name:"action"},{default:e(()=>[t(D,null,{default:e(()=>[t(v,{icon:"fas fa-arrow-circle-right",onClick:g=>i(u).push({name:"FinancePaymentMethodShow",params:{uuid:n.uuid}})},{default:e(()=>[r(m(a.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(v,{icon:"fas fa-edit",onClick:g=>i(u).push({name:"FinancePaymentMethodEdit",params:{uuid:n.uuid}})},{default:e(()=>[r(m(a.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t(v,{icon:"fas fa-copy",onClick:g=>i(u).push({name:"FinancePaymentMethodDuplicate",params:{uuid:n.uuid}})},{default:e(()=>[r(m(a.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t(v,{icon:"fas fa-trash",onClick:g=>i(c).emit("deleteItem",{uuid:n.uuid})},{default:e(()=>[r(m(a.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{J as default};
