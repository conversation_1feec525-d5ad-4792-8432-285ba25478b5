import{u as N,l as b,n as V,r as n,q as D,o as y,w as e,d as k,b as j,s as i,t as s,e as t,h as q,j as A,m as H,f,a as P,F as O,v as U}from"./app-DvIo72ZO.js";const R={class:"grid grid-cols-3 gap-6"},x={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},G={__name:"Filter",emits:["hide"],setup(B,{emit:g}){const c=N(),$=g,v={employees:[],startDate:"",endDate:""},m=b({...v});b({});const d=b({employees:[],isLoaded:!c.query.employees});return V(async()=>{d.employees=c.query.employees?c.query.employees.split(","):[],d.isLoaded=!0}),(u,l)=>{const p=n("BaseSelectSearch"),r=n("DatePicker"),h=n("FilterForm");return y(),D(h,{"init-form":v,form:m,multiple:["employees"],onHide:l[3]||(l[3]=o=>$("hide"))},{default:e(()=>[k("div",R,[k("div",x,[d.isLoaded?(y(),D(p,{key:0,multiple:"",name:"employees",label:u.$trans("global.select",{attribute:u.$trans("employee.employee")}),modelValue:m.employees,"onUpdate:modelValue":l[0]||(l[0]=o=>m.employees=o),"value-prop":"uuid","init-search":d.employees,"search-key":"name","search-action":"employee/summary"},{selectedOption:e(o=>[i(s(o.value.name)+" ("+s(o.value.codeNumber)+") ",1)]),listOption:e(o=>[i(s(o.option.name)+" ("+s(o.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):j("",!0)]),k("div",z,[t(r,{start:m.startDate,"onUpdate:start":l[1]||(l[1]=o=>m.startDate=o),end:m.endDate,"onUpdate:end":l[2]||(l[2]=o=>m.endDate=o),name:"dateBetween",as:"range",label:u.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},J={name:"EmployeeEditRequestList"},Q=Object.assign(J,{setup(B){const g=q(),c=A("emitter");let $=["filter"],v=["print","pdf","excel"];const m="employee/editRequest/",d=H(!1),u=b({}),l=p=>{Object.assign(u,p)};return(p,r)=>{const h=n("PageHeaderAction"),o=n("PageHeader"),w=n("ParentTransition"),_=n("DataCell"),F=n("TextMuted"),E=n("BaseBadge"),C=n("FloatingMenuItem"),I=n("FloatingMenu"),S=n("DataRow"),T=n("DataTable"),L=n("ListItem");return y(),D(L,{"init-url":m,onSetItems:l},{header:e(()=>[t(o,{title:p.$trans("employee.edit_request.edit_request"),navs:[{label:p.$trans("employee.employee"),path:"Employee"}]},{default:e(()=>[t(h,{url:"employee/edit-requests/",name:"EmployeeEditRequest",title:p.$trans("employee.edit_request.edit_request"),actions:f($),"dropdown-actions":f(v),onToggleFilter:r[0]||(r[0]=a=>d.value=!d.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(w,{appear:"",visibility:d.value},{default:e(()=>[t(G,{onRefresh:r[1]||(r[1]=a=>f(c).emit("listItems")),onHide:r[2]||(r[2]=a=>d.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(w,{appear:"",visibility:!0},{default:e(()=>[t(T,{header:u.headers,meta:u.meta,module:"employee.edit_request",onRefresh:r[3]||(r[3]=a=>f(c).emit("listItems"))},{default:e(()=>[(y(!0),P(O,null,U(u.data,a=>(y(),D(S,{key:a.uuid,onDoubleClick:M=>f(g).push({name:"EmployeeEditRequestShow",params:{uuid:a.uuid}})},{default:e(()=>[t(_,{name:"name"},{default:e(()=>[i(s(a.employee.name),1)]),_:2},1024),t(_,{name:"joiningDate"},{default:e(()=>[i(s(a.employee.joiningDate.formatted)+" ",1),t(F,{block:""},{default:e(()=>[i(s(a.employee.codeNumber),1)]),_:2},1024)]),_:2},1024),t(_,{name:"designation"},{default:e(()=>[i(s(a.employee.designation)+" ",1),t(F,{block:""},{default:e(()=>[i(s(a.employee.department),1)]),_:2},1024)]),_:2},1024),t(_,{name:"status"},{default:e(()=>[t(E,{design:a.status.color},{default:e(()=>[i(s(a.status.label),1)]),_:2},1032,["design"])]),_:2},1024),t(_,{name:"createdAt"},{default:e(()=>[i(s(a.createdAt.formatted),1)]),_:2},1024),t(_,{name:"action"},{default:e(()=>[t(I,null,{default:e(()=>[t(C,{icon:"fas fa-arrow-circle-right",onClick:M=>f(g).push({name:"EmployeeEditRequestShow",params:{uuid:a.uuid}})},{default:e(()=>[i(s(p.$trans("general.show")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Q as default};
