import{u as P,l as b,n as R,r as o,q as k,o as $,w as e,d as F,e as t,h as T,j as V,m as j,f as l,a as S,F as N,v as q,s as r,t as u}from"./app-DvIo72ZO.js";const E={class:"grid grid-cols-3 gap-6"},O={class:"col-span-3 sm:col-span-1"},U={__name:"Filter",emits:["hide"],setup(w,{emit:m}){P();const c=m,f={name:""},p=b({...f}),M=b({isLoaded:!0});return R(async()=>{M.isLoaded=!0}),(d,i)=>{const C=o("BaseInput"),n=o("FilterForm");return $(),k(n,{"init-form":f,form:p,multiple:[],onHide:i[1]||(i[1]=s=>c("hide"))},{default:e(()=>[F("div",E,[F("div",O,[t(C,{type:"text",modelValue:p.name,"onUpdate:modelValue":i[0]||(i[0]=s=>p.name=s),name:"name",label:d.$trans("mess.meal.props.name")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},z={name:"MessMealList"},J=Object.assign(z,{setup(w){const m=T(),c=V("emitter");let f=["create","filter"],p=["print","pdf","excel"];const M="mess/meal/",d=j(!1),i=b({}),C=n=>{Object.assign(i,n)};return(n,s)=>{const y=o("PageHeaderAction"),B=o("PageHeader"),h=o("ParentTransition"),_=o("DataCell"),g=o("FloatingMenuItem"),D=o("FloatingMenu"),I=o("DataRow"),A=o("BaseButton"),H=o("DataTable"),L=o("ListItem");return $(),k(L,{"init-url":M,"additional-query":{},onSetItems:C},{header:e(()=>[t(B,{title:n.$trans("mess.meal.meal"),navs:[{label:n.$trans("mess.mess"),path:"Mess"}]},{default:e(()=>[t(y,{url:"mess/meals/",name:"MessMeal",title:n.$trans("mess.meal.meal"),actions:l(f),"dropdown-actions":l(p),"config-path":"MessConfig",onToggleFilter:s[0]||(s[0]=a=>d.value=!d.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(h,{appear:"",visibility:d.value},{default:e(()=>[t(U,{onRefresh:s[1]||(s[1]=a=>l(c).emit("listItems")),onHide:s[2]||(s[2]=a=>d.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(h,{appear:"",visibility:!0},{default:e(()=>[t(H,{header:i.headers,meta:i.meta,module:"mess.meal",onRefresh:s[4]||(s[4]=a=>l(c).emit("listItems"))},{actionButton:e(()=>[t(A,{onClick:s[3]||(s[3]=a=>l(m).push({name:"MessMealCreate"}))},{default:e(()=>[r(u(n.$trans("global.add",{attribute:n.$trans("mess.meal.meal")})),1)]),_:1})]),default:e(()=>[($(!0),S(N,null,q(i.data,a=>($(),k(I,{key:a.uuid,onDoubleClick:v=>l(m).push({name:"MessMealShow",params:{uuid:a.uuid}})},{default:e(()=>[t(_,{name:"name"},{default:e(()=>[r(u(a.name),1)]),_:2},1024),t(_,{name:"type"},{default:e(()=>[r(u(a.type.label),1)]),_:2},1024),t(_,{name:"createdAt"},{default:e(()=>[r(u(a.createdAt.formatted),1)]),_:2},1024),t(_,{name:"action"},{default:e(()=>[t(D,null,{default:e(()=>[t(g,{icon:"fas fa-arrow-circle-right",onClick:v=>l(m).push({name:"MessMealShow",params:{uuid:a.uuid}})},{default:e(()=>[r(u(n.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(g,{icon:"fas fa-edit",onClick:v=>l(m).push({name:"MessMealEdit",params:{uuid:a.uuid}})},{default:e(()=>[r(u(n.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t(g,{icon:"fas fa-copy",onClick:v=>l(m).push({name:"MessMealDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[r(u(n.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t(g,{icon:"fas fa-trash",onClick:v=>l(c).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[r(u(n.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{J as default};
