import{u as S,H as v,I as q,l as f,n as C,r as m,q as H,o as y,w as u,d,e as l,f as o,s as B,t as F,N as O,K as T,a as L,F as N}from"./app-DvIo72ZO.js";import{d as x}from"./vuedraggable.umd-DSTqH_PI.js";const D={class:"grid grid-cols-3 gap-6"},I={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-2"},M=["onClick"],z={class:"mt-4 grid grid-cols-4 gap-4"},J={class:"col-span-4 sm:col-span-1"},Q={class:"col-span-4 sm:col-span-1"},W={class:"col-span-4 sm:col-span-1"},X={class:"col-span-4 sm:col-span-1"},Y={class:"col-span-4 sm:col-span-1"},Z={class:"col-span-4 sm:col-span-1"},ee={class:"mt-4"},ae={name:"ExamGradeForm"},re=Object.assign(ae,{setup(G){const _=S(),i={name:"",description:"",records:[]},b={uuid:v(),code:"",minScore:"",maxScore:"",value:"",label:"",isFailGrade:!1,description:""},g="exam/grade/",s=q(g),$=f({}),c=f({...i}),V=f({isLoaded:!_.params.uuid}),h=t=>{Object.assign($,t)},U=()=>{c.records.push({...b,uuid:v()}),V.isLoaded=!0},w=async t=>{await O()&&(c.records.length==1?c.records=[b]:c.records.splice(t,1))},E=t=>{let n=t.records.map(p=>({...p}));Object.assign(i,{...t,records:n}),Object.assign(c,T(i)),V.isLoaded=!0};return C(async()=>{_.params.uuid||U()}),(t,n)=>{const p=m("BaseInput"),P=m("BaseTextarea"),k=m("BaseSwitch"),R=m("BaseFieldset"),A=m("BaseBadge"),j=m("FormAction");return y(),H(j,{"pre-requisites":!1,onSetPreRequisites:h,"init-url":g,"init-form":i,form:c,"set-form":E,redirect:"ExamGrade"},{default:u(()=>[d("div",D,[d("div",I,[l(p,{type:"text",modelValue:c.name,"onUpdate:modelValue":n[0]||(n[0]=a=>c.name=a),name:"name",label:t.$trans("exam.grade.props.name"),error:o(s).name,"onUpdate:error":n[1]||(n[1]=a=>o(s).name=a)},null,8,["modelValue","label","error"])]),d("div",K,[l(P,{rows:1,modelValue:c.description,"onUpdate:modelValue":n[2]||(n[2]=a=>c.description=a),name:"description",label:t.$trans("exam.grade.props.description"),error:o(s).description,"onUpdate:error":n[3]||(n[3]=a=>o(s).description=a)},null,8,["modelValue","label","error"])])]),l(o(x),{list:c.records,"item-key":"uuid"},{item:u(({element:a,index:r})=>[l(R,{class:"mt-4"},{legend:u(()=>[n[5]||(n[5]=d("i",{class:"fas fa-arrows mr-2 cursor-pointer"},null,-1)),B(" "+F(r+1)+". ",1),d("span",{class:"text-danger ml-2 cursor-pointer",onClick:e=>w(r)},n[4]||(n[4]=[d("i",{class:"fas fa-times-circle"},null,-1)]),8,M)]),default:u(()=>[d("div",z,[d("div",J,[l(p,{type:"text",modelValue:a.code,"onUpdate:modelValue":e=>a.code=e,name:`records.${r}.code`,label:t.$trans("exam.grade.props.code"),error:o(s)[`records.${r}.code`],"onUpdate:error":e=>o(s)[`records.${r}.code`]=e},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),d("div",Q,[l(p,{type:"number",step:.01,modelValue:a.minScore,"onUpdate:modelValue":e=>a.minScore=e,name:`records.${r}.minScore`,label:t.$trans("exam.grade.props.min_score"),error:o(s)[`records.${r}.minScore`],"onUpdate:error":e=>o(s)[`records.${r}.minScore`]=e},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),d("div",W,[l(p,{type:"number",step:.01,modelValue:a.maxScore,"onUpdate:modelValue":e=>a.maxScore=e,name:`records.${r}.maxScore`,label:t.$trans("exam.grade.props.max_score"),error:o(s)[`records.${r}.maxScore`],"onUpdate:error":e=>o(s)[`records.${r}.maxScore`]=e},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),d("div",X,[l(p,{type:"number",modelValue:a.value,"onUpdate:modelValue":e=>a.value=e,name:`records.${r}.value`,label:t.$trans("exam.grade.props.value"),error:o(s)[`records.${r}.value`],"onUpdate:error":e=>o(s)[`records.${r}.value`]=e},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),d("div",Y,[l(p,{type:"text",modelValue:a.label,"onUpdate:modelValue":e=>a.label=e,name:`records.${r}.label`,label:t.$trans("exam.grade.props.label"),error:o(s)[`records.${r}.label`],"onUpdate:error":e=>o(s)[`records.${r}.label`]=e},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),d("div",Z,[l(k,{vertical:"",modelValue:a.isFailGrade,"onUpdate:modelValue":e=>a.isFailGrade=e,label:t.$trans("exam.grade.props.fail_grade"),name:`records.${r}.isFailGrade`,error:o(s)[`records.${r}.isFailGrade`],"onUpdate:error":e=>o(s)[`records.${r}.isFailGrade`]=e},null,8,["modelValue","onUpdate:modelValue","label","name","error","onUpdate:error"])])])]),_:2},1024)]),_:1},8,["list"]),d("div",ee,[l(A,{design:"primary",onClick:U,class:"cursor-pointer"},{default:u(()=>[B(F(t.$trans("global.add",{attribute:t.$trans("general.record")})),1)]),_:1})])]),_:1},8,["form"])}}}),oe={name:"ExamGradeAction"},le=Object.assign(oe,{setup(G){const _=S();return(i,b)=>{const g=m("PageHeaderAction"),s=m("PageHeader"),$=m("ParentTransition");return y(),L(N,null,[l(s,{title:i.$trans(o(_).meta.trans,{attribute:i.$trans(o(_).meta.label)}),navs:[{label:i.$trans("exam.exam"),path:"Exam"},{label:i.$trans("exam.grade.grade"),path:"ExamGradeList"}]},{default:u(()=>[l(g,{name:"ExamGrade",title:i.$trans("exam.grade.grade"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l($,{appear:"",visibility:!0},{default:u(()=>[l(re)]),_:1})],64)}}});export{le as default};
