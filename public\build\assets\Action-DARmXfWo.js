import{u as v,I as U,l as V,r as d,q as k,o as f,w as m,d as u,b as B,f as o,s as g,t as A,e as l,K as F,a as N,F as O}from"./app-DvIo72ZO.js";const P={class:"grid grid-cols-4 gap-6"},D={class:"col-span-4 sm:col-span-1"},j={class:"col-span-4 sm:col-span-1"},y={class:"col-span-4 sm:col-span-1"},H={class:"col-span-4 sm:col-span-1"},L={class:"col-span-4"},C={name:"StudentAttendanceTimesheetForm"},E=Object.assign(C,{setup($){const c=v(),r={student:"",date:"",inAt:"",outAt:"",remarks:""},b="student/attendance/timesheet/",s=U(b),n=V({...r}),i=V({student:"",isLoaded:!c.params.uuid}),h=a=>{var t,p;Object.assign(r,{student:(t=a.student)==null?void 0:t.uuid,date:a.date.value,inAt:a.inAtTime.at,outAt:a.outAtTime.at,remarks:a.remarks}),Object.assign(n,F(r)),i.student=(p=a.student)==null?void 0:p.uuid,i.isLoaded=!0};return(a,t)=>{const p=d("BaseSelectSearch"),_=d("DatePicker"),S=d("BaseTextarea"),T=d("FormAction");return f(),k(T,{"init-url":b,"init-form":r,form:n,"set-form":h,redirect:"StudentAttendanceTimesheet"},{default:m(()=>[u("div",P,[u("div",D,[i.isLoaded?(f(),k(p,{key:0,name:"student",label:a.$trans("global.select",{attribute:a.$trans("student.student")}),modelValue:n.student,"onUpdate:modelValue":t[0]||(t[0]=e=>n.student=e),error:o(s).student,"onUpdate:error":t[1]||(t[1]=e=>o(s).student=e),"value-prop":"uuid","init-search":i.student,"search-key":"name","search-action":"student/list"},{selectedOption:m(e=>[g(A(e.value.name)+" ("+A(e.value.codeNumber)+") ",1)]),listOption:m(e=>[g(A(e.option.name)+" ("+A(e.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","error","init-search"])):B("",!0)]),u("div",j,[l(_,{modelValue:n.date,"onUpdate:modelValue":t[2]||(t[2]=e=>n.date=e),name:"date",label:a.$trans("student.attendance.timesheet.props.date"),"no-clear":"",error:o(s).date,"onUpdate:error":t[3]||(t[3]=e=>o(s).date=e)},null,8,["modelValue","label","error"])]),u("div",y,[l(_,{modelValue:n.inAt,"onUpdate:modelValue":t[4]||(t[4]=e=>n.inAt=e),name:"inAt",label:a.$trans("student.attendance.timesheet.props.in_at"),as:"time",error:o(s).inAt,"onUpdate:error":t[5]||(t[5]=e=>o(s).inAt=e)},null,8,["modelValue","label","error"])]),u("div",H,[l(_,{modelValue:n.outAt,"onUpdate:modelValue":t[6]||(t[6]=e=>n.outAt=e),name:"outAt",label:a.$trans("student.attendance.timesheet.props.out_at"),as:"time",error:o(s).outAt,"onUpdate:error":t[7]||(t[7]=e=>o(s).outAt=e)},null,8,["modelValue","label","error"])]),u("div",L,[l(S,{modelValue:n.remarks,"onUpdate:modelValue":t[8]||(t[8]=e=>n.remarks=e),name:"remarks",label:a.$trans("student.attendance.timesheet.props.remarks"),error:o(s).remarks,"onUpdate:error":t[9]||(t[9]=e=>o(s).remarks=e)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),w={name:"StudentAttendanceTimesheetAction"},I=Object.assign(w,{setup($){const c=v();return(r,b)=>{const s=d("PageHeaderAction"),n=d("PageHeader"),i=d("ParentTransition");return f(),N(O,null,[l(n,{title:r.$trans(o(c).meta.trans,{attribute:r.$trans(o(c).meta.label)}),navs:[{label:r.$trans("student.student"),path:"Student"},{label:r.$trans("student.attendance.attendance"),path:"StudentAttendance"},{label:r.$trans("student.attendance.timesheet.timesheets"),path:"StudentAttendanceTimesheetList"}]},{default:m(()=>[l(s,{name:"StudentAttendanceTimesheet",title:r.$trans("student.attendance.timesheet.timesheet"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(i,{appear:"",visibility:!0},{default:m(()=>[l(E)]),_:1})],64)}}});export{I as default};
