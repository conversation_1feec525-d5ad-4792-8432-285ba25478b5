import{j as E,I as P,l as C,r as u,q as I,o as d,w as a,d as f,a as V,b as g,e as s,f as r,s as m,t as i,u as W,h as L,F as A,v as O,y as T,i as D,m as H}from"./app-DvIo72ZO.js";const M={class:"grid grid-cols-3 gap-6"},G={class:"col-span-3"},q={key:0,class:"col-span-1"},z={key:1,class:"col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={name:"SubjectAllocationForm"},Z=Object.assign(Y,{props:{uuid:{type:String,default:""}},emits:["completed"],setup(S,{emit:F}){const j=F,b=E("emitter"),_={type:"course",courses:[],batches:[],credit:1,maxClassPerWeek:"",examFee:0,courseFee:0,isElective:!1,hasNoExam:!1,hasGrading:!1},y="academic/subject/subjectRecord/",l=P(y),o=C({..._}),v=()=>{j("completed"),b.emit("listItems")};return(n,e)=>{const k=u("BaseRadioGroup"),p=u("BaseSelectSearch"),h=u("BaseInput"),$=u("BaseSwitch"),x=u("FormAction");return d(),I(x,{"init-url":y,uuid:S.uuid,"no-data-fetch":"",action:"create","init-form":_,form:o,"keep-adding":!1,"after-submit":v},{default:a(()=>[f("div",M,[f("div",G,[s(k,{options:[{label:n.$trans("academic.course.course"),value:"course"},{label:n.$trans("academic.batch.batch"),value:"batch"}],name:"type",modelValue:o.type,"onUpdate:modelValue":e[0]||(e[0]=t=>o.type=t),error:r(l).type,"onUpdate:error":e[1]||(e[1]=t=>r(l).type=t),horizontal:""},null,8,["options","modelValue","error"])]),o.type=="course"?(d(),V("div",q,[s(p,{name:"courses",multiple:"",label:n.$trans("academic.course.course"),modelValue:o.courses,"onUpdate:modelValue":e[2]||(e[2]=t=>o.courses=t),error:r(l).courses,"onUpdate:error":e[3]||(e[3]=t=>r(l).courses=t),"value-prop":"uuid","search-action":"academic/course/list"},{selectedOption:a(t=>[m(i(t.value.division.name)+" - "+i(t.value.nameWithTerm),1)]),listOption:a(t=>[m(i(t.option.division.name)+" - "+i(t.option.nameWithTerm),1)]),_:1},8,["label","modelValue","error"])])):g("",!0),o.type=="batch"?(d(),V("div",z,[s(p,{name:"batches",multiple:"",label:n.$trans("academic.batch.batch"),modelValue:o.batches,"onUpdate:modelValue":e[4]||(e[4]=t=>o.batches=t),error:r(l).batches,"onUpdate:error":e[5]||(e[5]=t=>r(l).batches=t),"search-key":"course_batch","value-prop":"uuid","search-action":"academic/batch/list"},{selectedOption:a(t=>[m(i(t.value.course.name)+" - "+i(t.value.name),1)]),listOption:a(t=>[m(i(t.option.course.nameWithTerm)+" - "+i(t.option.name),1)]),_:1},8,["label","modelValue","error"])])):g("",!0),f("div",J,[s(h,{step:.01,type:"number",modelValue:o.credit,"onUpdate:modelValue":e[6]||(e[6]=t=>o.credit=t),name:"credit",label:n.$trans("academic.subject.props.credit"),error:r(l).credit,"onUpdate:error":e[7]||(e[7]=t=>r(l).credit=t)},null,8,["modelValue","label","error"])]),f("div",K,[s(h,{type:"text",modelValue:o.maxClassPerWeek,"onUpdate:modelValue":e[8]||(e[8]=t=>o.maxClassPerWeek=t),name:"maxClassPerWeek",label:n.$trans("academic.subject.props.max_class_per_week"),error:r(l).maxClassPerWeek,"onUpdate:error":e[9]||(e[9]=t=>r(l).maxClassPerWeek=t)},null,8,["modelValue","label","error"])]),f("div",Q,[s(h,{currency:"",modelValue:o.examFee,"onUpdate:modelValue":e[10]||(e[10]=t=>o.examFee=t),name:"examFee",label:n.$trans("academic.subject.props.exam_fee"),error:r(l).examFee,"onUpdate:error":e[11]||(e[11]=t=>r(l).examFee=t)},null,8,["modelValue","label","error"])]),f("div",R,[s($,{vertical:"",modelValue:o.isElective,"onUpdate:modelValue":e[12]||(e[12]=t=>o.isElective=t),name:"isElective",label:n.$trans("academic.subject.props.is_elective"),error:r(l).isElective,"onUpdate:error":e[13]||(e[13]=t=>r(l).isElective=t)},null,8,["modelValue","label","error"])]),f("div",X,[s($,{vertical:"",modelValue:o.hasNoExam,"onUpdate:modelValue":e[14]||(e[14]=t=>o.hasNoExam=t),name:"hasNoExam",label:n.$trans("academic.subject.props.has_no_exam"),error:r(l).hasNoExam,"onUpdate:error":e[15]||(e[15]=t=>r(l).hasNoExam=t)},null,8,["modelValue","label","error"])])])]),_:1},8,["uuid","form"])}}}),ee={class:"grid grid-cols-3 gap-6"},te={class:"col-span-3 sm:col-span-1"},ae={name:"SubjectAllocationFeeForm"},se=Object.assign(ae,{props:{uuid:{type:String,default:""}},emits:["completed"],setup(S,{emit:F}){const j=F,b=E("emitter"),_={examFee:0,courseFee:0},y="academic/subject/subjectRecord/",l=P(y),o=C({..._}),v=()=>{j("completed"),b.emit("listItems")};return(n,e)=>{const k=u("BaseInput"),p=u("FormAction");return d(),I(p,{"init-url":y,uuid:S.uuid,"no-data-fetch":"",action:"updateFee","init-form":_,form:o,"keep-adding":!1,"after-submit":v},{default:a(()=>[f("div",ee,[f("div",te,[s(k,{currency:"",modelValue:o.examFee,"onUpdate:modelValue":e[0]||(e[0]=h=>o.examFee=h),name:"examFee",label:n.$trans("academic.subject.props.exam_fee"),error:r(l).examFee,"onUpdate:error":e[1]||(e[1]=h=>r(l).examFee=h)},null,8,["modelValue","label","error"])])])]),_:1},8,["uuid","form"])}}}),oe={class:"grid grid-cols-3 gap-6"},ne={class:"col-span-3 sm:col-span-1"},le={__name:"Filter",emits:["hide"],setup(S,{emit:F}){const j=F,b={course:""},_=C({...b});return(y,l)=>{const o=u("BaseInput"),v=u("FilterForm");return d(),I(v,{"init-form":b,form:_,onHide:l[1]||(l[1]=n=>j("hide"))},{default:a(()=>[f("div",oe,[f("div",ne,[s(o,{type:"text",modelValue:_.course,"onUpdate:modelValue":l[0]||(l[0]=n=>_.course=n),name:"course",label:y.$trans("academic.course.course")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},re={key:0},ie={key:0},ue={key:0,class:"far fa-check-circle fa-lg text-success"},ce={key:0,class:"far fa-check-circle fa-lg text-success"},me={key:0,class:"far fa-check-circle fa-lg text-success"},de={name:"AcademicSubjectRecordList"},pe=Object.assign(de,{props:{subject:{type:Object,default(){return{}}},showFilter:{type:Boolean,default:!1}},emits:["refresh","hideFilter"],setup(S,{emit:F}){const j=W();L();const b=E("emitter"),_=F,y="academic/subject/subjectRecord/",l=C({}),o=v=>{Object.assign(l,v)};return(v,n)=>{const e=u("ParentTransition"),k=u("TextMuted"),p=u("DataCell"),h=u("FloatingMenuItem"),$=u("FloatingMenu"),x=u("DataRow"),t=u("DataTable"),w=u("ListItem");return d(),V(A,null,[s(e,{appear:"",visibility:S.showFilter},{default:a(()=>[s(le,{onRefresh:n[0]||(n[0]=c=>r(b).emit("listItems")),onHide:n[1]||(n[1]=c=>_("hideFilter"))})]),_:1},8,["visibility"]),s(w,{spacing:!1,"init-url":y,uuid:r(j).params.uuid,"additional-query":{details:!0},onSetItems:o},{default:a(()=>[s(e,{appear:"",visibility:!0},{default:a(()=>[s(t,{header:l.headers,meta:l.meta,module:"academic.subject",onRefresh:n[2]||(n[2]=c=>r(b).emit("listItems"))},{default:a(()=>[(d(!0),V(A,null,O(l.data,c=>(d(),I(x,{key:c.uuid},{default:a(()=>[s(p,{name:"course"},{default:a(()=>{var U;return[m(i(((U=c.course)==null?void 0:U.nameWithTerm)||"-")+" ",1),s(k,{block:""},{default:a(()=>[c.courseFee.value?(d(),V("span",re,i(v.$trans("academic.subject.props.course_fee"))+": "+i(c.courseFee.formatted),1)):g("",!0)]),_:2},1024),s(k,{block:""},{default:a(()=>[c.examFee.value?(d(),V("span",ie,i(v.$trans("academic.subject.props.exam_fee"))+": "+i(c.examFee.formatted),1)):g("",!0)]),_:2},1024)]}),_:2},1024),s(p,{name:"batch"},{default:a(()=>{var U,B,N;return[m(i(((B=(U=c.batch)==null?void 0:U.course)==null?void 0:B.name)||"-")+" ",1),(N=c.batch)!=null&&N.uuid?(d(),I(k,{key:0,block:""},{default:a(()=>[m(i(c.batch.name),1)]),_:2},1024)):g("",!0)]}),_:2},1024),s(p,{name:"credit"},{default:a(()=>[m(i(c.credit),1)]),_:2},1024),s(p,{name:"maxClassPerWeek"},{default:a(()=>[m(i(c.maxClassPerWeek),1)]),_:2},1024),s(p,{name:"isElective"},{default:a(()=>[c.isElective?(d(),V("i",ue)):g("",!0)]),_:2},1024),s(p,{name:"hasNoExam"},{default:a(()=>[c.hasNoExam?(d(),V("i",ce)):g("",!0)]),_:2},1024),s(p,{name:"hasGrading"},{default:a(()=>[c.hasGrading?(d(),V("i",me)):g("",!0)]),_:2},1024),s(p,{name:"action"},{default:a(()=>[s($,null,{default:a(()=>[r(T)("subject:update")?(d(),I(h,{key:0,icon:"fas fa-trash",onClick:U=>r(b).emit("deleteItem",{uuid:S.subject.uuid,moduleUuid:c.uuid})},{default:a(()=>[m(i(v.$trans("general.delete")),1)]),_:2},1032,["onClick"])):g("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1},8,["uuid"])],64)}}}),fe={class:"space-y-2"},be={class:"space-y-4"},_e={name:"AcademicSubjectShow"},$e=Object.assign(_e,{setup(S){D();const F=W(),j=L(),b=E("emitter"),_={},y="academic/subject/",l=H(!1),o=C({..._}),v=n=>{Object.assign(o,n)};return(n,e)=>{const k=u("BaseButton"),p=u("PageHeaderAction"),h=u("PageHeader"),$=u("ListItemView"),x=u("ListContainerVertical"),t=u("BaseCard"),w=u("DetailLayoutVertical"),c=u("ShowItem"),U=u("ParentTransition");return d(),V(A,null,[s(h,{title:n.$trans(r(F).meta.trans,{attribute:n.$trans(r(F).meta.label)}),navs:[{label:n.$trans("academic.academic"),path:"Academic"},{label:n.$trans("academic.subject.subject"),path:"AcademicSubjectList"}]},{default:a(()=>[s(p,{name:"AcademicSubject",title:n.$trans("academic.subject.subject"),actions:["list"]},{default:a(()=>[s(k,{design:"white",onClick:e[0]||(e[0]=B=>l.value=!0)},{default:a(()=>e[5]||(e[5]=[f("i",{class:"fas fa-filter"},null,-1)])),_:1})]),_:1},8,["title"])]),_:1},8,["title","navs"]),s(U,{appear:"",visibility:!0},{default:a(()=>[s(c,{"init-url":y,uuid:r(F).params.uuid,onSetItem:v,onRedirectTo:e[4]||(e[4]=B=>r(j).push({name:"AcademicSubject"}))},{default:a(()=>[o.uuid?(d(),I(w,{key:0},{detail:a(()=>[f("div",fe,[s(t,{"no-padding":"","no-content-padding":""},{title:a(()=>[m(i(o.name),1)]),action:a(()=>e[6]||(e[6]=[])),default:a(()=>[s(x,null,{default:a(()=>[s($,{label:n.$trans("academic.subject.props.name")},{default:a(()=>[m(i(o.name),1)]),_:1},8,["label"]),s($,{label:n.$trans("academic.subject.props.alias")},{default:a(()=>[m(i(o.alias),1)]),_:1},8,["label"]),s($,{label:n.$trans("academic.subject.props.code")},{default:a(()=>[m(i(o.code),1)]),_:1},8,["label"]),s($,{label:n.$trans("academic.subject.props.shortcode")},{default:a(()=>[m(i(o.shortcode),1)]),_:1},8,["label"]),s($,{class:"col-span-1 sm:col-span-2",label:n.$trans("academic.subject.props.description")},{default:a(()=>[m(i(o.description),1)]),_:1},8,["label"]),s($,{label:n.$trans("general.created_at")},{default:a(()=>[m(i(o.createdAt.formatted),1)]),_:1},8,["label"]),s($,{label:n.$trans("general.updated_at")},{default:a(()=>[m(i(o.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})])]),default:a(()=>[f("div",be,[s(pe,{subject:o,"show-filter":l.value,onHideFilter:e[1]||(e[1]=B=>l.value=!1)},null,8,["subject","show-filter"])]),r(T)("subject:edit")?(d(),I(t,{key:0,"no-padding":"","no-content-padding":""},{title:a(()=>[m(i(n.$trans("academic.subject.allocation")),1)]),default:a(()=>[s(Z,{onCompleted:e[2]||(e[2]=B=>r(b).emit("refreshItem"))})]),_:1})):g("",!0),r(T)("subject:edit")?(d(),I(t,{key:1,"no-padding":"","no-content-padding":""},{title:a(()=>[m(i(n.$trans("academic.subject.props.fee")),1)]),default:a(()=>[s(se,{onCompleted:e[3]||(e[3]=B=>r(b).emit("refreshItem"))})]),_:1})):g("",!0)]),_:1})):g("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{$e as default};
