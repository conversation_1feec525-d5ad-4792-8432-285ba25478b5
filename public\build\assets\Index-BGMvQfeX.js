import{u as H,l as R,n as E,r as i,q as f,o as p,w as e,d as g,e as a,b as k,s as l,t as u,h as W,j as z,y as B,m as G,f as c,a as J,F as K,v as Q}from"./app-DvIo72ZO.js";const X={class:"grid grid-cols-3 gap-6"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3 sm:col-span-1"},te={class:"col-span-3 sm:col-span-1"},ae={class:"col-span-3 sm:col-span-1"},ne={class:"col-span-3 sm:col-span-1"},se={class:"col-span-3 sm:col-span-1"},oe={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(h,{emit:y}){const v=H(),S=y,D={codeNumber:"",applicationNumber:"",name:"",guardianName:"",courses:[],startDate:"",endDate:"",type:""},o=R({...D});R({});const $=R({isLoaded:!v.query.courses});return E(async()=>{$.courses=v.query.courses?v.query.courses.split(","):[],$.isLoaded=!0}),(d,n)=>{const V=i("BaseInput"),C=i("BaseSelectSearch"),r=i("DatePicker"),m=i("BaseSelect"),w=i("FilterForm");return p(),f(w,{"init-form":D,form:o,multiple:["courses"],onHide:n[9]||(n[9]=s=>S("hide"))},{default:e(()=>[g("div",X,[g("div",Y,[a(V,{type:"text",modelValue:o.codeNumber,"onUpdate:modelValue":n[0]||(n[0]=s=>o.codeNumber=s),name:"codeNumber",label:d.$trans("student.registration.props.code_number")},null,8,["modelValue","label"])]),g("div",Z,[a(V,{type:"text",modelValue:o.applicationNumber,"onUpdate:modelValue":n[1]||(n[1]=s=>o.applicationNumber=s),name:"applicationNumber",label:d.$trans("student.online_registration.props.number")},null,8,["modelValue","label"])]),g("div",x,[a(V,{type:"text",modelValue:o.name,"onUpdate:modelValue":n[2]||(n[2]=s=>o.name=s),name:"name",label:d.$trans("contact.props.name")},null,8,["modelValue","label"])]),g("div",ee,[a(V,{type:"text",modelValue:o.guardianName,"onUpdate:modelValue":n[3]||(n[3]=s=>o.guardianName=s),name:"guardianName",label:d.$trans("guardian.props.name")},null,8,["modelValue","label"])]),g("div",te,[$.isLoaded?(p(),f(C,{key:0,multiple:"",name:"courses",label:d.$trans("global.select",{attribute:d.$trans("academic.course.course")}),modelValue:o.courses,"onUpdate:modelValue":n[4]||(n[4]=s=>o.courses=s),"value-prop":"uuid","init-search":$.courses,"search-action":"academic/course/list"},{selectedOption:e(s=>[l(u(s.value.nameWithTerm),1)]),listOption:e(s=>[l(u(s.option.nameWithTerm),1)]),_:1},8,["label","modelValue","init-search"])):k("",!0)]),g("div",ae,[a(r,{start:o.startDate,"onUpdate:start":n[5]||(n[5]=s=>o.startDate=s),end:o.endDate,"onUpdate:end":n[6]||(n[6]=s=>o.endDate=s),name:"dateBetween",as:"range",label:d.$trans("general.date_between")},null,8,["start","end","label"])]),g("div",ne,[a(m,{modelValue:o.status,"onUpdate:modelValue":n[7]||(n[7]=s=>o.status=s),name:"status",label:d.$trans("student.registration.props.status"),options:h.preRequisites.statuses},null,8,["modelValue","label","options"])]),g("div",se,[a(m,{modelValue:o.type,"onUpdate:modelValue":n[8]||(n[8]=s=>o.type=s),name:"type",label:d.$trans("student.registration.props.type"),options:h.preRequisites.types},null,8,["modelValue","label","options"])])])]),_:1},8,["form"])}}},le={name:"StudentRegistrationList"},ie=Object.assign(le,{setup(h){const y=W(),v=z("emitter");let S=["filter"];B("registration:create")&&S.unshift("create");let D=[];B("registration:export")&&(D=["print","pdf","excel"]);const o="student/registration/",$=R({statuses:[]}),d=G(!1),n=R({}),V=r=>{Object.assign(n,r)},C=r=>{Object.assign($,r)};return(r,m)=>{const w=i("PageHeaderAction"),s=i("PageHeader"),q=i("ParentTransition"),N=i("TextMuted"),I=i("BaseBadge"),b=i("DataCell"),F=i("FloatingMenuItem"),O=i("FloatingMenu"),L=i("DataRow"),M=i("BaseButton"),j=i("DataTable"),A=i("ListItem");return p(),f(A,{"init-url":o,"pre-requisites":!0,onSetPreRequisites:C,onSetItems:V},{header:e(()=>[a(s,{title:r.$trans("student.registration.registration"),navs:[{label:r.$trans("student.student"),path:"Student"}]},{default:e(()=>[a(w,{url:"student/registrations/",name:"StudentRegistration",title:r.$trans("student.registration.registration"),actions:c(S),"dropdown-actions":c(D),onToggleFilter:m[0]||(m[0]=t=>d.value=!d.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[a(q,{appear:"",visibility:d.value},{default:e(()=>[a(oe,{onRefresh:m[1]||(m[1]=t=>c(v).emit("listItems")),"pre-requisites":$,onHide:m[2]||(m[2]=t=>d.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:e(()=>[a(q,{appear:"",visibility:!0},{default:e(()=>[a(j,{header:n.headers,meta:n.meta,module:"student.registration",onRefresh:m[4]||(m[4]=t=>c(v).emit("listItems"))},{actionButton:e(()=>[c(B)("registration:create")?(p(),f(M,{key:0,onClick:m[3]||(m[3]=t=>c(y).push({name:"StudentRegistrationCreate"}))},{default:e(()=>[l(u(r.$trans("global.add",{attribute:r.$trans("student.registration.registration")})),1)]),_:1})):k("",!0)]),default:e(()=>[(p(!0),J(K,null,Q(n.data,t=>(p(),f(L,{key:t.uuid,onDoubleClick:_=>c(y).push({name:"StudentRegistrationShow",params:{uuid:t.uuid}})},{default:e(()=>[a(b,{name:"codeNumber"},{default:e(()=>[l(u(t.codeNumber)+" ",1),a(N,{block:""},{default:e(()=>[l(u(t.period.name),1)]),_:2},1024),a(N,{block:""},{default:e(()=>[l(u(t.admissionDate.formatted),1)]),_:2},1024),t.isOnline?(p(),f(I,{key:0},{default:e(()=>[l(u(r.$trans("student.registration.online")),1)]),_:1})):k("",!0)]),_:2},1024),a(b,{name:"name"},{default:e(()=>[l(u(t.contact.name)+" ",1),a(N,{block:""},{default:e(()=>[l(u(t.contact.contactNumber),1)]),_:2},1024)]),_:2},1024),a(b,{name:"guardianName"},{default:e(()=>{var _,T;return[l(u(((T=(_=t.contact.guardian)==null?void 0:_.contact)==null?void 0:T.name)||"-")+" ",1),a(N,{block:""},{default:e(()=>{var P,U;return[l(u(((U=(P=t.contact.guardian)==null?void 0:P.contact)==null?void 0:U.contactNumber)||""),1)]}),_:2},1024)]}),_:2},1024),a(b,{name:"birthDate"},{default:e(()=>[l(u(t.contact.birthDate.formatted),1)]),_:2},1024),a(b,{name:"course"},{default:e(()=>{var _;return[l(u(((_=t.course)==null?void 0:_.nameWithTerm)||"-")+" ",1),t.batchName?(p(),f(N,{key:0,block:""},{default:e(()=>[l(u(t.batchName),1)]),_:2},1024)):k("",!0)]}),_:2},1024),a(b,{name:"status"},{default:e(()=>[a(I,{design:t.status.color},{default:e(()=>[l(u(t.status.label),1)]),_:2},1032,["design"])]),_:2},1024),a(b,{name:"date"},{default:e(()=>[l(u(t.date.formatted)+" ",1),t.isOnline?(p(),f(N,{key:0,block:""},{default:e(()=>[l(u(t.applicationNumber),1)]),_:2},1024)):k("",!0)]),_:2},1024),a(b,{name:"createdAt"},{default:e(()=>[l(u(t.createdAt.formatted),1)]),_:2},1024),a(b,{name:"action"},{default:e(()=>[a(O,null,{default:e(()=>[a(F,{icon:"fas fa-arrow-circle-right",onClick:_=>c(y).push({name:"StudentRegistrationShow",params:{uuid:t.uuid}})},{default:e(()=>[l(u(r.$trans("general.show")),1)]),_:2},1032,["onClick"]),c(B)("registration:edit")&&t.isEditable?(p(),f(F,{key:0,icon:"fas fa-edit",onClick:_=>c(y).push({name:"StudentRegistrationEdit",params:{uuid:t.uuid}})},{default:e(()=>[l(u(r.$trans("general.edit")),1)]),_:2},1032,["onClick"])):k("",!0),c(B)("registration:delete")&&t.isEditable?(p(),f(F,{key:1,icon:"fas fa-trash",onClick:_=>c(v).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[l(u(r.$trans("general.delete")),1)]),_:2},1032,["onClick"])):k("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{ie as default};
