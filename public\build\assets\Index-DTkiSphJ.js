import{u as R,j as B,l as w,I as A,n as S,r,q as F,o as $,w as i,d as b,e as n,b as U,s as k,t as g,f as c,i as q,m as C,a as D,F as H,aS as P}from"./app-DvIo72ZO.js";const T={class:"grid grid-cols-3 gap-6"},L={class:"col-span-3 sm:col-span-1"},N={class:"col-span-3 sm:col-span-1"},W={class:"col-span-3 sm:col-span-1"},j={__name:"Filter",props:{initUrl:{type:String,default:""}},emits:["hide"],setup(V,{emit:d}){const u=R();B("moment");const _=d,f=V,l={date:"",batches:[],status:"all"},o=w({...l}),m=A(f.initUrl),a=w({isLoaded:!u.query.batches});return S(async()=>{a.batches=u.query.batches?u.query.batches.split(","):[],a.isLoaded=!0}),(e,s)=>{const h=r("DatePicker"),p=r("BaseSelectSearch"),v=r("CustomCheckbox"),y=r("FilterForm");return $(),F(y,{"init-form":l,multiple:["batches"],form:o,onHide:s[4]||(s[4]=t=>_("hide"))},{default:i(()=>[b("div",T,[b("div",L,[n(h,{modelValue:o.date,"onUpdate:modelValue":s[0]||(s[0]=t=>o.date=t),name:"date",as:"date",label:e.$trans("general.date")},null,8,["modelValue","label"])]),b("div",N,[a.isLoaded?($(),F(p,{key:0,multiple:"",name:"batches",label:e.$trans("global.select",{attribute:e.$trans("academic.batch.batch")}),modelValue:o.batches,"onUpdate:modelValue":s[1]||(s[1]=t=>o.batches=t),"value-prop":"uuid","init-search":a.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:i(t=>[k(g(t.value.course.nameWithTerm)+" "+g(t.value.name),1)]),listOption:i(t=>[k(g(t.option.course.nameWithTerm)+" "+g(t.option.name),1)]),_:1},8,["label","modelValue","init-search"])):U("",!0)]),b("div",W,[n(v,{label:e.$trans("resource.status"),options:[{label:e.$trans("general.all"),value:"all"},{label:e.$trans("resource.submitted"),value:"submitted"},{label:e.$trans("resource.not_submitted"),value:"not_submitted"}],modelValue:o.status,"onUpdate:modelValue":s[2]||(s[2]=t=>o.status=t),error:c(m).status,"onUpdate:error":s[3]||(s[3]=t=>c(m).status=t)},null,8,["label","options","modelValue","error"])])])]),_:1},8,["form"])}}},E={name:"ResourceReportDateWiseAssignment"},I=Object.assign(E,{setup(V){const d=R();q();let u=["filter"],_=[];const f="resource/report/",l=C(!0),o=C(!1),m=async()=>{let a="/app/resource/reports/date-wise-assignment/export",e=d.query;window.open(P(a,e),"_blank").focus()};return S(async()=>{}),(a,e)=>{const s=r("PageHeaderAction"),h=r("PageHeader"),p=r("ParentTransition"),v=r("BaseCard");return $(),D(H,null,[n(h,{title:a.$trans(c(d).meta.label),navs:[{label:a.$trans("resource.resource"),path:"Resource"},{label:a.$trans("resource.report.report"),path:"ResourceReport"}]},{default:i(()=>[n(s,{url:"resource/reports/date-wise-assignment/",name:"ResourceReportDateWiseAssignment",title:a.$trans("resource.report.date_wise_assignment.date_wise_assignment"),actions:c(u),"dropdown-actions":c(_),onToggleFilter:e[0]||(e[0]=y=>l.value=!0)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"]),n(p,{appear:"",visibility:l.value},{default:i(()=>[n(j,{onAfterFilter:m,"init-url":f,onHide:e[1]||(e[1]=y=>l.value=!1)})]),_:1},8,["visibility"]),n(p,{appear:"",visibility:!0},{default:i(()=>[n(v,{"no-padding":"","no-content-padding":"","is-loading":o.value},null,8,["is-loading"])]),_:1})],64)}}});export{I as default};
