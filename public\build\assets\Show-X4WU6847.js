import{i as N,u as $,h as F,j as M,l as O,r as o,a as D,o as r,e as t,w as e,f as a,q as _,b as h,d as V,F as k,v as q,s as l,t as c}from"./app-DvIo72ZO.js";const E={class:"space-y-2"},U={class:"grid grid-cols-1 gap-x-4 gap-y-8 px-4 pt-4 sm:grid-cols-2"},z={name:"AcademicDepartmentShow"},K=Object.assign(z,{setup(G){N();const p=$(),v=F(),n=M("$trans"),w={},A="academic/department/",C=[{key:"program",label:n("academic.program.program"),visibility:!0},{key:"code",label:n("academic.program.props.code"),visibility:!0},{key:"action",label:"",visibility:!0}],i=O({...w}),x=b=>{Object.assign(i,b)};return(b,m)=>{const B=o("PageHeaderAction"),L=o("PageHeader"),d=o("ListItemView"),f=o("TextMuted"),S=o("ListContainerVertical"),g=o("BaseCard"),u=o("DataCell"),T=o("DataRow"),I=o("SimpleTable"),P=o("BaseDataView"),R=o("DetailLayoutVertical"),j=o("ShowItem"),H=o("ParentTransition");return r(),D(k,null,[t(L,{title:a(n)(a(p).meta.trans,{attribute:a(n)(a(p).meta.label)}),navs:[{label:a(n)("academic.academic"),path:"Academic"},{label:a(n)("academic.department.department"),path:"AcademicDepartmentList"}]},{default:e(()=>[t(B,{name:"AcademicDepartment",title:a(n)("academic.department.department"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(H,{appear:"",visibility:!0},{default:e(()=>[t(j,{"init-url":A,uuid:a(p).params.uuid,onSetItem:x,onRedirectTo:m[0]||(m[0]=s=>a(v).push({name:"AcademicDepartment"}))},{default:e(()=>[i.uuid?(r(),_(R,{key:0},{detail:e(()=>[V("div",E,[t(g,{"no-padding":"","no-content-padding":""},{title:e(()=>[l(c(a(n)("academic.department.department")),1)]),action:e(()=>m[1]||(m[1]=[])),default:e(()=>[t(S,null,{default:e(()=>[t(d,{label:a(n)("academic.department.props.name")},{default:e(()=>[l(c(i.name),1)]),_:1},8,["label"]),t(d,{label:a(n)("academic.department.props.code")},{default:e(()=>[l(c(i.code)+" ",1),t(f,{block:""},{default:e(()=>[l(c(i.shortcode),1)]),_:1})]),_:1},8,["label"]),t(d,{label:a(n)("academic.department.props.alias")},{default:e(()=>[l(c(i.alias),1)]),_:1},8,["label"]),t(d,{label:a(n)("general.created_at")},{default:e(()=>[l(c(i.createdAt.formatted),1)]),_:1},8,["label"]),t(d,{label:a(n)("general.updated_at")},{default:e(()=>[l(c(i.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})])]),default:e(()=>[t(g,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:e(()=>[l(c(a(n)("global.detail",{attribute:a(n)("academic.department.department")})),1)]),default:e(()=>[i.programs.length>0?(r(),_(I,{key:0,header:C},{default:e(()=>[(r(!0),D(k,null,q(i.programs,s=>(r(),_(T,{key:s.uuid},{default:e(()=>[t(u,{name:"program"},{default:e(()=>[l(c(s.name)+" ",1),t(f,{block:""},{default:e(()=>{var y;return[l(c((y=s.type)==null?void 0:y.label),1)]}),_:2},1024)]),_:2},1024),t(u,{name:"code"},{default:e(()=>[l(c(s.code),1)]),_:2},1024),t(u,{name:"action"})]),_:2},1024))),128))]),_:1})):h("",!0),V("dl",U,[t(P,{class:"col-span-1 sm:col-span-2",label:a(n)("academic.department.props.description"),paragraph:""},{default:e(()=>[l(c(i.description),1)]),_:1},8,["label"])])]),_:1})]),_:1})):h("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{K as default};
