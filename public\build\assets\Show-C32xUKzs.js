import{i as v,u as P,h as V,l as A,r as s,a as D,o as c,e as a,w as e,f as u,q as f,b as _,d as H,s as o,t as d,y as I,F as N}from"./app-DvIo72ZO.js";const T={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},j={name:"StudentCustomFeeShow"},R=Object.assign(j,{props:{student:{type:Object,default(){return{}}}},setup(l){v();const i=P(),p=V(),b={},g="student/customFee/",n=A({...b}),S=t=>{Object.assign(n,t)};return(t,m)=>{const B=s("PageHeaderAction"),$=s("PageHeader"),r=s("BaseDataView"),h=s("BaseButton"),C=s("ShowButton"),w=s("BaseCard"),F=s("ShowItem"),k=s("ParentTransition");return c(),D(N,null,[a($,{title:t.$trans(u(i).meta.trans,{attribute:t.$trans(u(i).meta.label)}),navs:[{label:t.$trans("student.student"),path:"Student"},{label:l.student.contact.name,path:{name:"StudentShow",params:{uuid:l.student.uuid}}}]},{default:e(()=>[a(B,{name:"StudentCustomFee",title:t.$trans("student.fee.custom_fee"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(k,{appear:"",visibility:!0},{default:e(()=>[a(F,{"init-url":g,uuid:u(i).params.uuid,"module-uuid":u(i).params.muuid,onSetItem:S,onRedirectTo:m[1]||(m[1]=y=>u(p).push({name:"StudentCustomFee",params:{uuid:l.student.uuid}}))},{default:e(()=>[n.uuid?(c(),f(w,{key:0},{title:e(()=>[o(d(n.head.name),1)]),footer:e(()=>[a(C,null,{default:e(()=>[u(I)("fee:edit")?(c(),f(h,{key:0,design:"primary",onClick:m[0]||(m[0]=y=>u(p).push({name:"StudentCustomFeeEdit",params:{uuid:l.student.uuid,muuid:n.uuid}}))},{default:e(()=>[o(d(t.$trans("general.edit")),1)]),_:1})):_("",!0)]),_:1})]),default:e(()=>[H("dl",T,[a(r,{label:t.$trans("student.fee.props.amount")},{default:e(()=>[o(d(n.amount.formatted),1)]),_:1},8,["label"]),a(r,{class:"col-span-1 sm:col-span-2",label:t.$trans("student.fee.props.remarks")},{default:e(()=>[o(d(n.remarks),1)]),_:1},8,["label"]),a(r,{label:t.$trans("student.fee.props.due_date")},{default:e(()=>[o(d(n.dueDate.formatted),1)]),_:1},8,["label"]),a(r,{label:t.$trans("general.created_at")},{default:e(()=>[o(d(n.createdAt.formatted),1)]),_:1},8,["label"]),a(r,{label:t.$trans("general.updated_at")},{default:e(()=>[o(d(n.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):_("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{R as default};
