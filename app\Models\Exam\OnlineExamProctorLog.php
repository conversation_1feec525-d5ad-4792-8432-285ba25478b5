<?php

namespace App\Models\Exam;

use App\Casts\DateTimeCast;
use App\Concerns\HasFilter;
use App\Concerns\HasMeta;
use App\Concerns\HasUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class OnlineExamProctorLog extends Model
{
    use HasFactory, HasFilter, HasMeta, HasUuid, LogsActivity;

    protected $guarded = [];

    protected $primaryKey = 'id';

    protected $table = 'online_exam_proctoring_logs';

    protected $casts = [
        'detected_at' => DateTimeCast::class,
        'data' => 'array',
        'meta' => 'array',
    ];

    protected $appends = [
        'media_url',
        'has_media',
        'formatted_description'
    ];

    public function submission(): BelongsTo
    {
        return $this->belongsTo(OnlineExamSubmission::class, 'online_exam_submission_id');
    }

    public function getModelName(): string
    {
        return 'OnlineExamProctorLog';
    }

    public function scopeByEventType($query, string $eventType)
    {
        return $query->where('event_type', $eventType);
    }

    public function scopeBySeverity($query, string $severity)
    {
        return $query->where('severity', $severity);
    }

    public function scopeCritical($query)
    {
        return $query->where('severity', 'critical');
    }

    public function scopeWarning($query)
    {
        return $query->where('severity', 'warning');
    }

    public function scopeInfo($query)
    {
        return $query->where('severity', 'info');
    }

    public function scopeWithMedia($query)
    {
        return $query->whereNotNull('media_path');
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->useLogName('online_exam_proctor_log')
            ->logAll()
            ->logExcept(['updated_at'])
            ->logOnlyDirty();
    }

    /**
     * Get the full media URL if media_path exists
     */
    public function getMediaUrlAttribute(): ?string
    {
        if (!$this->media_path) {
            return null;
        }

        // Always use secure download endpoint for media access
        // This ensures proper authorization and works with both local and cloud storage
        return route('app.online-exams.proctoring.downloadMedia', [
            'onlineExam' => $this->submission->exam->uuid,
            'log' => $this->uuid
        ]);
    }

    /**
     * Check if this log entry has associated media
     */
    public function hasMedia(): bool
    {
        return !empty($this->media_path);
    }

    /**
     * Get has_media attribute for JSON response
     */
    public function getHasMediaAttribute(): bool
    {
        return $this->hasMedia();
    }

    /**
     * Get formatted event description
     */
    public function getFormattedDescriptionAttribute(): string
    {
        $baseDescription = $this->description ?? $this->event_type;
        
        if ($this->data && is_array($this->data)) {
            $additionalInfo = [];
            
            if (isset($this->data['face_count'])) {
                $additionalInfo[] = "Faces detected: {$this->data['face_count']}";
            }
            
            if (isset($this->data['audio_level'])) {
                $additionalInfo[] = "Audio level: {$this->data['audio_level']}dB";
            }
            
            if (isset($this->data['tab_title'])) {
                $additionalInfo[] = "Tab: {$this->data['tab_title']}";
            }
            
            if (!empty($additionalInfo)) {
                $baseDescription .= ' (' . implode(', ', $additionalInfo) . ')';
            }
        }
        
        return $baseDescription;
    }
}
