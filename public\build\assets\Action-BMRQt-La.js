import{u as M,H as $,I,c as O,l as k,r as d,q as R,o as v,w as L,d as i,a as _,e as s,f as o,F as N,v as A,J as w,K as h}from"./app-DvIo72ZO.js";const J={class:"grid grid-cols-3 gap-6"},K={class:"col-span-3 sm:col-span-2"},z={class:"mt-4 grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-2 sm:col-span-1"},W={class:"mt-4 grid grid-cols-3 gap-6"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"mt-4 grid grid-cols-3 gap-6"},x={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3"},ae={class:"grid grid-cols-1"},re={class:"col"},te={name:"FinanceTransactionForm"},ne=Object.assign(te,{setup(F){const g=M(),m={type:"",primaryLedger:{},date:"",records:[{secondaryLedger:{},amount:""}],paymentMethod:"",instrumentNumber:"",instrumentDate:"",clearingDate:"",bankDetail:"",referenceNumber:"",remarks:"",media:[],mediaUpdated:!1,mediaToken:$(),mediaHash:[]},U="finance/transaction/",n=I(U),D=O(()=>r.paymentMethod?u.paymentMethods.find(t=>t.uuid===r.paymentMethod):{}),u=k({types:[],paymentMethods:[],primaryLedgers:[]}),r=k({...m}),V=k({primaryLedger:"",secondaryLedger:"",isLoaded:!g.params.uuid}),B=t=>{Object.assign(u,t),Object.assign(r,h(m))},P=()=>{r.mediaToken=$(),r.mediaHash=[]},T=t=>{var b,y;let a=[];t.records.forEach(f=>{a.push({secondaryLedger:f.ledger,amount:f.amount.value})});let l=t.payments[0]||{};Object.assign(m,{...t,records:a,type:t.type.value,primaryLedger:l==null?void 0:l.ledger,paymentMethod:l==null?void 0:l.methodUuid,date:t.date.value,instrumentNumber:l.instrumentNumber||"",instrumentDate:((b=l.instrumentDate)==null?void 0:b.value)||"",clearingDate:((y=l.clearingDate)==null?void 0:y.value)||"",bankDetail:l.bankDetail||"",referenceNumber:l.referenceNumber||""}),Object.assign(r,h(m)),V.isLoaded=!0};return(t,a)=>{const l=d("CustomCheckbox"),b=d("BaseSelect"),y=d("LedgerBalance"),f=d("DatePicker"),H=d("BaseSelectSearch"),j=d("BaseInput"),S=d("PaymentMethodInput"),q=d("BaseTextarea"),E=d("MediaUpload"),C=d("FormAction");return v(),R(C,{"pre-requisites":!0,onSetPreRequisites:B,"init-url":U,"init-form":m,form:r,"set-form":T,redirect:"FinanceTransaction",onResetMediaFiles:P},{default:L(()=>[i("div",J,[i("div",K,[s(l,{disabled:!!o(g).params.uuid,label:t.$trans("finance.transaction.props.type"),options:u.types,modelValue:r.type,"onUpdate:modelValue":a[0]||(a[0]=e=>r.type=e),error:o(n).type,"onUpdate:error":a[1]||(a[1]=e=>o(n).type=e)},null,8,["disabled","label","options","modelValue","error"])])]),i("div",z,[i("div",G,[s(b,{modelValue:r.primaryLedger,"onUpdate:modelValue":a[2]||(a[2]=e=>r.primaryLedger=e),name:"primaryLedger",label:t.$trans("global.select",{attribute:t.$trans("finance.ledger.ledger")}),"object-prop":!0,"label-prop":"name","value-prop":"uuid",options:u.primaryLedgers,error:o(n).primaryLedger,"onUpdate:error":a[3]||(a[3]=e=>o(n).primaryLedger=e)},null,8,["modelValue","label","options","error"]),s(y,{label:!0,ledger:r.primaryLedger},null,8,["ledger"])]),i("div",Q,[s(f,{modelValue:r.date,"onUpdate:modelValue":a[4]||(a[4]=e=>r.date=e),name:"date",label:t.$trans("finance.transaction.props.date"),"no-clear":"",error:o(n).date,"onUpdate:error":a[5]||(a[5]=e=>o(n).date=e)},null,8,["modelValue","label","error"])])]),(v(!0),_(N,null,A(r.records,(e,c)=>(v(),_("div",W,[i("div",X,[s(H,{name:`records.${c}.secondaryLedger`,label:t.$trans("global.select",{attribute:t.$trans("finance.ledger.secondary_ledger")}),modelValue:e.secondaryLedger,"onUpdate:modelValue":p=>e.secondaryLedger=p,error:o(n)[`records.${c}.secondaryLedger.uuid`],"onUpdate:error":p=>o(n)[`records.${c}.secondaryLedger.uuid`]=p,"object-prop":!0,"label-prop":"name","value-prop":"uuid","init-search":V.secondaryLedger,"search-action":"finance/ledger/list","additional-search-query":{subType:r.type=="transfer"?"primary":"secondary"}},null,8,["name","label","modelValue","onUpdate:modelValue","error","onUpdate:error","init-search","additional-search-query"]),s(y,{label:!0,ledger:e.secondaryLedger},null,8,["ledger"])]),i("div",Y,[s(j,{currency:"",type:"text",modelValue:e.amount,"onUpdate:modelValue":p=>e.amount=p,name:`records.${c}.amount`,label:t.$trans("finance.transaction.props.amount"),error:o(n)[`records.${c}.amount`],"onUpdate:error":p=>o(n)[`records.${c}.amount`]=p},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])])]))),256)),i("div",Z,[i("div",x,[s(b,{modelValue:r.paymentMethod,"onUpdate:modelValue":a[6]||(a[6]=e=>r.paymentMethod=e),name:"paymentMethod",label:t.$trans("finance.payment_method.payment_method"),"label-prop":"name","value-prop":"uuid",options:u.paymentMethods,error:o(n).paymentMethod,"onUpdate:error":a[7]||(a[7]=e=>o(n).paymentMethod=e)},null,8,["modelValue","label","options","error"])]),s(S,{"selected-payment-method":D.value,instrumentNumber:r.instrumentNumber,"onUpdate:instrumentNumber":a[8]||(a[8]=e=>r.instrumentNumber=e),instrumentDate:r.instrumentDate,"onUpdate:instrumentDate":a[9]||(a[9]=e=>r.instrumentDate=e),clearingDate:r.clearingDate,"onUpdate:clearingDate":a[10]||(a[10]=e=>r.clearingDate=e),bankDetail:r.bankDetail,"onUpdate:bankDetail":a[11]||(a[11]=e=>r.bankDetail=e),referenceNumber:r.referenceNumber,"onUpdate:referenceNumber":a[12]||(a[12]=e=>r.referenceNumber=e),formErrors:o(n),"onUpdate:formErrors":a[13]||(a[13]=e=>w(n)?n.value=e:null)},null,8,["selected-payment-method","instrumentNumber","instrumentDate","clearingDate","bankDetail","referenceNumber","formErrors"]),i("div",ee,[s(q,{modelValue:r.remarks,"onUpdate:modelValue":a[14]||(a[14]=e=>r.remarks=e),name:"remarks",label:t.$trans("finance.transaction.props.remarks"),error:o(n).remarks,"onUpdate:error":a[15]||(a[15]=e=>o(n).remarks=e)},null,8,["modelValue","label","error"])])]),i("div",ae,[i("div",re,[s(E,{multiple:"",label:t.$trans("general.file"),module:"transaction",media:r.media,"media-token":r.mediaToken,onIsUpdated:a[16]||(a[16]=e=>r.mediaUpdated=!0),onSetHash:a[17]||(a[17]=e=>r.mediaHash.push(e))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),oe={name:"FinanceTransactionAction"},le=Object.assign(oe,{setup(F){const g=M();return(m,U)=>{const n=d("PageHeaderAction"),D=d("PageHeader"),u=d("ParentTransition");return v(),_(N,null,[s(D,{title:m.$trans(o(g).meta.trans,{attribute:m.$trans(o(g).meta.label)}),navs:[{label:m.$trans("finance.finance"),path:"Finance"},{label:m.$trans("finance.transaction.transaction"),path:"FinanceTransactionList"}]},{default:L(()=>[s(n,{name:"FinanceTransaction",title:m.$trans("finance.transaction.transaction"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),s(u,{appear:"",visibility:!0},{default:L(()=>[s(ne)]),_:1})],64)}}});export{le as default};
