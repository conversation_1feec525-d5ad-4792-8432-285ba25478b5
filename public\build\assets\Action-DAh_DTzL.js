import{u as j,H as F,I as G,l as C,n as J,r as d,q as R,o as V,w as p,d as i,a as w,b as Q,e as l,f as s,F as N,v as W,s as I,t as u,N as X,K as Y}from"./app-DvIo72ZO.js";const Z={class:"grid grid-cols-3 gap-6"},x={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3 sm:col-span-1"},te={class:"col-span-3 sm:col-span-1"},oe={class:"col-span-2 sm:col-span-1"},ne=["onClick"],re={class:"mt-4 grid grid-cols-4 gap-4"},se={class:"col-span-4 sm:col-span-1"},ae={class:"col-span-4 sm:col-span-1"},ie={class:"col-span-4 sm:col-span-2"},le={class:"mt-4"},de={class:"mt-4 grid grid-cols-1 gap-4"},me={class:"col"},ce={class:"col"},pe={name:"InventoryStockTransferForm"},ue=Object.assign(pe,{setup(A){const _=j(),c={date:"",fromPlace:"",toPlace:"",items:[],description:"",media:[],mediaUpdated:!1,mediaToken:F(),mediaHash:[]},$={uuid:F(),item:"",quantity:1,description:""},P="inventory/stockTransfer/",r=G(P),v=C({inventories:[],places:[]}),U=C({isLoaded:!_.params.uuid}),n=C({...c}),D=t=>{Object.assign(v,t)},M=()=>{n.mediaToken=F(),n.mediaHash=[]},O=()=>{n.items.push({...$,uuid:F()})},E=async t=>{await X()&&(n.items.length==1?n.items=[$]:n.items.splice(t,1))},L=t=>{var f,B,q,S,b,T,h;let o=[];t.items.forEach(y=>{o.push({uuid:y.uuid,item:y.item,quantity:y.quantity,description:y.description})}),Object.assign(c,{...t,items:o,date:(f=t.date)==null?void 0:f.value,inventory:((B=t.inventory)==null?void 0:B.uuid)||"",fromPlace:((q=t.fromPlace)==null?void 0:q.uuid)||"",toPlace:((S=t.toPlace)==null?void 0:S.uuid)||""}),Object.assign(n,Y(c)),U.inventory=((b=t.inventory)==null?void 0:b.uuid)||"",U.fromPlace=((T=t.fromPlace)==null?void 0:T.uuid)||"",U.toPlace=((h=t.toPlace)==null?void 0:h.uuid)||"",U.isLoaded=!0},K=t=>{t!=n.inventory&&(n.items=[$])};return J(async()=>{_.params.uuid||O()}),(t,o)=>{const f=d("BaseSelect"),B=d("DatePicker"),q=d("BaseSelectSearch"),S=d("BaseInput"),b=d("BaseTextarea"),T=d("BaseFieldset"),h=d("BaseBadge"),y=d("MediaUpload"),z=d("FormAction");return V(),R(z,{"pre-requisites":!0,onSetPreRequisites:D,"init-url":P,"init-form":c,form:n,"set-form":L,redirect:"InventoryStockTransfer",onResetMediaFiles:M},{default:p(()=>[i("div",Z,[i("div",x,[l(f,{modelValue:n.inventory,"onUpdate:modelValue":o[0]||(o[0]=e=>n.inventory=e),name:"inventory",label:t.$trans("inventory.inventory"),options:v.inventories,"label-prop":"name","value-prop":"uuid",error:s(r).inventory,"onUpdate:error":o[1]||(o[1]=e=>s(r).inventory=e),onChange:K},null,8,["modelValue","label","options","error"])]),i("div",ee,[l(f,{modelValue:n.fromPlace,"onUpdate:modelValue":o[2]||(o[2]=e=>n.fromPlace=e),name:"fromPlace",label:t.$trans("inventory.stock_transfer.props.from"),options:v.places,"label-prop":"fullName","value-prop":"uuid",error:s(r).fromPlace,"onUpdate:error":o[3]||(o[3]=e=>s(r).fromPlace=e)},null,8,["modelValue","label","options","error"])]),i("div",te,[l(f,{modelValue:n.toPlace,"onUpdate:modelValue":o[4]||(o[4]=e=>n.toPlace=e),name:"toPlace",label:t.$trans("inventory.stock_transfer.props.to"),options:v.places,"label-prop":"fullName","value-prop":"uuid",error:s(r).toPlace,"onUpdate:error":o[5]||(o[5]=e=>s(r).toPlace=e)},null,8,["modelValue","label","options","error"])]),i("div",oe,[l(B,{modelValue:n.date,"onUpdate:modelValue":o[6]||(o[6]=e=>n.date=e),name:"date",label:t.$trans("inventory.stock_transfer.props.date"),"no-clear":"",error:s(r).date,"onUpdate:error":o[7]||(o[7]=e=>s(r).date=e)},null,8,["modelValue","label","error"])])]),n.inventory?(V(),w(N,{key:0},[(V(!0),w(N,null,W(n.items,(e,m)=>(V(),R(T,{class:"mt-4",key:e.uuid},{legend:p(()=>[I(u(t.$trans("inventory.item"))+" "+u(m+1)+". ",1),i("span",{class:"text-danger ml-2 cursor-pointer",onClick:H=>E(m)},o[12]||(o[12]=[i("i",{class:"fas fa-times-circle"},null,-1)]),8,ne)]),default:p(()=>{var H;return[i("div",re,[i("div",se,[l(q,{name:`items.${m}.item`,label:t.$trans("global.select",{attribute:t.$trans("inventory.stock_item.stock_item")}),modelValue:e.item,"onUpdate:modelValue":a=>e.item=a,error:s(r)[`items.${m}.item`],"onUpdate:error":a=>s(r)[`items.${m}.item`]=a,"value-prop":"uuid","object-prop":!0,"init-search":(H=e==null?void 0:e.item)==null?void 0:H.name,"init-search-key":"name","search-action":"inventory/stockItem/list","additional-search-query":{inventory:n.inventory}},{selectedOption:p(a=>{var k,g;return[I(u(a.value.name)+" "+u((g=(k=a.value)==null?void 0:k.category)==null?void 0:g.name),1)]}),listOption:p(a=>{var k,g;return[I(u(a.option.name)+" "+u((g=(k=a.option)==null?void 0:k.category)==null?void 0:g.name),1)]}),_:2},1032,["name","label","modelValue","onUpdate:modelValue","error","onUpdate:error","init-search","additional-search-query"])]),i("div",ae,[l(S,{type:"number",step:.01,modelValue:e.quantity,"onUpdate:modelValue":a=>e.quantity=a,name:`items.${m}.quantity`,label:t.$trans("inventory.stock_transfer.props.quantity"),error:s(r)[`items.${m}.quantity`],"onUpdate:error":a=>s(r)[`items.${m}.quantity`]=a},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),i("div",ie,[l(b,{rows:1,modelValue:e.description,"onUpdate:modelValue":a=>e.description=a,name:`items.${m}.description`,label:t.$trans("inventory.stock_transfer.props.description"),error:s(r)[`items.${m}.description`],"onUpdate:error":a=>s(r)[`items.${m}.description`]=a},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])])])]}),_:2},1024))),128)),i("div",le,[l(h,{design:"primary",onClick:O,class:"cursor-pointer"},{default:p(()=>[I(u(t.$trans("global.add",{attribute:t.$trans("inventory.stock_item.stock_item")})),1)]),_:1})])],64)):Q("",!0),i("div",de,[i("div",me,[l(b,{rows:1,modelValue:n.description,"onUpdate:modelValue":o[8]||(o[8]=e=>n.description=e),name:"description",label:t.$trans("inventory.stock_transfer.props.description"),error:s(r).description,"onUpdate:error":o[9]||(o[9]=e=>s(r).description=e)},null,8,["modelValue","label","error"])]),i("div",ce,[l(y,{multiple:"",label:t.$trans("general.file"),module:"stock_transfer",media:n.media,"media-token":n.mediaToken,onIsUpdated:o[10]||(o[10]=e=>n.mediaUpdated=!0),onSetHash:o[11]||(o[11]=e=>n.mediaHash.push(e))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),ve={name:"InventoryStockTransferAction"},ye=Object.assign(ve,{setup(A){const _=j();return(c,$)=>{const P=d("PageHeaderAction"),r=d("PageHeader"),v=d("ParentTransition");return V(),w(N,null,[l(r,{title:c.$trans(s(_).meta.trans,{attribute:c.$trans(s(_).meta.label)}),navs:[{label:c.$trans("inventory.inventory"),path:"Inventory"},{label:c.$trans("inventory.stock_transfer.stock_transfer"),path:"InventoryStockTransferList"}]},{default:p(()=>[l(P,{name:"InventoryStockTransfer",title:c.$trans("inventory.stock_transfer.stock_transfer"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(v,{appear:"",visibility:!0},{default:p(()=>[l(ue)]),_:1})],64)}}});export{ye as default};
