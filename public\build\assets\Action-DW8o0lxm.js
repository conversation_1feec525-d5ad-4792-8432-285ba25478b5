import{i as L,u as V,h as A,H as $,I as w,m as M,l as b,r as l,q as E,o as _,w as m,d as c,a as f,f as n,B as C,e as d,s as g,t as i,K as y,F as I}from"./app-DvIo72ZO.js";const P={class:"grid grid-cols-3 gap-6"},K={key:0,class:"col-span-3 sm:col-span-1"},z={key:1,class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3"},W={class:"mt-4 grid grid-cols-1"},X={class:"col"},Y={name:"StudentLeaveRequestForm"},Z=Object.assign(Y,{setup(R){L();const v=V();A();const r={student:"",startDate:"",endDate:"",reason:"",media:[],mediaUpdated:!1,mediaToken:$(),mediaHash:[]},S="student/leaveRequest/",o=w(S);M(!1);const p=b({students:[],categories:[]}),u=b({selectedStudent:null}),a=b({...r}),q=b({student:"",isLoaded:!v.params.uuid}),k=s=>{Object.assign(p,s),Object.assign(a,y(r))},B=()=>{a.mediaToken=$(),a.mediaHash=[]},j=s=>{let t=s.student;u.selectedStudent={uuid:t.uuid,name:t.name,codeNumber:t.codeNumber,courseName:t.courseName,batchName:t.batchName,joiningDate:t.joiningDate},Object.assign(r,{...s,student:t.uuid,startDate:s.startDate.value,endDate:s.endDate.value,category:s.category.uuid}),Object.assign(a,y(r)),q.isLoaded=!0},D=s=>{a.student=s?s.uuid:""},N=()=>{u.selectedStudent=null,a.student=""};return(s,t)=>{const U=l("BaseSelect"),F=l("BaseSelectSearch"),O=l("DatePicker"),H=l("BaseTextarea"),T=l("MediaUpload"),h=l("FormAction");return _(),E(h,{"pre-requisites":!0,onSetPreRequisites:k,"init-url":S,"init-form":r,form:a,"set-form":j,redirect:"StudentLeaveRequest",onResetMediaFiles:B},{default:m(()=>[c("div",P,[n(C)(["student","guardian"],"any")?(_(),f("div",K,[d(U,{name:"student",label:s.$trans("global.select",{attribute:s.$trans("student.student")}),modelValue:u.selectedStudent,"onUpdate:modelValue":t[0]||(t[0]=e=>u.selectedStudent=e),error:n(o).student,"onUpdate:error":t[1]||(t[1]=e=>n(o).student=e),options:p.students,"value-prop":"uuid","object-prop":!0,onSelected:D,onRemoved:N},{selectedOption:m(e=>[g(i(e.value.name)+" ("+i(e.value.courseName+" "+e.value.batchName)+") ",1)]),listOption:m(e=>[g(i(e.option.name)+" ("+i(e.option.courseName+" "+e.option.batchName)+") ",1)]),_:1},8,["label","modelValue","error","options"])])):(_(),f("div",z,[d(F,{name:"student",label:s.$trans("global.select",{attribute:s.$trans("student.student")}),modelValue:u.selectedStudent,"onUpdate:modelValue":t[2]||(t[2]=e=>u.selectedStudent=e),error:n(o).student,"onUpdate:error":t[3]||(t[3]=e=>n(o).student=e),"value-prop":"uuid","object-prop":!0,"init-search":q.student,"search-key":"name","search-action":"student/summary",onSelected:D,onRemoved:N,"additional-search-query":{status:"all"}},{selectedOption:m(e=>[g(i(e.value.name)+" ("+i(e.value.courseName+" "+e.value.batchName)+") ",1)]),listOption:m(e=>[g(i(e.option.name)+" ("+i(e.option.courseName+" "+e.option.batchName)+") ",1)]),_:1},8,["label","modelValue","error","init-search"])])),c("div",G,[d(O,{start:a.startDate,"onUpdate:start":t[4]||(t[4]=e=>a.startDate=e),end:a.endDate,"onUpdate:end":t[5]||(t[5]=e=>a.endDate=e),name:"dateBetween",as:"range",label:s.$trans("general.date_between")},null,8,["start","end","label"])]),c("div",J,[d(U,{name:"student",label:s.$trans("student.leave_request.props.category"),modelValue:a.category,"onUpdate:modelValue":t[6]||(t[6]=e=>a.category=e),error:n(o).category,"onUpdate:error":t[7]||(t[7]=e=>n(o).category=e),options:p.categories,"value-prop":"uuid","label-prop":"name"},null,8,["label","modelValue","error","options"])]),c("div",Q,[d(H,{rows:1,modelValue:a.reason,"onUpdate:modelValue":t[8]||(t[8]=e=>a.reason=e),name:"reason",label:s.$trans("student.leave_request.props.reason"),error:n(o).reason,"onUpdate:error":t[9]||(t[9]=e=>n(o).reason=e)},null,8,["modelValue","label","error"])])]),c("div",W,[c("div",X,[d(T,{multiple:"",label:s.$trans("general.file"),module:"student_leave_request",media:a.media,"media-token":a.mediaToken,onIsUpdated:t[10]||(t[10]=e=>a.mediaUpdated=!0),onSetHash:t[11]||(t[11]=e=>a.mediaHash.push(e)),error:n(o).media,"onUpdate:error":t[12]||(t[12]=e=>n(o).media=e)},null,8,["label","media","media-token","error"])])])]),_:1},8,["form"])}}}),x={name:"StudentLeaveRequestAction"},te=Object.assign(x,{setup(R){const v=V();return(r,S)=>{const o=l("PageHeaderAction"),p=l("PageHeader"),u=l("ParentTransition");return _(),f(I,null,[d(p,{title:r.$trans(n(v).meta.trans,{attribute:r.$trans(n(v).meta.label)}),navs:[{label:r.$trans("student.student"),path:"Student"},{label:r.$trans("student.leave_request.leave_request"),path:"StudentLeaveRequest"}]},{default:m(()=>[d(o,{name:"StudentLeaveRequest",title:r.$trans("student.leave_request.leave_request"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),d(u,{appear:"",visibility:!0},{default:m(()=>[d(Z)]),_:1})],64)}}});export{te as default};
