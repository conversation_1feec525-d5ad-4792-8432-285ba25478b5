import{i as L,u as S,h as C,l as P,r as i,a as V,o as u,e as s,w as t,f as c,q as d,b as p,d as j,s as l,t as o,F as q}from"./app-DvIo72ZO.js";const H={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},I={name:"AcademicBookListShow"},D=Object.assign(I,{setup(N){L();const b=S(),_=C(),k={},f="academic/bookList/",e=P({...k}),g=a=>{Object.assign(e,a)};return(a,m)=>{const B=i("PageHeaderAction"),$=i("PageHeader"),n=i("BaseDataView"),y=i("BaseButton"),h=i("ShowButton"),v=i("BaseCard"),A=i("ShowItem"),w=i("ParentTransition");return u(),V(q,null,[s($,{title:a.$trans(c(b).meta.trans,{attribute:a.$trans(c(b).meta.label)}),navs:[{label:a.$trans("academic.academic"),path:"Academic"},{label:a.$trans("academic.book_list.book_list"),path:"AcademicBookListList"}]},{default:t(()=>[s(B,{name:"AcademicBookList",title:a.$trans("academic.book_list.book_list"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),s(w,{appear:"",visibility:!0},{default:t(()=>[s(A,{"init-url":f,uuid:c(b).params.uuid,onSetItem:g,onRedirectTo:m[1]||(m[1]=r=>c(_).push({name:"AcademicBookList"}))},{default:t(()=>[e.uuid?(u(),d(v,{key:0},{title:t(()=>[l(o(e.title)+" ("+o(e.type.label)+") ",1)]),footer:t(()=>[s(h,null,{default:t(()=>[s(y,{design:"primary",onClick:m[0]||(m[0]=r=>c(_).push({name:"AcademicBookListEdit",params:{uuid:e.uuid}}))},{default:t(()=>[l(o(a.$trans("general.edit")),1)]),_:1})]),_:1})]),default:t(()=>[j("dl",H,[s(n,{label:a.$trans("academic.course.course")},{default:t(()=>{var r;return[l(o(((r=e.course)==null?void 0:r.name)||"-"),1)]}),_:1},8,["label"]),s(n,{label:a.$trans("academic.subject.subject")},{default:t(()=>{var r;return[l(o(((r=e.subject)==null?void 0:r.name)||"-"),1)]}),_:1},8,["label"]),e.type.value!="notebook"?(u(),d(n,{key:0,label:a.$trans("academic.book_list.props.publisher")},{default:t(()=>[l(o(e.publisher),1)]),_:1},8,["label"])):p("",!0),e.type.value!="notebook"?(u(),d(n,{key:1,label:a.$trans("academic.book_list.props.author")},{default:t(()=>[l(o(e.author),1)]),_:1},8,["label"])):p("",!0),e.type.value=="notebook"&&e.quantity?(u(),d(n,{key:2,label:a.$trans("academic.book_list.props.quantity")},{default:t(()=>[l(o(e.quantity)+" "+o(a.$trans("list.unit.pcs")),1)]),_:1},8,["label"])):p("",!0),e.type.value=="notebook"&&e.pages?(u(),d(n,{key:3,label:a.$trans("academic.book_list.props.pages")},{default:t(()=>[l(o(e.pages)+" "+o(a.$trans("academic.book_list.props.pages")),1)]),_:1},8,["label"])):p("",!0),s(n,{class:"col-span-1 sm:col-span-2",label:a.$trans("academic.book_list.props.description")},{default:t(()=>[l(o(e.description),1)]),_:1},8,["label"]),s(n,{label:a.$trans("general.created_at")},{default:t(()=>[l(o(e.createdAt.formatted),1)]),_:1},8,["label"]),s(n,{label:a.$trans("general.updated_at")},{default:t(()=>[l(o(e.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):p("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{D as default};
