import{u as z,j as X,l as q,I as Y,n as E,r as i,q as y,o as p,w as a,d as c,e,b as _,s as d,t as r,h as Z,i as x,y as ee,m as L,a as C,f as S,F as A,v as I}from"./app-DvIo72ZO.js";const te={class:"grid grid-cols-3 gap-6"},ae={class:"col-span-3 sm:col-span-1"},ne={class:"col-span-3 sm:col-span-1"},se={class:"col-span-3 sm:col-span-1"},le={class:"col-span-3 sm:col-span-1"},oe={class:"col-span-3 sm:col-span-1"},re={class:"col-span-3 sm:col-span-1"},de={class:"col-span-3 sm:col-span-1"},ue={class:"col-span-3 sm:col-span-1"},ie={class:"col-span-3 sm:col-span-1"},me={class:"col-span-3 sm:col-span-1"},pe={__name:"Filter",props:{initUrl:{type:String,default:""},preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(M,{emit:F}){const f=z();X("moment");const R=F,B=M,N={codeNumber:"",voucherNumber:"",pgAccount:"",name:"",batches:[],startDate:"",endDate:"",period:"",ledgers:[],paymentMethods:[],status:""},s=q({...N});Y(B.initUrl);const m=q({isLoaded:!f.query.batches}),b=q({paymentMethods:B.preRequisites.paymentMethods});return E(async()=>{m.batches=f.query.batches?f.query.batches.split(","):[],m.ledgers=f.query.ledgers?f.query.ledgers.split(","):[],m.paymentMethods=f.query.paymentMethods?f.query.paymentMethods.split(","):[],m.isLoaded=!0}),(u,t)=>{const v=i("BaseInput"),$=i("BaseSelectSearch"),T=i("DatePicker"),o=i("BaseSelect"),V=i("FilterForm");return p(),y(V,{"init-form":N,multiple:["batches","ledgers","paymentMethods"],form:s,onHide:t[11]||(t[11]=n=>R("hide"))},{default:a(()=>[c("div",te,[c("div",ae,[e(v,{type:"text",modelValue:s.codeNumber,"onUpdate:modelValue":t[0]||(t[0]=n=>s.codeNumber=n),name:"codeNumber",label:u.$trans("student.admission.props.code_number")},null,8,["modelValue","label"])]),c("div",ne,[e(v,{type:"text",modelValue:s.voucherNumber,"onUpdate:modelValue":t[1]||(t[1]=n=>s.voucherNumber=n),name:"voucherNumber",label:u.$trans("finance.transaction.props.code_number")},null,8,["modelValue","label"])]),c("div",se,[e(v,{type:"text",modelValue:s.name,"onUpdate:modelValue":t[2]||(t[2]=n=>s.name=n),name:"name",label:u.$trans("contact.props.name")},null,8,["modelValue","label"])]),c("div",le,[m.isLoaded?(p(),y($,{key:0,multiple:"",name:"batches",label:u.$trans("global.select",{attribute:u.$trans("academic.batch.batch")}),modelValue:s.batches,"onUpdate:modelValue":t[3]||(t[3]=n=>s.batches=n),"value-prop":"uuid","init-search":m.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:a(n=>[d(r(n.value.course.name)+" "+r(n.value.name),1)]),listOption:a(n=>[d(r(n.option.course.nameWithTerm)+" "+r(n.option.name),1)]),_:1},8,["label","modelValue","init-search"])):_("",!0)]),c("div",oe,[e(T,{start:s.startDate,"onUpdate:start":t[4]||(t[4]=n=>s.startDate=n),end:s.endDate,"onUpdate:end":t[5]||(t[5]=n=>s.endDate=n),name:"dateBetween",as:"range",label:u.$trans("general.date_between")},null,8,["start","end","label"])]),c("div",re,[e(o,{name:"period",label:u.$trans("global.select",{attribute:u.$trans("academic.period.period")}),modelValue:s.period,"onUpdate:modelValue":t[6]||(t[6]=n=>s.period=n),"label-prop":"name","value-prop":"uuid",options:M.preRequisites.periods},null,8,["label","modelValue","options"])]),c("div",de,[m.isLoaded?(p(),y($,{key:0,multiple:"",name:"ledgers",label:u.$trans("global.select",{attribute:u.$trans("finance.ledger.ledger")}),modelValue:s.ledgers,"onUpdate:modelValue":t[7]||(t[7]=n=>s.ledgers=n),"label-props":"name","value-prop":"uuid","init-search":m.ledgers,"search-action":"finance/ledger/list"},null,8,["label","modelValue","init-search"])):_("",!0)]),c("div",ue,[e(o,{multiple:"",modelValue:s.paymentMethods,"onUpdate:modelValue":t[8]||(t[8]=n=>s.paymentMethods=n),name:"paymentMethods",label:u.$trans("finance.payment_method.payment_method"),"label-prop":"name","value-prop":"uuid",options:b.paymentMethods},null,8,["modelValue","label","options"])]),c("div",ie,[e(o,{name:"status",label:u.$trans("global.select",{attribute:u.$trans("finance.transaction.props.status")}),modelValue:s.status,"onUpdate:modelValue":t[9]||(t[9]=n=>s.status=n),options:M.preRequisites.statuses},null,8,["label","modelValue","options"])]),c("div",me,[e(v,{type:"text",modelValue:s.pgAccount,"onUpdate:modelValue":t[10]||(t[10]=n=>s.pgAccount=n),name:"pgAccount",label:u.$trans("finance.config.props.pg_account")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},ce={name:"FinanceReportFeePayment"},be=Object.assign(ce,{setup(M){const F=z(),f=Z(),R=x();let B=["filter"],N=[];ee("finance:export")&&(N=["print","pdf","excel"]);const s="finance/report/",m=L(!1),b=L(!1),u=q({periods:[],ledgers:[],paymentMethods:[]}),t=q({headers:[],data:[],meta:{total:0}}),v=async()=>{b.value=!0,await R.dispatch(s+"preRequisite",{name:"fee-payment",params:F.query}).then(o=>{b.value=!1,Object.assign(u,o)}).catch(o=>{b.value=!1})},$=async()=>{b.value=!0,await R.dispatch(s+"fetchReport",{name:"fee-payment",params:F.query}).then(o=>{b.value=!1,Object.assign(t,o)}).catch(o=>{b.value=!1})},T=o=>{window.open(`/app/students/${o.studentUuid}/transactions/${o.uuid}/export?action=print`)};return E(async()=>{await v(),await $()}),(o,V)=>{const n=i("PageHeaderAction"),W=i("PageHeader"),j=i("ParentTransition"),k=i("TextMuted"),D=i("BaseBadge"),g=i("DataCell"),O=i("FloatingMenuItem"),G=i("FloatingMenu"),J=i("DataRow"),K=i("DataTable"),Q=i("BaseCard");return p(),C(A,null,[e(W,{title:o.$trans(S(F).meta.label),navs:[{label:o.$trans("finance.finance"),path:"Finance"},{label:o.$trans("finance.report.report"),path:"FinanceReport"}]},{default:a(()=>[e(n,{url:"finance/reports/fee-payment/",name:"FinanceReportFeePayment",title:o.$trans("finance.report.fee_payment.fee_payment"),actions:S(B),"dropdown-actions":S(N),headers:t.headers,onToggleFilter:V[0]||(V[0]=l=>m.value=!m.value)},null,8,["title","actions","dropdown-actions","headers"])]),_:1},8,["title","navs"]),e(j,{appear:"",visibility:m.value},{default:a(()=>[e(pe,{onAfterFilter:$,"init-url":s,"pre-requisites":u,onHide:V[1]||(V[1]=l=>m.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"]),e(j,{appear:"",visibility:!0},{default:a(()=>[e(Q,{"no-padding":"","no-content-padding":"","is-loading":b.value},{default:a(()=>[e(K,{header:t.headers,footer:t.footers,meta:t.meta,module:"finance.report.fee_payment",onRefresh:$},{default:a(()=>[(p(!0),C(A,null,I(t.data,l=>(p(),y(J,{key:l.uuid},{default:a(()=>[e(g,{name:"voucherNumber"},{default:a(()=>[d(r(l.voucherNumber)+" ",1),l.isOnline?(p(),y(k,{key:0,block:""},{default:a(()=>[d(r(l.referenceNumber),1)]),_:2},1024)):_("",!0),l.isRejected?(p(),y(D,{key:1,design:"warning",size:"sm"},{default:a(()=>[d(r(o.$trans("general.rejected")),1)]),_:1})):_("",!0),l.isCancelled?(p(),y(D,{key:2,design:"danger",size:"sm"},{default:a(()=>[d(r(o.$trans("general.cancelled")),1)]),_:1})):_("",!0),l.isOnline&&!l.isCompleted?(p(),y(D,{key:3,design:"danger",size:"sm"},{default:a(()=>[d(r(o.$trans("general.failed")),1)]),_:1})):_("",!0)]),_:2},1024),e(g,{name:"name"},{default:a(()=>[d(r(l.name)+" ",1),e(k,{block:""},{default:a(()=>[d(r(l.rollNumber||l.codeNumber),1)]),_:2},1024),e(D,{design:"primary",size:"sm"},{default:a(()=>[d(r(l.feeType),1)]),_:2},1024)]),_:2},1024),e(g,{name:"fatherName"},{default:a(()=>[d(r(l.fatherName)+" ",1),e(k,{block:""},{default:a(()=>[d(r(l.contactNumber),1)]),_:2},1024)]),_:2},1024),e(g,{name:"course"},{default:a(()=>[d(r(l.courseName)+" ",1),e(k,{block:""},{default:a(()=>[d(r(l.batchName),1)]),_:2},1024)]),_:2},1024),e(g,{name:"amount"},{default:a(()=>[d(r(l.amount.formatted),1)]),_:2},1024),e(g,{name:"date"},{default:a(()=>[d(r(l.date.formatted),1)]),_:2},1024),e(g,{name:"ledger"},{default:a(()=>{var h,w,P,H;return[d(r((w=(h=l.payment)==null?void 0:h.ledger)==null?void 0:w.name)+" ",1),e(k,{block:""},{default:a(()=>{var U;return[d(r((U=l.payment)==null?void 0:U.methodName),1)]}),_:2},1024),(H=(P=l.payment)==null?void 0:P.summary)!=null&&H.length?(p(),y(k,{key:0,block:""},{default:a(()=>[(p(!0),C(A,null,I(l.payment.summary,U=>(p(),C("div",null,r(U),1))),256))]),_:2},1024)):_("",!0)]}),_:2},1024),e(g,{name:"user"},{default:a(()=>{var h,w;return[d(r(((w=(h=l.user)==null?void 0:h.profile)==null?void 0:w.name)||"-"),1)]}),_:2},1024),e(g,{name:"action"},{default:a(()=>[e(G,null,{default:a(()=>[e(O,{icon:"fas fa-print",onClick:h=>T(l)},{default:a(()=>[d(r(o.$trans("global.print",{attribute:o.$trans("student.fee.receipt")})),1)]),_:2},1032,["onClick"]),e(O,{icon:"fas fa-arrow-circle-right",onClick:h=>S(f).push({name:"StudentShowFee",params:{uuid:l.studentUuid}})},{default:a(()=>[d(r(o.$trans("global.show",{attribute:o.$trans("student.fee.fee")})),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","footer","meta"])]),_:1},8,["is-loading"])]),_:1})],64)}}});export{be as default};
