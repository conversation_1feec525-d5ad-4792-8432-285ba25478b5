import{u as N,h as U,i as S,m as I,l as C,L as E,n as K,r as o,a,o as t,q as k,b as f,e as d,w as i,d as n,F as m,v as _,f as y,s as P,t as g,K as $}from"./app-DvIo72ZO.js";const W={class:"space-x-4"},z={class:"scroller-thin-y scroller-hidden overflow-x-hidden h-96"},G={class:"border border-gray-200 dark:border-gray-700 sm:rounded-lg scroller-thin-x scroller-hidden scrollbar-track-transparent scrollbar-thumb-body dark:scrollbar-thumb-dm-body"},J={key:0,class:"table min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Q={class:"bg-gray-50 dark:bg-neutral-700"},X={class:"sticky-column bg-gray-50 dark:bg-neutral-700 px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400"},Y={class:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400"},Z={class:"dark:bg-dark-body divide-y divide-gray-200 bg-white dark:divide-gray-700"},ee={class:"sticky-column bg-white dark:bg-dark-body py-2 pl-6 text-sm text-gray-500 dark:text-gray-400"},te={class:"py-2 pl-6 text-sm text-gray-500 dark:text-gray-400"},se={name:"TeamConfigPermissionAssign"},oe=Object.assign(se,{props:{team:{type:Object,default(){return{name:""}}}},setup(b){const l=N(),v=U(),T=S(),p=I(!1),x="team/permission/",u=C({modules:[]}),r={selectedModule:"",assignedPermissions:[]},c=C({...r}),A=e=>{v.push({name:"TeamConfigPermissionAssignModule",params:{module:e}})},w=async()=>{p.value=!0,await T.dispatch(x+"preRequisite",{uuid:l.params.uuid,data:l.params.module||"general"}).then(e=>{p.value=!1,Object.assign(u,{modules:e.modules,selectedModule:e.selectedModule,roles:e.roles}),r.selectedModule=e.selectedModule,r.assignedPermissions=e.assignedPermissions,Object.assign(c,$(r))}).catch(e=>{p.value=!1})};return E(()=>l.params.module,e=>{e&&(r.selectedModule=e,Object.assign(c,$(r)),w())}),K(()=>{w()}),(e,B)=>{const M=o("DropdownItem"),V=o("DropdownButton"),D=o("BaseButton"),L=o("PageHeader"),j=o("CardHeader"),F=o("BaseCheckbox"),O=o("BaseLoader"),R=o("FormAction"),q=o("ParentTransition");return t(),a(m,null,[b.team.uuid?(t(),k(L,{key:0,title:e.$trans(y(l).meta.label),navs:[{label:e.$trans("team.team"),path:"TeamList"},{label:b.team.name,path:{name:"TeamShow",params:{uuid:b.team.uuid}}},{label:e.$trans("team.config.config"),path:"TeamConfig"}]},{default:i(()=>[n("div",W,[u.modules.length?(t(),k(V,{key:0,direction:"down",label:e.$trans("module."+u.selectedModule)},{default:i(()=>[n("div",z,[(t(!0),a(m,null,_(u.modules,s=>(t(),a("div",{key:s.value},[s.value!=y(l).params.module?(t(),k(M,{key:0,as:"span",onClick:h=>A(s.value)},{default:i(()=>[P(g(s.label),1)]),_:2},1032,["onClick"])):f("",!0)]))),128))])]),_:1},8,["label"])):f("",!0),d(D,{onClick:B[0]||(B[0]=s=>y(v).push({name:"TeamConfigUserPermission"}))},{default:i(()=>[P(g(e.$trans("team.config.permission.user_permission")),1)]),_:1})])]),_:1},8,["title","navs"])):f("",!0),d(q,{appear:"",visibility:!0},{default:i(()=>[d(R,{"no-data-fetch":"","init-url":x,uuid:y(l).params.uuid,action:"roleWiseAssign","init-form":r,form:c,"stay-on":""},{default:i(()=>[d(j,{first:"",title:e.$trans("team.config.permission.permission_config"),description:e.$trans("team.config.permission.permission_info")},null,8,["title","description"]),d(O,{"is-loading":p.value},{default:i(()=>[n("div",G,[c.assignedPermissions.length?(t(),a("table",J,[n("thead",Q,[n("tr",null,[n("th",X,g(e.$trans("team.config.permission.permission")),1),(t(!0),a(m,null,_(u.roles,s=>(t(),a("th",Y,g(s.label),1))),256))])]),n("tbody",Z,[(t(!0),a(m,null,_(c.assignedPermissions,s=>(t(),a("tr",{key:s.name},[n("td",ee,g(s.name),1),(t(!0),a(m,null,_(s.roles,h=>(t(),a("td",te,[d(F,{modelValue:h.isAssigned,"onUpdate:modelValue":H=>h.isAssigned=H},null,8,["modelValue","onUpdate:modelValue"])]))),256))]))),128))])])):f("",!0)])]),_:1},8,["is-loading"])]),_:1},8,["uuid","form"])]),_:1})],64)}}});export{oe as default};
