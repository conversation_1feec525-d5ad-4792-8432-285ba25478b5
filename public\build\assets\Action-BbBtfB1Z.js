import{u as j,l as $,K as C,I as H,L as N,r as u,q as U,o as p,w as c,d as r,a as q,b as g,e as l,f as n,s as x,t as d,F as w,v as R}from"./app-DvIo72ZO.js";const D={class:"grid grid-cols-4 gap-6"},I={class:"col-span-4 sm:col-span-2"},z={class:"col-span-4 sm:col-span-1"},K={class:"col-span-4 sm:col-span-2"},W={class:"col-span-4 sm:col-span-3"},G={class:"col-span-4 sm:col-span-1"},J={class:"col-span-4"},M={class:"col-span-4"},X={key:0,class:"mt-6"},Y={class:"flex items-center justify-between mb-4"},Z={class:"text-lg font-medium"},ee={key:0,class:"text-red-500 text-sm mb-4"},te={class:"space-y-4"},se={class:"flex-1"},oe={class:"flex items-center space-x-2"},ae={name:"ExamQuestionBankForm"},ne=Object.assign(ae,{setup(E){const k=j(),i={subject:"",batches:[],type:"",title:"",mark:1,header:"",description:"",options:[{title:"",isCorrect:!1},{title:"",isCorrect:!1}]},s=$(C(i)),a=H("exam/questionBank/"),h=$({subjects:[],types:[]}),m=$({batches:[],isLoaded:!k.params.uuid}),S="exam/questionBank/",O=o=>{Object.assign(h,o),k.params.uuid||(m.isLoaded=!0)},F=o=>{var y,_,V;let t=((y=o.batches)==null?void 0:y.map(B=>B.uuid))||[],b=o.options&&o.options.length>0?C(o.options):[{title:"",isCorrect:!1},{title:"",isCorrect:!1}];Object.assign(i,{...o,subject:((_=o.subject)==null?void 0:_.uuid)||"",batches:t,type:((V=o.type)==null?void 0:V.value)||"",options:b}),Object.assign(s,C(i)),m.batches=t,m.isLoaded=!0},L=()=>{s.options.push({title:"",isCorrect:!1})},A=o=>{s.options.length>1&&s.options.splice(o,1)},P=o=>{s.options[o].isCorrect&&s.options.forEach((t,b)=>{b!==o&&(t.isCorrect=!1)})};return N(()=>s.type,o=>{o==="mcq"&&(!s.options||s.options.length===0)&&(s.options=[{title:"",isCorrect:!1},{title:"",isCorrect:!1}])}),(o,t)=>{const b=u("BaseSelect"),y=u("BaseSelectSearch"),_=u("BaseInput"),V=u("BaseTextarea"),B=u("BaseButton"),T=u("BaseSwitch"),Q=u("FormAction");return p(),U(Q,{"pre-requisites":!0,onSetPreRequisites:O,"init-url":S,"init-form":i,form:s,"set-form":F,redirect:"ExamQuestionBank"},{default:c(()=>[r("div",D,[r("div",I,[l(b,{modelValue:s.subject,"onUpdate:modelValue":t[0]||(t[0]=e=>s.subject=e),name:"subject",label:o.$trans("exam.question_bank.props.subject"),"label-prop":"name","value-prop":"uuid",options:h.subjects,error:n(a).subject,"onUpdate:error":t[1]||(t[1]=e=>n(a).subject=e),required:""},null,8,["modelValue","label","options","error"])]),r("div",z,[l(b,{modelValue:s.type,"onUpdate:modelValue":t[2]||(t[2]=e=>s.type=e),name:"type",label:o.$trans("exam.question_bank.props.type"),options:h.types,error:n(a).type,"onUpdate:error":t[3]||(t[3]=e=>n(a).type=e),required:""},null,8,["modelValue","label","options","error"])]),r("div",K,[m.isLoaded?(p(),U(y,{key:0,multiple:"",name:"batches",label:o.$trans("academic.batch.batch"),modelValue:s.batches,"onUpdate:modelValue":t[4]||(t[4]=e=>s.batches=e),error:n(a).batches,"onUpdate:error":t[5]||(t[5]=e=>n(a).batches=e),"value-prop":"uuid","init-search":m.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:c(e=>[x(d(e.value.course.name)+" - "+d(e.value.name),1)]),listOption:c(e=>[x(d(e.option.course.nameWithTerm)+" - "+d(e.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):g("",!0)]),r("div",W,[l(_,{type:"text",modelValue:s.title,"onUpdate:modelValue":t[6]||(t[6]=e=>s.title=e),name:"title",label:o.$trans("exam.question_bank.props.title"),error:n(a).title,"onUpdate:error":t[7]||(t[7]=e=>n(a).title=e),required:""},null,8,["modelValue","label","error"])]),r("div",G,[l(_,{type:"number",modelValue:s.mark,"onUpdate:modelValue":t[8]||(t[8]=e=>s.mark=e),name:"mark",label:o.$trans("exam.question_bank.props.mark"),error:n(a).mark,"onUpdate:error":t[9]||(t[9]=e=>n(a).mark=e),step:"0.01",min:"0.01",required:""},null,8,["modelValue","label","error"])]),r("div",J,[l(V,{modelValue:s.header,"onUpdate:modelValue":t[10]||(t[10]=e=>s.header=e),name:"header",label:o.$trans("exam.question_bank.props.header"),error:n(a).header,"onUpdate:error":t[11]||(t[11]=e=>n(a).header=e),rows:3},null,8,["modelValue","label","error"])]),r("div",M,[l(V,{modelValue:s.description,"onUpdate:modelValue":t[12]||(t[12]=e=>s.description=e),name:"description",label:o.$trans("exam.question_bank.props.description"),error:n(a).description,"onUpdate:error":t[13]||(t[13]=e=>n(a).description=e),rows:4},null,8,["modelValue","label","error"])])]),s.type==="mcq"?(p(),q("div",X,[r("div",Y,[r("h3",Z,d(o.$trans("exam.question_bank.props.options")),1),l(B,{type:"button",variant:"soft",onClick:L},{default:c(()=>[t[14]||(t[14]=r("i",{class:"fas fa-plus mr-2"},null,-1)),x(" "+d(o.$trans("exam.question_bank.add_option")),1)]),_:1})]),n(a).options?(p(),q("div",ee,d(n(a).options),1)):g("",!0),r("div",te,[(p(!0),q(w,null,R(s.options,(e,f)=>(p(),q("div",{key:f,class:"flex items-center space-x-4 p-4 border rounded-lg"},[r("div",se,[l(_,{type:"text",modelValue:e.title,"onUpdate:modelValue":v=>e.title=v,name:`option_${f}`,label:o.$trans("exam.question_bank.option_text"),error:n(a)[`options.${f}.title`],required:""},null,8,["modelValue","onUpdate:modelValue","name","label","error"])]),r("div",oe,[l(T,{vertical:"",modelValue:e.isCorrect,"onUpdate:modelValue":v=>e.isCorrect=v,name:`option_correct_${f}`,label:o.$trans("exam.question_bank.correct_answer"),onChange:v=>P(f)},null,8,["modelValue","onUpdate:modelValue","name","label","onChange"]),s.options.length>1?(p(),U(B,{key:0,type:"button",variant:"soft",size:"sm",onClick:v=>A(f)},{default:c(()=>t[15]||(t[15]=[r("i",{class:"fas fa-trash"},null,-1)])),_:2},1032,["onClick"])):g("",!0)])]))),128))])])):g("",!0)]),_:1},8,["form"])}}}),re={name:"ExamQuestionBankAction"},ie=Object.assign(re,{setup(E){const k=j();return(i,s)=>{const a=u("PageHeaderAction"),h=u("PageHeader"),m=u("ParentTransition");return p(),q(w,null,[l(h,{title:i.$trans(n(k).meta.trans,{attribute:i.$trans(n(k).meta.label)}),navs:[{label:i.$trans("exam.exam"),path:"Exam"},{label:i.$trans("exam.question_bank.question_bank"),path:"ExamQuestionBankList"}]},{default:c(()=>[l(a,{name:"ExamQuestionBank",title:i.$trans("exam.question_bank.question_bank"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(m,{appear:"",visibility:!0},{default:c(()=>[l(ne)]),_:1})],64)}}});export{ie as default};
