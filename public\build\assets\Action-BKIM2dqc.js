import{u as v,h as F,I as P,l as _,r as l,q as V,o as y,w as u,d as c,e as i,f as t,b as S,s as f,t as g,K as I,a as O,F as N}from"./app-DvIo72ZO.js";const T={class:"grid grid-cols-3 gap-6"},j={class:"col-span-3 sm:col-span-1"},q={class:"col-span-2 sm:col-span-1"},R={class:"col-span-2 sm:col-span-1"},H={class:"col-span-2 sm:col-span-1"},L={class:"col-span-3"},E={name:"AcademicCourseInchargeForm"},w=Object.assign(E,{setup(h){const d=v();F();const n={course:"",employee:"",startDate:"",endDate:"",remarks:""},b="academic/courseIncharge/",s=P(b),p=_({courses:[]}),r=_({...n}),m=_({course:"",employee:"",isLoaded:!d.params.uuid}),k=o=>{Object.assign(p,o)},$=o=>{Object.assign(n,{...o,startDate:o.startDate.value,endDate:o.endDate.value,course:o.course.uuid,employee:o.employee.uuid}),Object.assign(r,I(n)),m.course=o.course.uuid,m.employee=o.employee.uuid,m.isLoaded=!0};return(o,a)=>{const A=l("BaseSelect"),U=l("BaseSelectSearch"),D=l("DatePicker"),B=l("BaseTextarea"),C=l("FormAction");return y(),V(C,{"pre-requisites":!0,onSetPreRequisites:k,"init-url":b,"init-form":n,form:r,setForm:$,redirect:"AcademicCourseIncharge"},{default:u(()=>[c("div",T,[c("div",j,[i(A,{name:"course",label:o.$trans("academic.course.course"),modelValue:r.course,"onUpdate:modelValue":a[0]||(a[0]=e=>r.course=e),error:t(s).course,"onUpdate:error":a[1]||(a[1]=e=>t(s).course=e),"value-prop":"uuid","label-prop":"nameWithTermAndDivision",options:p.courses},null,8,["label","modelValue","error","options"])]),c("div",q,[m.isLoaded?(y(),V(U,{key:0,name:"employee",label:o.$trans("global.select",{attribute:o.$trans("employee.employee")}),modelValue:r.employee,"onUpdate:modelValue":a[2]||(a[2]=e=>r.employee=e),error:t(s).employee,"onUpdate:error":a[3]||(a[3]=e=>t(s).employee=e),"value-prop":"uuid","init-search":m.employee,"search-key":"name","search-action":"employee/list"},{selectedOption:u(e=>[f(g(e.value.name)+" ("+g(e.value.codeNumber)+") ",1)]),listOption:u(e=>[f(g(e.option.name)+" ("+g(e.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","error","init-search"])):S("",!0)]),c("div",R,[i(D,{modelValue:r.startDate,"onUpdate:modelValue":a[4]||(a[4]=e=>r.startDate=e),name:"startDate",label:o.$trans("employee.incharge.props.start_date"),"no-clear":"",error:t(s).startDate,"onUpdate:error":a[5]||(a[5]=e=>t(s).startDate=e)},null,8,["modelValue","label","error"])]),c("div",H,[i(D,{modelValue:r.endDate,"onUpdate:modelValue":a[6]||(a[6]=e=>r.endDate=e),name:"endDate",label:o.$trans("employee.incharge.props.end_date"),"no-clear":"",error:t(s).endDate,"onUpdate:error":a[7]||(a[7]=e=>t(s).endDate=e)},null,8,["modelValue","label","error"])]),c("div",L,[i(B,{modelValue:r.remarks,"onUpdate:modelValue":a[8]||(a[8]=e=>r.remarks=e),name:"remarks",label:o.$trans("employee.incharge.props.remarks"),error:t(s).remarks,"onUpdate:error":a[9]||(a[9]=e=>t(s).remarks=e)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),K={name:"AcademicCourseInchargeAction"},z=Object.assign(K,{setup(h){const d=v();return(n,b)=>{const s=l("PageHeaderAction"),p=l("PageHeader"),r=l("ParentTransition");return y(),O(N,null,[i(p,{title:n.$trans(t(d).meta.trans,{attribute:n.$trans(t(d).meta.label)}),navs:[{label:n.$trans("academic.academic"),path:"Academic"},{label:n.$trans("academic.course.course"),path:"AcademicCourse"},{label:n.$trans("academic.course_incharge.course_incharge"),path:"AcademicCourseInchargeList"}]},{default:u(()=>[i(s,{name:"AcademicCourseIncharge",title:n.$trans("academic.course_incharge.course_incharge"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),i(r,{appear:"",visibility:!0},{default:u(()=>[i(w)]),_:1})],64)}}});export{z as default};
