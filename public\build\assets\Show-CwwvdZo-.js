import{i as N,u as $,h as F,j as v,l as O,r as i,a as p,o as s,e as a,w as t,f as e,q as m,b as k,d as A,F as B,v as q,s as n,t as r}from"./app-DvIo72ZO.js";const z={class:"space-y-2"},E={class:"space-y-4"},U={key:1,class:"px-4 py-2"},G={name:"LibraryBookAdditionShow"},M=Object.assign(G,{setup(J){N();const c=$(),L=F(),o=v("$trans");v("emitter");const V={},h="library/bookAddition/",w=[{key:"book",label:o("library.book.book"),visibility:!0},{key:"number",label:o("library.book_addition.props.number"),visibility:!0},{key:"condition",label:o("library.book_addition.props.condition"),visibility:!0}],l=O({...V}),C=y=>{Object.assign(l,y)};return(y,d)=>{const S=i("PageHeaderAction"),D=i("PageHeader"),_=i("ListItemView"),x=i("ListContainerVertical"),f=i("BaseCard"),b=i("DataCell"),I=i("DataRow"),P=i("SimpleTable"),T=i("BaseAlert"),H=i("DetailLayoutVertical"),R=i("ShowItem"),j=i("ParentTransition");return s(),p(B,null,[a(D,{title:e(o)(e(c).meta.trans,{attribute:e(o)(e(c).meta.label)}),navs:[{label:e(o)("library.library"),path:"Library"},{label:e(o)("library.book_addition.book_addition"),path:"LibraryBookAdditionList"}]},{default:t(()=>[a(S,{name:"LibraryBookAddition",title:e(o)("library.book_addition.book_addition"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(j,{appear:"",visibility:!0},{default:t(()=>[a(R,{"init-url":h,uuid:e(c).params.uuid,onSetItem:C,onRedirectTo:d[0]||(d[0]=u=>e(L).push({name:"LibraryBookAddition"}))},{default:t(()=>[l.uuid?(s(),m(H,{key:0},{detail:t(()=>[A("div",z,[a(f,{"no-padding":"","no-content-padding":""},{title:t(()=>[n(r(e(o)("library.book_addition.book_addition")),1)]),action:t(()=>d[1]||(d[1]=[])),default:t(()=>[a(x,null,{default:t(()=>[a(_,{label:e(o)("library.book_addition.props.date")},{default:t(()=>[n(r(l.date.formatted),1)]),_:1},8,["label"]),a(_,{label:e(o)("library.book_addition.props.remarks")},{default:t(()=>[n(r(l.remarks),1)]),_:1},8,["label"]),a(_,{label:e(o)("general.created_at")},{default:t(()=>[n(r(l.createdAt.formatted),1)]),_:1},8,["label"]),a(_,{label:e(o)("general.updated_at")},{default:t(()=>[n(r(l.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})])]),default:t(()=>[A("div",E,[a(f,{"no-padding":"","no-content-padding":""},{title:t(()=>[n(r(e(o)("library.book_addition.props.copies")),1)]),footer:t(()=>d[2]||(d[2]=[])),default:t(()=>[l.copies.length>0?(s(),m(P,{key:0,header:w},{default:t(()=>[(s(!0),p(B,null,q(l.copies,u=>(s(),m(I,{key:u.uuid},{default:t(()=>[a(b,{name:"book"},{default:t(()=>[n(r(u.book.title),1)]),_:2},1024),a(b,{name:"number"},{default:t(()=>[n(r(u.number),1)]),_:2},1024),a(b,{name:"condition"},{default:t(()=>{var g;return[n(r(((g=u.condition)==null?void 0:g.name)||"-"),1)]}),_:2},1024)]),_:2},1024))),128))]),_:1})):k("",!0),l.copies.length===0?(s(),p("div",U,[a(T,{design:"info",size:"xs"},{default:t(()=>[n(r(e(o)("general.errors.addition_not_found")),1)]),_:1})])):k("",!0)]),_:1})])]),_:1})):k("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{M as default};
