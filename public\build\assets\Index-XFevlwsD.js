import{u as k,l as w,r as s,q as b,o as _,w as t,d as I,e as a,f as o,h as M,j as N,m as B,a as C,F as S,v as x,s as p,b as O,am as E,t as c}from"./app-DvIo72ZO.js";const K={class:"grid grid-cols-3 gap-6"},U={class:"col-span-3 sm:col-span-1"},h={__name:"Filter",emits:["hide"],setup(T,{emit:l}){const v=k(),d=l,$={name:""},m=w({...$});return(u,r)=>{const g=s("BaseInput"),i=s("FilterForm");return _(),b(i,{"init-form":$,form:m,onHide:r[1]||(r[1]=e=>d("hide"))},{default:t(()=>[I("div",K,[I("div",U,[a(g,{type:"text",modelValue:m.name,"onUpdate:modelValue":r[0]||(r[0]=e=>m.name=e),name:"name",label:u.$trans(`${o(v).meta.transKey}.props.name`)},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},z={class:"inline-block w-6"},G={name:"OptionList"},Q=Object.assign(G,{setup(T){const l=k(),v=M(),d=N("emitter"),$="option/",m=B(!1),u=B(!1),r=w({}),g=i=>{Object.assign(r,i)};return(i,e)=>{const D=s("PageHeaderAction"),H=s("PageHeader"),V=s("BaseImport"),y=s("ParentTransition"),f=s("DataCell"),F=s("FloatingMenuItem"),P=s("FloatingMenu"),R=s("DataRow"),q=s("BaseButton"),j=s("DataTable"),A=s("ListItem");return _(),b(A,{"init-url":$,onSetItems:g},{header:t(()=>[a(H,{title:i.$trans(o(l).meta.label),navs:o(l).meta.navs},{default:t(()=>[a(D,{url:"options/","additional-dropdown-actions-query":{type:o(l).meta.queryType},name:o(l).meta.prefix,title:i.$trans(o(l).meta.label),actions:["create","filter"],"dropdown-actions":["import","print","pdf","excel"],onToggleFilter:e[0]||(e[0]=n=>m.value=!m.value),onToggleImport:e[1]||(e[1]=n=>u.value=!u.value)},null,8,["additional-dropdown-actions-query","name","title"])]),_:1},8,["title","navs"])]),import:t(()=>[a(y,{appear:"",visibility:u.value},{default:t(()=>[a(V,{path:`options/import?type=${o(l).meta.queryType}`,onCancelled:e[2]||(e[2]=n=>u.value=!1),onHide:e[3]||(e[3]=n=>u.value=!1),onCompleted:e[4]||(e[4]=n=>o(d).emit("listItems"))},null,8,["path"])]),_:1},8,["visibility"])]),filter:t(()=>[a(y,{appear:"",visibility:m.value},{default:t(()=>[a(h,{onRefresh:e[5]||(e[5]=n=>o(d).emit("listItems")),onHide:e[6]||(e[6]=n=>m.value=!1)})]),_:1},8,["visibility"])]),default:t(()=>[a(y,{appear:"",visibility:!0},{default:t(()=>[a(j,{header:r.headers,meta:r.meta,module:o(l).meta.transKey,onRefresh:e[8]||(e[8]=n=>o(d).emit("listItems"))},{actionButton:t(()=>[a(q,{onClick:e[7]||(e[7]=n=>o(v).push({name:`${o(l).meta.prefix}Create`}))},{default:t(()=>[p(c(i.$trans("global.add",{attribute:i.$trans(o(l).meta.label)})),1)]),_:1})]),default:t(()=>[(_(!0),C(S,null,x(r.data,n=>(_(),b(R,{key:n.uuid},{default:t(()=>[a(f,{name:"name"},{default:t(()=>[I("div",z,[n.color?(_(),C("div",{key:0,class:"h-5 w-5 rounded-full",style:E(`background-color: ${n.color};`)},"   ",4)):O("",!0)]),p(" "+c(n.name),1)]),_:2},1024),a(f),a(f,{name:"description"},{default:t(()=>[p(c(n.descriptionSummary),1)]),_:2},1024),a(f,{name:"createdAt"},{default:t(()=>[p(c(n.createdAt.formatted),1)]),_:2},1024),a(f,{name:"action"},{default:t(()=>[a(P,null,{default:t(()=>[a(F,{icon:"fas fa-edit",onClick:L=>o(v).push({name:`${o(l).meta.prefix}Edit`,params:{uuid:n.uuid}})},{default:t(()=>[p(c(i.$trans("general.edit")),1)]),_:2},1032,["onClick"]),a(F,{icon:"fas fa-trash",onClick:L=>o(d).emit("deleteItem",{uuid:n.uuid})},{default:t(()=>[p(c(i.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","meta","module"])]),_:1})]),_:1})}}});export{Q as default};
