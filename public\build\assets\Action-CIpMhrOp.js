import{u as B,I as j,l as S,r as i,q as w,o as v,w as p,d,a as _,e as l,f as t,F as N,s as $,t as f,K as P}from"./app-DvIo72ZO.js";const k={class:"grid grid-cols-3 gap-6"},A={class:"col-span-3"},D={class:"col-span-3 sm:col-span-2"},E={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3 sm:col-span-1"},I={class:"col-span-3 sm:col-span-1"},L={class:"col-span-3 sm:col-span-1"},C={name:"StudentGuardianForm"},z=Object.assign(C,{setup(c){const u=B(),s={guardianType:"new",name:"",relation:"",contactNumber:"",guardian:null},V="student/guardian/",r=j(V),m=S({relations:[]}),y=[{value:"new",label:"New Guardian"},{value:"existing",label:"Existing Guardian"}],n=S({...s}),G=S({isLoaded:!u.params.muuid}),F=o=>{Object.assign(m,o)},O=o=>{var e,g,b;Object.assign(s,{...o,name:((e=o.contact)==null?void 0:e.name)||"",relation:(g=o.relation)==null?void 0:g.value,contactNumber:((b=o.contact)==null?void 0:b.contactNumber)||""}),Object.assign(n,P(s)),G.isLoaded=!0},q=o=>{const e={...o};return e.guardianType&&(e.guardian_type=e.guardianType,delete e.guardianType),e};return(o,e)=>{const g=i("BaseRadioGroup"),b=i("BaseSelectSearch"),T=i("BaseSelect"),U=i("BaseInput"),R=i("FormAction");return v(),w(R,{"pre-requisites":!0,onSetPreRequisites:F,"no-data-fetch":"","init-url":V,uuid:t(u).params.uuid,"module-uuid":t(u).params.muuid,"init-form":s,form:n,"set-form":O,"transform-request":q,redirect:{name:"StudentGuardian",params:{uuid:t(u).params.uuid}}},{default:p(()=>[d("div",k,[d("div",A,[l(g,{"top-margin":"",options:y,name:"guardianType",modelValue:n.guardianType,"onUpdate:modelValue":e[0]||(e[0]=a=>n.guardianType=a),error:t(r).guardianType,"onUpdate:error":e[1]||(e[1]=a=>t(r).guardianType=a),horizontal:""},null,8,["modelValue","error"])]),n.guardianType==="existing"?(v(),_(N,{key:0},[d("div",D,[l(b,{name:"guardian",label:o.$trans("global.select",{attribute:o.$trans("guardian.guardian")}),modelValue:n.guardian,"onUpdate:modelValue":e[2]||(e[2]=a=>n.guardian=a),error:t(r).guardian,"onUpdate:error":e[3]||(e[3]=a=>t(r).guardian=a),"value-prop":"uuid","search-action":"guardian/list"},{selectedOption:p(a=>[$(f(a.value.name)+" ("+f(a.value.contactNumber)+") ",1)]),listOption:p(a=>[$(f(a.option.name)+" ("+f(a.option.contactNumber)+") ",1)]),_:1},8,["label","modelValue","error"])]),d("div",E,[l(T,{modelValue:n.relation,"onUpdate:modelValue":e[4]||(e[4]=a=>n.relation=a),name:"relation",label:o.$trans("contact.props.relation"),options:m.relations,"label-prop":"label","value-prop":"value",error:t(r).relation,"onUpdate:error":e[5]||(e[5]=a=>t(r).relation=a)},null,8,["modelValue","label","options","error"])])],64)):(v(),_(N,{key:1},[d("div",H,[l(U,{disabled:t(u).params.muuid,type:"text",modelValue:n.name,"onUpdate:modelValue":e[6]||(e[6]=a=>n.name=a),name:"name",label:o.$trans("contact.props.name"),error:t(r).name,"onUpdate:error":e[7]||(e[7]=a=>t(r).name=a),autofocus:""},null,8,["disabled","modelValue","label","error"])]),d("div",I,[l(T,{modelValue:n.relation,"onUpdate:modelValue":e[8]||(e[8]=a=>n.relation=a),name:"relation",label:o.$trans("contact.props.relation"),options:m.relations,"label-prop":"label","value-prop":"value",error:t(r).relation,"onUpdate:error":e[9]||(e[9]=a=>t(r).relation=a)},null,8,["modelValue","label","options","error"])]),d("div",L,[l(U,{disabled:t(u).params.muuid,type:"text",modelValue:n.contactNumber,"onUpdate:modelValue":e[10]||(e[10]=a=>n.contactNumber=a),name:"contactNumber",label:o.$trans("contact.props.contact_number"),error:t(r).contactNumber,"onUpdate:error":e[11]||(e[11]=a=>t(r).contactNumber=a)},null,8,["disabled","modelValue","label","error"])])],64))])]),_:1},8,["uuid","module-uuid","form","redirect"])}}}),K={name:"StudentGuardianAction"},M=Object.assign(K,{props:{student:{type:Object,default(){return{}}}},setup(c){const u=B();return(s,V)=>{const r=i("PageHeaderAction"),m=i("PageHeader"),y=i("ParentTransition");return v(),_(N,null,[l(m,{title:s.$trans(t(u).meta.trans,{attribute:s.$trans(t(u).meta.label)}),navs:[{label:s.$trans("student.student"),path:"StudentList"},{label:c.student.contact.name,path:{name:"StudentShow",params:{uuid:c.student.uuid}}},{label:s.$trans("guardian.guardian"),path:{name:"StudentGuardian",params:{uuid:c.student.uuid}}}]},{default:p(()=>[l(r,{name:"StudentGuardian",title:s.$trans("guardian.guardian"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(y,{appear:"",visibility:!0},{default:p(()=>[l(z)]),_:1})],64)}}});export{M as default};
