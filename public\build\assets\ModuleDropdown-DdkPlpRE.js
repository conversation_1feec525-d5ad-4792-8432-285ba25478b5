import{u as c,h as d,r as f,a as L,o as t,q as r,b as p,f as e,y as m,w as u,s as y,t as i,F as k}from"./app-DvIo72ZO.js";const E={name:"EmployeeLeaveModuleDropdown"},C=Object.assign(E,{setup(_){const a=c(),n=d();return(s,o)=>{const l=f("DropdownItem");return t(),L(k,null,[e(a).name!="EmployeeLeaveRequestList"&&e(m)("leave-request:read")?(t(),r(l,{key:0,onClick:o[0]||(o[0]=v=>e(n).push({name:"EmployeeLeaveRequest"}))},{default:u(()=>[y(i(s.$trans("employee.leave.request.request")),1)]),_:1})):p("",!0),e(a).name!="EmployeeLeaveAllocationList"&&e(m)("leave-allocation:read")?(t(),r(l,{key:1,onClick:o[1]||(o[1]=v=>e(n).push({name:"EmployeeLeaveAllocation"}))},{default:u(()=>[y(i(s.$trans("employee.leave.allocation.allocation")),1)]),_:1})):p("",!0),e(a).name!="EmployeeLeaveTypeList"&&e(m)("leave:config")?(t(),r(l,{key:2,onClick:o[2]||(o[2]=v=>e(n).push({name:"EmployeeLeaveType"}))},{default:u(()=>[y(i(s.$trans("employee.leave.type.type")),1)]),_:1})):p("",!0)],64)}}});export{C as _};
