import{j as G,i as J,u as K,h as Q,I as W,l as v,m as X,r as u,a as R,o as c,e as a,w as r,f as o,q as B,b,d,s as i,t as l,x as Y,y as C,F as T}from"./app-DvIo72ZO.js";const Z={class:"flex items-center"},x={class:"mr-2"},ee={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},te={class:"mt-4 text-sm space-y-2"},se={class:"font-semibold"},ae={class:"grid grid-cols-3 gap-6"},re={class:"col-span-3 sm:col-span-1"},ne={class:"col-span-3 sm:col-span-1"},oe={class:"col-span-3 sm:col-span-1"},le={class:"col-span-2 sm:col-span-1"},ue={class:"col-span-3"},de={name:"StudentTransferRequestShow"},pe=Object.assign(de,{setup(ie){G("emitter"),J();const _=K(),V=Q(),k={},S={status:"",reason:"",transferDate:"",transferCertificateNumber:"",comment:""},q="student/transferRequest/",f=W(q),g=v({reasons:[],statuses:[]}),m=v({...S}),$=X(!1),s=v({...k}),y=e=>{Object.assign(s,e)},U=e=>{Object.assign(g,e)},h=()=>{$.value=!0};return(e,t)=>{const P=u("PageHeaderAction"),w=u("PageHeader"),I=u("BaseBadge"),p=u("BaseDataView"),j=u("ListMedia"),F=u("BaseButton"),A=u("ShowButton"),N=u("BaseCard"),D=u("BaseSelect"),E=u("BaseInput"),H=u("DatePicker"),L=u("BaseTextarea"),O=u("FormAction"),M=u("ShowItem"),z=u("ParentTransition");return c(),R(T,null,[a(w,{title:e.$trans(o(_).meta.trans,{attribute:e.$trans(o(_).meta.label)}),navs:[{label:e.$trans("student.student"),path:"Student"},{label:e.$trans("student.transfer_request.transfer_request"),path:"StudentTransferRequestList"}]},{default:r(()=>[a(P,{name:"StudentTransferRequest",title:e.$trans("student.transfer_request.transfer_request"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(z,{appear:"",visibility:!0},{default:r(()=>[a(M,{"init-url":q,uuid:o(_).params.uuid,onSetItem:y,onRedirectTo:t[11]||(t[11]=n=>o(V).push({name:"StudentTransferRequest"})),refresh:$.value,onRefreshed:t[12]||(t[12]=n=>$.value=!1)},{default:r(()=>[s.uuid?(c(),B(N,{key:0},{title:r(()=>[d("div",Z,[d("span",x,l(s.codeNumber),1),a(I,{design:s.status.color},{default:r(()=>[i(l(s.status.label),1)]),_:1},8,["design"])])]),footer:r(()=>[a(A,null,{default:r(()=>[o(C)("student:transfer-request")&&s.isEditable?(c(),B(F,{key:0,design:"primary",onClick:t[0]||(t[0]=n=>o(V).push({name:"StudentTransferRequestEdit",params:{uuid:s.uuid}}))},{default:r(()=>[i(l(e.$trans("general.edit")),1)]),_:1})):b("",!0)]),_:1})]),default:r(()=>[d("dl",ee,[a(p,{label:e.$trans("student.admission.props.code_number")},{default:r(()=>[i(l(s.student.codeNumber),1)]),_:1},8,["label"]),a(p,{label:e.$trans("contact.props.birth_date")},{default:r(()=>[i(l(s.student.birthDate.formatted),1)]),_:1},8,["label"]),a(p,{label:e.$trans("contact.props.father_name")},{default:r(()=>[i(l(s.student.fatherName),1)]),_:1},8,["label"]),a(p,{label:e.$trans("contact.props.mother_name")},{default:r(()=>[i(l(s.student.motherName),1)]),_:1},8,["label"]),a(p,{label:e.$trans("academic.course.course")},{default:r(()=>[i(l(s.student.courseName+" "+s.student.batchName),1)]),_:1},8,["label"]),a(p,{label:e.$trans("student.admission.props.date")},{default:r(()=>[i(l(s.student.joiningDate.formatted),1)]),_:1},8,["label"]),a(p,{label:e.$trans("student.transfer.props.date")},{default:r(()=>[i(l(s.student.leavingDate.formatted||"-"),1)]),_:1},8,["label"]),a(p,{label:e.$trans("student.transfer_request.props.request_date")},{default:r(()=>[i(l(s.requestDate.formatted),1)]),_:1},8,["label"]),a(p,{class:"col-span-1 sm:col-span-2",label:e.$trans("student.transfer_request.props.reason")},{default:r(()=>[i(l(s.reason),1)]),_:1},8,["label"]),a(p,{label:e.$trans("student.transfer_request.props.processed_at")},{default:r(()=>[i(l(s.processedAt.formatted),1)]),_:1},8,["label"]),a(p,{label:e.$trans("student.transfer_request.props.comment")},{default:r(()=>[d("span",{class:Y({"text-danger":s.status.value=="rejected"})},l(s.comment),3)]),_:1},8,["label"])]),d("div",te,[d("div",null,[d("h2",se,l(e.$trans("student.transfer_request.props.application")),1),a(j,{section:"application",media:s.media,url:`/app/student/transfer-requests/${s.uuid}/`},null,8,["media","url"])])])]),_:1})):b("",!0),o(C)("student:transfer-request-action")&&s.uuid&&s.status.value!="approved"?(c(),B(N,{key:1},{title:r(()=>[i(l(e.$trans("student.transfer_request.props.action")),1)]),default:r(()=>[a(O,{"no-card":"","pre-requisites":!0,onSetPreRequisites:U,"keep-adding":!1,"init-url":q,uuid:s.uuid,"no-data-fetch":!0,action:"action","init-form":S,form:m,"after-submit":h},{default:r(()=>[d("div",ae,[d("div",re,[a(D,{name:"status",label:e.$trans("global.select",{attribute:e.$trans("student.transfer_request.props.status")}),modelValue:m.status,"onUpdate:modelValue":t[1]||(t[1]=n=>m.status=n),options:g.statuses,error:o(f).status,"onUpdate:error":t[2]||(t[2]=n=>o(f).status=n)},null,8,["label","modelValue","options","error"])]),m.status=="approved"?(c(),R(T,{key:0},[d("div",ne,[a(E,{type:"text",modelValue:m.transferCertificateNumber,"onUpdate:modelValue":t[3]||(t[3]=n=>m.transferCertificateNumber=n),name:"transferCertificateNumber",label:e.$trans("student.transfer_request.props.certificate_number"),error:o(f).transferCertificateNumber,"onUpdate:error":t[4]||(t[4]=n=>o(f).transferCertificateNumber=n)},null,8,["modelValue","label","error"])]),d("div",oe,[a(D,{name:"reason",label:e.$trans("global.select",{attribute:e.$trans("student.transfer.props.reason")}),"label-prop":"name","value-prop":"uuid",modelValue:m.reason,"onUpdate:modelValue":t[5]||(t[5]=n=>m.reason=n),options:g.reasons,error:o(f).reason,"onUpdate:error":t[6]||(t[6]=n=>o(f).reason=n)},null,8,["label","modelValue","options","error"])]),d("div",le,[a(H,{modelValue:m.transferDate,"onUpdate:modelValue":t[7]||(t[7]=n=>m.transferDate=n),name:"transferDate",label:e.$trans("student.transfer.props.date"),"no-clear":"",error:o(f).transferDate,"onUpdate:error":t[8]||(t[8]=n=>o(f).transferDate=n)},null,8,["modelValue","label","error"])])],64)):b("",!0),d("div",ue,[a(L,{modelValue:m.comment,"onUpdate:modelValue":t[9]||(t[9]=n=>m.comment=n),name:"comment",label:e.$trans("student.transfer_request.props.comment"),error:o(f).comment,"onUpdate:error":t[10]||(t[10]=n=>o(f).comment=n)},null,8,["modelValue","label","error"])])])]),_:1},8,["uuid","form"])]),_:1})):b("",!0)]),_:1},8,["uuid","refresh"])]),_:1})],64)}}});export{pe as default};
