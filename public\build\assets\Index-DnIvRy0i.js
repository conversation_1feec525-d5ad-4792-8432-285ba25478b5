import{u as H,l as $,n as P,r as o,q as v,o as b,w as e,d as w,b as y,s,t as a,e as n,h as j,j as E,m as O,f as p,a as U,F as q,v as z}from"./app-DvIo72ZO.js";const G={class:"grid grid-cols-3 gap-6"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},Q={__name:"Filter",emits:["hide"],setup(N,{emit:g}){const f=H(),h=g,k={students:[],startDate:"",endDate:""},i=$({...k});$({});const m=$({students:[],isLoaded:!f.query.students});return P(async()=>{m.students=f.query.students?f.query.students.split(","):[],m.isLoaded=!0}),(_,r)=>{const u=o("BaseSelectSearch"),l=o("DatePicker"),B=o("FilterForm");return b(),v(B,{"init-form":k,form:i,multiple:["students"],onHide:r[3]||(r[3]=d=>h("hide"))},{default:e(()=>[w("div",G,[w("div",J,[m.isLoaded?(b(),v(u,{key:0,multiple:"",name:"students",label:_.$trans("global.select",{attribute:_.$trans("student.student")}),modelValue:i.students,"onUpdate:modelValue":r[0]||(r[0]=d=>i.students=d),"value-prop":"uuid","init-search":m.students,"search-key":"name","search-action":"student/summary"},{selectedOption:e(d=>[s(a(d.value.name)+" ("+a(d.value.codeNumber)+") ",1)]),listOption:e(d=>[s(a(d.option.name)+" ("+a(d.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):y("",!0)]),w("div",K,[n(l,{start:i.startDate,"onUpdate:start":r[1]||(r[1]=d=>i.startDate=d),end:i.endDate,"onUpdate:end":r[2]||(r[2]=d=>i.endDate=d),name:"dateBetween",as:"range",label:_.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},W={name:"StudentTransferRequestList"},Y=Object.assign(W,{setup(N){const g=j(),f=E("emitter");let h=["create","filter"],k=["print","pdf","excel"];const i="student/transferRequest/",m=O(!1),_=$({}),r=u=>{Object.assign(_,u)};return(u,l)=>{const B=o("PageHeaderAction"),d=o("PageHeader"),F=o("ParentTransition"),c=o("DataCell"),D=o("TextMuted"),T=o("BaseBadge"),C=o("FloatingMenuItem"),I=o("FloatingMenu"),L=o("DataRow"),M=o("BaseButton"),V=o("DataTable"),A=o("ListItem");return b(),v(A,{"init-url":i,onSetItems:r},{header:e(()=>[n(d,{title:u.$trans("student.transfer_request.transfer_request"),navs:[{label:u.$trans("student.student"),path:"Student"}]},{default:e(()=>[n(B,{url:"student/transfer-requests/",name:"StudentTransferRequest",title:u.$trans("student.transfer_request.transfer_request"),actions:p(h),"dropdown-actions":p(k),onToggleFilter:l[0]||(l[0]=t=>m.value=!m.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[n(F,{appear:"",visibility:m.value},{default:e(()=>[n(Q,{onRefresh:l[1]||(l[1]=t=>p(f).emit("listItems")),onHide:l[2]||(l[2]=t=>m.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[n(F,{appear:"",visibility:!0},{default:e(()=>[n(V,{header:_.headers,meta:_.meta,module:"student.transfer_request",onRefresh:l[4]||(l[4]=t=>p(f).emit("listItems"))},{actionButton:e(()=>[n(M,{onClick:l[3]||(l[3]=t=>p(g).push({name:"StudentTransferRequestCreate"}))},{default:e(()=>[s(a(u.$trans("global.add",{attribute:u.$trans("student.transfer.transfer")})),1)]),_:1})]),default:e(()=>[(b(!0),U(q,null,z(_.data,t=>(b(),v(L,{key:t.uuid,onDoubleClick:S=>p(g).push({name:"StudentTransferRequestShow",params:{uuid:t.uuid}})},{default:e(()=>[n(c,{name:"codeNumber"},{default:e(()=>[s(a(t.codeNumber),1)]),_:2},1024),n(c,{name:"name"},{default:e(()=>[s(a(t.student.name)+" ",1),n(D,{block:""},{default:e(()=>[s(a(t.student.contactNumber),1)]),_:2},1024)]),_:2},1024),n(c,{name:"parent"},{default:e(()=>[s(a(t.student.fatherName)+" ",1),n(D,{block:""},{default:e(()=>[s(a(t.student.motherName),1)]),_:2},1024)]),_:2},1024),n(c,{name:"admissionDate"},{default:e(()=>[s(a(t.student.joiningDate.formatted)+" ",1),n(D,{block:""},{default:e(()=>[s(a(t.student.codeNumber),1)]),_:2},1024)]),_:2},1024),n(c,{name:"course"},{default:e(()=>[s(a(t.student.courseName)+" ",1),n(D,{block:""},{default:e(()=>[s(a(t.student.batchName),1)]),_:2},1024)]),_:2},1024),n(c,{name:"requestDate"},{default:e(()=>[s(a(t.requestDate.formatted),1)]),_:2},1024),n(c,{name:"status"},{default:e(()=>[n(T,{design:t.status.color},{default:e(()=>[s(a(t.status.label),1)]),_:2},1032,["design"])]),_:2},1024),n(c,{name:"createdAt"},{default:e(()=>[s(a(t.createdAt.formatted),1)]),_:2},1024),n(c,{name:"action"},{default:e(()=>[n(I,null,{default:e(()=>[n(C,{icon:"fas fa-arrow-circle-right",onClick:S=>p(g).push({name:"StudentTransferRequestShow",params:{uuid:t.uuid}})},{default:e(()=>[s(a(u.$trans("general.show")),1)]),_:2},1032,["onClick"]),t.isEditable?(b(),v(C,{key:0,icon:"fas fa-edit",onClick:S=>p(g).push({name:"StudentTransferRequestEdit",params:{uuid:t.uuid}})},{default:e(()=>[s(a(u.$trans("general.edit")),1)]),_:2},1032,["onClick"])):y("",!0),t.isEditable?(b(),v(C,{key:1,icon:"fas fa-trash",onClick:S=>p(f).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[s(a(u.$trans("general.delete")),1)]),_:2},1032,["onClick"])):y("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Y as default};
