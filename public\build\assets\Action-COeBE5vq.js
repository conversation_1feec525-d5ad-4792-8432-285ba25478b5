import{u as g,H as h,I as H,l as u,r as n,q as j,o as b,w as v,d,e as i,f as l,K as q,a as A,F as O}from"./app-DvIo72ZO.js";const D={class:"grid grid-cols-3 gap-6"},I={class:"col-span-3 sm:col-span-1"},M={class:"col-span-3 sm:col-span-1"},S={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3"},L={class:"grid grid-cols-1"},N={class:"col"},w={name:"TransportVehicleTravelRecordForm"},y=Object.assign(w,{setup(f){const c=g(),o={vehicle:"",log:"",date:"",remarks:"",media:[],mediaUpdated:!1,mediaToken:h(),mediaHash:[]},m="transport/vehicle/travelRecord/",s=H(m),p=u({vehicles:[]}),r=u({...o}),_=u({vehicle:"",isLoaded:!c.params.uuid}),V=a=>{Object.assign(p,a)},T=()=>{r.mediaToken=h(),r.mediaHash=[]},k=a=>{var e;Object.assign(o,{...a,date:a.date.value,vehicle:(e=a.vehicle)==null?void 0:e.uuid}),Object.assign(r,q(o)),_.vehicle=a.vehicle.name,_.isLoaded=!0};return(a,e)=>{const $=n("BaseSelect"),U=n("BaseInput"),R=n("DatePicker"),B=n("BaseTextarea"),F=n("MediaUpload"),P=n("FormAction");return b(),j(P,{"pre-requisites":!0,onSetPreRequisites:V,"init-url":m,"init-form":o,form:r,"set-form":k,redirect:"TransportVehicleTravelRecord",onResetMediaFiles:T},{default:v(()=>[d("div",D,[d("div",I,[i($,{modelValue:r.vehicle,"onUpdate:modelValue":e[0]||(e[0]=t=>r.vehicle=t),name:"vehicle",label:a.$trans("transport.vehicle.vehicle"),"label-prop":"nameWithRegistrationNumber","value-prop":"uuid",options:p.vehicles,error:l(s).vehicle,"onUpdate:error":e[1]||(e[1]=t=>l(s).vehicle=t)},null,8,["modelValue","label","options","error"])]),d("div",M,[i(U,{type:"number",modelValue:r.log,"onUpdate:modelValue":e[2]||(e[2]=t=>r.log=t),"trailing-text":a.$trans("list.unit.km"),name:"log",label:a.$trans("transport.vehicle.travel_record.props.log"),error:l(s).log,"onUpdate:error":e[3]||(e[3]=t=>l(s).log=t)},null,8,["modelValue","trailing-text","label","error"])]),d("div",S,[i(R,{modelValue:r.date,"onUpdate:modelValue":e[4]||(e[4]=t=>r.date=t),name:"date",label:a.$trans("transport.vehicle.travel_record.props.date"),"no-clear":"",error:l(s).date,"onUpdate:error":e[5]||(e[5]=t=>l(s).date=t)},null,8,["modelValue","label","error"])]),d("div",E,[i(B,{modelValue:r.remarks,"onUpdate:modelValue":e[6]||(e[6]=t=>r.remarks=t),name:"remarks",label:a.$trans("transport.vehicle.travel_record.props.remarks"),error:l(s).remarks,"onUpdate:error":e[7]||(e[7]=t=>l(s).remarks=t)},null,8,["modelValue","label","error"])])]),d("div",L,[d("div",N,[i(F,{multiple:"",label:a.$trans("general.file"),module:"vehicle_travel_record",media:r.media,"media-token":r.mediaToken,onIsUpdated:e[8]||(e[8]=t=>r.mediaUpdated=!0),onSetHash:e[9]||(e[9]=t=>r.mediaHash.push(t))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),C={name:"TransportVehicleTravelRecordAction"},W=Object.assign(C,{setup(f){const c=g();return(o,m)=>{const s=n("PageHeaderAction"),p=n("PageHeader"),r=n("ParentTransition");return b(),A(O,null,[i(p,{title:o.$trans(l(c).meta.trans,{attribute:o.$trans(l(c).meta.label)}),navs:[{label:o.$trans("transport.transport"),path:"Transport"},{label:o.$trans("transport.vehicle.vehicle"),path:"TransportVehicle"},{label:o.$trans("transport.vehicle.travel_record.travel_record"),path:"TransportVehicleTravelRecordList"}]},{default:v(()=>[i(s,{name:"TransportVehicleTravelRecord",title:o.$trans("transport.vehicle.travel_record.travel_record"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),i(r,{appear:"",visibility:!0},{default:v(()=>[i(y)]),_:1})],64)}}});export{W as default};
