import{u as S,i as K,I as J,m as P,l as L,n as R,L as G,r as _,q as O,b,o as i,w as o,d as n,e as l,s as c,t as e,f as w,a as d,F as V,v as H,K as W,x as I,A as Q,aj as z,b0 as X,h as Y,j as Z,p as q}from"./app-DvIo72ZO.js";const ee={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},te={class:"mt-4"},ae={class:"mt-4 grid grid-cols-1 gap-6"},se={key:0,class:"mb-4"},oe=["innerHTML"],ne={class:"dark:text-gray-400 flex justify-between"},ie={class:"flex items-center"},re={class:"mr-2 text-lg text-gray-600 dark:text-gray-300"},le=["innerHTML"],de={class:"flex gap-2"},ue={class:"font-semibold"},ce={class:"dark:text-gray-300 font-semibold text-sm italic"},me={key:0},_e={key:0,class:"text-success far fa-check-circle fa-lg"},be={key:1,class:"text-danger far fa-times-circle fa-lg"},pe={key:1,class:"whitespace-pre-wrap break-words text-sm font-normal"},fe={key:2,class:"ml-1"},ve={key:3,class:"ml-1 text-success"},xe={class:"grid grid-cols-4 gap-6"},ge={class:"col-span-4 sm:col-span-1"},he={class:"col-span-4 sm:col-span-3"},ye={name:"ExamOnlineExamEvaluationForm"},ke=Object.assign(ye,{props:{visibility:{type:Boolean,default:!1},submission:{type:Object,default:()=>({})}},emits:["close","refresh"],setup(h,{emit:F}){const M=S(),T=K(),y=F,E=h,f={questions:[]},v="exam/onlineExam/submission/",m=J(v),p=P(!1),s=L({}),B=L({...f}),U=async()=>{f.questions=[],p.value=!0,await T.dispatch(v+"getQuestions",{uuid:M.params.uuid,moduleUuid:E.submission.uuid}).then(a=>{Object.assign(s,a),a.questions.forEach(x=>{f.questions.push({...x,obtainedMark:x.obtainedMark||0,comment:x.comment||""})}),Object.assign(B,W(f)),p.value=!1}).catch(a=>{p.value=!1})},j=()=>{y("close")},r=()=>{y("refresh"),j()};return R(()=>{}),G(E.submission,a=>{console.log("submission",a.uuid),U()},{deep:!0,immediate:!0}),(a,x)=>{const g=_("TextMuted"),$=_("BaseDataView"),A=_("BaseHeading"),C=_("BaseInput"),D=_("FormAction"),u=_("BaseModal");return s.uuid?(i(),O(u,{key:0,show:h.visibility,onClose:j},{title:o(()=>[c(e(s.studentName)+" ",1),l(g,null,{default:o(()=>[c(e(s.admissionNumber)+" - "+e(s.courseName+" "+s.batchName),1)]),_:1})]),default:o(()=>[n("dl",ee,[l($,{label:a.$trans("exam.online_exam.submission.props.started_at")},{default:o(()=>[c(e(s.startedAt.formatted),1)]),_:1},8,["label"]),l($,{label:a.$trans("exam.online_exam.submission.props.submitted_at")},{default:o(()=>[c(e(s.submittedAt.formatted),1)]),_:1},8,["label"]),l($,{label:a.$trans("exam.online_exam.submission.props.obtained_mark")},{default:o(()=>[c(e(s.obtainedMark)+" / "+e(s.maxMark),1)]),_:1},8,["label"])]),n("div",te,[l(A,null,{default:o(()=>[c(e(a.$trans("exam.online_exam.question.questions")),1)]),_:1}),l(D,{"no-card":"","no-data-fetch":"",action:"evaluate",uuid:w(M).params.uuid,"module-uuid":s.uuid,"keep-adding":!1,"init-url":v,"init-form":f,form:B,"after-submit":r},{default:o(()=>[n("div",ae,[(i(!0),d(V,null,H(B.questions,(t,k)=>(i(),d("div",{class:"col-span-3 sm:col-span-1",key:t.uuid},[t.header?(i(),d("div",se,[n("div",{class:"text-lg font-semibold dark:text-gray-300",innerHTML:t.header},null,8,oe)])):b("",!0),n("div",ne,[n("div",ie,[n("span",re,e(k+1)+".",1),n("div",{innerHTML:t.title},null,8,le)]),n("div",de,[n("span",ue,"("+e(t.mark)+")",1)])]),n("div",ce,[t.type.value=="mcq"?(i(),d("span",me,[t.obtainedMark>0?(i(),d("i",_e)):(i(),d("i",be))])):b("",!0),t.type.value==="multi_line_question"||t.type.value==="single_line_question"?(i(),d("pre",pe,e(t.answer),1)):(i(),d("span",fe,e(t.answer),1)),t.type.value=="mcq"&&t.obtainedMark<=0?(i(),d("span",ve,e(t.correctAnswer),1)):b("",!0)]),n("div",xe,[n("div",ge,[l(C,{type:"number",modelValue:t.obtainedMark,"onUpdate:modelValue":N=>t.obtainedMark=N,name:`questions.${k}.obtainedMark`,placeholder:a.$trans("exam.online_exam.obtained_mark"),error:w(m)[`questions.${k}.obtainedMark`],"onUpdate:error":N=>w(m)[`questions.${k}.obtainedMark`]=N},null,8,["modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"])]),n("div",he,[l(C,{type:"text",modelValue:t.comment,"onUpdate:modelValue":N=>t.comment=N,name:`questions.${k}.comment`,placeholder:a.$trans("exam.online_exam.comment"),error:w(m)[`questions.${k}.comment`],"onUpdate:error":N=>w(m)[`questions.${k}.comment`]=N},null,8,["modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"])])])]))),128))])]),_:1},8,["uuid","module-uuid","form"])])]),_:1},8,["show"])):b("",!0)}}}),$e={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},we={class:"mt-4"},Me={class:"border-b border-gray-200 dark:border-gray-700"},Ee={class:"-mb-px flex space-x-8"},Be={key:0,class:"ml-1 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"},Ce={class:"mt-6"},Ne={class:"grid grid-cols-1 gap-6"},Oe={key:0,class:"mb-4"},Te=["innerHTML"],je={class:"dark:text-gray-400 flex justify-between"},Ae={class:"flex items-center"},De={class:"mr-2 text-lg text-gray-600 dark:text-gray-300"},Pe=["innerHTML"],Ue={class:"flex gap-2"},Ve={class:"font-semibold"},Le={class:"dark:text-gray-300 font-semibold text-sm italic"},Fe={key:0},Ie={key:0,class:"text-success far fa-check-circle fa-lg"},Se={key:1,class:"text-danger far fa-times-circle fa-lg"},Re={key:1,class:"whitespace-pre-wrap break-words text-sm font-normal"},He={key:2,class:"ml-1"},Qe={key:3,class:"ml-1 text-success"},ze={key:0},Ke={name:"ExamOnlineExamSubmissionDetail"},Ge=Object.assign(Ke,{props:{visibility:{type:Boolean,default:!1},submission:{type:Object,default:()=>{}}},emits:["close","refresh"],setup(h,{emit:F}){const M=S(),T=K(),y=F,E=h,f="exam/onlineExam/submission/",v=P(!1),m=P("answers"),p=P(null),s=L({}),B=async()=>{v.value=!0,await T.dispatch(f+"getQuestions",{uuid:M.params.uuid,moduleUuid:E.submission.uuid}).then(r=>{var x;const a={...r};a.exam&&a.exam.hasOwnProperty("enableProctoring")&&(a.exam.enableProctoring=!!Number(a.exam.enableProctoring)),Object.assign(s,a),(x=s.exam)!=null&&x.enableProctoring&&U(),v.value=!1}).catch(r=>{v.value=!1})},U=async()=>{try{const r=await T.dispatch("api/get",{url:`online-exams/${s.exam.uuid}/proctoring/submissions/${s.uuid}/summary`});p.value=r.summary}catch(r){console.error("Failed to load proctoring summary:",r)}},j=()=>{y("close")};return R(()=>{}),G(E.submission,r=>{B()},{deep:!0,immediate:!0}),(r,a)=>{const x=_("TextMuted"),g=_("BaseDataView"),$=_("BaseModal");return s.uuid?(i(),O($,{key:0,show:h.visibility,onClose:j},{title:o(()=>[c(e(s.studentName)+" ",1),l(x,null,{default:o(()=>[c(e(s.admissionNumber)+" - "+e(s.courseName+" "+s.batchName),1)]),_:1})]),default:o(()=>{var A,C,D;return[n("dl",$e,[l(g,{label:r.$trans("exam.online_exam.submission.props.started_at")},{default:o(()=>[c(e(s.startedAt.formatted),1)]),_:1},8,["label"]),l(g,{label:r.$trans("exam.online_exam.submission.props.submitted_at")},{default:o(()=>[c(e(s.submittedAt.formatted),1)]),_:1},8,["label"]),l(g,{label:r.$trans("exam.online_exam.submission.props.obtained_mark")},{default:o(()=>[c(e(s.obtainedMark)+" / "+e(s.maxMark),1)]),_:1},8,["label"])]),n("div",we,[n("div",Me,[n("nav",Ee,[n("button",{onClick:a[0]||(a[0]=u=>m.value="answers"),class:I(["py-2 px-1 border-b-2 font-medium text-sm",m.value==="answers"?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"])},e(r.$trans("exam.online_exam.question.questions")||"Questions & Answers"),3),(A=s.exam)!=null&&A.enableProctoring?(i(),d("button",{key:0,onClick:a[1]||(a[1]=u=>m.value="proctoring"),class:I(["py-2 px-1 border-b-2 font-medium text-sm",m.value==="proctoring"?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"])},[c(e(r.$trans("exam.proctoring.review.tab_title")||"Proctoring Review")+" ",1),((C=p.value)==null?void 0:C.critical_events)>0?(i(),d("span",Be,e(p.value.critical_events),1)):b("",!0)],2)):b("",!0)])])]),n("div",Ce,[Q(n("div",null,[n("div",Ne,[(i(!0),d(V,null,H(s.questions,(u,t)=>(i(),d("div",{class:"col-span-3 sm:col-span-1",key:u.uuid},[u.header?(i(),d("div",Oe,[n("div",{class:"text-lg font-semibold dark:text-gray-300",innerHTML:u.header},null,8,Te)])):b("",!0),n("div",je,[n("div",Ae,[n("span",De,e(t+1)+".",1),n("div",{innerHTML:u.title},null,8,Pe)]),n("div",Ue,[n("span",{class:I(["font-semibold",{"text-success":u.obtainedMark>0,"text-danger":u.obtainedMark<0}])},e(u.obtainedMark)+" / ",3),n("span",Ve,e(u.mark),1)])]),n("div",Le,[u.type.value=="mcq"?(i(),d("span",Fe,[u.obtainedMark>0?(i(),d("i",Ie)):(i(),d("i",Se))])):b("",!0),u.type.value==="multi_line_question"||u.type.value==="single_line_question"?(i(),d("pre",Re,e(u.answer),1)):(i(),d("span",He,e(u.answer),1)),u.type.value=="mcq"&&u.obtainedMark<=0?(i(),d("span",Qe,e(u.correctAnswer),1)):b("",!0)])]))),128))])],512),[[z,m.value==="answers"]]),(D=s.exam)!=null&&D.enableProctoring?Q((i(),d("div",ze,[l(X,{"exam-uuid":s.exam.uuid,"submission-uuid":s.uuid},null,8,["exam-uuid","submission-uuid"])],512)),[[z,m.value==="proctoring"]]):b("",!0)])]}),_:1},8,["show"])):b("",!0)}}}),Je={name:"OnlineExamSubmissionList"},Xe=Object.assign(Je,{props:{onlineExam:{type:Object,default(){return{}}}},emits:["refresh"],setup(h,{emit:F}){const M=S(),T=Y(),y=Z("emitter"),E="exam/onlineExam/submission/",f=P(!1),v=P(!1),m=L({}),p=L({}),s=r=>{Object.assign(m,r)},B=r=>{Object.assign(p,r),v.value=!0},U=async r=>{Object.assign(p,r),f.value=!0},j=r=>{T.push({name:"ExamOnlineExamSubmissionProctorReview",params:{uuid:M.params.uuid,submissionUuid:r.uuid}})};return R(async()=>{y.on("actionPerformed",()=>{refreshOnlineExam()})}),q(()=>{y.all.delete("actionPerformed")}),(r,a)=>{const x=_("TextMuted"),g=_("DataCell"),$=_("FloatingMenuItem"),A=_("FloatingMenu"),C=_("DataRow"),D=_("DataTable"),u=_("ListItem");return i(),d(V,null,[l(u,{"init-url":E,uuid:w(M).params.uuid,onSetItems:s},{default:o(()=>[l(D,{header:m.headers,meta:m.meta,module:"exam.online_exam.submission",onRefresh:a[0]||(a[0]=t=>w(y).emit("listItems"))},{actionButton:o(()=>a[5]||(a[5]=[])),default:o(()=>[(i(!0),d(V,null,H(m.data,t=>(i(),O(C,{key:t.uuid},{default:o(()=>[l(g,{name:"studentName"},{default:o(()=>[c(e(t.studentName)+" ",1),l(x,{block:""},{default:o(()=>[c(e(t.admissionNumber),1)]),_:2},1024)]),_:2},1024),l(g,{name:"courseName"},{default:o(()=>[c(e(t.courseName+" "+t.batchName),1)]),_:2},1024),l(g,{name:"submittedAt"},{default:o(()=>[c(e(t.submittedAt.formatted)+" ",1),l(x,{block:""},{default:o(()=>[c("Started At: "+e(t.startedAt.formatted),1)]),_:2},1024)]),_:2},1024),l(g,{name:"evaluatedAt"},{default:o(()=>[c(e(t.evaluatedAt.formatted||"-"),1)]),_:2},1024),l(g,{name:"obtainedMark"},{default:o(()=>[c(e(t.obtainedMark),1)]),_:2},1024),l(g,{name:"action"},{default:o(()=>[l(A,null,{default:o(()=>[l($,{icon:"fas fa-arrow-circle-right",onClick:k=>B(t)},{default:o(()=>[c(e(r.$trans("global.view",{attribute:r.$trans("exam.online_exam.submission.submission")})),1)]),_:2},1032,["onClick"]),h.onlineExam.enableProctoring?(i(),O($,{key:0,icon:"fas fa-shield-alt",onClick:k=>j(t)},{default:o(()=>[c(e(r.$trans("exam.proctoring.review.view")||"Proctoring Review"),1)]),_:2},1032,["onClick"])):b("",!0),h.onlineExam.resultPublishedAt.value?b("",!0):(i(),d(V,{key:1},[h.onlineExam.canEvaluate?(i(),O($,{key:0,icon:"far fa-check-circle",onClick:k=>U(t)},{default:o(()=>[c(e(r.$trans("exam.online_exam.evaluate")),1)]),_:2},1032,["onClick"])):b("",!0),l($,{icon:"fas fa-trash",onClick:k=>w(y).emit("deleteItem",{uuid:h.onlineExam.uuid,moduleUuid:t.uuid})},{default:o(()=>[c(e(r.$trans("general.delete")),1)]),_:2},1032,["onClick"])],64))]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","meta"])]),_:1},8,["uuid"]),v.value&&p.uuid?(i(),O(Ge,{key:0,visibility:v.value,submission:p,onClose:a[1]||(a[1]=t=>v.value=!1),onRefresh:a[2]||(a[2]=t=>w(y).emit("listItems"))},null,8,["visibility","submission"])):b("",!0),f.value&&h.onlineExam.canEvaluate&&p.uuid?(i(),O(ke,{key:1,visibility:f.value,submission:p,onClose:a[3]||(a[3]=t=>f.value=!1),onRefresh:a[4]||(a[4]=t=>w(y).emit("listItems"))},null,8,["visibility","submission"])):b("",!0)],64)}}});export{Xe as default};
