import{l as T,r,q as b,o as f,w as e,d as C,e as t,h as j,j as S,m as E,f as i,a as w,F as I,v as D,s as l,t as s,b as U,y as q}from"./app-DvIo72ZO.js";const O={class:"grid grid-cols-3 gap-6"},z={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},J={__name:"Filter",emits:["hide"],setup(V,{emit:m}){const v=m,$={name:"",alias:""},p=T({...$});return(k,u)=>{const c=r("BaseInput"),B=r("FilterForm");return f(),b(B,{"init-form":$,form:p,onHide:u[2]||(u[2]=o=>v("hide"))},{default:e(()=>[C("div",O,[C("div",z,[t(c,{type:"text",modelValue:p.name,"onUpdate:modelValue":u[0]||(u[0]=o=>p.name=o),name:"name",label:k.$trans("hostel.block.props.name")},null,8,["modelValue","label"])]),C("div",G,[t(c,{type:"text",modelValue:p.alias,"onUpdate:modelValue":u[1]||(u[1]=o=>p.alias=o),name:"alias",label:k.$trans("hostel.block.props.alias")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},K=["innerHTML"],Q={name:"HostelBlockList"},X=Object.assign(Q,{setup(V){const m=j(),v=S("emitter");let $=["create","filter"],p=["print","pdf","excel"];const k="hostel/block/",u=E(!1),c=T({}),B=o=>{Object.assign(c,o)};return(o,a)=>{const g=r("BaseButton"),M=r("PageHeaderAction"),A=r("PageHeader"),F=r("ParentTransition"),H=r("TextMuted"),_=r("DataCell"),h=r("FloatingMenuItem"),L=r("FloatingMenu"),P=r("DataRow"),R=r("DataTable"),N=r("ListItem");return f(),b(N,{"init-url":k,"additional-query":{details:!0},onSetItems:B},{header:e(()=>[t(A,{title:o.$trans("hostel.block.block"),navs:[{label:o.$trans("hostel.hostel"),path:"Hostel"}]},{default:e(()=>[t(M,{url:"hostel/blocks/",name:"HostelBlock",title:o.$trans("hostel.block.block"),actions:i($),"dropdown-actions":i(p),onToggleFilter:a[3]||(a[3]=n=>u.value=!u.value)},{default:e(()=>[i(q)("hostel-incharge:read")?(f(),b(g,{key:0,design:"white",onClick:a[0]||(a[0]=n=>i(m).push({name:"HostelBlockIncharge"}))},{default:e(()=>[l(s(o.$trans("employee.incharge.incharge")),1)]),_:1})):U("",!0),t(g,{design:"white",onClick:a[1]||(a[1]=n=>i(m).push({name:"HostelFloor"}))},{default:e(()=>[l(s(o.$trans("hostel.floor.floor")),1)]),_:1}),t(g,{design:"white",onClick:a[2]||(a[2]=n=>i(m).push({name:"HostelRoom"}))},{default:e(()=>[l(s(o.$trans("hostel.room.room")),1)]),_:1})]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(F,{appear:"",visibility:u.value},{default:e(()=>[t(J,{onRefresh:a[4]||(a[4]=n=>i(v).emit("listItems")),onHide:a[5]||(a[5]=n=>u.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(F,{appear:"",visibility:!0},{default:e(()=>[t(R,{header:c.headers,meta:c.meta,module:"hostel.block",onRefresh:a[7]||(a[7]=n=>i(v).emit("listItems"))},{actionButton:e(()=>[t(g,{onClick:a[6]||(a[6]=n=>i(m).push({name:"HostelBlockCreate"}))},{default:e(()=>[l(s(o.$trans("global.add",{attribute:o.$trans("hostel.block.block")})),1)]),_:1})]),default:e(()=>[(f(!0),w(I,null,D(c.data,n=>(f(),b(P,{key:n.uuid,onDoubleClick:d=>i(m).push({name:"HostelBlockShow",params:{uuid:n.uuid}})},{default:e(()=>[t(_,{name:"name"},{default:e(()=>[l(s(n.name)+" ",1),t(H,{block:""},{default:e(()=>[l(s(n.alias),1)]),_:2},1024)]),_:2},1024),t(_,{name:"address"},{default:e(()=>[C("span",{innerHTML:n.address},null,8,K)]),_:2},1024),t(_,{name:"contact"},{default:e(()=>[l(s(n.contactNumber)+" ",1),t(H,{block:""},{default:e(()=>[l(s(n.contactEmail),1)]),_:2},1024)]),_:2},1024),t(_,{name:"incharge"},{default:e(()=>[(f(!0),w(I,null,D(n.incharges,d=>{var y;return f(),w("div",null,[l(s(((y=d==null?void 0:d.employee)==null?void 0:y.name)||"-")+" ",1),t(H,null,{default:e(()=>[l(s(d==null?void 0:d.period),1)]),_:2},1024)])}),256))]),_:2},1024),t(_,{name:"createdAt"},{default:e(()=>[l(s(n.createdAt.formatted),1)]),_:2},1024),t(_,{name:"action"},{default:e(()=>[t(L,null,{default:e(()=>[t(h,{icon:"fas fa-arrow-circle-right",onClick:d=>i(m).push({name:"HostelBlockShow",params:{uuid:n.uuid}})},{default:e(()=>[l(s(o.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(h,{icon:"fas fa-edit",onClick:d=>i(m).push({name:"HostelBlockEdit",params:{uuid:n.uuid}})},{default:e(()=>[l(s(o.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t(h,{icon:"fas fa-copy",onClick:d=>i(m).push({name:"HostelBlockDuplicate",params:{uuid:n.uuid}})},{default:e(()=>[l(s(o.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t(h,{icon:"fas fa-trash",onClick:d=>i(v).emit("deleteItem",{uuid:n.uuid})},{default:e(()=>[l(s(o.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{X as default};
