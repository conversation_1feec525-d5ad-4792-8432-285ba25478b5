import{i as j,u as $,h as F,j as k,l as O,r as l,a as y,o as d,e as t,w as e,f as n,q as v,b,d as _,F as g,v as z,s as r,t as s}from"./app-DvIo72ZO.js";const E={class:"space-y-2"},U=["innerHTML"],G={class:"space-y-4"},J={key:1,class:"px-4 py-2"},K={class:"px-4 py-2"},Q={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},W={name:"InventoryStockPurchaseShow"},Z=Object.assign(W,{setup(X){j();const m=$(),P=F(),a=k("$trans");k("emitter");const S={},L="inventory/stockPurchase/",V=[{key:"name",label:a("inventory.stock_item.props.name"),visibility:!0},{key:"quantity",label:a("inventory.stock_purchase.props.quantity"),visibility:!0},{key:"unitPrice",label:a("inventory.stock_purchase.props.unit_price"),visibility:!0},{key:"amount",label:a("inventory.stock_purchase.props.amount"),visibility:!0}],o=O({...S}),w=f=>{Object.assign(o,f)};return(f,u)=>{const I=l("PageHeaderAction"),x=l("PageHeader"),c=l("ListItemView"),B=l("ListContainerVertical"),h=l("BaseCard"),T=l("TextMuted"),p=l("DataCell"),C=l("DataRow"),D=l("SimpleTable"),H=l("BaseAlert"),N=l("ListMedia"),A=l("BaseDataView"),M=l("DetailLayoutVertical"),q=l("ShowItem"),R=l("ParentTransition");return d(),y(g,null,[t(x,{title:n(a)(n(m).meta.trans,{attribute:n(a)(n(m).meta.label)}),navs:[{label:n(a)("inventory.inventory"),path:"Inventory"},{label:n(a)("inventory.stock_purchase.stock_purchase"),path:"InventoryStockPurchaseList"}]},{default:e(()=>[t(I,{name:"InventoryStockPurchase",title:n(a)("inventory.stock_purchase.stock_purchase"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(R,{appear:"",visibility:!0},{default:e(()=>[t(q,{"init-url":L,uuid:n(m).params.uuid,onSetItem:w,onRedirectTo:u[0]||(u[0]=i=>n(P).push({name:"InventoryStockPurchase"}))},{default:e(()=>[o.uuid?(d(),v(M,{key:0},{detail:e(()=>[_("div",E,[t(h,{"no-padding":"","no-content-padding":""},{title:e(()=>[r(s(n(a)("inventory.stock_purchase.props.code_number"))+" "+s(o.codeNumber),1)]),action:e(()=>u[1]||(u[1]=[])),default:e(()=>[t(B,null,{default:e(()=>[t(c,{label:n(a)("inventory.inventory")},{default:e(()=>{var i;return[r(s((i=o.inventory)==null?void 0:i.name),1)]}),_:1},8,["label"]),t(c,{label:n(a)("inventory.stock_purchase.props.voucher_number")},{default:e(()=>[r(s(o.voucherNumber),1)]),_:1},8,["label"]),t(c,{label:n(a)("inventory.vendor")},{default:e(()=>[r(s(o.vendor.name)+" ("+s(o.vendor.type.name)+") ",1)]),_:1},8,["label"]),t(c,{label:n(a)("inventory.place")},{default:e(()=>{var i;return[r(s(((i=o.place)==null?void 0:i.fullName)||"-"),1)]}),_:1},8,["label"]),t(c,{label:n(a)("inventory.stock_purchase.props.date")},{default:e(()=>[r(s(o.date.formatted),1)]),_:1},8,["label"]),t(c,{label:n(a)("inventory.stock_purchase.props.total")},{default:e(()=>[r(s(o.total.formatted),1)]),_:1},8,["label"]),t(c,{label:n(a)("inventory.stock_purchase.props.description")},{default:e(()=>[_("div",{innerHTML:o.description},null,8,U)]),_:1},8,["label"]),t(c,{label:n(a)("general.created_at")},{default:e(()=>[r(s(o.createdAt.formatted),1)]),_:1},8,["label"]),t(c,{label:n(a)("general.updated_at")},{default:e(()=>[r(s(o.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})])]),default:e(()=>[_("div",G,[t(h,{"no-padding":"","no-content-padding":""},{title:e(()=>[r(s(n(a)("inventory.stock_purchase.props.items")),1)]),footer:e(()=>u[2]||(u[2]=[])),default:e(()=>[o.items.length>0?(d(),v(D,{key:0,header:V},{default:e(()=>[(d(!0),y(g,null,z(o.items,i=>(d(),v(C,{key:i.uuid},{default:e(()=>[t(p,{name:"name"},{default:e(()=>[r(s(i.item.name)+" ",1),t(T,{block:""},{default:e(()=>[r(s(i.description),1)]),_:2},1024)]),_:2},1024),t(p,{name:"quantity"},{default:e(()=>[r(s(i.quantity),1)]),_:2},1024),t(p,{name:"unitPrice"},{default:e(()=>[r(s(i.unitPrice.formatted),1)]),_:2},1024),t(p,{name:"amount"},{default:e(()=>[r(s(i.amount.formatted),1)]),_:2},1024)]),_:2},1024))),128))]),_:1})):b("",!0),o.items.length===0?(d(),y("div",J,[t(H,{design:"info",size:"xs"},{default:e(()=>[r(s(n(a)("general.errors.record_not_found")),1)]),_:1})])):b("",!0),_("div",K,[_("dl",Q,[t(A,{class:"col-span-1 sm:col-span-2"},{default:e(()=>[t(N,{media:o.media,url:`/app/inventory/stock-purchases/${o.uuid}/`},null,8,["media","url"])]),_:1})])])]),_:1})])]),_:1})):b("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{Z as default};
