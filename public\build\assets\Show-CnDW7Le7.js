import{j as ie,i as pe,u as ce,h as me,g as I,I as be,l as G,m as ve,r as l,a as J,o as c,e as t,w as n,f as i,q,b as g,d as y,s as r,t as d,F as fe}from"./app-DvIo72ZO.js";const $e={key:0,class:"mb-4"},we={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},_e={class:"grid grid-cols-3 gap-6"},ge={class:"col-span-3 sm:col-span-1"},Ae={class:"col-span-3"},qe={name:"StudentEditRequestShow"},Ne=Object.assign(qe,{setup(ye){ie("emitter"),pe();const h=ce(),K=me(),Q={},S={status:"",comment:""},N="student/editRequest/",W=I("contact.uniqueIdNumber1Label"),X=I("contact.uniqueIdNumber2Label"),Y=I("contact.uniqueIdNumber3Label"),A=be(N),m=G({...S}),L=ve(!1),e=G({...Q}),Z=a=>{Object.assign(e,a)},x=()=>{L.value=!0};return(a,o)=>{const ee=l("PageHeaderAction"),ae=l("PageHeader"),te=l("BaseAlert"),se=l("BaseBadge"),p=l("BaseDataView"),s=l("HorizontalListItem"),B=l("HorizontalList"),R=l("BaseFieldset"),ne=l("ListMedia"),V=l("BaseCard"),le=l("BaseSelect"),re=l("BaseTextarea"),de=l("FormAction"),oe=l("ShowItem"),ue=l("ParentTransition");return c(),J(fe,null,[t(ae,{title:a.$trans(i(h).meta.trans,{attribute:a.$trans(i(h).meta.label)}),navs:[{label:a.$trans("student.student"),path:"Student"},{label:a.$trans("student.edit_request.edit_request"),path:"StudentEditRequestList"}]},{default:n(()=>[t(ee,{name:"StudentEditRequest",title:a.$trans("student.edit_request.edit_request"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(ue,{appear:"",visibility:!0},{default:n(()=>[t(oe,{"init-url":N,uuid:i(h).params.uuid,onSetItem:Z,onRedirectTo:o[4]||(o[4]=u=>i(K).push({name:"StudentEditRequest"})),refresh:L.value,onRefreshed:o[5]||(o[5]=u=>L.value=!1)},{default:n(()=>[e.uuid?(c(),q(V,{key:0},{title:n(()=>[r(d(e.student.name),1)]),default:n(()=>[e.isRejected&&e.comment?(c(),J("div",$e,[t(te,{design:"error",size:"xs"},{default:n(()=>[r(d(e.comment),1)]),_:1})])):g("",!0),y("dl",we,[t(p,{label:a.$trans("student.admission.props.code_number")},{default:n(()=>[r(d(e.student.codeNumber)+" ",1),t(se,{design:e.status.color},{default:n(()=>[r(d(e.status.label),1)]),_:1},8,["design"])]),_:1},8,["label"]),t(p,{label:a.$trans("contact.props.birth_date")},{default:n(()=>[r(d(e.student.birthDate.formatted),1)]),_:1},8,["label"]),t(p,{label:a.$trans("contact.props.father_name")},{default:n(()=>[r(d(e.student.fatherName),1)]),_:1},8,["label"]),t(p,{label:a.$trans("contact.props.mother_name")},{default:n(()=>[r(d(e.student.motherName),1)]),_:1},8,["label"]),t(p,{label:a.$trans("academic.course.course")},{default:n(()=>[r(d(e.student.courseName+" "+e.student.batchName),1)]),_:1},8,["label"]),t(p,{label:a.$trans("student.edit_request.request_by")},{default:n(()=>[r(d(e.user.profile.name),1)]),_:1},8,["label"]),t(p,{class:"col-span-1 sm:col-span-2"},{default:n(()=>{var u,z,E,k,C,P,F,H,j,T,U,D,M;return[t(B,null,{default:n(()=>[t(s,{label:a.$trans("contact.props.contact_number"),value:e.data.new.contactNumber},null,8,["label","value"]),t(s,{label:a.$trans("contact.props.alternate_contact_number"),value:e.data.new.alternateContactNumber},null,8,["label","value"]),t(s,{label:a.$trans("contact.props.email"),value:e.data.new.email},null,8,["label","value"]),t(s,{label:a.$trans("contact.props.father_contact_number"),value:e.data.new.fatherContactNumber},null,8,["label","value"]),t(s,{label:a.$trans("contact.props.father_email"),value:e.data.new.fatherEmail},null,8,["label","value"]),t(s,{label:a.$trans("contact.props.mother_contact_number"),value:e.data.new.motherContactNumber},null,8,["label","value"]),t(s,{label:a.$trans("contact.props.mother_email"),value:e.data.new.motherEmail},null,8,["label","value"]),t(s,{label:i(W),value:e.data.new.uniqueIdNumber1},null,8,["label","value"]),t(s,{label:i(X),value:e.data.new.uniqueIdNumber2},null,8,["label","value"]),t(s,{label:i(Y),value:e.data.new.uniqueIdNumber3},null,8,["label","value"]),t(s,{label:a.$trans("contact.props.birth_place"),value:e.data.new.birthPlace},null,8,["label","value"]),t(s,{label:a.$trans("contact.props.nationality"),value:e.data.new.nationality},null,8,["label","value"]),t(s,{label:a.$trans("contact.props.mother_tongue"),value:e.data.new.motherTongue},null,8,["label","value"]),t(s,{label:a.$trans("contact.props.blood_group"),value:a.$trans("list.blood_groups."+e.data.new.bloodGroup)},null,8,["label","value"]),t(s,{label:a.$trans("contact.religion.religion"),value:e.data.new.religion},null,8,["label","value"]),t(s,{label:a.$trans("contact.category.category"),value:e.data.new.category},null,8,["label","value"]),t(s,{label:a.$trans("contact.caste.caste"),value:e.data.new.caste},null,8,["label","value"])]),_:1}),(u=e.data.new.presentAddress)!=null&&u.addressLine1||(z=e.data.new.presentAddress)!=null&&z.addressLine2||(E=e.data.new.presentAddress)!=null&&E.city||(k=e.data.new.presentAddress)!=null&&k.state||(C=e.data.new.presentAddress)!=null&&C.zipcode||(P=e.data.new.presentAddress)!=null&&P.country?(c(),q(R,{key:0,class:"mt-4"},{legend:n(()=>[r(d(a.$trans("contact.props.present_address")),1)]),default:n(()=>[t(B,null,{default:n(()=>{var b,v,f,$,w,_;return[t(s,{label:a.$trans("contact.props.address.address_line1"),value:(b=e.data.new.presentAddress)==null?void 0:b.addressLine1},null,8,["label","value"]),t(s,{label:a.$trans("contact.props.address.address_line2"),value:(v=e.data.new.presentAddress)==null?void 0:v.addressLine2},null,8,["label","value"]),t(s,{label:a.$trans("contact.props.address.city"),value:(f=e.data.new.presentAddress)==null?void 0:f.city},null,8,["label","value"]),t(s,{label:a.$trans("contact.props.address.state"),value:($=e.data.new.presentAddress)==null?void 0:$.state},null,8,["label","value"]),t(s,{label:a.$trans("contact.props.address.zipcode"),value:(w=e.data.new.presentAddress)==null?void 0:w.zipcode},null,8,["label","value"]),t(s,{label:a.$trans("contact.props.address.country"),value:(_=e.data.new.presentAddress)==null?void 0:_.country},null,8,["label","value"])]}),_:1})]),_:1})):g("",!0),(F=e.data.new.permanentAddress)!=null&&F.sameAsPresentAddress||(H=e.data.new.permanentAddress)!=null&&H.addressLine1||(j=e.data.new.permanentAddress)!=null&&j.addressLine2||(T=e.data.new.permanentAddress)!=null&&T.city||(U=e.data.new.permanentAddress)!=null&&U.state||(D=e.data.new.permanentAddress)!=null&&D.zipcode||(M=e.data.new.permanentAddress)!=null&&M.country?(c(),q(R,{key:1,class:"mt-4"},{legend:n(()=>[r(d(a.$trans("contact.props.permanent_address")),1)]),default:n(()=>[t(B,null,{default:n(()=>{var b,v,f,$,w,_,O;return[t(s,{label:a.$trans("contact.props.same_as_present_address"),value:(b=e.data.new.permanentAddress)!=null&&b.sameAsPresentAddress?a.$trans("general.yes"):a.$trans("general.no")},null,8,["label","value"]),t(s,{label:a.$trans("contact.props.address.address_line1"),value:(v=e.data.new.permanentAddress)==null?void 0:v.addressLine1},null,8,["label","value"]),t(s,{label:a.$trans("contact.props.address.address_line2"),value:(f=e.data.new.permanentAddress)==null?void 0:f.addressLine2},null,8,["label","value"]),t(s,{label:a.$trans("contact.props.address.city"),value:($=e.data.new.permanentAddress)==null?void 0:$.city},null,8,["label","value"]),t(s,{label:a.$trans("contact.props.address.state"),value:(w=e.data.new.permanentAddress)==null?void 0:w.state},null,8,["label","value"]),t(s,{label:a.$trans("contact.props.address.zipcode"),value:(_=e.data.new.permanentAddress)==null?void 0:_.zipcode},null,8,["label","value"]),t(s,{label:a.$trans("contact.props.address.country"),value:(O=e.data.new.permanentAddress)==null?void 0:O.country},null,8,["label","value"])]}),_:1})]),_:1})):g("",!0)]}),_:1}),t(p,{class:"col-span-1 sm:col-span-2"},{default:n(()=>[t(ne,{media:e.media,url:`/app/student/edit-requests/${e.uuid}/`},null,8,["media","url"])]),_:1}),t(p,{label:a.$trans("general.created_at")},{default:n(()=>[r(d(e.createdAt.formatted),1)]),_:1},8,["label"]),t(p,{label:a.$trans("general.updated_at")},{default:n(()=>[r(d(e.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):g("",!0),e.uuid&&e.status.value=="pending"?(c(),q(V,{key:1},{title:n(()=>[r(d(a.$trans("student.edit_request.props.action")),1)]),default:n(()=>[t(de,{"no-card":"","keep-adding":!1,"init-url":N,uuid:e.uuid,"no-data-fetch":!0,action:"action","init-form":S,form:m,"after-submit":x},{default:n(()=>[y("div",_e,[y("div",ge,[t(le,{name:"status",label:a.$trans("global.select",{attribute:a.$trans("reception.complaint.props.status")}),modelValue:m.status,"onUpdate:modelValue":o[0]||(o[0]=u=>m.status=u),options:[{value:"approve",label:a.$trans("student.edit_request.statuses.approve")},{value:"reject",label:a.$trans("student.edit_request.statuses.reject")}],error:i(A).status,"onUpdate:error":o[1]||(o[1]=u=>i(A).status=u)},null,8,["label","modelValue","options","error"])]),y("div",Ae,[t(re,{modelValue:m.comment,"onUpdate:modelValue":o[2]||(o[2]=u=>m.comment=u),name:"comment",label:a.$trans("student.edit_request.props.comment"),error:i(A).comment,"onUpdate:error":o[3]||(o[3]=u=>i(A).comment=u)},null,8,["modelValue","label","error"])])])]),_:1},8,["uuid","form"])]),_:1})):g("",!0)]),_:1},8,["uuid","refresh"])]),_:1})],64)}}});export{Ne as default};
