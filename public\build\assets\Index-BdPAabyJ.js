import{u as G,j as M,l as q,I as E,n as H,r as p,q as k,o as d,w as n,d as m,e,b as O,s as r,t as s,i as W,y as z,m as D,a as C,f as R,F as T,v as j}from"./app-DvIo72ZO.js";const J={class:"grid grid-cols-3 gap-6"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3 sm:col-span-1"},ee={__name:"Filter",props:{initUrl:{type:String,default:""},preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(v,{emit:y}){const b=G();M("moment");const w=y,F=v,_={codeNumber:"",name:"",batches:[],feeConcession:"",feeConcessionType:"",feeGroup:""},o=q({..._});E(F.initUrl);const u=q({isLoaded:!b.query.batches});return H(async()=>{u.batches=b.query.batches?b.query.batches.split(","):[],u.isLoaded=!0}),(c,a)=>{const $=p("BaseInput"),g=p("BaseSelectSearch"),i=p("BaseSelect"),h=p("FilterForm");return d(),k(h,{"init-form":_,multiple:["batches"],form:o,onHide:a[6]||(a[6]=t=>w("hide"))},{default:n(()=>[m("div",J,[m("div",K,[e($,{type:"text",modelValue:o.codeNumber,"onUpdate:modelValue":a[0]||(a[0]=t=>o.codeNumber=t),name:"codeNumber",label:c.$trans("student.admission.props.code_number")},null,8,["modelValue","label"])]),m("div",Q,[e($,{type:"text",modelValue:o.name,"onUpdate:modelValue":a[1]||(a[1]=t=>o.name=t),name:"name",label:c.$trans("contact.props.name")},null,8,["modelValue","label"])]),m("div",X,[u.isLoaded?(d(),k(g,{key:0,multiple:"",name:"batches",label:c.$trans("global.select",{attribute:c.$trans("academic.batch.batch")}),modelValue:o.batches,"onUpdate:modelValue":a[2]||(a[2]=t=>o.batches=t),"value-prop":"uuid","init-search":u.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:n(t=>[r(s(t.value.course.name)+" "+s(t.value.name),1)]),listOption:n(t=>[r(s(t.option.course.nameWithTerm)+" "+s(t.option.name),1)]),_:1},8,["label","modelValue","init-search"])):O("",!0)]),m("div",Y,[e(i,{name:"feeGroup",label:c.$trans("global.select",{attribute:c.$trans("finance.fee_group.fee_group")}),modelValue:o.feeGroup,"onUpdate:modelValue":a[3]||(a[3]=t=>o.feeGroup=t),options:v.preRequisites.feeGroups,"value-prop":"uuid","label-prop":"name"},null,8,["label","modelValue","options"])]),m("div",Z,[e(i,{name:"feeConcession",label:c.$trans("global.select",{attribute:c.$trans("finance.fee_concession.fee_concession")}),modelValue:o.feeConcession,"onUpdate:modelValue":a[4]||(a[4]=t=>o.feeConcession=t),options:v.preRequisites.feeConcessions,"value-prop":"uuid","label-prop":"name"},null,8,["label","modelValue","options"])]),m("div",x,[e(i,{name:"feeConcessionType",label:c.$trans("global.select",{attribute:c.$trans("finance.fee_concession.type.type")}),modelValue:o.feeConcessionType,"onUpdate:modelValue":a[5]||(a[5]=t=>o.feeConcessionType=t),options:v.preRequisites.feeConcessionTypes,"value-prop":"uuid","label-prop":"name"},null,8,["label","modelValue","options"])])])]),_:1},8,["form"])}}},ae={key:0,class:"flex justify-between mr-4"},ne={name:"FinanceReportFeeConcession"},oe=Object.assign(ne,{setup(v){const y=G(),b=W();let w=["filter"],F=[];z("finance:export")&&(F=["print","pdf","excel"]);const _="finance/report/",o=D(!1),u=D(!1),c=q({feeGroups:[],feeConcessions:[],feeConcessionTypes:[]}),a=q({headers:[],data:[],meta:{total:0}}),$=async()=>{u.value=!0,await b.dispatch(_+"preRequisite",{name:"fee-concession",params:y.query}).then(i=>{u.value=!1,Object.assign(c,i)}).catch(i=>{u.value=!1})},g=async()=>{u.value=!0,await b.dispatch(_+"fetchReport",{name:"fee-concession",params:y.query}).then(i=>{u.value=!1,Object.assign(a,i)}).catch(i=>{u.value=!1})};return H(async()=>{await $(),await g()}),(i,h)=>{const t=p("PageHeaderAction"),A=p("PageHeader"),B=p("ParentTransition"),f=p("DataCell"),V=p("TextMuted"),L=p("DataRow"),P=p("DataTable"),I=p("BaseCard");return d(),C(T,null,[e(A,{title:i.$trans(R(y).meta.label),navs:[{label:i.$trans("finance.finance"),path:"Finance"},{label:i.$trans("finance.report.report"),path:"FinanceReport"}]},{default:n(()=>[e(t,{url:"finance/reports/fee-concession/",name:"FinanceReportFeeConcession",title:i.$trans("finance.report.fee_concession.fee_concession"),actions:R(w),"dropdown-actions":R(F),headers:a.headers,onToggleFilter:h[0]||(h[0]=l=>o.value=!o.value)},null,8,["title","actions","dropdown-actions","headers"])]),_:1},8,["title","navs"]),e(B,{appear:"",visibility:o.value},{default:n(()=>[e(ee,{onAfterFilter:g,"init-url":_,"pre-requisites":c,onHide:h[1]||(h[1]=l=>o.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"]),e(B,{appear:"",visibility:!0},{default:n(()=>[e(I,{"no-padding":"","no-content-padding":"","is-loading":u.value},{default:n(()=>[e(P,{header:a.headers,footer:a.footers,meta:a.meta,module:"finance.report.fee_concession",onRefresh:g},{default:n(()=>[(d(!0),C(T,null,j(a.data,l=>(d(),k(L,{key:l.uuid},{default:n(()=>[e(f,{name:"codeNumber"},{default:n(()=>[r(s(l.codeNumber),1)]),_:2},1024),e(f,{name:"name"},{default:n(()=>[r(s(l.name)+" ",1),e(V,{block:""},{default:n(()=>[r(s(l.rollNumber),1)]),_:2},1024)]),_:2},1024),e(f,{name:"fatherName"},{default:n(()=>[r(s(l.fatherName)+" ",1),e(V,{block:""},{default:n(()=>[r(s(l.contactNumber),1)]),_:2},1024)]),_:2},1024),e(f,{name:"course"},{default:n(()=>[r(s(l.courseName)+" ",1),e(V,{block:""},{default:n(()=>[r(s(l.batchName),1)]),_:2},1024)]),_:2},1024),e(f,{name:"installment"},{default:n(()=>[r(s(l.installmentTitle)+" ",1),e(V,{block:""},{default:n(()=>[r(s(l.feeGroupName),1)]),_:2},1024)]),_:2},1024),e(f,{name:"concession"},{default:n(()=>[r(s(l.concessionName)+" ",1),e(V,{block:""},{default:n(()=>[r(s(l.concessionType),1)]),_:2},1024)]),_:2},1024),e(f,{name:"detail",table:""},{default:n(()=>[(d(!0),C(T,null,j(l.records,N=>{var S,U;return d(),C(T,null,[N.concession.value>0?(d(),C("div",ae,[r(s(((S=N.head)==null?void 0:S.name)||((U=N.defaultFeeHead)==null?void 0:U.label)||"-")+" ",1),m("span",null,s(N.concession.formatted),1)])):O("",!0)],64)}),256))]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","footer","meta"])]),_:1},8,["is-loading"])]),_:1})],64)}}});export{oe as default};
