import{l as D,r as i,q as _,o as m,w as e,d as F,e as a,h as j,j as L,y as f,m as x,f as r,a as E,F as O,v as U,s,t as n,b}from"./app-DvIo72ZO.js";const q={class:"grid grid-cols-3 gap-6"},z={class:"col-span-3 sm:col-span-1"},G={__name:"Filter",emits:["hide"],setup(S,{emit:d}){const C=d,g={name:""},v=D({...g});return(h,u)=>{const $=i("BaseInput"),T=i("FilterForm");return m(),_(T,{"init-form":g,form:v,onHide:u[1]||(u[1]=o=>C("hide"))},{default:e(()=>[F("div",q,[F("div",z,[a($,{type:"text",modelValue:v.name,"onUpdate:modelValue":u[0]||(u[0]=o=>v.name=o),name:"name",label:h.$trans("transport.route.props.name")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},J={class:"text-xs"},K={name:"TransportRouteList"},W=Object.assign(K,{setup(S){const d=j(),C=L("emitter");let g=["filter"];f("transport:config")&&g.unshift("config"),f("transport-route:create")&&g.unshift("create");let v=[];f("transport-route:export")&&(v=["print","pdf","excel"]);const h="transport/route/",u=x(!1),$=D({}),T=o=>{Object.assign($,o)};return(o,l)=>{const w=i("BaseButton"),A=i("PageHeaderAction"),P=i("PageHeader"),B=i("ParentTransition"),y=i("TextMuted"),p=i("DataCell"),k=i("FloatingMenuItem"),V=i("FloatingMenu"),H=i("DataRow"),M=i("DataTable"),N=i("ListItem");return m(),_(N,{"init-url":h,onSetItems:T},{header:e(()=>[a(P,{title:o.$trans("transport.route.route"),navs:[{label:o.$trans("transport.transport"),path:"Transport"}]},{default:e(()=>[a(A,{url:"transport/routes/",name:"TransportRoute",title:o.$trans("transport.route.route"),actions:r(g),"dropdown-actions":r(v),"config-path":"TransportConfig",onToggleFilter:l[1]||(l[1]=t=>u.value=!u.value)},{default:e(()=>[a(w,{design:"white",onClick:l[0]||(l[0]=t=>r(d).push({name:"TransportStoppage"}))},{default:e(()=>[s(n(o.$trans("transport.stoppage.stoppage")),1)]),_:1})]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[a(B,{appear:"",visibility:u.value},{default:e(()=>[a(G,{onRefresh:l[2]||(l[2]=t=>r(C).emit("listItems")),onHide:l[3]||(l[3]=t=>u.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[a(B,{appear:"",visibility:!0},{default:e(()=>[a(M,{header:$.headers,meta:$.meta,module:"transport.route",onRefresh:l[5]||(l[5]=t=>r(C).emit("listItems"))},{actionButton:e(()=>[r(f)("transport-route:create")?(m(),_(w,{key:0,onClick:l[4]||(l[4]=t=>r(d).push({name:"TransportRouteCreate"}))},{default:e(()=>[s(n(o.$trans("global.add",{attribute:o.$trans("transport.route.route")})),1)]),_:1})):b("",!0)]),default:e(()=>[(m(!0),E(O,null,U($.data,t=>(m(),_(H,{key:t.uuid,onDoubleClick:c=>r(d).push({name:"TransportRouteShow",params:{uuid:t.uuid}})},{default:e(()=>[a(p,{name:"name"},{default:e(()=>[s(n(t.name)+" ",1),a(y,{block:""},{default:e(()=>[s(n(t.direction.label),1)]),_:2},1024)]),_:2},1024),a(p,{name:"vehicle"},{default:e(()=>{var c;return[s(n((c=t.vehicle)==null?void 0:c.name)+" ",1),a(y,{block:""},{default:e(()=>{var I;return[s(n((I=t.vehicle)==null?void 0:I.registrationNumber),1)]}),_:2},1024)]}),_:2},1024),a(p,{name:"time"},{default:e(()=>[s(n(t.arrivalStartsAt.formatted)+" ",1),a(y,{block:""},{default:e(()=>[s(n(t.departureStartsAt.formatted),1)]),_:2},1024)]),_:2},1024),a(p,{name:"maxCapacity"},{default:e(()=>[F("span",J,n(t.routePassengersCount),1),s("/"+n(t.maxCapacity),1)]),_:2},1024),a(p,{name:"routeStoppagesCount"},{default:e(()=>[s(n(t.routeStoppagesCount),1)]),_:2},1024),a(p,{name:"createdAt"},{default:e(()=>[s(n(t.createdAt.formatted),1)]),_:2},1024),a(p,{name:"action"},{default:e(()=>[a(V,null,{default:e(()=>[a(k,{icon:"fas fa-arrow-circle-right",onClick:c=>r(d).push({name:"TransportRouteShow",params:{uuid:t.uuid}})},{default:e(()=>[s(n(o.$trans("general.show")),1)]),_:2},1032,["onClick"]),r(f)("transport-route:edit")?(m(),_(k,{key:0,icon:"fas fa-edit",onClick:c=>r(d).push({name:"TransportRouteEdit",params:{uuid:t.uuid}})},{default:e(()=>[s(n(o.$trans("general.edit")),1)]),_:2},1032,["onClick"])):b("",!0),r(f)("transport-route:create")?(m(),_(k,{key:1,icon:"fas fa-copy",onClick:c=>r(d).push({name:"TransportRouteDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[s(n(o.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):b("",!0),r(f)("transport-route:delete")?(m(),_(k,{key:2,icon:"fas fa-trash",onClick:c=>r(C).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[s(n(o.$trans("general.delete")),1)]),_:2},1032,["onClick"])):b("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{W as default};
