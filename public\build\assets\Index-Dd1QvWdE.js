import{l as B,r as l,q as g,o as v,w as t,d as C,e,u as A,h as U,j as L,y as F,m as M,f as s,a as V,F as w,v as N,s as i,t as o,b as S}from"./app-DvIo72ZO.js";const O={class:"grid grid-cols-3 gap-6"},q={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},G={__name:"Filter",emits:["hide"],setup(d,{emit:y}){const p=y,$={course:"",institute:"",affiliatedTo:""},r=B({...$});return(_,u)=>{const c=l("BaseInput"),D=l("FilterForm");return v(),g(D,{"init-form":$,form:r,onHide:u[3]||(u[3]=n=>p("hide"))},{default:t(()=>[C("div",O,[C("div",q,[e(c,{type:"text",modelValue:r.course,"onUpdate:modelValue":u[0]||(u[0]=n=>r.course=n),name:"course",label:_.$trans("student.qualification.props.course")},null,8,["modelValue","label"])]),C("div",E,[e(c,{type:"text",modelValue:r.institute,"onUpdate:modelValue":u[1]||(u[1]=n=>r.institute=n),name:"institute",label:_.$trans("student.qualification.props.institute")},null,8,["modelValue","label"])]),C("div",z,[e(c,{type:"text",modelValue:r.affiliatedTo,"onUpdate:modelValue":u[2]||(u[2]=n=>r.affiliatedTo=n),name:"affiliatedTo",label:_.$trans("student.qualification.props.affiliated_to")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},J={name:"StudentQualificationList"},W=Object.assign(J,{props:{student:{type:Object,default(){return{}}}},setup(d){const y=A(),p=U(),$=L("emitter");let r=["filter"];F("student:edit")&&r.unshift("create");const _="student/qualification/",u=M(!1),c=B({}),D=n=>{Object.assign(c,n)};return(n,m)=>{const I=l("PageHeaderAction"),T=l("PageHeader"),h=l("ParentTransition"),f=l("DataCell"),b=l("FloatingMenuItem"),Q=l("FloatingMenu"),H=l("DataRow"),P=l("BaseButton"),R=l("DataTable"),j=l("ListItem");return v(),g(j,{"init-url":_,uuid:s(y).params.uuid,onSetItems:D},{header:t(()=>[d.student.uuid?(v(),g(T,{key:0,title:n.$trans("student.qualification.qualification"),navs:[{label:n.$trans("student.student"),path:"Student"},{label:d.student.contact.name,path:{name:"StudentShow",params:{uuid:d.student.uuid}}}]},{default:t(()=>[e(I,{url:`students/${d.student.uuid}/qualifications/`,name:"StudentQualification",title:n.$trans("student.qualification.qualification"),actions:s(r),"dropdown-actions":["print","pdf","excel"],onToggleFilter:m[0]||(m[0]=a=>u.value=!u.value)},null,8,["url","title","actions"])]),_:1},8,["title","navs"])):S("",!0)]),filter:t(()=>[e(h,{appear:"",visibility:u.value},{default:t(()=>[e(G,{onRefresh:m[1]||(m[1]=a=>s($).emit("listItems")),onHide:m[2]||(m[2]=a=>u.value=!1)})]),_:1},8,["visibility"])]),default:t(()=>[e(h,{appear:"",visibility:!0},{default:t(()=>[e(R,{header:c.headers,meta:c.meta,module:"student.qualification",onRefresh:m[4]||(m[4]=a=>s($).emit("listItems"))},{actionButton:t(()=>[s(F)("student:edit")?(v(),g(P,{key:0,onClick:m[3]||(m[3]=a=>s(p).push({name:"StudentQualificationCreate"}))},{default:t(()=>[i(o(n.$trans("global.add",{attribute:n.$trans("student.qualification.qualification")})),1)]),_:1})):S("",!0)]),default:t(()=>[(v(!0),V(w,null,N(c.data,a=>(v(),g(H,{key:a.uuid,onDoubleClick:k=>s(p).push({name:"StudentQualificationShow",params:{uuid:d.student.uuid,muuid:a.uuid}})},{default:t(()=>[e(f,{name:"course"},{default:t(()=>[i(o(a.course),1)]),_:2},1024),e(f,{name:"institute"},{default:t(()=>[i(o(a.institute),1)]),_:2},1024),e(f,{name:"level"},{default:t(()=>[i(o(a.level.name),1)]),_:2},1024),e(f,{name:"startDate"},{default:t(()=>[i(o(a.startDate.formatted),1)]),_:2},1024),e(f,{name:"endDate"},{default:t(()=>[i(o(a.endDate.formatted),1)]),_:2},1024),e(f,{name:"result"},{default:t(()=>[i(o(a.result),1)]),_:2},1024),e(f,{name:"createdAt"},{default:t(()=>[i(o(a.createdAt.formatted),1)]),_:2},1024),e(f,{name:"action"},{default:t(()=>[e(Q,null,{default:t(()=>[e(b,{icon:"fas fa-arrow-circle-right",onClick:k=>s(p).push({name:"StudentQualificationShow",params:{uuid:d.student.uuid,muuid:a.uuid}})},{default:t(()=>[i(o(n.$trans("general.show")),1)]),_:2},1032,["onClick"]),s(F)("student:edit")?(v(),V(w,{key:0},[e(b,{icon:"fas fa-edit",onClick:k=>s(p).push({name:"StudentQualificationEdit",params:{uuid:d.student.uuid,muuid:a.uuid}})},{default:t(()=>[i(o(n.$trans("general.edit")),1)]),_:2},1032,["onClick"]),e(b,{icon:"fas fa-copy",onClick:k=>s(p).push({name:"StudentQualificationDuplicate",params:{uuid:d.student.uuid,muuid:a.uuid}})},{default:t(()=>[i(o(n.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),e(b,{icon:"fas fa-trash",onClick:k=>s($).emit("deleteItem",{uuid:d.student.uuid,moduleUuid:a.uuid})},{default:t(()=>[i(o(n.$trans("general.delete")),1)]),_:2},1032,["onClick"])],64)):S("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1},8,["uuid"])}}});export{W as default};
