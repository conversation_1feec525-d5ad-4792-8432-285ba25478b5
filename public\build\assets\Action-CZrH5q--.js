import{u as U,l as h,I as N,r as s,q as L,o as c,w as m,d as p,a as g,b as A,f as r,s as B,t as d,e as i,F as k,v as S,K as E}from"./app-DvIo72ZO.js";const R={class:"grid grid-cols-2 gap-6"},w={class:"col-span-2 sm:col-span-1"},H={class:"col-span-2 sm:col-span-1"},C={class:"col-span-2"},I={class:"col-span-4 sm:col-span-1"},K={key:0},z={class:"col-span-4 sm:col-span-1"},G={name:"EmployeeLeaveAllocationForm"},J=Object.assign(G,{setup(F){const v=U(),l={employee:"",startDate:"",endDate:"",records:[],description:""},D="employee/leave/allocation/",_=h({leaveTypes:[]}),n=N(D),t=h({...l}),b=h({employee:"",isLoaded:!v.params.uuid}),O=o=>{Object.assign(_,o),_.leaveTypes.forEach(a=>{l.records.push({leaveType:a,allotted:0})}),Object.assign(t,E(l))},P=o=>{var a,f;Object.assign(l,{employee:(a=o.employee)==null?void 0:a.uuid,startDate:o.startDate.value,endDate:o.endDate.value,description:o.description}),l.records.forEach(u=>{let y=o.records.find(T=>T.leaveType.uuid==u.leaveType.uuid);y!==void 0&&(u.leaveType=y.leaveType,u.allotted=y.allotted)}),Object.assign(t,E(l)),b.employee=(f=o.employee)==null?void 0:f.uuid,b.isLoaded=!0};return(o,a)=>{const f=s("BaseSelectSearch"),u=s("DatePicker"),y=s("BaseTextarea"),T=s("BaseLabel"),j=s("BaseInput"),q=s("FormAction");return c(),L(q,{"pre-requisites":!0,onSetPreRequisites:O,"init-url":D,"init-form":l,form:t,"set-form":P,redirect:"EmployeeLeaveAllocation"},{default:m(()=>[p("div",R,[p("div",w,[b.isLoaded?(c(),L(f,{key:0,name:"employee",label:o.$trans("global.select",{attribute:o.$trans("employee.employee")}),modelValue:t.employee,"onUpdate:modelValue":a[0]||(a[0]=e=>t.employee=e),error:r(n).employee,"onUpdate:error":a[1]||(a[1]=e=>r(n).employee=e),"value-prop":"uuid","init-search":b.employee,"additional-search-query":{self:0},"search-key":"name","search-action":"employee/list"},{selectedOption:m(e=>[B(d(e.value.name)+" ("+d(e.value.codeNumber)+") ",1)]),listOption:m(e=>[B(d(e.option.name)+" ("+d(e.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","error","init-search"])):A("",!0)]),p("div",H,[i(u,{start:t.startDate,"onUpdate:start":a[2]||(a[2]=e=>t.startDate=e),end:t.endDate,"onUpdate:end":a[3]||(a[3]=e=>t.endDate=e),name:"dateBetween",as:"range",label:o.$trans("general.date_between")},null,8,["start","end","label"])]),p("div",C,[i(y,{modelValue:t.description,"onUpdate:modelValue":a[4]||(a[4]=e=>t.description=e),name:"description",label:o.$trans("employee.leave.allocation.props.description"),error:r(n).description,"onUpdate:error":a[5]||(a[5]=e=>r(n).description=e)},null,8,["modelValue","label","error"])])]),(c(!0),g(k,null,S(t.records,(e,$)=>(c(),g("div",{class:"mt-4 grid grid-cols-4 gap-3",key:e.leaveType.uuid},[p("div",I,[i(T,{class:"mt-4"},{default:m(()=>[B(d(e.leaveType.name)+" ",1),e.leaveType.alias?(c(),g("span",K,"("+d(e.leaveType.alias)+")",1)):A("",!0)]),_:2},1024)]),p("div",z,[i(j,{type:"number",name:`records.${$}.allotted`,modelValue:e.allotted,"onUpdate:modelValue":V=>e.allotted=V,placeholder:o.$trans("employee.leave.allocation.props.allotted"),error:r(n)[`records.${$}.allotted`],"onUpdate:error":V=>r(n)[`records.${$}.allotted`]=V},null,8,["name","modelValue","onUpdate:modelValue","placeholder","error","onUpdate:error"])])]))),128))]),_:1},8,["form"])}}}),M={name:"EmployeeLeaveAllocationAction"},W=Object.assign(M,{setup(F){const v=U();return(l,D)=>{const _=s("PageHeaderAction"),n=s("PageHeader"),t=s("ParentTransition");return c(),g(k,null,[i(n,{title:l.$trans(r(v).meta.trans,{attribute:l.$trans(r(v).meta.label)}),navs:[{label:l.$trans("employee.leave.leave"),path:"EmployeeLeave"},{label:l.$trans("employee.leave.allocation.allocation"),path:"EmployeeLeaveAllocationList"}]},{default:m(()=>[i(_,{name:"EmployeeLeaveAllocation",title:l.$trans("employee.leave.allocation.allocation"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),i(t,{appear:"",visibility:!0},{default:m(()=>[i(J)]),_:1})],64)}}});export{W as default};
