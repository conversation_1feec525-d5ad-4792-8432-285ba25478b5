import{u as P,l as A,n as E,r as u,q as $,o as n,w as e,d as w,e as t,b as C,i as G,I as J,m as U,a as y,f as m,s as l,t as o,L as Z,h as ee,j as te,y as I,z as se,F as H,v as N,A as z}from"./app-DvIo72ZO.js";import{d as K}from"./vuedraggable.umd-DSTqH_PI.js";const ae={class:"grid grid-cols-3 gap-6"},oe={class:"col-span-3 sm:col-span-1"},ne={class:"col-span-3 sm:col-span-1"},ie={class:"col-span-3 sm:col-span-1"},le={class:"col-span-3 sm:col-span-1"},re={__name:"Filter",emits:["hide"],setup(M,{emit:L}){const f=P(),v=L,g={name:"",code:"",shortcode:"",divisions:[]},_=A({...g}),h=A({divisions:[],isLoaded:!f.query.divisions});return E(async()=>{h.divisions=f.query.divisions?f.query.divisions.split(","):[],h.isLoaded=!0}),(r,d)=>{const k=u("BaseInput"),B=u("BaseSelectSearch"),p=u("FilterForm");return n(),$(p,{"init-form":g,form:_,multiple:["divisions"],onHide:d[4]||(d[4]=i=>v("hide"))},{default:e(()=>[w("div",ae,[w("div",oe,[t(k,{type:"text",modelValue:_.name,"onUpdate:modelValue":d[0]||(d[0]=i=>_.name=i),name:"name",label:r.$trans("academic.course.props.name")},null,8,["modelValue","label"])]),w("div",ne,[t(k,{type:"text",modelValue:_.code,"onUpdate:modelValue":d[1]||(d[1]=i=>_.code=i),name:"code",label:r.$trans("academic.course.props.code")},null,8,["modelValue","label"])]),w("div",ie,[t(k,{type:"text",modelValue:_.shortcode,"onUpdate:modelValue":d[2]||(d[2]=i=>_.shortcode=i),name:"shortcode",label:r.$trans("academic.course.props.shortcode")},null,8,["modelValue","label"])]),w("div",le,[h.isLoaded?(n(),$(B,{key:0,multiple:"",name:"divisions",label:r.$trans("global.select",{attribute:r.$trans("academic.division.division")}),modelValue:_.divisions,"onUpdate:modelValue":d[3]||(d[3]=i=>_.divisions=i),"value-prop":"uuid","init-search":h.divisions,"search-action":"academic/division/list"},null,8,["label","modelValue","init-search"])):C("",!0)])])]),_:1},8,["form"])}}},de={key:0},ce={class:"flex border rounded-xl px-4 py-2"},ue={key:1},me={key:2,class:"mt-4 flex justify-end"},fe={name:"AcademicCourseReorder"},pe=Object.assign(fe,{props:{visibility:{type:Boolean,default:!1}},emits:["close","refresh"],setup(M,{emit:L}){P();const f=G(),v=L,g={courses:[]};J("academic/course/");const h=U(!1),r=A({courses:[]});A({...g});const d=async()=>{h.value=!0,await f.dispatch("academic/course/list",{params:{all:!0}}).then(p=>{h.value=!1,r.courses=p}).catch(p=>{h.value=!1})},k=async()=>{h.value=!0,await f.dispatch("academic/course/reorder",{data:{courses:r.courses}}).then(p=>{h.value=!1,v("refresh"),v("close")}).catch(p=>{h.value=!1})},B=()=>{v("close")};return E(()=>{d()}),(p,i)=>{const R=u("BaseLabel"),c=u("BaseAlert"),a=u("BaseButton"),F=u("BaseModal");return n(),$(F,{show:M.visibility,onClose:B},{title:e(()=>[l(o(p.$trans("global.reorder",{attribute:p.$trans("academic.course.course")})),1)]),default:e(()=>[r.courses.length?(n(),y("div",de,[t(m(K),{class:"space-y-2",list:r.courses,"item-key":"uuid"},{item:e(({element:D,index:T})=>[w("div",ce,[i[0]||(i[0]=w("i",{class:"fas fa-arrows mr-2 cursor-pointer"},null,-1)),t(R,null,{default:e(()=>[l(o(D.name),1)]),_:2},1024)])]),_:1},8,["list"])])):(n(),y("div",ue,[t(c,{design:"info",size:"xs"},{default:e(()=>[l(o(p.$trans("general.errors.record_not_found")),1)]),_:1})])),r.courses.length?(n(),y("div",me,[t(a,{onClick:k},{default:e(()=>[l(o(p.$trans("general.reorder")),1)]),_:1})])):C("",!0)]),_:1},8,["show"])}}}),ve={key:0},_e={class:"flex border rounded-xl px-4 py-2"},be={key:1},he={key:2,class:"mt-4 flex justify-end"},$e={name:"AcademicCourseReorderBatch"},ye=Object.assign($e,{props:{selectedCourse:{type:Object,default:()=>{}},visibility:{type:Boolean,default:!1}},emits:["close","refresh"],setup(M,{emit:L}){P();const f=G(),v=L,g=M,_={batches:[]};J("academic/course/");const r=U(!1),d=A({batches:[]});A({..._});const k=async()=>{r.value=!0,await f.dispatch("academic/batch/list",{params:{courses:[g.selectedCourse.uuid],all:!0}}).then(i=>{r.value=!1,d.batches=i}).catch(i=>{r.value=!1})},B=async()=>{r.value=!0,await f.dispatch("academic/course/reorderBatch",{data:{course:g.selectedCourse.uuid,batches:d.batches}}).then(i=>{r.value=!1,v("refresh"),v("close")}).catch(i=>{r.value=!1})},p=()=>{v("close")};return Z(()=>g.selectedCourse,i=>{i&&k()},{deep:!0}),(i,R)=>{const c=u("BaseLabel"),a=u("BaseAlert"),F=u("BaseButton"),D=u("BaseModal");return n(),$(D,{show:M.visibility,onClose:p},{title:e(()=>[l(o(i.$trans("global.reorder",{attribute:i.$trans("academic.batch.batch")})),1)]),default:e(()=>[d.batches.length?(n(),y("div",ve,[t(m(K),{class:"space-y-2",list:d.batches,"item-key":"uuid"},{item:e(({element:T,index:q})=>[w("div",_e,[R[0]||(R[0]=w("i",{class:"fas fa-arrows mr-2 cursor-pointer"},null,-1)),t(c,null,{default:e(()=>[l(o(T.name),1)]),_:2},1024)])]),_:1},8,["list"])])):(n(),y("div",be,[t(a,{design:"info",size:"xs"},{default:e(()=>[l(o(i.$trans("general.errors.record_not_found")),1)]),_:1})])),d.batches.length?(n(),y("div",he,[t(F,{onClick:B},{default:e(()=>[l(o(i.$trans("general.reorder")),1)]),_:1})])):C("",!0)]),_:1},8,["show"])}}}),ge={key:0},ke={key:0,class:"far fa-check-circle fa-xl text-success"},Ce={key:1,class:"far fa-circle-xmark fa-xl text-danger"},Be={name:"AcademicCourseList"},Fe=Object.assign(Be,{emits:["refresh"],setup(M,{emit:L}){const f=ee(),v=te("emitter");let g=["filter"];I("course:create")&&g.unshift("create");let _=[];I("course:export")&&(_=["print","pdf","excel"]);const h="academic/course/",r=U(!1),d=U(!1),k=U(!1),B=A({}),p=A({selectedCourse:null}),i=c=>{Object.assign(B,c)},R=c=>{p.selectedCourse=c,k.value=!0};return(c,a)=>{const F=u("BaseButton"),D=u("PageHeaderAction"),T=u("PageHeader"),q=u("ParentTransition"),j=u("TextMuted"),V=u("DataCell"),S=u("FloatingMenuItem"),Q=u("FloatingMenu"),W=u("DataRow"),X=u("DataTable"),Y=u("ListItem"),O=se("tooltip");return n(),y(H,null,[t(Y,{"init-url":h,"additional-query":{details:!0},onSetItems:i},{header:e(()=>[t(T,{title:c.$trans("academic.course.course"),navs:[{label:c.$trans("academic.academic"),path:"Academic"}]},{default:e(()=>[t(D,{url:"academic/courses/",name:"AcademicCourse",title:c.$trans("academic.course.course"),actions:m(g),"dropdown-actions":m(_),"additional-dropdown-actions-query":{details:!0},onToggleFilter:a[2]||(a[2]=s=>r.value=!r.value)},{default:e(()=>[z((n(),$(F,{design:"white",onClick:a[0]||(a[0]=s=>d.value=!d.value)},{default:e(()=>a[11]||(a[11]=[w("i",{class:"fas fa-arrows-up-down-left-right"},null,-1)])),_:1})),[[O,c.$trans("global.reorder",{attribute:c.$trans("academic.course.course")})]]),m(I)("course-incharge:read")?(n(),$(F,{key:0,design:"white",onClick:a[1]||(a[1]=s=>m(f).push({name:"AcademicCourseIncharge"}))},{default:e(()=>[l(o(c.$trans("academic.course.incharge")),1)]),_:1})):C("",!0)]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(q,{appear:"",visibility:r.value},{default:e(()=>[t(re,{onRefresh:a[3]||(a[3]=s=>m(v).emit("listItems")),onHide:a[4]||(a[4]=s=>r.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(q,{appear:"",visibility:!0},{default:e(()=>[t(X,{header:B.headers,meta:B.meta,module:"academic.course",onRefresh:a[6]||(a[6]=s=>m(v).emit("listItems"))},{actionButton:e(()=>[m(I)("course:create")?(n(),$(F,{key:0,onClick:a[5]||(a[5]=s=>m(f).push({name:"AcademicCourseCreate"}))},{default:e(()=>[l(o(c.$trans("global.add",{attribute:c.$trans("academic.course.course")})),1)]),_:1})):C("",!0)]),default:e(()=>[(n(!0),y(H,null,N(B.data,s=>(n(),$(W,{key:s.uuid,onDoubleClick:b=>m(f).push({name:"AcademicCourseShow",params:{uuid:s.uuid}})},{default:e(()=>[t(V,{name:"name"},{default:e(()=>[l(o(s.name)+" ",1),s.term?(n(),y("span",ge,"("+o(s.term)+")",1)):C("",!0),s.enableRegistration?(n(),$(j,{key:1,block:""},{default:e(()=>[l(o(s.registrationFee.formatted),1)]),_:2},1024)):C("",!0),s.pgAccount?(n(),$(j,{key:2,block:""},{default:e(()=>[l(o(s.pgAccount),1)]),_:2},1024)):C("",!0)]),_:2},1024),t(V,{name:"division"},{default:e(()=>[l(o(s.division.name)+" ",1),t(j,{block:""},{default:e(()=>{var b;return[l(o((b=s.division.program)==null?void 0:b.name),1)]}),_:2},1024)]),_:2},1024),t(V,{name:"code"},{default:e(()=>[l(o(s.code)+" ",1),t(j,{block:""},{default:e(()=>[l(o(s.shortcode),1)]),_:2},1024)]),_:2},1024),t(V,{name:"incharge"},{default:e(()=>[(n(!0),y(H,null,N(s.incharges,b=>{var x;return n(),y("div",null,[l(o(((x=b==null?void 0:b.employee)==null?void 0:x.name)||"-")+" ",1),t(j,null,{default:e(()=>[l(o(b==null?void 0:b.period),1)]),_:2},1024)])}),256))]),_:2},1024),t(V,{name:"registration"},{default:e(()=>[s.enableRegistration?z((n(),y("i",ke,null,512)),[[O,c.$trans("academic.course.props.enable_registartion")]]):(n(),y("i",Ce))]),_:2},1024),t(V,{name:"createdAt"},{default:e(()=>[l(o(s.createdAt.formatted),1)]),_:2},1024),t(V,{name:"action"},{default:e(()=>[t(Q,null,{default:e(()=>[t(S,{icon:"fas fa-arrow-circle-right",onClick:b=>m(f).push({name:"AcademicCourseShow",params:{uuid:s.uuid}})},{default:e(()=>[l(o(c.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(S,{icon:"fas fa-arrows-alt",onClick:b=>R(s)},{default:e(()=>[l(o(c.$trans("general.reorder")),1)]),_:2},1032,["onClick"]),m(I)("course:edit")?(n(),$(S,{key:0,icon:"fas fa-edit",onClick:b=>m(f).push({name:"AcademicCourseEdit",params:{uuid:s.uuid}})},{default:e(()=>[l(o(c.$trans("general.edit")),1)]),_:2},1032,["onClick"])):C("",!0),m(I)("course:create")?(n(),$(S,{key:1,icon:"fas fa-copy",onClick:b=>m(f).push({name:"AcademicCourseDuplicate",params:{uuid:s.uuid}})},{default:e(()=>[l(o(c.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):C("",!0),m(I)("course:delete")?(n(),$(S,{key:2,icon:"fas fa-trash",onClick:b=>m(v).emit("deleteItem",{uuid:s.uuid})},{default:e(()=>[l(o(c.$trans("general.delete")),1)]),_:2},1032,["onClick"])):C("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1}),t(pe,{visibility:d.value,onClose:a[7]||(a[7]=s=>d.value=!1),onRefresh:a[8]||(a[8]=s=>m(v).emit("listItems"))},null,8,["visibility"]),t(ye,{"selected-course":p.selectedCourse,visibility:k.value,onClose:a[9]||(a[9]=s=>k.value=!1),onRefresh:a[10]||(a[10]=s=>m(v).emit("listItems"))},null,8,["selected-course","visibility"])],64)}}});export{Fe as default};
