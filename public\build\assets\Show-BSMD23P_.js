import{i as D,u as S,h as C,l as P,r as i,a as T,o as m,e as t,w as a,f as r,q as p,b as _,d as V,s as o,t as s,y as N,F as H}from"./app-DvIo72ZO.js";const R={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},j={name:"AcademicDivisionInchargeShow"},M=Object.assign(j,{setup(E){D();const d=S(),u=C(),g={},h="academic/divisionIncharge/",n=P({...g}),f=e=>{Object.assign(n,e)};return(e,c)=>{const v=i("PageHeaderAction"),b=i("PageHeader"),B=i("TextMuted"),l=i("BaseDataView"),$=i("BaseButton"),y=i("ShowButton"),A=i("BaseCard"),I=i("ShowItem"),k=i("ParentTransition");return m(),T(H,null,[t(b,{title:e.$trans(r(d).meta.trans,{attribute:e.$trans(r(d).meta.label)}),navs:[{label:e.$trans("academic.academic"),path:"Academic"},{label:e.$trans("academic.division.division"),path:"AcademicDivision"},{label:e.$trans("academic.division_incharge.division_incharge"),path:"AcademicDivisionInchargeList"}]},{default:a(()=>[t(v,{name:"AcademicDivisionIncharge",title:e.$trans("academic.division_incharge.division_incharge"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(k,{appear:"",visibility:!0},{default:a(()=>[t(I,{"init-url":h,uuid:r(d).params.uuid,onSetItem:f,onRedirectTo:c[1]||(c[1]=w=>r(u).push({name:"AcademicDivisionIncharge"}))},{default:a(()=>[n.uuid?(m(),p(A,{key:0},{title:a(()=>[o(s(n.division.name),1)]),footer:a(()=>[t(y,null,{default:a(()=>[r(N)("division-incharge:edit")?(m(),p($,{key:0,design:"primary",onClick:c[0]||(c[0]=w=>r(u).push({name:"AcademicDivisionInchargeEdit",params:{uuid:n.uuid}}))},{default:a(()=>[o(s(e.$trans("general.edit")),1)]),_:1})):_("",!0)]),_:1})]),default:a(()=>[V("dl",R,[t(l,{label:e.$trans("employee.employee")},{default:a(()=>[o(s(n.employee.name)+" ",1),t(B,{block:""},{default:a(()=>[o(s(n.employee.codeNumber),1)]),_:1})]),_:1},8,["label"]),t(l,{label:e.$trans("employee.incharge.props.period")},{default:a(()=>[o(s(n.period),1)]),_:1},8,["label"]),t(l,{class:"col-span-1 sm:col-span-2",label:e.$trans("employee.incharge.props.remarks")},{default:a(()=>[o(s(n.remarks),1)]),_:1},8,["label"]),t(l,{label:e.$trans("general.created_at")},{default:a(()=>[o(s(n.createdAt.formatted),1)]),_:1},8,["label"]),t(l,{label:e.$trans("general.updated_at")},{default:a(()=>[o(s(n.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):_("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{M as default};
