import{u as A,l as w,n as P,r as s,q as _,o as p,w as e,d as I,b as v,s as u,t as l,e as a,h as R,j,y as g,m as q,f as n,a as O,F as U,v as E}from"./app-DvIo72ZO.js";const z={class:"grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={__name:"Filter",emits:["hide"],setup(H,{emit:h}){const k=A(),$=h,b={employees:[],startDate:"",endDate:""},d=w({...b}),m=w({employees:[],isLoaded:!k.query.employees});return P(async()=>{m.employees=k.query.employees?k.query.employees.split(","):[],m.isLoaded=!0}),(f,c)=>{const r=s("BaseSelectSearch"),i=s("DatePicker"),C=s("FilterForm");return p(),_(C,{"init-form":b,form:d,multiple:["employees"],onHide:c[3]||(c[3]=o=>$("hide"))},{default:e(()=>[I("div",z,[I("div",G,[m.isLoaded?(p(),_(r,{key:0,multiple:"",name:"employees",label:f.$trans("global.select",{attribute:f.$trans("employee.employee")}),modelValue:d.employees,"onUpdate:modelValue":c[0]||(c[0]=o=>d.employees=o),"value-prop":"uuid","init-search":m.employees,"search-key":"name","search-action":"employee/list"},{selectedOption:e(o=>[u(l(o.value.name)+" ("+l(o.value.codeNumber)+") ",1)]),listOption:e(o=>[u(l(o.option.name)+" ("+l(o.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):v("",!0)]),I("div",J,[a(i,{start:d.startDate,"onUpdate:start":c[1]||(c[1]=o=>d.startDate=o),end:d.endDate,"onUpdate:end":c[2]||(c[2]=o=>d.endDate=o),name:"dateBetween",as:"range",label:f.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},Q={name:"HostelBlockInchargeList"},X=Object.assign(Q,{setup(H){const h=R(),k=j("emitter");let $=["filter"];g("hostel-incharge:create")&&$.unshift("create");let b=[];g("hostel-incharge:export")&&(b=["print","pdf","excel"]);const d="hostel/blockIncharge/",m=q(!1),f=w({}),c=r=>{Object.assign(f,r)};return(r,i)=>{const C=s("PageHeaderAction"),o=s("PageHeader"),F=s("ParentTransition"),y=s("DataCell"),S=s("TextMuted"),B=s("FloatingMenuItem"),T=s("FloatingMenu"),L=s("DataRow"),M=s("BaseButton"),N=s("DataTable"),V=s("ListItem");return p(),_(V,{"init-url":d,onSetItems:c},{header:e(()=>[a(o,{title:r.$trans("hostel.block_incharge.block_incharge"),navs:[{label:r.$trans("hostel.hostel"),path:"Hostel"}]},{default:e(()=>[a(C,{url:"hostel/block-incharges/",name:"HostelBlockIncharge",title:r.$trans("hostel.block_incharge.block_incharge"),actions:n($),"dropdown-actions":n(b),onToggleFilter:i[0]||(i[0]=t=>m.value=!m.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[a(F,{appear:"",visibility:m.value},{default:e(()=>[a(K,{onRefresh:i[1]||(i[1]=t=>n(k).emit("listItems")),onHide:i[2]||(i[2]=t=>m.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[a(F,{appear:"",visibility:!0},{default:e(()=>[a(N,{header:f.headers,meta:f.meta,module:"hostel.block_incharge",onRefresh:i[4]||(i[4]=t=>n(k).emit("listItems"))},{actionButton:e(()=>[n(g)("hostel-incharge:create")?(p(),_(M,{key:0,onClick:i[3]||(i[3]=t=>n(h).push({name:"HostelBlockInchargeCreate"}))},{default:e(()=>[u(l(r.$trans("global.add",{attribute:r.$trans("hostel.block_incharge.block_incharge")})),1)]),_:1})):v("",!0)]),default:e(()=>[(p(!0),O(U,null,E(f.data,t=>(p(),_(L,{key:t.uuid,onDoubleClick:D=>n(h).push({name:"HostelBlockInchargeShow",params:{uuid:t.uuid}})},{default:e(()=>[a(y,{name:"block"},{default:e(()=>[u(l(t.block.name),1)]),_:2},1024),a(y,{name:"employee"},{default:e(()=>[u(l(t.employee.name)+" ",1),a(S,{block:""},{default:e(()=>[u(l(t.employee.codeNumber),1)]),_:2},1024)]),_:2},1024),a(y,{name:"period"},{default:e(()=>[u(l(t.period),1)]),_:2},1024),a(y,{name:"createdAt"},{default:e(()=>[u(l(t.createdAt.formatted),1)]),_:2},1024),a(y,{name:"action"},{default:e(()=>[a(T,null,{default:e(()=>[a(B,{icon:"fas fa-arrow-circle-right",onClick:D=>n(h).push({name:"HostelBlockInchargeShow",params:{uuid:t.uuid}})},{default:e(()=>[u(l(r.$trans("general.show")),1)]),_:2},1032,["onClick"]),n(g)("hostel-incharge:edit")?(p(),_(B,{key:0,icon:"fas fa-edit",onClick:D=>n(h).push({name:"HostelBlockInchargeEdit",params:{uuid:t.uuid}})},{default:e(()=>[u(l(r.$trans("general.edit")),1)]),_:2},1032,["onClick"])):v("",!0),n(g)("hostel-incharge:create")?(p(),_(B,{key:1,icon:"fas fa-copy",onClick:D=>n(h).push({name:"HostelBlockInchargeDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[u(l(r.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):v("",!0),n(g)("hostel-incharge:delete")?(p(),_(B,{key:2,icon:"fas fa-trash",onClick:D=>n(k).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[u(l(r.$trans("general.delete")),1)]),_:2},1032,["onClick"])):v("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{X as default};
