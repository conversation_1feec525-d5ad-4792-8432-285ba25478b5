import{h as A,I as $,l as g,r as d,q as R,o as _,w as u,d as c,e as i,f as s,K as U,u as B,a as F,F as j}from"./app-DvIo72ZO.js";const k={class:"grid grid-cols-3 gap-6"},q={class:"col-span-3 sm:col-span-1"},w={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3 sm:col-span-1"},S={name:"AcademicPeriodForm"},E=Object.assign(S,{setup(D){A();const l={session:"",name:"",code:"",shortcode:"",alias:"",startDate:"",endDate:"",enableRegistration:!0,description:""},n="academic/period/",r=$(n),m=g({sessions:[]}),t=g({...l}),p=o=>{Object.assign(m,o)},f=o=>{var e;Object.assign(l,{...o,startDate:o.startDate.value,endDate:o.endDate.value,session:((e=o.session)==null?void 0:e.uuid)||""}),Object.assign(t,U(l))};return(o,e)=>{const P=d("BaseInput"),b=d("DatePicker"),V=d("BaseSwitch"),v=d("FormAction");return _(),R(v,{"has-setup-wizard":!0,"pre-requisites":!0,onSetPreRequisites:p,"init-url":n,"init-form":l,form:t,"set-form":f,redirect:"AcademicPeriod"},{default:u(()=>[c("div",k,[c("div",q,[i(P,{type:"text",modelValue:t.name,"onUpdate:modelValue":e[0]||(e[0]=a=>t.name=a),name:"name",label:o.$trans("academic.period.props.name"),error:s(r).name,"onUpdate:error":e[1]||(e[1]=a=>s(r).name=a),placeholder:"2024/2025 Academic Session"},null,8,["modelValue","label","error"])]),c("div",w,[i(b,{modelValue:t.startDate,"onUpdate:modelValue":e[2]||(e[2]=a=>t.startDate=a),name:"startDate",label:o.$trans("academic.period.props.start_date"),"no-clear":"",error:s(r).startDate,"onUpdate:error":e[3]||(e[3]=a=>s(r).startDate=a),placeholder:"2024-09-09"},null,8,["modelValue","label","error"])]),c("div",O,[i(b,{modelValue:t.endDate,"onUpdate:modelValue":e[4]||(e[4]=a=>t.endDate=a),name:"endDate",label:o.$trans("academic.period.props.end_date"),"no-clear":"",error:s(r).endDate,"onUpdate:error":e[5]||(e[5]=a=>s(r).endDate=a),placeholder:"2025-07-25"},null,8,["modelValue","label","error"])]),c("div",H,[i(V,{vertical:"",modelValue:t.enableRegistration,"onUpdate:modelValue":e[6]||(e[6]=a=>t.enableRegistration=a),name:"enableRegistration",label:o.$trans("global.enable",{attribute:o.$trans("student.registration.registration")}),error:s(r).enableRegistration,"onUpdate:error":e[7]||(e[7]=a=>s(r).enableRegistration=a)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),I={name:"AcademicPeriodAction"},y=Object.assign(I,{setup(D){const l=B();return(n,r)=>{const m=d("PageHeaderAction"),t=d("PageHeader"),p=d("ParentTransition");return _(),F(j,null,[i(t,{title:n.$trans(s(l).meta.trans,{attribute:n.$trans(s(l).meta.label)}),navs:[{label:n.$trans("academic.academic"),path:"Academic"},{label:n.$trans("academic.period.period"),path:"AcademicPeriodList"}]},{default:u(()=>[i(m,{name:"AcademicPeriod",title:n.$trans("academic.period.period"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),i(p,{appear:"",visibility:!0},{default:u(()=>[i(E)]),_:1})],64)}}});export{y as default};
