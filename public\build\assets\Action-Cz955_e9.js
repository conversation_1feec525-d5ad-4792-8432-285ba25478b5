import{i as I,u as A,h as z,I as G,g as J,l as v,r as i,z as K,q as f,o as m,w as c,d as l,a as u,b as p,e as n,f as r,s as w,t as b,F as D,J as M,A as Q,aU as W,K as X}from"./app-DvIo72ZO.js";const Y={class:"grid grid-cols-3 gap-6"},Z={class:"col-span-3"},h={key:0,class:"col-span-3"},x={key:0,class:"ml-1"},ee={key:0,class:"ml-1"},oe={class:"col-span-3 sm:col-span-2"},te={class:"flex"},ae={class:"col-span-3 sm:col-span-1"},re={class:"col-span-3 sm:col-span-1"},se={class:"col-span-3 sm:col-span-1"},le={class:"col-span-3 sm:col-span-1"},ne={class:"mt-4 grid grid-cols-3 gap-6"},me={key:0,class:"col-span-3 sm:col-span-1"},pe={key:1,class:"col-span-3 sm:col-span-1"},ie={class:"col-span-3 sm:col-span-1"},de={class:"col-span-3 sm:col-span-1"},ue={class:"col-span-3 sm:col-span-1"},ye={class:"col-span-3 sm:col-span-1"},be={class:"col-span-3 sm:col-span-1"},ce={key:0,class:"mt-4 grid grid-cols-3 gap-6"},ge={class:"col-span-3"},Ve={class:"text-danger"},fe={class:"col-span-3 sm:col-span-1"},Ue={class:"col-span-3 sm:col-span-1"},Ne={class:"col-span-3 sm:col-span-1"},ve={class:"flex space-x-2"},we={class:"col-span-3 sm:col-span-1"},$e={name:"EmployeeForm"},Te=Object.assign($e,{setup(j){I();const U=A();z();const y={department:"",designation:"",employmentStatus:"",joiningDate:"",codeNumber:"",employeeType:"new",firstName:"",middleName:"",thirdName:"",lastName:"",birthDate:"",gender:"",contactNumber:"",email:"",roles:[],createUserAccount:!1,username:"",password:"",passwordConfirmation:"",type:""},$="employee/",a=G($),T=J("teams"),g=v({genders:[],employeeTypes:[],types:[],codeNumber:"",roles:[]}),t=v({...y}),k=v({hidePassword:!0}),d=v({department:"",designation:"",employmentStatus:"",isLoaded:!U.params.uuid}),P=s=>{Object.assign(t,s),d.department=s.department.name,d.designation=s.designation.name,d.employmentStatus=s.employmentStatus.name,d.isLoaded=!0},E=s=>{Object.assign(g,s),y.codeNumber=s.codeNumber,Object.assign(t,X(y))},q=()=>{var s=W(12);t.password=s,t.passwordConfirmation=s};return(s,e)=>{const B=i("BaseRadioGroup"),N=i("BaseSelectSearch"),C=i("BaseLabel"),F=i("NameInput"),_=i("DatePicker"),V=i("BaseInput"),S=i("BaseSelect"),L=i("BaseSwitch"),R=i("HelperText"),O=i("FormAction"),H=K("tooltip");return m(),f(O,{"pre-requisites":!0,onSetPreRequisites:E,"init-url":$,"init-form":y,form:t,"set-form":P,redirect:"Employee"},{default:c(()=>[l("div",Y,[l("div",Z,[n(B,{"top-margin":"",options:g.employeeTypes,name:"employeeType",modelValue:t.employeeType,"onUpdate:modelValue":e[0]||(e[0]=o=>t.employeeType=o),error:r(a).employeeType,"onUpdate:error":e[1]||(e[1]=o=>r(a).employeeType=o),horizontal:""},null,8,["options","modelValue","error"])]),t.employeeType=="existing"||t.employeeType=="other_team_member"?(m(),u("div",h,[n(N,{name:"employee",label:s.$trans("global.select",{attribute:s.$trans("employee.employee")}),modelValue:t.employee,"onUpdate:modelValue":e[2]||(e[2]=o=>t.employee=o),error:r(a).employee,"onUpdate:error":e[3]||(e[3]=o=>r(a).employee=o),"value-prop":"uuid","search-action":"employee/list","search-key":"search","additional-search-query":{status:"all",otherTeamMember:t.employeeType=="other_team_member"}},{selectedOption:c(o=>[w(b(o.value.name)+" ("+b(o.value.codeNumber)+") ",1),t.employeeType=="other_team_member"?(m(),u("span",x,b(o.value.teamName),1)):p("",!0)]),listOption:c(o=>[w(b(o.option.name)+" ("+b(o.option.codeNumber)+") ",1),t.employeeType=="other_team_member"?(m(),u("span",ee,b(o.option.teamName),1)):p("",!0)]),_:1},8,["label","modelValue","error","additional-search-query"])])):p("",!0),t.employeeType=="new"?(m(),u(D,{key:1},[l("div",oe,[n(C,null,{default:c(()=>[w(b(s.$trans("employee.props.name")),1)]),_:1}),l("div",te,[n(F,{firstName:t.firstName,"onUpdate:firstName":e[4]||(e[4]=o=>t.firstName=o),middleName:t.middleName,"onUpdate:middleName":e[5]||(e[5]=o=>t.middleName=o),thirdName:t.thirdName,"onUpdate:thirdName":e[6]||(e[6]=o=>t.thirdName=o),lastName:t.lastName,"onUpdate:lastName":e[7]||(e[7]=o=>t.lastName=o),formErrors:r(a),"onUpdate:formErrors":e[8]||(e[8]=o=>M(a)?a.value=o:null)},null,8,["firstName","middleName","thirdName","lastName","formErrors"])])]),l("div",ae,[n(C,null,{default:c(()=>[w(b(s.$trans("contact.props.gender")),1)]),_:1}),n(B,{"top-margin":"",options:g.genders,name:"gender",modelValue:t.gender,"onUpdate:modelValue":e[9]||(e[9]=o=>t.gender=o),error:r(a).gender,"onUpdate:error":e[10]||(e[10]=o=>r(a).gender=o),horizontal:""},null,8,["options","modelValue","error"])]),l("div",re,[n(_,{modelValue:t.birthDate,"onUpdate:modelValue":e[11]||(e[11]=o=>t.birthDate=o),name:"birthDate",label:s.$trans("contact.props.birth_date"),"no-clear":"",error:r(a).birthDate,"onUpdate:error":e[12]||(e[12]=o=>r(a).birthDate=o)},null,8,["modelValue","label","error"])]),l("div",se,[n(V,{type:"text",modelValue:t.contactNumber,"onUpdate:modelValue":e[13]||(e[13]=o=>t.contactNumber=o),name:"contactNumber",label:s.$trans("contact.props.contact_number"),error:r(a).contactNumber,"onUpdate:error":e[14]||(e[14]=o=>r(a).contactNumber=o),autofocus:""},null,8,["modelValue","label","error"])]),l("div",le,[n(V,{type:"text",modelValue:t.email,"onUpdate:modelValue":e[15]||(e[15]=o=>t.email=o),name:"email",label:s.$trans("contact.props.email"),error:r(a).email,"onUpdate:error":e[16]||(e[16]=o=>r(a).email=o),autofocus:""},null,8,["modelValue","label","error"])])],64)):p("",!0)]),l("div",ne,[t.employeeType!="other_team_member"?(m(),u("div",me,[n(V,{type:"text",modelValue:t.codeNumber,"onUpdate:modelValue":e[17]||(e[17]=o=>t.codeNumber=o),name:"codeNumber",label:s.$trans("employee.props.code_number"),error:r(a).codeNumber,"onUpdate:error":e[18]||(e[18]=o=>r(a).codeNumber=o),autofocus:""},null,8,["modelValue","label","error"])])):p("",!0),t.employeeType=="other_team_member"&&r(T).length>1?(m(),u("div",pe,[n(S,{modelValue:t.roles,"onUpdate:modelValue":e[19]||(e[19]=o=>t.roles=o),name:"roles",label:s.$trans("contact.login.props.role"),options:g.roles,multiple:"","label-prop":"label","value-prop":"uuid",error:r(a).roles,"onUpdate:error":e[20]||(e[20]=o=>r(a).roles=o)},null,8,["modelValue","label","options","error"])])):p("",!0),l("div",ie,[n(_,{modelValue:t.joiningDate,"onUpdate:modelValue":e[21]||(e[21]=o=>t.joiningDate=o),name:"joiningDate",label:s.$trans("employee.props.joining_date"),"no-clear":"",error:r(a).joiningDate,"onUpdate:error":e[22]||(e[22]=o=>r(a).joiningDate=o)},null,8,["modelValue","label","error"])]),l("div",de,[n(S,{name:"type",label:s.$trans("global.select",{attribute:s.$trans("employee.type")}),options:g.types,modelValue:t.type,"onUpdate:modelValue":e[23]||(e[23]=o=>t.type=o),error:r(a).type,"onUpdate:error":e[24]||(e[24]=o=>r(a).type=o)},null,8,["label","options","modelValue","error"])]),l("div",ue,[d.isLoaded?(m(),f(N,{key:0,name:"employmentStatus",label:s.$trans("global.select",{attribute:s.$trans("employee.employment_status.employment_status")}),modelValue:t.employmentStatus,"onUpdate:modelValue":e[25]||(e[25]=o=>t.employmentStatus=o),error:r(a).employmentStatus,"onUpdate:error":e[26]||(e[26]=o=>r(a).employmentStatus=o),"label-prop":"name","value-prop":"uuid","init-search":d.employmentStatus,onChange:e[27]||(e[27]=()=>{}),"search-action":"option/list","additional-search-query":{type:"employment_status"}},null,8,["label","modelValue","error","init-search"])):p("",!0)]),l("div",ye,[d.isLoaded?(m(),f(N,{key:0,name:"department",label:s.$trans("global.select",{attribute:s.$trans("employee.department.department")}),modelValue:t.department,"onUpdate:modelValue":e[28]||(e[28]=o=>t.department=o),error:r(a).department,"onUpdate:error":e[29]||(e[29]=o=>r(a).department=o),"label-prop":"name","value-prop":"uuid","init-search":d.department,onChange:e[30]||(e[30]=()=>{}),"search-action":"employee/department/list"},null,8,["label","modelValue","error","init-search"])):p("",!0)]),l("div",be,[d.isLoaded?(m(),f(N,{key:0,name:"designation",label:s.$trans("global.select",{attribute:s.$trans("employee.designation.designation")}),modelValue:t.designation,"onUpdate:modelValue":e[31]||(e[31]=o=>t.designation=o),error:r(a).designation,"onUpdate:error":e[32]||(e[32]=o=>r(a).designation=o),"label-prop":"name","value-prop":"uuid","init-search":d.designation,onChange:e[33]||(e[33]=()=>{}),"search-action":"employee/designation/list"},null,8,["label","modelValue","error","init-search"])):p("",!0)])]),t.employeeType=="new"?(m(),u("div",ce,[l("div",ge,[n(L,{vertical:"",modelValue:t.createUserAccount,"onUpdate:modelValue":e[34]||(e[34]=o=>t.createUserAccount=o),name:"createUserAccount",label:s.$trans("global.create",{attribute:s.$trans("contact.user_account")}),error:r(a).createUserAccount,"onUpdate:error":e[35]||(e[35]=o=>r(a).createUserAccount=o)},null,8,["modelValue","label","error"]),t.email?p("",!0):(m(),f(R,{key:0},{default:c(()=>[l("div",Ve,b(s.$trans("employee.email_required_to_create_account")),1)]),_:1}))]),t.email&&t.createUserAccount?(m(),u(D,{key:0},[l("div",fe,[n(S,{modelValue:t.roles,"onUpdate:modelValue":e[36]||(e[36]=o=>t.roles=o),name:"roles",label:s.$trans("contact.login.props.role"),options:g.roles,multiple:"","label-prop":"label","value-prop":"uuid",error:r(a).roles,"onUpdate:error":e[37]||(e[37]=o=>r(a).roles=o)},null,8,["modelValue","label","options","error"])]),l("div",Ue,[n(V,{type:"text",modelValue:t.username,"onUpdate:modelValue":e[38]||(e[38]=o=>t.username=o),name:"username",label:s.$trans("contact.login.props.username"),error:r(a).username,"onUpdate:error":e[39]||(e[39]=o=>r(a).username=o)},null,8,["modelValue","label","error"])]),l("div",Ne,[n(V,{type:k.hidePassword?"password":"text",modelValue:t.password,"onUpdate:modelValue":e[41]||(e[41]=o=>t.password=o),name:"password",label:s.$trans("contact.login.props.password"),error:r(a).password,"onUpdate:error":e[42]||(e[42]=o=>r(a).password=o)},{"additional-label":c(()=>[l("div",ve,[Q(l("i",{class:"fas fa-key cursor-pointer",onClick:q},null,512),[[H,s.$trans("global.generate",{attribute:s.$trans("auth.login.props.password")})]]),t.password?(m(),u("i",{key:0,class:"fas fa-eye cursor-pointer",onClick:e[40]||(e[40]=o=>k.hidePassword=!k.hidePassword)})):p("",!0)])]),_:1},8,["type","modelValue","label","error"])]),l("div",we,[n(V,{type:"password",modelValue:t.passwordConfirmation,"onUpdate:modelValue":e[43]||(e[43]=o=>t.passwordConfirmation=o),name:"passwordConfirmation",label:s.$trans("contact.login.props.password_confirmation"),error:r(a).passwordConfirmation,"onUpdate:error":e[44]||(e[44]=o=>r(a).passwordConfirmation=o)},null,8,["modelValue","label","error"])])],64)):p("",!0)])):p("",!0)]),_:1},8,["form"])}}}),ke={name:"EmployeeAction"},De=Object.assign(ke,{setup(j){const U=A();return(y,$)=>{const a=i("PageHeaderAction"),T=i("PageHeader"),g=i("ParentTransition");return m(),u(D,null,[n(T,{title:y.$trans(r(U).meta.trans,{attribute:y.$trans(r(U).meta.label)}),navs:[{label:y.$trans("employee.employee"),path:"EmployeeList"}]},{default:c(()=>[n(a,{name:"Employee",title:y.$trans("employee.employee"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(g,{appear:"",visibility:!0},{default:c(()=>[n(Te)]),_:1})],64)}}});export{De as default};
