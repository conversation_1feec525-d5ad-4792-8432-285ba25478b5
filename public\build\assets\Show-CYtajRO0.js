import{i as C,u as P,h as U,l as A,r as l,a as f,o as r,e as a,w as e,f as u,q as m,b as H,d as I,s as n,t as s,F as g,v as N}from"./app-DvIo72ZO.js";const D={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},F={name:"TodoShow"},E=Object.assign(F,{setup(R){C();const p=P(),_=U(),y={},B="utility/todo/",o=A({...y}),$=t=>{Object.assign(o,t)};return(t,c)=>{const h=l("PageHeaderAction"),T=l("PageHeader"),i=l("BaseDataView"),b=l("BaseBadge"),w=l("BaseButton"),k=l("ShowButton"),v=l("BaseCard"),S=l("ShowItem"),V=l("ParentTransition");return r(),f(g,null,[a(T,{title:t.$trans(u(p).meta.trans,{attribute:t.$trans(u(p).meta.label)}),navs:[{label:t.$trans("utility.utility"),path:"Utility"},{label:t.$trans("utility.todo.todo"),path:"UtilityTodoList"}]},{default:e(()=>[a(h,{name:"UtilityTodo",title:t.$trans("utility.todo.todo"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(V,{appear:"",visibility:!0},{default:e(()=>[a(S,{"init-url":B,uuid:u(p).params.uuid,onSetItem:$,onRedirectTo:c[1]||(c[1]=d=>u(_).push({name:"Todo"}))},{default:e(()=>[o.uuid?(r(),m(v,{key:0},{title:e(()=>[n(s(o.title),1)]),footer:e(()=>[a(k,null,{default:e(()=>[a(w,{design:"primary",onClick:c[0]||(c[0]=d=>u(_).push({name:"UtilityTodoEdit",params:{uuid:o.uuid}}))},{default:e(()=>[n(s(t.$trans("general.edit")),1)]),_:1})]),_:1})]),default:e(()=>[I("dl",D,[a(i,{label:t.$trans("utility.todo.due")},{default:e(()=>[n(s(o.due.formatted),1)]),_:1},8,["label"]),a(i,null,{default:e(()=>[o.status?(r(),m(b,{key:0,design:"success",label:t.$trans("utility.todo.completed")},null,8,["label"])):(r(),m(b,{key:1,design:"error",label:t.$trans("utility.todo.incomplete")},null,8,["label"]))]),_:1}),a(i,{class:"col-span-1 sm:col-span-2",label:t.$trans("utility.todo.props.description"),html:""},{default:e(()=>[n(s(o.description),1)]),_:1},8,["label"]),(r(!0),f(g,null,N(o.customFields||[],d=>(r(),m(i,{key:d.uuid,label:d.label},{default:e(()=>[n(s(d.formattedValue),1)]),_:2},1032,["label"]))),128)),a(i,{label:t.$trans("general.created_at")},{default:e(()=>[n(s(o.createdAt.formatted),1)]),_:1},8,["label"]),a(i,{label:t.$trans("general.updated_at")},{default:e(()=>[n(s(o.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):H("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{E as default};
