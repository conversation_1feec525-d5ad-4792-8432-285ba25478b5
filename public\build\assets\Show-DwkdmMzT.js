import{i as x,u as C,h as N,j as $,l as I,r as s,a as P,o as _,e as l,w as e,f as t,q as f,b as g,d as r,s as o,t as i,F as S}from"./app-DvIo72ZO.js";const j={class:"space-y-2"},H={class:"grid grid-cols-1 gap-x-4 gap-y-8 px-4 pt-4 sm:grid-cols-2"},T={class:"text-sm space-y-2"},z={name:"RecruitmentApplicationShow"},q=Object.assign(z,{setup(F){x();const u=C(),h=N(),a=$("$trans"),v={},y="recruitment/application/",n=I({...v}),V=b=>{Object.assign(n,b)};return(b,p)=>{const w=s("PageHeaderAction"),A=s("PageHeader"),c=s("ListItemView"),L=s("ListContainerVertical"),m=s("BaseCard"),D=s("BaseDataView"),d=s("ListMedia"),B=s("DetailLayoutVertical"),R=s("ShowItem"),k=s("ParentTransition");return _(),P(S,null,[l(A,{title:t(a)(t(u).meta.trans,{attribute:t(a)(t(u).meta.label)}),navs:[{label:t(a)("recruitment.recruitment"),path:"Recruitment"},{label:t(a)("recruitment.application.application"),path:"RecruitmentApplicationList"}]},{default:e(()=>[l(w,{name:"RecruitmentApplication",title:t(a)("recruitment.application.application"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(k,{appear:"",visibility:!0},{default:e(()=>[l(R,{"init-url":y,uuid:t(u).params.uuid,onSetItem:V,onRedirectTo:p[0]||(p[0]=M=>t(h).push({name:"RecruitmentApplication"}))},{default:e(()=>[n.uuid?(_(),f(B,{key:0},{detail:e(()=>[r("div",j,[l(m,{"no-padding":"","no-content-padding":""},{title:e(()=>[o(i(t(a)("recruitment.application.application")),1)]),action:e(()=>p[1]||(p[1]=[])),default:e(()=>[l(L,null,{default:e(()=>[l(c,{label:t(a)("recruitment.vacancy.vacancy")},{default:e(()=>[o(i(n.vacancy.title),1)]),_:1},8,["label"]),l(c,{label:t(a)("employee.designation.designation")},{default:e(()=>[o(i(n.designation.name),1)]),_:1},8,["label"]),l(c,{label:t(a)("contact.props.name")},{default:e(()=>[o(i(n.contact.name),1)]),_:1},8,["label"]),l(c,{label:t(a)("contact.props.gender")},{default:e(()=>[o(i(n.contact.gender.label),1)]),_:1},8,["label"]),l(c,{label:t(a)("contact.props.birth_date")},{default:e(()=>[o(i(n.contact.birthDate.formatted),1)]),_:1},8,["label"]),l(c,{label:t(a)("contact.props.contact_number")},{default:e(()=>[o(i(n.contact.contactNumber),1)]),_:1},8,["label"]),l(c,{label:t(a)("contact.props.email")},{default:e(()=>[o(i(n.contact.email),1)]),_:1},8,["label"]),l(c,{label:t(a)("contact.props.father_name")},{default:e(()=>[o(i(n.contact.fatherName),1)]),_:1},8,["label"]),l(c,{label:t(a)("contact.props.mother_name")},{default:e(()=>[o(i(n.contact.motherName),1)]),_:1},8,["label"]),l(c,{label:t(a)("contact.props.address.address")},{default:e(()=>[o(i(n.contact.presentAddressDisplay),1)]),_:1},8,["label"]),l(c,{label:t(a)("recruitment.application.props.application_date")},{default:e(()=>[o(i(n.applicationDate.formatted),1)]),_:1},8,["label"]),l(c,{label:t(a)("recruitment.application.props.availability_date")},{default:e(()=>[o(i(n.availabilityDate.formatted),1)]),_:1},8,["label"]),l(c,{label:t(a)("general.created_at")},{default:e(()=>[o(i(n.createdAt.formatted),1)]),_:1},8,["label"]),l(c,{label:t(a)("general.updated_at")},{default:e(()=>[o(i(n.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})])]),default:e(()=>[l(m,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:e(()=>[o(i(t(a)("general.detail")),1)]),default:e(()=>[r("dl",H,[l(D,{class:"col-span-1 sm:col-span-2",label:t(a)("recruitment.application.props.cover_letter"),html:""},{default:e(()=>[o(i(n.coverLetter),1)]),_:1},8,["label"])]),n.media.length>0?(_(),f(m,{key:0,class:"mt-4"},{title:e(()=>[o(i(t(a)("general.media")),1)]),default:e(()=>[r("div",T,[r("div",null,[r("h2",null,i(t(a)("recruitment.vacancy.wizard.resume")),1),l(d,{section:"resume",media:n.media,url:`/app/recruitment/applications/${n.uuid}/`},null,8,["media","url"])]),r("div",null,[r("h2",null,i(t(a)("recruitment.vacancy.wizard.marksheet")),1),l(d,{section:"marksheet",media:n.media,url:`/app/recruitment/applications/${n.uuid}/`},null,8,["media","url"])]),r("div",null,[r("h2",null,i(t(a)("contact.props.id_proof")),1),l(d,{section:"id_proof",media:n.media,url:`/app/recruitment/applications/${n.uuid}/`},null,8,["media","url"])]),r("div",null,[r("h2",null,i(t(a)("contact.props.address_proof")),1),l(d,{section:"address_proof",media:n.media,url:`/app/recruitment/applications/${n.uuid}/`},null,8,["media","url"])])])]),_:1})):g("",!0)]),_:1})]),_:1})):g("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{q as default};
