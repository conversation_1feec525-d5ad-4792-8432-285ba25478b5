import{u as P,l as C,n as M,r as o,q as v,o as f,w as e,d as h,b as $,s as p,t as s,e as a,h as O,j as U,y as b,m as j,f as m,a as w,F,v as z}from"./app-DvIo72ZO.js";import{_ as G}from"./ModuleDropdown-DdkPlpRE.js";const J={class:"grid grid-cols-3 gap-6"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},W={__name:"Filter",emits:["hide"],setup(E,{emit:g}){const c=P(),k=g,D={employees:[],leaveTypes:[],startDate:"",endDate:""},d=C({...D}),r=C({employees:[],leaveTypes:[],isLoaded:!(c.query.employees||c.query.leaveTypes)});return M(async()=>{r.employees=c.query.employees?c.query.employees.split(","):[],r.leaveTypes=c.query.leaveTypes?c.query.leaveTypes.split(","):[],r.isLoaded=!0}),(y,i)=>{const l=o("BaseSelectSearch"),u=o("DatePicker"),T=o("FilterForm");return f(),v(T,{"init-form":D,form:d,multiple:["employees","leaveTypes"],onHide:i[4]||(i[4]=n=>k("hide"))},{default:e(()=>[h("div",J,[h("div",K,[r.isLoaded?(f(),v(l,{key:0,multiple:"",name:"employees",label:y.$trans("global.select",{attribute:y.$trans("employee.employee")}),modelValue:d.employees,"onUpdate:modelValue":i[0]||(i[0]=n=>d.employees=n),"value-prop":"uuid","init-search":r.employees,"search-key":"name","search-action":"employee/list"},{selectedOption:e(n=>[p(s(n.value.name)+" ("+s(n.value.codeNumber)+") ",1)]),listOption:e(n=>[p(s(n.option.name)+" ("+s(n.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):$("",!0)]),h("div",Q,[r.isLoaded?(f(),v(l,{key:0,multiple:"",name:"leaveTypes",label:y.$trans("global.select",{attribute:y.$trans("employee.leave.type.type")}),modelValue:d.leaveTypes,"onUpdate:modelValue":i[1]||(i[1]=n=>d.leaveTypes=n),"label-prop":"name","value-prop":"uuid","init-search":r.leaveTypes,"search-key":"name","search-action":"leave/type/list"},null,8,["label","modelValue","init-search"])):$("",!0)]),h("div",R,[a(u,{start:d.startDate,"onUpdate:start":i[2]||(i[2]=n=>d.startDate=n),end:d.endDate,"onUpdate:end":i[3]||(i[3]=n=>d.endDate=n),name:"dateBetween",as:"range",label:y.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},X={name:"EmployeeLeaveRequestList"},x=Object.assign(X,{setup(E){const g=O(),c=U("emitter");let k=["filter"];b("leave-request:create")&&k.unshift("create"),b("leave:config")&&k.unshift("config");let D=[];b("leave-request:export")&&(D=["print","pdf","excel"]);const d="employee/leave/request/",r=j(!1),y=C({}),i=l=>{Object.assign(y,l)};return(l,u)=>{const T=o("PageHeaderAction"),n=o("PageHeader"),L=o("ParentTransition"),_=o("DataCell"),V=o("BaseBadge"),q=o("FloatingMenuItem"),I=o("FloatingMenu"),S=o("DataRow"),N=o("BaseButton"),A=o("DataTable"),H=o("ListItem");return f(),v(H,{"init-url":d,onSetItems:i},{header:e(()=>[a(n,{title:l.$trans("employee.leave.request.request"),navs:[{label:l.$trans("employee.leave.leave"),path:"EmployeeLeave"}]},{default:e(()=>[a(T,{url:"employee/leave/requests/",name:"EmployeeLeaveRequest","config-path":"EmployeeLeaveConfig",title:l.$trans("employee.leave.request.request"),actions:m(k),"dropdown-actions":m(D),onToggleFilter:u[0]||(u[0]=t=>r.value=!r.value)},{moduleOption:e(()=>[a(G)]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[a(L,{appear:"",visibility:r.value},{default:e(()=>[a(W,{onRefresh:u[1]||(u[1]=t=>m(c).emit("listItems")),onHide:u[2]||(u[2]=t=>r.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[a(L,{appear:"",visibility:!0},{default:e(()=>[a(A,{header:y.headers,meta:y.meta,module:"employee.leave.request",onRefresh:u[4]||(u[4]=t=>m(c).emit("listItems"))},{actionButton:e(()=>[m(b)("leave-request:create")?(f(),v(N,{key:0,onClick:u[3]||(u[3]=t=>m(g).push({name:"EmployeeLeaveRequestCreate"}))},{default:e(()=>[p(s(l.$trans("global.add",{attribute:l.$trans("employee.leave.request.request")})),1)]),_:1})):$("",!0)]),default:e(()=>[(f(!0),w(F,null,z(y.data,t=>(f(),v(S,{key:t.uuid,onDoubleClick:B=>m(g).push({name:"EmployeeLeaveRequestShow",params:{uuid:t.uuid}})},{default:e(()=>[a(_,{name:"employee"},{default:e(()=>[p(s(t.employee.name)+" ("+s(t.employee.codeNumber)+") ",1)]),_:2},1024),a(_,{name:"leaveType"},{default:e(()=>[p(s(t.leaveType.name),1)]),_:2},1024),a(_,{name:"startDate"},{default:e(()=>[p(s(t.startDate.formatted),1)]),_:2},1024),a(_,{name:"endDate"},{default:e(()=>[p(s(t.endDate.formatted),1)]),_:2},1024),a(_,{name:"status"},{default:e(()=>[a(V,{label:t.status.label,design:t.status.color},null,8,["label","design"])]),_:2},1024),a(_,{name:"createdAt"},{default:e(()=>[p(s(t.createdAt.formatted),1)]),_:2},1024),a(_,{name:"action"},{default:e(()=>[a(I,null,{default:e(()=>[a(q,{icon:"fas fa-arrow-circle-right",onClick:B=>m(g).push({name:"EmployeeLeaveRequestShow",params:{uuid:t.uuid}})},{default:e(()=>[p(s(l.$trans("general.show")),1)]),_:2},1032,["onClick"]),t.status.value=="requested"?(f(),w(F,{key:0},[m(b)("leave-request:edit")?(f(),v(q,{key:0,icon:"fas fa-edit",onClick:B=>m(g).push({name:"EmployeeLeaveRequestEdit",params:{uuid:t.uuid}})},{default:e(()=>[p(s(l.$trans("general.edit")),1)]),_:2},1032,["onClick"])):$("",!0),m(b)("leave-request:delete")?(f(),v(q,{key:1,icon:"fas fa-trash",onClick:B=>m(c).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[p(s(l.$trans("general.delete")),1)]),_:2},1032,["onClick"])):$("",!0)],64)):$("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{x as default};
