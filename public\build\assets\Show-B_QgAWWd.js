import{i as F,u as O,h as q,j as U,l as z,r as n,a as p,o as i,e as t,w as e,f as a,q as _,b as c,d as k,F as g,v as V,s as r,t as u,y as G}from"./app-DvIo72ZO.js";const J={class:"space-y-2"},K={key:0,class:"far fa-lg fa-check-circle text-success"},Q={class:"text-xs"},W={class:"grid grid-cols-1 gap-x-4 gap-y-8 px-4 pt-4 sm:grid-cols-2"},X={name:"ResourceSyllabusShow"},ee=Object.assign(X,{setup(Y){F();const y=O(),h=q(),l=U("$trans"),x={},R="resource/syllabus/",C=[{key:"unitNumber",label:l("resource.syllabus.props.unit_number"),visibility:!0},{key:"unitName",label:l("resource.syllabus.props.unit_name"),visibility:!0},{key:"period",label:l("general.period"),visibility:!0},{key:"completionDate",label:l("resource.syllabus.props.completion_date"),visibility:!0},{key:"action",label:"",visibility:!0}],o=z({...x}),L=v=>{Object.assign(o,v)};return(v,d)=>{const N=n("PageHeaderAction"),T=n("PageHeader"),S=n("TextMuted"),f=n("ListItemView"),P=n("ListContainerVertical"),B=n("BaseCard"),m=n("DataCell"),D=n("DataRow"),j=n("SimpleTable"),w=n("BaseDataView"),I=n("ListMedia"),$=n("BaseButton"),A=n("ShowButton"),H=n("DetailLayoutVertical"),M=n("ShowItem"),E=n("ParentTransition");return i(),p(g,null,[t(T,{title:a(l)(a(y).meta.trans,{attribute:a(l)(a(y).meta.label)}),navs:[{label:a(l)("resource.resource"),path:"Resource"},{label:a(l)("resource.syllabus.syllabus"),path:"ResourceSyllabus"}]},{default:e(()=>[t(N,{name:"ResourceSyllabus",title:a(l)("resource.syllabus.syllabus"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(E,{appear:"",visibility:!0},{default:e(()=>[t(M,{"init-url":R,uuid:a(y).params.uuid,"module-uuid":a(y).params.muuid,onSetItem:L,onRedirectTo:d[1]||(d[1]=s=>a(h).push({name:"ResourceSyllabus",params:{uuid:o.uuid}}))},{default:e(()=>[o.uuid?(i(),_(H,{key:0},{detail:e(()=>[k("div",J,[t(B,{"no-padding":"","no-content-padding":""},{title:e(()=>[r(u(a(l)("resource.syllabus.syllabus")),1)]),action:e(()=>d[2]||(d[2]=[])),default:e(()=>[t(P,null,{default:e(()=>[t(f,{label:a(l)("academic.course.course")},{default:e(()=>[(i(!0),p(g,null,V(o.records,s=>{var b;return i(),p("div",null,[r(u(((b=s.batch.course)==null?void 0:b.name)+" "+s.batch.name)+" ",1),s.subject?(i(),_(S,{key:0},{default:e(()=>[r(u(s.subject.name),1)]),_:2},1024)):c("",!0)])}),256))]),_:1},8,["label"]),t(f,{label:a(l)("employee.employee")},{default:e(()=>{var s;return[r(u(((s=o.employee)==null?void 0:s.name)||"-")+" ",1),o.employee?(i(),_(S,{key:0,block:""},{default:e(()=>[r(u(o.employee.code),1)]),_:1})):c("",!0)]}),_:1},8,["label"]),t(f,{label:a(l)("general.created_at")},{default:e(()=>[r(u(o.createdAt.formatted),1)]),_:1},8,["label"]),t(f,{label:a(l)("general.updated_at")},{default:e(()=>[r(u(o.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})])]),default:e(()=>[t(B,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:e(()=>[r(u(a(l)("global.detail",{attribute:a(l)("resource.syllabus.syllabus")})),1)]),footer:e(()=>[t(A,null,{default:e(()=>[a(G)("syllabus:edit")&&o.isEditable?(i(),_($,{key:0,design:"primary",onClick:d[0]||(d[0]=s=>a(h).push({name:"ResourceSyllabusEdit",params:{uuid:o.uuid}}))},{default:e(()=>[r(u(a(l)("general.edit")),1)]),_:1})):c("",!0)]),_:1})]),default:e(()=>[o.units.length>0?(i(),_(j,{key:0,header:C},{default:e(()=>[(i(!0),p(g,null,V(o.units,s=>(i(),p(g,{key:s.uuid},[t(D,null,{default:e(()=>[t(m,{name:"unitNumber"},{default:e(()=>[r(u(s.unitNumber||"-"),1)]),_:2},1024),t(m,{name:"unitName"},{default:e(()=>[r(u(s.unitName)+" ",1),s.completionDate.value?(i(),p("i",K)):c("",!0)]),_:2},1024),t(m,{name:"period"},{default:e(()=>[r(u(s.startDate.formatted)+" - "+u(s.endDate.formatted),1)]),_:2},1024),t(m,{name:"completiondate"},{default:e(()=>{var b;return[r(u(((b=s.completionDate)==null?void 0:b.formatted)||"-"),1)]}),_:2},1024),t(m,{name:"action"})]),_:2},1024),s.description?(i(),_(D,{key:0},{default:e(()=>[t(m,{colspan:10},{default:e(()=>[k("span",Q,u(s.description),1)]),_:2},1024)]),_:2},1024)):c("",!0)],64))),128))]),_:1})):c("",!0),k("dl",W,[t(w,{class:"col-span-1 sm:col-span-2",label:a(l)("resource.syllabus.props.remarks")},{default:e(()=>[r(u(o.remarks),1)]),_:1},8,["label"]),t(w,{class:"col-span-1 sm:col-span-2"},{default:e(()=>[t(I,{media:o.media,url:`/app/resource/syllabuses/${o.uuid}/`},null,8,["media","url"])]),_:1})])]),_:1})]),_:1})):c("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{ee as default};
