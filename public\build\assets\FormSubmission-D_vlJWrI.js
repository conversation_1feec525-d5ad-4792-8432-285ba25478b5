import{i as te,u as ae,h as se,j as le,m as I,l as P,n as oe,L as ne,r,a as c,o as i,e as n,w as e,f as s,q as b,b as d,d as A,s as o,t as l,F as C,v as z,B as ie}from"./app-DvIo72ZO.js";const ue={class:"space-y-4"},de={class:"px-4 py-2 mb-4 grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},me={key:0},re={key:1,class:"grid grid-cols-1 gap-x-4 gap-y-8 px-4 pt-4 sm:grid-cols-3"},ce={key:2,class:"grid grid-cols-1 gap-x-4 gap-y-8 px-4 pt-4 sm:grid-cols-2"},fe={key:3,class:"mt-4 px-4"},pe={key:4,class:"mt-4 px-4"},_e={key:5,class:"mt-4 px-4"},be={class:"mt-4"},xe={key:6,class:"mt-4 px-4"},he={class:"text-sm"},ve={key:7,class:"mt-4 px-4"},ge={class:"text-sm"},ye={key:1,class:"px-4"},ke={name:"ExamScheduleFormSubmission"},we=Object.assign(ke,{setup(Fe){const v=te(),L=ae(),j=se(),t=le("$trans"),H={},w=I(!1),m=I(!1),B="exam/schedule/",R=[{key:"sno",label:t("general.sno"),visibility:!0},{key:"subject",label:t("academic.subject.subject"),visibility:!0},{key:"date",label:t("exam.schedule.props.date"),visibility:!0},{key:"assessment",label:t("exam.assessment.assessment"),visibility:!0},{key:"action",label:"",visibility:!0}],a=P({...H}),f=P({student:{}}),M=u=>{Object.assign(a,u)},V=async()=>{m.value=!0,await v.dispatch("student/getSummary",{params:{feeSummary:!0,date:"today"}}).then(u=>{m.value=!1,Object.assign(f,u)}).catch(u=>{m.value=!1})},$=async()=>{m.value=!0,await v.dispatch(B+"confirmForm",{uuid:a.uuid}).then(u=>{m.value=!1,w.value=!0,V()}).catch(u=>{m.value=!1})},O=async()=>{m.value=!0,await v.dispatch(B+"submitForm",{uuid:a.uuid}).then(u=>{m.value=!1,w.value=!0}).catch(u=>{m.value=!1})},U=async()=>{m.value=!0,await v.dispatch("exam/form/printExamForm",{uuid:a.formUuid}).then(u=>{m.value=!1,window.open("/print").document.write(u)}).catch(u=>{m.value=!1})},q=async()=>{m.value=!0,await v.dispatch("exam/form/printAdmitCard",{uuid:a.formUuid}).then(u=>{m.value=!1,window.open("/print").document.write(u)}).catch(u=>{m.value=!1})};return oe(()=>{V()}),ne(()=>a,u=>{(!a.hasForm||!ie("student"))&&j.push({name:"ExamScheduleShow",params:{uuid:u.uuid}})},{deep:!0}),(u,S)=>{const G=r("PageHeaderAction"),J=r("PageHeader"),p=r("ListItemView"),K=r("ListContainerVertical"),E=r("BaseCard"),g=r("TextMuted"),x=r("BaseDataView"),y=r("DataCell"),Q=r("DataRow"),W=r("SimpleTable"),k=r("BaseAlert"),D=r("BaseButton"),X=r("DetailLayoutVertical"),Y=r("ShowItem"),Z=r("ParentTransition");return i(),c(C,null,[n(J,{title:s(t)(s(L).meta.label),navs:[{label:s(t)("exam.exam"),path:"Exam"},{label:s(t)("exam.schedule.schedule"),path:"ExamScheduleList"}]},{default:e(()=>[n(G,{name:"ExamSchedule",title:s(t)("exam.schedule.schedule"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(Z,{appear:"",visibility:!0},{default:e(()=>[n(Y,{"additional-query":{formSubmission:!0},"init-url":B,uuid:s(L).params.uuid,onSetItem:M,onRedirectTo:S[0]||(S[0]=h=>s(j).push({name:"ExamSchedule"})),refresh:w.value,onRefreshed:S[1]||(S[1]=h=>w.value=!1)},{default:e(()=>[a.uuid?(i(),b(X,{key:0},{detail:e(()=>[f.student.uuid?(i(),b(E,{key:0,"no-padding":"","no-content-padding":""},{title:e(()=>[o(l(s(t)("global.detail",{attribute:s(t)("exam.schedule.schedule")})),1)]),default:e(()=>[n(K,null,{default:e(()=>[n(p,{label:s(t)("student.props.name")},{default:e(()=>[o(l(f.student.name),1)]),_:1},8,["label"]),n(p,{label:s(t)("contact.props.father_name")},{default:e(()=>[o(l(f.student.fatherName),1)]),_:1},8,["label"]),n(p,{label:s(t)("contact.props.mother_name")},{default:e(()=>[o(l(f.student.motherName),1)]),_:1},8,["label"]),n(p,{label:s(t)("contact.props.birth_date")},{default:e(()=>[o(l(f.student.birthDate.formatted),1)]),_:1},8,["label"]),n(p,{label:s(t)("contact.props.gender")},{default:e(()=>[o(l(f.student.gender.label),1)]),_:1},8,["label"]),n(p,{label:s(t)("academic.course.course")},{default:e(()=>[o(l(f.student.courseName+" "+f.student.batchName),1)]),_:1},8,["label"]),n(p,{label:s(t)("student.props.roll_number")},{default:e(()=>[o(l(f.student.rollNumber),1)]),_:1},8,["label"]),n(p,{label:s(t)("student.fee.total")},{default:e(()=>[o(l(f.feeSummary.totalFee.formatted),1)]),_:1},8,["label"]),n(p,{label:s(t)("student.fee.paid")},{default:e(()=>[o(l(f.feeSummary.paidFee.formatted),1)]),_:1},8,["label"]),n(p,{label:s(t)("student.fee.balance")},{default:e(()=>[o(l(f.feeSummary.balanceFee.formatted),1)]),_:1},8,["label"]),n(p,{label:s(t)("exam.form.props.submitted_at")},{default:e(()=>[o(l(a.submittedAt.formatted||"-"),1)]),_:1},8,["label"]),n(p,{label:s(t)("exam.form.props.approved_at")},{default:e(()=>[o(l(a.approvedAt.formatted||"-"),1)]),_:1},8,["label"])]),_:1})]),_:1})):d("",!0)]),default:e(()=>[A("div",ue,[n(E,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:e(()=>[o(l(a.exam.name)+" ",1),n(g,null,{default:e(()=>{var h,F;return[o(l((F=(h=a.exam.term)==null?void 0:h.division)==null?void 0:F.name),1)]}),_:1})]),default:e(()=>{var h,F,N;return[A("dl",de,[n(x,{label:s(t)("academic.batch.batch")},{default:e(()=>[o(l(a.batch.course.name)+" ",1),n(g,null,{default:e(()=>[o(l(a.batch.name),1)]),_:1})]),_:1},8,["label"]),n(x,{label:s(t)("exam.grade.grade")},{default:e(()=>[o(l(a.grade.name),1)]),_:1},8,["label"]),n(x,{label:s(t)("exam.assessment.assessment")},{default:e(()=>[o(l(a.assessment.name),1)]),_:1},8,["label"]),n(x,{label:s(t)("exam.observation.observation")},{default:e(()=>{var _;return[o(l((_=a.observation)==null?void 0:_.name),1)]}),_:1},8,["label"])]),a.records.length>0?(i(),c(C,{key:0},[a.records.length>0?(i(),b(W,{key:0,header:R},{default:e(()=>[(i(!0),c(C,null,z(a.records,(_,ee)=>(i(),b(Q,{key:_.uuid},{default:e(()=>[n(y,{name:"sno"},{default:e(()=>[o(l(ee+1),1)]),_:2},1024),n(y,{name:"subject"},{default:e(()=>[o(l(_.subject.name)+" ",1),_.subject.code?(i(),c("span",me,"("+l(_.subject.code)+")",1)):d("",!0),_.hasGrading?(i(),b(g,{key:1,block:""},{default:e(()=>[o("("+l(s(t)("exam.schedule.props.grading"))+")",1)]),_:1})):d("",!0),_.examFee.value?(i(),b(g,{key:2,block:""},{default:e(()=>[o(l(s(t)("academic.subject.props.exam_fee"))+" : "+l(_.examFee.formatted),1)]),_:2},1024)):d("",!0)]),_:2},1024),n(y,{name:"date"},{default:e(()=>[o(l(_.date.formatted),1)]),_:2},1024),n(y,{name:"assessment"},{default:e(()=>[(i(!0),c(C,null,z(_.assessments,T=>(i(),c("div",null,[o(l(T.name)+" ",1),n(g,null,{default:e(()=>[o(l(T.maxMark),1)]),_:2},1024)]))),256))]),_:2},1024),n(y,{name:"action"})]),_:2},1024))),128))]),_:1})):d("",!0),(h=a.exam.configDetail)!=null&&h.examFormFee.value?(i(),c("dl",re,[n(x,{label:s(t)("exam.form.props.fee")},{default:e(()=>[o(l(a.exam.configDetail.examFormFee.formatted),1)]),_:1},8,["label"]),(F=a.exam.configDetail)!=null&&F.examFormLastDate.value?(i(),b(x,{key:0,label:s(t)("exam.form.props.last_date")},{default:e(()=>[o(l(a.exam.configDetail.examFormLastDate.formatted),1)]),_:1},8,["label"])):d("",!0),(N=a.exam.configDetail)!=null&&N.examFormLateFee.value?(i(),b(x,{key:1,label:s(t)("exam.form.props.late_fee")},{default:e(()=>[o(l(a.exam.configDetail.examFormLateFee.formatted),1)]),_:1},8,["label"])):d("",!0)])):d("",!0),a.description?(i(),c("dl",ce,[n(x,{class:"col-span-1 sm:col-span-2",label:s(t)("exam.schedule.props.description")},{default:e(()=>[o(l(a.description),1)]),_:1},8,["label"])])):d("",!0),!a.confirmedAt.value&&a.payableFee.value?(i(),c("div",fe,[n(k,{size:"xs",design:"info"},{default:e(()=>[o(l(s(t)("exam.form.fee_info",{attribute:a.payableFee.formatted})),1)]),_:1})])):d("",!0),a.confirmedAt.value&&!a.submittedAt.value?(i(),c("div",pe,[n(k,{size:"xs",design:"info"},{default:e(()=>[o(l(s(t)("exam.form.confirmed_info",{datetime:a.confirmedAt.formatted})),1)]),_:1})])):d("",!0),a.submittedAt.value?(i(),c("div",_e,[a.approvedAt.value?(i(),b(k,{key:0,size:"xs",design:"success"},{default:e(()=>[o(l(s(t)("exam.form.approved_info",{datetime:a.approvedAt.formatted})),1)]),_:1})):(i(),b(k,{key:1,size:"xs",design:"info",class:"text-sm"},{default:e(()=>[o(l(s(t)("exam.form.submitted_info",{datetime:a.submittedAt.formatted})),1)]),_:1})),A("div",be,[a.submittedAt.value?(i(),b(D,{key:0,onClick:U},{default:e(()=>[o(l(s(t)("global.print",{attribute:s(t)("exam.schedule.form")})),1)]),_:1})):d("",!0),a.submittedAt.value&&a.publishAdmitCard&&a.approvedAt.value?(i(),b(D,{key:1,onClick:q,design:"success"},{default:e(()=>[o(l(s(t)("global.print",{attribute:s(t)("exam.admit_card.admit_card")})),1)]),_:1})):d("",!0)])])):d("",!0),a.confirmedAt.value?d("",!0):(i(),c("div",xe,[A("p",he,l(s(t)("exam.form.fee_confirmation")),1),n(D,{class:"mt-4",onClick:$},{default:e(()=>[o(l(s(t)("general.confirm")),1)]),_:1})])),a.confirmedAt.value&&!a.submittedAt.value?(i(),c("div",ve,[A("p",ge,l(s(t)("exam.form.disclaimer")),1),n(D,{class:"mt-4",onClick:O},{default:e(()=>[o(l(s(t)("general.submit")),1)]),_:1})])):d("",!0)],64)):(i(),c("div",ye,[n(k,{design:"error",size:"xs"},{default:e(()=>[o(l(s(t)("exam.form.not_eligible_to_submit")),1)]),_:1})]))]}),_:1})])]),_:1})):d("",!0)]),_:1},8,["uuid","refresh"])]),_:1})],64)}}});export{we as default};
