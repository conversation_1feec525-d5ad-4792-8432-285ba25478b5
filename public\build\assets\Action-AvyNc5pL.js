import{u as R,H as g,I as S,l as f,n as I,r as p,q as T,o as v,w as y,d as r,a as D,e as i,f as a,F as O,v as N,s as k,t as A,N as K,K as z}from"./app-DvIo72ZO.js";const G={class:"grid grid-cols-3 gap-6"},J={class:"col-span-3"},Q={class:"col-span-3 sm:col-span-1"},W=["onClick"],X={class:"mt-4 grid grid-cols-3 gap-4"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3 sm:col-span-1"},ee={class:"mt-4"},te={class:"mt-4 grid grid-cols-3 gap-6"},oe={class:"col-span-3"},se={class:"col-span-3"},ae={class:"grid grid-cols-1"},ne={class:"col"},re={name:"RecruitmentVacancyForm"},ie=Object.assign(re,{setup(F){const c=R(),m={title:"",records:[],description:"",responsibility:"",lastApplicationDate:"",media:[],mediaUpdated:!1,mediaToken:g(),mediaHash:[]},V={uuid:g(),employmentType:"",designation:"",numberOfPositions:""},_="recruitment/vacancy/",n=S(_),b=f({designations:[],employmentTypes:[]});f({});const o=f({...m}),$=f({isLoaded:!0}),H=s=>{Object.assign(b,s)},h=()=>{o.mediaToken=g(),o.mediaHash=[]},U=()=>{o.records.push({...V,uuid:g()}),$.isLoaded=!0},j=async s=>{await K()&&(o.records.length==1?o.records=[V]:o.records.splice(s,1))},E=s=>{let t=[];s.records.forEach(u=>{t.push({uuid:g(),employmentType:u.employmentType.uuid,designation:u.designation.uuid,numberOfPositions:u.numberOfPositions})}),Object.assign(m,{...s,lastApplicationDate:s.lastApplicationDate.value,records:t}),Object.assign(o,z(m)),$.isLoaded=!0};return I(async()=>{c.params.uuid||U()}),(s,t)=>{const u=p("BaseInput"),q=p("DatePicker"),P=p("BaseSelect"),w=p("BaseFieldset"),C=p("BaseBadge"),B=p("BaseEditor"),L=p("MediaUpload"),M=p("FormAction");return v(),T(M,{"pre-requisites":!0,onSetPreRequisites:H,"init-url":_,"init-form":m,form:o,"set-form":E,redirect:"RecruitmentVacancy",onResetMediaFiles:h},{default:y(()=>[r("div",G,[r("div",J,[i(u,{type:"text",modelValue:o.title,"onUpdate:modelValue":t[0]||(t[0]=e=>o.title=e),name:"title",label:s.$trans("recruitment.vacancy.props.title"),error:a(n).title,"onUpdate:error":t[1]||(t[1]=e=>a(n).title=e),autofocus:""},null,8,["modelValue","label","error"])]),r("div",Q,[i(q,{modelValue:o.lastApplicationDate,"onUpdate:modelValue":t[2]||(t[2]=e=>o.lastApplicationDate=e),name:"lastApplicationDate",label:s.$trans("recruitment.vacancy.props.last_application_date"),"no-clear":"",error:a(n).lastApplicationDate,"onUpdate:error":t[3]||(t[3]=e=>a(n).lastApplicationDate=e)},null,8,["modelValue","label","error"])])]),(v(!0),D(O,null,N(o.records,(e,d)=>(v(),T(w,{class:"mt-4",key:e.uuid},{legend:y(()=>[k(A(d+1)+". ",1),r("span",{class:"text-danger ml-2 cursor-pointer",onClick:l=>j(d)},t[10]||(t[10]=[r("i",{class:"fas fa-times-circle"},null,-1)]),8,W)]),default:y(()=>[r("div",X,[r("div",Y,[i(P,{name:`records.${d}.employmentType`,label:s.$trans("global.select",{attribute:s.$trans("employee.employment_type.employment_type")}),modelValue:e.employmentType,"onUpdate:modelValue":l=>e.employmentType=l,error:a(n)[`records.${d}.employmentType`],"onUpdate:error":l=>a(n)[`records.${d}.employmentType`]=l,options:b.employmentTypes,"value-prop":"uuid","label-prop":"name"},null,8,["name","label","modelValue","onUpdate:modelValue","error","onUpdate:error","options"])]),r("div",Z,[i(P,{name:`records.${d}.designation`,label:s.$trans("global.select",{attribute:s.$trans("employee.designation.designation")}),modelValue:e.designation,"onUpdate:modelValue":l=>e.designation=l,error:a(n)[`records.${d}.designation`],"onUpdate:error":l=>a(n)[`records.${d}.designation`]=l,options:b.designations,"value-prop":"uuid","label-prop":"name"},null,8,["name","label","modelValue","onUpdate:modelValue","error","onUpdate:error","options"])]),r("div",x,[i(u,{type:"number",modelValue:e.numberOfPositions,"onUpdate:modelValue":l=>e.numberOfPositions=l,name:`records.${d}.numberOfPositions`,label:s.$trans("recruitment.vacancy.props.number_of_positions"),error:a(n)[`records.${d}.numberOfPositions`],"onUpdate:error":l=>a(n)[`records.${d}.numberOfPositions`]=l},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])])])]),_:2},1024))),128)),r("div",ee,[i(C,{design:"primary",onClick:U,class:"cursor-pointer"},{default:y(()=>[k(A(s.$trans("global.add",{attribute:s.$trans("general.record")})),1)]),_:1})]),r("div",te,[r("div",oe,[i(B,{modelValue:o.description,"onUpdate:modelValue":t[4]||(t[4]=e=>o.description=e),name:"description",edit:!!a(c).params.uuid,label:s.$trans("recruitment.vacancy.props.description"),error:a(n).description,"onUpdate:error":t[5]||(t[5]=e=>a(n).description=e)},null,8,["modelValue","edit","label","error"])]),r("div",se,[i(B,{modelValue:o.responsibility,"onUpdate:modelValue":t[6]||(t[6]=e=>o.responsibility=e),name:"responsibility",edit:!!a(c).params.uuid,label:s.$trans("recruitment.vacancy.props.responsibility"),error:a(n).responsibility,"onUpdate:error":t[7]||(t[7]=e=>a(n).responsibility=e)},null,8,["modelValue","edit","label","error"])])]),r("div",ae,[r("div",ne,[i(L,{multiple:"",label:s.$trans("general.file"),module:"job_vacancy",media:o.media,"media-token":o.mediaToken,onIsUpdated:t[8]||(t[8]=e=>o.mediaUpdated=!0),onSetHash:t[9]||(t[9]=e=>o.mediaHash.push(e))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),le={name:"RecruitmentVacancyAction"},pe=Object.assign(le,{setup(F){const c=R();return(m,V)=>{const _=p("PageHeaderAction"),n=p("PageHeader"),b=p("ParentTransition");return v(),D(O,null,[i(n,{title:m.$trans(a(c).meta.trans,{attribute:m.$trans(a(c).meta.label)}),navs:[{label:m.$trans("recruitment.recruitment"),path:"Recruitment"},{label:m.$trans("recruitment.vacancy.vacancy"),path:"RecruitmentVacancyList"}]},{default:y(()=>[i(_,{name:"RecruitmentVacancy",title:m.$trans("recruitment.vacancy.vacancy"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),i(b,{appear:"",visibility:!0},{default:y(()=>[i(ie)]),_:1})],64)}}});export{pe as default};
