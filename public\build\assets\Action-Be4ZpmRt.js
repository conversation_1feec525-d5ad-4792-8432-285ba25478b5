import{u as $,h as O,i as T,H as V,I as P,m as q,l as g,r as i,q as U,o as v,w as u,d as c,e as l,f as r,b as y,s as B,t as f,K as A,a as E,F as I}from"./app-DvIo72ZO.js";const N={class:"grid grid-cols-4 gap-6"},C={class:"col-span-4"},D={class:"col-span-4 sm:col-span-1"},w={class:"col-span-4 sm:col-span-1"},K={class:"col-span-4"},W={class:"mt-4 grid grid-cols-1"},z={class:"col"},G={name:"ResourceLearningMaterialForm"},J=Object.assign(G,{setup(R){const d=$();O(),T();const o={title:"",batches:[],subject:"",description:"",media:[],mediaUpdated:!1,mediaToken:V(),mediaHash:[]},h="resource/learningMaterial/",n=P(h);q(!1);const m=g({subjects:[]});g({selectedBatch:null,subjects:[]});const a=g({...o}),p=g({batches:[],subject:"",isLoaded:!d.params.uuid}),F=s=>{Object.assign(m,s)},k=()=>{a.mediaToken=V(),a.mediaHash=[]},M=s=>{var b,_;let t=s.records.map(j=>j.batch.uuid)||[];Object.assign(o,{...s,batches:t,subject:((_=(b=s.records[0])==null?void 0:b.subject)==null?void 0:_.uuid)||""}),Object.assign(a,A(o)),p.batches=t,p.isLoaded=!0};return(s,t)=>{const b=i("BaseInput"),_=i("BaseSelectSearch"),j=i("BaseSelect"),S=i("BaseEditor"),H=i("MediaUpload"),L=i("FormAction");return v(),U(L,{"pre-requisites":!0,onSetPreRequisites:F,"init-url":h,"init-form":o,form:a,setForm:M,redirect:"ResourceLearningMaterial",onResetMediaFiles:k},{default:u(()=>[c("div",N,[c("div",C,[l(b,{type:"text",modelValue:a.title,"onUpdate:modelValue":t[0]||(t[0]=e=>a.title=e),name:"title",label:s.$trans("resource.learning_material.props.title"),error:r(n).title,"onUpdate:error":t[1]||(t[1]=e=>r(n).title=e),autofocus:""},null,8,["modelValue","label","error"])]),c("div",D,[p.isLoaded?(v(),U(_,{key:0,multiple:"",name:"batches",label:s.$trans("academic.batch.batch"),modelValue:a.batches,"onUpdate:modelValue":t[2]||(t[2]=e=>a.batches=e),error:r(n).batches,"onUpdate:error":t[3]||(t[3]=e=>r(n).batches=e),"value-prop":"uuid","init-search":p.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:u(e=>[B(f(e.value.course.name)+" - "+f(e.value.name),1)]),listOption:u(e=>[B(f(e.option.course.nameWithTerm)+" - "+f(e.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):y("",!0)]),c("div",w,[l(j,{modelValue:a.subject,"onUpdate:modelValue":t[4]||(t[4]=e=>a.subject=e),name:"subject",label:s.$trans("academic.subject.subject"),"label-prop":"name","value-prop":"uuid",options:m.subjects,error:r(n).subject,"onUpdate:error":t[5]||(t[5]=e=>r(n).subject=e)},null,8,["modelValue","label","options","error"])]),c("div",K,[l(S,{modelValue:a.description,"onUpdate:modelValue":t[6]||(t[6]=e=>a.description=e),name:"description",edit:!!r(d).params.uuid,label:s.$trans("resource.learning_material.props.description"),error:r(n).description,"onUpdate:error":t[7]||(t[7]=e=>r(n).description=e)},null,8,["modelValue","edit","label","error"])])]),c("div",W,[c("div",z,[l(H,{multiple:"",label:s.$trans("general.file"),module:"learning_material",media:a.media,"media-token":a.mediaToken,onIsUpdated:t[8]||(t[8]=e=>a.mediaUpdated=!0),onSetHash:t[9]||(t[9]=e=>a.mediaHash.push(e))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),Q={name:"ResourceLearningMaterialAction"},Y=Object.assign(Q,{setup(R){const d=$();return(o,h)=>{const n=i("PageHeaderAction"),m=i("PageHeader"),a=i("ParentTransition");return v(),E(I,null,[l(m,{title:o.$trans(r(d).meta.trans,{attribute:o.$trans(r(d).meta.label)}),navs:[{label:o.$trans("resource.resource"),path:"Resource"},{label:o.$trans("resource.learning_material.learning_material"),path:"ResourceLearningMaterialList"}]},{default:u(()=>[l(n,{name:"ResourceLearningMaterial",title:o.$trans("resource.learning_material.learning_material"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),l(a,{appear:"",visibility:!0},{default:u(()=>[l(J)]),_:1})],64)}}});export{Y as default};
