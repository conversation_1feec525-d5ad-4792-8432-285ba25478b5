import{u as S,I as E,l as x,r as i,q as k,o as m,w as t,d as g,e as r,f as e,i as O,h as M,j as w,a as I,b as V,F as C,v as z,s as o,t as s}from"./app-DvIo72ZO.js";const G={class:"grid grid-cols-3 gap-6"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},Q={name:"LibraryBookIssueReturnForm"},W=Object.assign(Q,{emits:["refresh"],setup(A,{emit:_}){S();const L=_,a={number:"",returnDate:""},D="library/transaction/",f=E(D),b=x({...a}),T=()=>{L("refresh")};return(n,l)=>{const v=i("BaseInput"),u=i("DatePicker"),h=i("FormAction");return m(),k(h,{"no-card":"","no-data-fetch":"",action:"returnBook","keep-adding":!1,"init-url":D,"init-form":a,form:b,"after-submit":T},{default:t(()=>[g("div",G,[g("div",J,[r(v,{type:"text",modelValue:b.number,"onUpdate:modelValue":l[0]||(l[0]=d=>b.number=d),name:"number",label:n.$trans("library.book.props.number"),error:e(f).number,"onUpdate:error":l[1]||(l[1]=d=>e(f).number=d)},null,8,["modelValue","label","error"])]),g("div",K,[r(u,{modelValue:b.returnDate,"onUpdate:modelValue":l[2]||(l[2]=d=>b.returnDate=d),name:"returnDate",label:n.$trans("library.transaction.props.return_date"),"no-clear":"",error:e(f).returnDate,"onUpdate:error":l[3]||(l[3]=d=>e(f).returnDate=d)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),X={class:"space-y-2"},Y={class:"space-y-4"},Z={key:1,class:"px-4 py-2"},tt={name:"LibraryTransactionShow"},at=Object.assign(tt,{setup(A){O();const _=S(),L=M(),a=w("$trans"),D=w("emitter"),f={},b="library/transaction/",T=[{key:"number",label:a("library.book.props.number"),visibility:!0},{key:"title",label:a("library.book.props.title"),visibility:!0},{key:"author",label:a("library.book.props.author"),visibility:!0},{key:"condition",label:a("library.book.props.condition"),visibility:!0},{key:"returnDate",label:a("library.transaction.props.return_date"),visibility:!0}],n=x({...f}),l=v=>{Object.assign(n,v)};return(v,u)=>{const h=i("PageHeaderAction"),d=i("PageHeader"),p=i("ListItemView"),P=i("TextMuted"),R=i("ListContainerVertical"),B=i("BaseCard"),y=i("DataCell"),F=i("DataRow"),U=i("SimpleTable"),j=i("BaseAlert"),H=i("DetailLayoutVertical"),N=i("ShowItem"),q=i("ParentTransition");return m(),I(C,null,[r(d,{title:e(a)(e(_).meta.trans,{attribute:e(a)(e(_).meta.label)}),navs:[{label:e(a)("library.library"),path:"Library"},{label:e(a)("library.transaction.transaction"),path:"LibraryTransactionList"}]},{default:t(()=>[r(h,{name:"LibraryTransaction",title:e(a)("library.transaction.transaction"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),r(q,{appear:"",visibility:!0},{default:t(()=>[r(N,{"init-url":b,uuid:e(_).params.uuid,onSetItem:l,onRedirectTo:u[1]||(u[1]=c=>e(L).push({name:"LibraryTransaction"}))},{default:t(()=>[n.uuid?(m(),k(H,{key:0},{detail:t(()=>[g("div",X,[r(B,{"no-padding":"","no-content-padding":""},{title:t(()=>[o(s(e(a)("library.transaction.transaction")),1)]),action:t(()=>u[2]||(u[2]=[])),default:t(()=>[r(R,null,{default:t(()=>[r(p,{label:e(a)("library.transaction.props.to")},{default:t(()=>[o(s(n.to.label),1)]),_:1},8,["label"]),r(p,{label:e(a)("library.transaction.props.requester")},{default:t(()=>[o(s(n.requester.name)+" ",1),r(P,{block:""},{default:t(()=>[o(s(n.requester.contactNumber),1)]),_:1})]),_:1},8,["label"]),r(p,{label:e(a)("library.transaction.props.issue_date")},{default:t(()=>[o(s(n.issueDate.formatted),1)]),_:1},8,["label"]),r(p,{label:e(a)("library.transaction.props.due_date")},{default:t(()=>[o(s(n.dueDate.formatted),1)]),_:1},8,["label"]),r(p,{label:e(a)("library.transaction.props.remarks")},{default:t(()=>[o(s(n.remarks),1)]),_:1},8,["label"]),r(p,{label:e(a)("general.created_at")},{default:t(()=>[o(s(n.createdAt.formatted),1)]),_:1},8,["label"]),r(p,{label:e(a)("general.updated_at")},{default:t(()=>[o(s(n.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})])]),default:t(()=>[g("div",Y,[r(B,{"no-padding":"","no-content-padding":""},{title:t(()=>[o(s(e(a)("library.transaction.props.details")),1)]),footer:t(()=>u[3]||(u[3]=[])),default:t(()=>[n.records.length>0?(m(),k(U,{key:0,header:T},{default:t(()=>[(m(!0),I(C,null,z(n.records,c=>(m(),k(F,{key:c.uuid},{default:t(()=>[r(y,{name:"number"},{default:t(()=>[o(s(c.copy.number),1)]),_:2},1024),r(y,{name:"title"},{default:t(()=>[o(s(c.copy.book.title),1)]),_:2},1024),r(y,{name:"author"},{default:t(()=>[o(s(c.copy.book.author.name),1)]),_:2},1024),r(y,{name:"condition"},{default:t(()=>{var $;return[o(s((($=c.copy.condition)==null?void 0:$.name)||"-"),1)]}),_:2},1024),r(y,{name:"returnDate"},{default:t(()=>[o(s(c.returnDate.formatted),1)]),_:2},1024)]),_:2},1024))),128))]),_:1})):V("",!0),n.records.length===0?(m(),I("div",Z,[r(j,{design:"info",size:"xs"},{default:t(()=>[o(s(e(a)("general.errors.record_not_found")),1)]),_:1})])):V("",!0)]),_:1}),n.isReturned?V("",!0):(m(),k(B,{key:0},{title:t(()=>[o(s(e(a)("library.transaction.return")),1)]),default:t(()=>[r(W,{uuid:e(_).params.uuid,onRefresh:u[0]||(u[0]=c=>e(D).emit("refreshItem"))},null,8,["uuid"])]),_:1}))])]),_:1})):V("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{at as default};
