import{G as de,u as ve,h as pe,i as me,O as _e,j as Y,m as v,L as fe,n as ge,r as C,z as he,a as c,o as n,e as p,w as u,q as E,b as q,d as e,s as f,x as P,t as i,f as o,F as H,v as R,Y as j,A as W,a9 as X,a8 as ye,a5 as A}from"./app-DvIo72ZO.js";const xe={class:"px-4"},we={key:0,class:"badge bg-info me-2"},be={key:0,class:"empty-state"},ke={class:"empty-state-title"},Ce={class:"empty-state-subtitle"},Ie={class:"mt-4"},Me={class:"text-muted mb-2"},Te={class:"d-flex flex-wrap gap-2 justify-content-center"},De={key:1,class:"messages-list"},qe={class:"message-avatar"},He={class:"message-content"},Re={class:"message-bubble"},Se=["innerHTML"],Ae={key:0,class:"message-actions"},Ne={class:"d-flex flex-wrap gap-1"},Be={class:"message-time"},$e={key:0,class:"message-wrapper"},Ve={class:"chat-input-area"},ze={key:0,class:"quick-commands mb-3"},Ee={class:"d-flex flex-wrap gap-1"},Pe={class:"chat-input-container"},je={class:"chat-input-wrapper"},Fe=["placeholder","disabled","onKeydown"],Le={key:0,class:"fas fa-spinner fa-spin"},Oe={key:1,class:"fas fa-paper-plane"},Ue={class:"chat-input-hint"},Ke={key:0,class:"text-center py-4"},Ge={class:"spinner-border",role:"status"},Qe={class:"visually-hidden"},Je={key:1,class:"text-center py-4"},Ye={class:"text-muted"},We={key:2,class:"list-group"},Xe=["onClick"],Ze={class:"ms-2 me-auto"},et={class:"fw-bold"},tt={class:"text-muted"},st={class:"text-muted"},at={class:"badge bg-primary rounded-pill"},nt={class:"mt-3 d-flex justify-content-end"},ot={name:"AIAssistant"},it=Object.assign(ot,{setup(lt){const g=ve(),F=pe(),b=me(),D=_e(),l=Y("$trans"),L=Y("moment"),y=v([]),m=v(""),h=v(!1),O=v(!1),_=v(null),x=v(!1),N=v(!1),S=v(null),k=v(null),I=v(!1),B=v([]),$=v(!1),V=v(null),U=[{text:"Mark attendance for today"},{text:"Show student statistics"},{text:"Create an announcement"},{text:"Generate a report"},{text:"Help me with..."}],K=async()=>{if(!m.value.trim())return;const a={id:Date.now(),role:"user",content:m.value,timestamp:new Date};y.value.push(a);const t=m.value;m.value="";try{x.value=!0;let r={};if(g.query.contextData)try{r=typeof g.query.contextData=="string"?JSON.parse(g.query.contextData):g.query.contextData}catch(w){console.warn("Failed to parse contextData from URL:",w),r={}}const d=await b.dispatch("ai/assistant/processCommand",{message:t,context:g.query.context||"general",contextData:r}),M={id:Date.now()+1,role:"assistant",content:d.result.message.content,actions:d.result.actions||[],timestamp:new Date};if(y.value.push(M),d.result.conversationId){V.value=d.result.conversationId,b.commit("ai/assistant/SET_CURRENT_CONVERSATION",d.result.conversationId);const w={...g.query,conversationId:d.result.conversationId};F.replace({query:w})}z()}catch(r){const d={id:Date.now()+1,role:"assistant",content:"I apologize, but I encountered an error while processing your request. Please try again.",timestamp:new Date};y.value.push(d),D.error(r.message||"Failed to send message")}finally{x.value=!1}},Z=async a=>{try{N.value=!0;const t=V.value||g.query.conversationId||b.getters["ai/assistant/currentConversation"];if(!t)throw new Error("No active conversation found. Please start a conversation first.");const r=await b.dispatch("ai/assistant/executeAction",{action:a.type,parameters:a.parameters||{},conversationId:t}),d={id:Date.now(),role:"assistant",content:`Action "${a.description}" executed successfully: ${r.result.message}`,timestamp:new Date};y.value.push(d),z()}catch(t){D.error(t.message||"Failed to execute action")}finally{N.value=!1}},G=a=>{m.value=a,Q()},ee=a=>a.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/\n/g,"<br>"),z=()=>{A(()=>{S.value&&(S.value.scrollTop=S.value.scrollHeight)})},Q=()=>{A(()=>{k.value&&k.value.focus()})},te=()=>{m.value+=`
`,A(()=>{J()})},J=()=>{A(()=>{k.value&&(k.value.style.height="auto",k.value.style.height=Math.min(k.value.scrollHeight,120)+"px")})},se=()=>{if("webkitSpeechRecognition"in window||"SpeechRecognition"in window){O.value=!0;const a=window.SpeechRecognition||window.webkitSpeechRecognition;_.value=new a,_.value.continuous=!1,_.value.interimResults=!1,_.value.lang="en-US",_.value.onresult=t=>{const r=t.results[0][0].transcript;m.value=r,h.value=!1},_.value.onerror=()=>{h.value=!1,D.error("Voice recognition error")},_.value.onend=()=>{h.value=!1}}},ae=()=>{_.value&&(h.value?(_.value.stop(),h.value=!1):(_.value.start(),h.value=!0))},ne=async()=>{const a=g.query.conversationId;if(a)try{const r=(await b.dispatch("ai/conversation/fetchMessages",{conversationUuid:a})).data.map(d=>{var M;return{id:d.uuid,role:d.role,content:d.content,timestamp:new Date(d.createdAt),actions:((M=d.metadata)==null?void 0:M.actions)||[]}});y.value=r,V.value=a,b.commit("ai/assistant/SET_CURRENT_CONVERSATION",a),z()}catch{D.error("Failed to load conversation history")}},oe=async()=>{$.value=!0;try{const a=await b.dispatch("ai/conversation/list",{type:"assistant",perPage:20});B.value=a.data}catch{D.error("Failed to load conversation history")}finally{$.value=!1}},ie=a=>{I.value=!1,F.push({name:"AIAssistant",query:{conversationId:a.uuid}})};return fe(I,a=>{a&&oe()}),ge(()=>{se(),ne(),Q()}),(a,t)=>{const r=C("BaseButton"),d=C("PageHeader"),M=C("CardHeader"),w=C("TextMuted"),le=C("BaseCard"),re=C("ParentTransition"),ce=C("BaseModal"),ue=he("tooltip");return n(),c("div",null,[p(d,{title:o(l)("ai.assistant.assistant"),navs:[{label:o(l)("ai.ai"),path:"AIIndex"}]},{actions:u(()=>[O.value?(n(),E(r,{key:0,onClick:ae,design:h.value?"danger":"white",size:"sm"},{default:u(()=>[e("i",{class:P(h.value?"fas fa-microphone-slash":"fas fa-microphone")},null,2),f(" "+i(h.value?o(l)("general.stop"):o(l)("ai.voice_input")),1)]),_:1},8,["design"])):q("",!0)]),_:1},8,["title","navs"]),p(re,{appear:"",visibility:!0},{default:u(()=>[e("div",xe,[p(le,null,{header:u(()=>[p(M,{title:o(l)("ai.assistant.assistant"),icon:"fas fa-robot"},{actions:u(()=>[o(g).query.conversationId?(n(),c("span",we,i(o(l)("ai.conversation.continuing_conversation")),1)):q("",!0),W((n(),E(r,{onClick:t[0]||(t[0]=s=>I.value=!0),design:"white",size:"sm"},{default:u(()=>t[4]||(t[4]=[e("i",{class:"fas fa-history"},null,-1)])),_:1})),[[ue,o(l)("ai.conversation.conversations")]])]),_:1},8,["title"])]),footer:u(()=>[e("div",Ve,[y.value.length>0?(n(),c("div",ze,[p(w,{class:"mb-2"},{default:u(()=>[f(i(o(l)("ai.assistant.quick_commands"))+":",1)]),_:1}),e("div",Ee,[(n(),c(H,null,R(U,s=>p(r,{key:s.text,onClick:T=>G(s.text),design:"white",size:"sm",disabled:x.value},{default:u(()=>[f(i(s.text),1)]),_:2},1032,["onClick","disabled"])),64))])])):q("",!0),e("form",{onSubmit:j(K,["prevent"]),class:"chat-form"},[e("div",Pe,[e("div",je,[W(e("textarea",{"onUpdate:modelValue":t[1]||(t[1]=s=>m.value=s),placeholder:o(l)("ai.assistant.type_message"),disabled:x.value,ref_key:"messageInput",ref:k,class:"chat-input-field",rows:"1",onKeydown:[X(j(K,["exact","prevent"]),["enter"]),X(j(te,["shift","exact"]),["enter"])],onInput:J},null,40,Fe),[[ye,m.value]]),p(r,{type:"submit",design:"primary",disabled:!m.value.trim()||x.value,class:"chat-send-btn"},{default:u(()=>[x.value?(n(),c("i",Le)):(n(),c("i",Oe))]),_:1},8,["disabled"])]),e("div",Ue,[p(w,null,{default:u(()=>[f(i(o(l)("ai.assistant.input_hint")),1)]),_:1})])])],32)])]),default:u(()=>[e("div",{class:"chat-container",ref_key:"chatContainer",ref:S},[y.value.length===0?(n(),c("div",be,[t[5]||(t[5]=e("div",{class:"empty-state-icon"},[e("i",{class:"fas fa-robot"})],-1)),e("h3",ke,i(o(l)("ai.assistant.welcome")),1),e("p",Ce,i(o(l)("ai.assistant.welcome_message")),1),e("div",Ie,[e("p",Me,i(o(l)("ai.assistant.quick_commands"))+":",1),e("div",Te,[(n(),c(H,null,R(U,s=>p(r,{key:s.text,onClick:T=>G(s.text),design:"white",size:"sm",disabled:x.value},{default:u(()=>[f(i(s.text),1)]),_:2},1032,["onClick","disabled"])),64))])])])):(n(),c("div",De,[(n(!0),c(H,null,R(y.value,s=>(n(),c("div",{key:s.id,class:"message-wrapper"},[e("div",{class:P(["message",s.role==="user"?"message-user":"message-assistant"])},[e("div",qe,[e("i",{class:P(s.role==="user"?"fas fa-user":"fas fa-robot")},null,2)]),e("div",He,[e("div",Re,[e("div",{innerHTML:ee(s.content)},null,8,Se),s.actions&&s.actions.length>0?(n(),c("div",Ae,[p(w,{class:"mb-2"},{default:u(()=>[f(i(o(l)("ai.assistant.suggested_actions"))+":",1)]),_:1}),e("div",Ne,[(n(!0),c(H,null,R(s.actions,T=>(n(),E(r,{key:T.type,onClick:rt=>Z(T),design:"white",size:"sm",disabled:N.value},{default:u(()=>[f(i(T.description),1)]),_:2},1032,["onClick","disabled"]))),128))])])):q("",!0)]),e("div",Be,[p(w,null,{default:u(()=>[f(i(o(L)(s.timestamp).format("HH:mm")),1)]),_:2},1024)])])],2)]))),128)),x.value?(n(),c("div",$e,t[6]||(t[6]=[e("div",{class:"message message-assistant"},[e("div",{class:"message-avatar"},[e("i",{class:"fas fa-robot"})]),e("div",{class:"message-content"},[e("div",{class:"message-bubble"},[e("div",{class:"typing-indicator"},[e("span"),e("span"),e("span")])])])],-1)]))):q("",!0)]))],512)]),_:1})])]),_:1}),p(ce,{visibility:I.value,onClose:t[3]||(t[3]=s=>I.value=!1),size:"lg"},{title:u(()=>[f(i(o(l)("ai.conversation.conversations")),1)]),default:u(()=>[$.value?(n(),c("div",Ke,[e("div",Ge,[e("span",Qe,i(o(l)("general.loading")),1)])])):B.value.length===0?(n(),c("div",Je,[t[7]||(t[7]=e("i",{class:"fas fa-comments fa-3x text-muted mb-3"},null,-1)),e("p",Ye,i(o(l)("general.no_records_found")),1)])):(n(),c("div",We,[(n(!0),c(H,null,R(B.value,s=>(n(),c("div",{key:s.uuid,class:"list-group-item list-group-item-action d-flex justify-content-between align-items-start",onClick:T=>ie(s),style:{cursor:"pointer"}},[e("div",Ze,[e("div",et,i(s.title),1),e("small",tt,i(s.latestMessage?s.latestMessage.content.substring(0,100)+"...":o(l)("general.no_messages")),1),t[8]||(t[8]=e("br",null,null,-1)),e("small",st,i(o(L)(s.updatedAt).fromNow()),1)]),e("span",at,i(s.messagesCount||0),1)],8,Xe))),128))])),e("div",nt,[p(r,{onClick:t[2]||(t[2]=s=>I.value=!1),design:"secondary"},{default:u(()=>[f(i(o(l)("general.close")),1)]),_:1})])]),_:1},8,["visibility"])])}}}),ut=de(it,[["__scopeId","data-v-7bc5102b"]]);export{ut as default};
