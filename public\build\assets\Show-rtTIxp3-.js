import{i as H,u as N,h as $,j as F,l as M,r as n,a as y,o as d,e as a,w as e,f as t,q as m,b as _,d as D,F as V,v as O,s,t as c}from"./app-DvIo72ZO.js";const q={class:"space-y-2"},E={class:"grid grid-cols-1 gap-x-4 gap-y-8 px-4 pt-4 sm:grid-cols-2"},U={name:"AcademicDivisionShow"},J=Object.assign(U,{setup(z){H();const p=N(),h=$(),i=F("$trans"),k={},w="academic/division/",A=[{key:"course",label:i("academic.course.course"),visibility:!0},{key:"action",label:"",visibility:!0}],o=M({...k}),C=b=>{Object.assign(o,b)};return(b,u)=>{const x=n("PageHeaderAction"),B=n("PageHeader"),g=n("TextMuted"),r=n("ListItemView"),L=n("ListContainerVertical"),f=n("BaseCard"),v=n("DataCell"),S=n("DataRow"),T=n("SimpleTable"),I=n("BaseDataView"),P=n("DetailLayoutVertical"),R=n("ShowItem"),j=n("ParentTransition");return d(),y(V,null,[a(B,{title:t(i)(t(p).meta.trans,{attribute:t(i)(t(p).meta.label)}),navs:[{label:t(i)("academic.academic"),path:"Academic"},{label:t(i)("academic.division.division"),path:"AcademicDivisionList"}]},{default:e(()=>[a(x,{name:"AcademicDivision",title:t(i)("academic.division.division"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(j,{appear:"",visibility:!0},{default:e(()=>[a(R,{"init-url":w,uuid:t(p).params.uuid,onSetItem:C,onRedirectTo:u[0]||(u[0]=l=>t(h).push({name:"AcademicDivision"}))},{default:e(()=>[o.uuid?(d(),m(P,{key:0},{detail:e(()=>[D("div",q,[a(f,{"no-padding":"","no-content-padding":""},{title:e(()=>[s(c(t(i)("academic.division.division")),1)]),action:e(()=>u[1]||(u[1]=[])),default:e(()=>[a(L,null,{default:e(()=>[a(r,{label:t(i)("academic.division.props.name")},{default:e(()=>[s(c(o.name)+" ",1),o.pgAccount?(d(),m(g,{key:0,block:""},{default:e(()=>[s(c(o.pgAccount),1)]),_:1})):_("",!0)]),_:1},8,["label"]),a(r,{label:t(i)("academic.division.props.code")},{default:e(()=>[s(c(o.code)+" ",1),a(g,{block:""},{default:e(()=>[s(c(o.shortcode),1)]),_:1})]),_:1},8,["label"]),a(r,{label:t(i)("academic.program.program")},{default:e(()=>{var l;return[s(c(((l=o==null?void 0:o.program)==null?void 0:l.name)||"-"),1)]}),_:1},8,["label"]),a(r,{label:t(i)("general.created_at")},{default:e(()=>[s(c(o.createdAt.formatted),1)]),_:1},8,["label"]),a(r,{label:t(i)("general.updated_at")},{default:e(()=>[s(c(o.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})])]),default:e(()=>[a(f,{"no-padding":"","no-content-padding":"","bottom-content-padding":""},{title:e(()=>[s(c(t(i)("global.detail",{attribute:t(i)("academic.division.division")})),1)]),default:e(()=>[o.courses.length>0?(d(),m(T,{key:0,header:A},{default:e(()=>[(d(!0),y(V,null,O(o.courses,l=>(d(),m(S,{key:l.uuid},{default:e(()=>[a(v,{name:"course"},{default:e(()=>[s(c(l.name),1)]),_:2},1024),a(v,{name:"action"})]),_:2},1024))),128))]),_:1})):_("",!0),D("dl",E,[a(I,{class:"col-span-1 sm:col-span-2",label:t(i)("academic.division.props.description")},{default:e(()=>[s(c(o.description),1)]),_:1},8,["label"])])]),_:1})]),_:1})):_("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{J as default};
