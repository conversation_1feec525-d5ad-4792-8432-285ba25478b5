import{i as w,u as M,h as H,I as O,m as P,l as p,n as T,r as c,a as t,o as a,e as u,q as B,b as d,w as r,d as s,F as h,v as C,t as i,x as L,s as A}from"./app-DvIo72ZO.js";const N={class:"font-semibold text-sm dark:text-gray-400"},S={class:"grid grid-cols-1 gap-6"},V={key:0,class:"mb-4"},$=["innerHTML"],j={class:"dark:text-gray-400 flex justify-between"},F={class:"flex items-center"},R={class:"mr-2 text-lg text-gray-600 dark:text-gray-300"},U=["innerHTML"],z={class:"flex gap-2"},D={class:"dark:text-gray-300 font-semibold text-sm italic"},I={key:0},G={key:0,class:"text-success far fa-check-circle fa-lg"},J={key:1,class:"text-danger far fa-times-circle fa-lg"},K={key:1,class:"whitespace-pre-wrap break-words text-sm font-normal"},Q={key:2,class:"ml-1"},W={key:3,class:"ml-1 text-success"},X={name:"ExamOnlineExamSubmission"},ee=Object.assign(X,{setup(Y){const g=w(),f=M(),x=H();O("exam/onlineExam/");const m=P(!1),n=p({});p({});const k=async()=>{await g.dispatch("exam/onlineExam/get",{uuid:f.params.uuid,params:{submission:!0}}).then(o=>{var _,l;if(Object.assign(n,o),!n.hasSubmission||!((_=n.submittedAt)!=null&&_.value)){x.push({name:"ExamOnlineExam"});return}if(!((l=n.resultPublishedAt)!=null&&l.value)){x.push({name:"ExamOnlineExam"});return}m.value=!1}).catch(o=>{m.value=!1})};return T(async()=>{await k()}),(o,_)=>{const l=c("PageHeaderAction"),b=c("PageHeader"),v=c("BaseCard"),y=c("ParentTransition");return a(),t(h,null,[u(b,{title:n.title,navs:[{label:o.$trans("exam.exam"),path:"Exam"},{label:o.$trans("exam.online_exam.online_exam"),path:"ExamOnlineExam"}]},{default:r(()=>[u(l,{name:"ExamOnlineExam",title:o.$trans("exam.online_exam.online_exam"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n.uuid?(a(),B(y,{key:0,appear:"",visibility:!0},{default:r(()=>[u(v,{"is-loading":m.value},{title:r(()=>[A(i(n.title),1)]),action:r(()=>[s("span",N,i(o.$trans("exam.online_exam.obtained_mark"))+": "+i(n.obtainedMark),1)]),default:r(()=>[s("div",S,[(a(!0),t(h,null,C(n.questions,(e,E)=>(a(),t("div",{class:"col-span-3 sm:col-span-1",key:e.uuid},[e.header?(a(),t("div",V,[s("div",{class:"text-lg font-semibold dark:text-gray-300",innerHTML:e.header},null,8,$)])):d("",!0),s("div",j,[s("div",F,[s("span",R,i(E+1)+".",1),s("div",{innerHTML:e.title},null,8,U)]),s("div",z,[s("span",{class:L(["font-semibold",{"text-success":e.obtainedMark>0,"text-danger":e.obtainedMark<0}])},"("+i(e.obtainedMark)+")",3)])]),s("div",D,[e.type.value=="mcq"?(a(),t("span",I,[e.obtainedMark>0?(a(),t("i",G)):(a(),t("i",J))])):d("",!0),e.type.value==="multi_line_question"||e.type.value==="single_line_question"?(a(),t("pre",K,i(e.answer),1)):(a(),t("span",Q,i(e.answer),1)),e.type.value=="mcq"&&e.obtainedMark<=0?(a(),t("span",W,i(e.correctAnswer),1)):d("",!0)])]))),128))])]),_:1},8,["is-loading"])]),_:1})):d("",!0)],64)}}});export{ee as default};
