import{u as _,I as P,l as g,r as m,q as L,o as V,w as b,d as i,e as n,f as r,K as B,a as F,F as j}from"./app-DvIo72ZO.js";const q={class:"grid grid-cols-3 gap-6"},A={class:"col-span-3 sm:col-span-1"},O={class:"col-span-2 sm:col-span-1"},w={class:"col-span-2 sm:col-span-1"},D={class:"col-span-3"},E={class:"col-span-3"},H={name:"MessMealLogForm"},R=Object.assign(H,{setup(v){const u=_(),l={meal:"",date:"",menuItems:[],description:"",remarks:""},c="mess/mealLog/",t=P(c),d=g({meals:[],menuItems:[]}),o=g({...l}),$=g({isLoaded:!u.params.uuid}),k=a=>{Object.assign(d,a)},M=a=>{let e=[];a.records.forEach(p=>{e.push(p.item.uuid)}),Object.assign(l,{...a,meal:a.meal.uuid,date:a.date.value,menuItems:e}),Object.assign(o,B(l)),$.isLoaded=!0};return(a,e)=>{const p=m("BaseSelect"),U=m("DatePicker"),f=m("BaseTextarea"),I=m("FormAction");return V(),L(I,{"pre-requisites":!0,onSetPreRequisites:k,"init-url":c,"init-form":l,form:o,"set-form":M,redirect:"MessMealLog"},{default:b(()=>[i("div",q,[i("div",A,[n(p,{modelValue:o.meal,"onUpdate:modelValue":e[0]||(e[0]=s=>o.meal=s),name:"meal",label:a.$trans("mess.meal.meal"),"label-prop":"name","value-prop":"uuid",options:d.meals,error:r(t).meal,"onUpdate:error":e[1]||(e[1]=s=>r(t).meal=s)},null,8,["modelValue","label","options","error"])]),i("div",O,[n(U,{modelValue:o.date,"onUpdate:modelValue":e[2]||(e[2]=s=>o.date=s),name:"date",label:a.$trans("mess.meal.log.props.date"),"no-clear":"",error:r(t).date,"onUpdate:error":e[3]||(e[3]=s=>r(t).date=s)},null,8,["modelValue","label","error"])]),i("div",w,[n(p,{multiple:"",name:"menuItems",label:a.$trans("global.select",{attribute:a.$trans("mess.menu.item")}),modelValue:o.menuItems,"onUpdate:modelValue":e[4]||(e[4]=s=>o.menuItems=s),options:d.menuItems,error:r(t).menuItems,"onUpdate:error":e[5]||(e[5]=s=>r(t).menuItems=s),"value-prop":"uuid","label-prop":"name"},null,8,["label","modelValue","options","error"])]),i("div",D,[n(f,{rows:1,modelValue:o.description,"onUpdate:modelValue":e[6]||(e[6]=s=>o.description=s),name:"description",label:a.$trans("mess.meal.log.props.description"),error:r(t).description,"onUpdate:error":e[7]||(e[7]=s=>r(t).description=s)},null,8,["modelValue","label","error"])]),i("div",E,[n(f,{rows:1,modelValue:o.remarks,"onUpdate:modelValue":e[8]||(e[8]=s=>o.remarks=s),name:"remarks",label:a.$trans("mess.meal.log.props.remarks"),error:r(t).remarks,"onUpdate:error":e[9]||(e[9]=s=>r(t).remarks=s)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),T={name:"MessMealLogAction"},C=Object.assign(T,{setup(v){const u=_();return(l,c)=>{const t=m("PageHeaderAction"),d=m("PageHeader"),o=m("ParentTransition");return V(),F(j,null,[n(d,{title:l.$trans(r(u).meta.trans,{attribute:l.$trans(r(u).meta.label)}),navs:[{label:l.$trans("mess.mess"),path:"Mess"},{label:l.$trans("mess.meal.log.log"),path:"MessMealLogList"}]},{default:b(()=>[n(t,{name:"MessMealLog",title:l.$trans("mess.meal.log.log"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(o,{appear:"",visibility:!0},{default:b(()=>[n(R)]),_:1})],64)}}});export{C as default};
