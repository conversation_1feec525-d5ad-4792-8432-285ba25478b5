import{l as I,r as n,q as k,o as f,w as e,d as h,e as a,u as j,h as L,j as M,y as V,m as O,f as l,a as w,F as E,v as q,s,b as B,t as i}from"./app-DvIo72ZO.js";const z={class:"grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},Q={__name:"Filter",emits:["hide"],setup(o,{emit:F}){const y=F,v={name:"",alias:"",number:""},d=I({...v});return(g,m)=>{const c=n("BaseInput"),$=n("FilterForm");return f(),k($,{"init-form":v,form:d,onHide:m[3]||(m[3]=p=>y("hide"))},{default:e(()=>[h("div",z,[h("div",G,[a(c,{type:"text",modelValue:d.name,"onUpdate:modelValue":m[0]||(m[0]=p=>d.name=p),name:"name",label:g.$trans("finance.account.props.name")},null,8,["modelValue","label"])]),h("div",J,[a(c,{type:"text",modelValue:d.alias,"onUpdate:modelValue":m[1]||(m[1]=p=>d.alias=p),name:"alias",label:g.$trans("finance.account.props.alias")},null,8,["modelValue","label"])]),h("div",K,[a(c,{type:"text",modelValue:d.number,"onUpdate:modelValue":m[2]||(m[2]=p=>d.number=p),name:"number",label:g.$trans("finance.account.props.number")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},W={name:"EmployeeAccountList"},Y=Object.assign(W,{props:{employee:{type:Object,default(){return{}}}},setup(o){const F=j(),y=L(),v=M("emitter"),d=o;let g=["filter"];(V("employee:edit")||d.employee.selfService)&&g.unshift("create");const m="employee/account/",c=O(!1),$=I({}),p=u=>{Object.assign($,u)};return(u,r)=>{const D=n("PageHeaderAction"),S=n("PageHeader"),A=n("ParentTransition"),N=n("BaseBadge"),b=n("DataCell"),_=n("FloatingMenuItem"),H=n("FloatingMenu"),P=n("DataRow"),R=n("BaseButton"),T=n("DataTable"),U=n("ListItem");return f(),k(U,{"init-url":m,uuid:l(F).params.uuid,onSetItems:p},{header:e(()=>[o.employee.uuid?(f(),k(S,{key:0,title:u.$trans("finance.account.account"),navs:[{label:u.$trans("employee.employee"),path:"Employee"},{label:o.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:o.employee.uuid}}}]},{default:e(()=>[a(D,{url:`employees/${o.employee.uuid}/accounts/`,name:"EmployeeAccount",title:u.$trans("finance.account.account"),actions:l(g),"dropdown-actions":["print","pdf","excel"],onToggleFilter:r[0]||(r[0]=t=>c.value=!c.value)},null,8,["url","title","actions"])]),_:1},8,["title","navs"])):B("",!0)]),filter:e(()=>[a(A,{appear:"",visibility:c.value},{default:e(()=>[a(Q,{onRefresh:r[1]||(r[1]=t=>l(v).emit("listItems")),onHide:r[2]||(r[2]=t=>c.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[a(A,{appear:"",visibility:!0},{default:e(()=>[a(T,{header:$.headers,meta:$.meta,module:"employee.account",onRefresh:r[4]||(r[4]=t=>l(v).emit("listItems"))},{actionButton:e(()=>[l(V)("employee:edit")||o.employee.selfService?(f(),k(R,{key:0,onClick:r[3]||(r[3]=t=>l(y).push({name:"EmployeeAccountCreate"}))},{default:e(()=>[s(i(u.$trans("global.add",{attribute:u.$trans("finance.account.account")})),1)]),_:1})):B("",!0)]),default:e(()=>[(f(!0),w(E,null,q($.data,t=>(f(),k(P,{key:t.uuid,onDoubleClick:C=>l(y).push({name:"EmployeeAccountShow",params:{uuid:t.uuid}})},{default:e(()=>[a(b,{name:"name"},{default:e(()=>[s(i(t.name)+" ",1),t.selfUpload?(f(),k(N,{key:0,design:t.verificationStatus.color},{default:e(()=>[s(i(t.verificationStatus.label),1)]),_:2},1032,["design"])):B("",!0)]),_:2},1024),a(b,{name:"alias"},{default:e(()=>[s(i(t.alias),1)]),_:2},1024),a(b,{name:"number"},{default:e(()=>[s(i(t.number),1)]),_:2},1024),a(b,{name:"bankName"},{default:e(()=>[s(i(t.bankName),1)]),_:2},1024),a(b,{name:"branchName"},{default:e(()=>[s(i(t.branchName),1)]),_:2},1024),a(b,{name:"createdAt"},{default:e(()=>[s(i(t.createdAt.formatted),1)]),_:2},1024),a(b,{name:"action"},{default:e(()=>[a(H,null,{default:e(()=>[a(_,{icon:"fas fa-arrow-circle-right",onClick:C=>l(y).push({name:"EmployeeAccountShow",params:{uuid:o.employee.uuid,muuid:t.uuid}})},{default:e(()=>[s(i(u.$trans("general.show")),1)]),_:2},1032,["onClick"]),l(V)("employee:edit")||o.employee.selfService?(f(),w(E,{key:0},[a(_,{icon:"fas fa-edit",onClick:C=>l(y).push({name:"EmployeeAccountEdit",params:{uuid:o.employee.uuid,muuid:t.uuid}})},{default:e(()=>[s(i(u.$trans("general.edit")),1)]),_:2},1032,["onClick"]),a(_,{icon:"fas fa-copy",onClick:C=>l(y).push({name:"EmployeeAccountDuplicate",params:{uuid:o.employee.uuid,muuid:t.uuid}})},{default:e(()=>[s(i(u.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),a(_,{icon:"fas fa-trash",onClick:C=>l(v).emit("deleteItem",{uuid:o.employee.uuid,moduleUuid:t.uuid})},{default:e(()=>[s(i(u.$trans("general.delete")),1)]),_:2},1032,["onClick"])],64)):B("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1},8,["uuid"])}}});export{Y as default};
