import{i as S,u as v,h as k,l as C,r as o,a as P,o as p,e as a,w as t,f as i,q as V,b as y,d as N,s as n,t as l,F as H}from"./app-DvIo72ZO.js";const I={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},T={name:"AssetBuildingRoomShow"},E=Object.assign(T,{setup(D){S();const d=v(),m=k(),c={},b="asset/building/room/",s=C({...c}),g=e=>{Object.assign(s,e)};return(e,u)=>{const _=o("PageHeaderAction"),f=o("PageHeader"),r=o("BaseDataView"),B=o("BaseButton"),$=o("ShowButton"),h=o("BaseCard"),A=o("ShowItem"),w=o("ParentTransition");return p(),P(H,null,[a(f,{title:e.$trans(i(d).meta.trans,{attribute:e.$trans(i(d).meta.label)}),navs:[{label:e.$trans("asset.asset"),path:"Asset"},{label:e.$trans("asset.building.building"),path:"AssetBuilding"},{label:e.$trans("asset.building.room.room"),path:"AssetBuildingRoomList"}]},{default:t(()=>[a(_,{name:"AssetBuildingRoom",title:e.$trans("asset.building.room.room"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(w,{appear:"",visibility:!0},{default:t(()=>[a(A,{"init-url":b,uuid:i(d).params.uuid,onSetItem:g,onRedirectTo:u[1]||(u[1]=R=>i(m).push({name:"AssetBuildingRoom"}))},{default:t(()=>[s.uuid?(p(),V(h,{key:0},{title:t(()=>[n(l(s.name),1)]),footer:t(()=>[a($,null,{default:t(()=>[a(B,{design:"primary",onClick:u[0]||(u[0]=R=>i(m).push({name:"AssetBuildingRoomEdit",params:{uuid:s.uuid}}))},{default:t(()=>[n(l(e.$trans("general.edit")),1)]),_:1})]),_:1})]),default:t(()=>[N("dl",I,[a(r,{label:e.$trans("asset.building.room.props.name")},{default:t(()=>[n(l(s.name),1)]),_:1},8,["label"]),a(r,{label:e.$trans("asset.building.room.props.number")},{default:t(()=>[n(l(s.number),1)]),_:1},8,["label"]),a(r,{label:e.$trans("asset.building.floor.floor")},{default:t(()=>[n(l(s.floorNameWithBlock),1)]),_:1},8,["label"]),a(r,{class:"col-span-1 sm:col-span-2",label:e.$trans("asset.building.room.props.description")},{default:t(()=>[n(l(s.description),1)]),_:1},8,["label"]),a(r,{label:e.$trans("general.created_at")},{default:t(()=>[n(l(s.createdAt.formatted),1)]),_:1},8,["label"]),a(r,{label:e.$trans("general.updated_at")},{default:t(()=>[n(l(s.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):y("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{E as default};
