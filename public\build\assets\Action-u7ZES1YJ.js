import{u as F,h as I,i as E,H as D,I as K,m as W,l as j,n as z,r as l,q as P,o as h,w as p,d as r,a as L,b as G,f as n,s as B,t as b,e as i,F as y,v as J,N as Q,K as X}from"./app-DvIo72ZO.js";const Y={class:"grid grid-cols-4 gap-6"},Z={class:"col-span-4 sm:col-span-1"},x={class:"col-span-4 sm:col-span-1"},ee={class:"col-span-4 sm:col-span-2"},se={class:"col-span-4 sm:col-span-1"},ae={class:"col-span-4 sm:col-span-1"},te=["onClick"],oe={class:"mt-4 grid grid-cols-4 gap-4"},ne={class:"col-span-4 sm:col-span-1"},re={class:"col-span-4 sm:col-span-3"},le={class:"mt-4"},ie={class:"mt-4 grid grid-cols-1"},de={class:"col"},ce={name:"ResourceLessonPlanForm"},ue=Object.assign(ce,{setup(S){const _=F();I(),E();const d={batches:[],subject:"",topic:"",startDate:"",endDate:"",details:[],media:[],mediaUpdated:!1,mediaToken:D(),mediaHash:[]},k={uuid:D(),heading:"",description:""},v="resource/lessonPlan/",o=K(v);W(!1);const V=j({subjects:[],statuses:[]});j({selectedBatch:null,subjects:[]});const a=j({...d}),g=j({batches:[],subject:"",isLoaded:!_.params.uuid}),H=t=>{Object.assign(V,t)},T=()=>{a.mediaToken=D(),a.mediaHash=[]},R=()=>{a.details.push({...k,uuid:D()}),g.isLoaded=!0},O=async t=>{await Q()&&(a.details.length==1?a.details=[k]:a.details.splice(t,1))},w=t=>{var U,f;let s=t.details.map(m=>({...m})),$=t.records.map(m=>m.batch.uuid)||[];Object.assign(d,{...t,details:s,startDate:t.startDate.value,endDate:t.endDate.value,batches:$,subject:((f=(U=t.records[0])==null?void 0:U.subject)==null?void 0:f.uuid)||""}),Object.assign(a,X(d)),g.batches=$,g.isLoaded=!0};return z(async()=>{_.params.uuid||R()}),(t,s)=>{const $=l("BaseSelectSearch"),U=l("BaseSelect"),f=l("BaseInput"),m=l("DatePicker"),A=l("BaseTextarea"),C=l("BaseFieldset"),q=l("BaseBadge"),M=l("MediaUpload"),N=l("FormAction");return h(),P(N,{"pre-requisites":!0,onSetPreRequisites:H,"init-url":v,"init-form":d,form:a,setForm:w,redirect:"ResourceLessonPlan",onResetMediaFiles:T},{default:p(()=>[r("div",Y,[r("div",Z,[g.isLoaded?(h(),P($,{key:0,multiple:"",name:"batches",label:t.$trans("academic.batch.batch"),modelValue:a.batches,"onUpdate:modelValue":s[0]||(s[0]=e=>a.batches=e),error:n(o).batches,"onUpdate:error":s[1]||(s[1]=e=>n(o).batches=e),"value-prop":"uuid","init-search":g.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:p(e=>[B(b(e.value.course.name)+" - "+b(e.value.name),1)]),listOption:p(e=>[B(b(e.option.course.nameWithTerm)+" - "+b(e.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):G("",!0)]),r("div",x,[i(U,{modelValue:a.subject,"onUpdate:modelValue":s[2]||(s[2]=e=>a.subject=e),name:"subject",label:t.$trans("academic.subject.subject"),"label-prop":"name","value-prop":"uuid",options:V.subjects,error:n(o).subject,"onUpdate:error":s[3]||(s[3]=e=>n(o).subject=e)},null,8,["modelValue","label","options","error"])]),r("div",ee,[i(f,{type:"text",modelValue:a.topic,"onUpdate:modelValue":s[4]||(s[4]=e=>a.topic=e),name:"topic",label:t.$trans("resource.lesson_plan.props.topic"),error:n(o).topic,"onUpdate:error":s[5]||(s[5]=e=>n(o).topic=e),autofocus:""},null,8,["modelValue","label","error"])]),r("div",se,[i(m,{modelValue:a.startDate,"onUpdate:modelValue":s[6]||(s[6]=e=>a.startDate=e),name:"startDate",label:t.$trans("resource.lesson_plan.props.start_date"),"no-clear":"",error:n(o).startDate,"onUpdate:error":s[7]||(s[7]=e=>n(o).startDate=e)},null,8,["modelValue","label","error"])]),r("div",ae,[i(m,{modelValue:a.endDate,"onUpdate:modelValue":s[8]||(s[8]=e=>a.endDate=e),name:"endDate",label:t.$trans("resource.lesson_plan.props.end_date"),"no-clear":"",error:n(o).endDate,"onUpdate:error":s[9]||(s[9]=e=>n(o).endDate=e)},null,8,["modelValue","label","error"])])]),(h(!0),L(y,null,J(a.details,(e,c)=>(h(),P(C,{class:"mt-4",key:e.uuid},{legend:p(()=>[B(b(c+1)+". ",1),r("span",{class:"text-danger ml-2 cursor-pointer",onClick:u=>O(c)},s[12]||(s[12]=[r("i",{class:"fas fa-times-circle"},null,-1)]),8,te)]),default:p(()=>[r("div",oe,[r("div",ne,[i(f,{type:"text",modelValue:e.heading,"onUpdate:modelValue":u=>e.heading=u,name:`details.${c}.heading`,label:t.$trans("resource.lesson_plan.props.heading"),error:n(o)[`details.${c}.heading`],"onUpdate:error":u=>n(o)[`details.${c}.heading`]=u},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),r("div",re,[i(A,{rows:1,modelValue:e.description,"onUpdate:modelValue":u=>e.description=u,name:`details.${c}.description`,label:t.$trans("resource.lesson_plan.props.description"),error:n(o)[`details.${c}.description`],"onUpdate:error":u=>n(o)[`details.${c}.description`]=u},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])])])]),_:2},1024))),128)),r("div",le,[i(q,{design:"primary",onClick:R,class:"cursor-pointer"},{default:p(()=>[B(b(t.$trans("global.add",{attribute:t.$trans("general.detail")})),1)]),_:1})]),r("div",ie,[r("div",de,[i(M,{multiple:"",label:t.$trans("general.file"),module:"lesson_plan",media:a.media,"media-token":a.mediaToken,onIsUpdated:s[10]||(s[10]=e=>a.mediaUpdated=!0),onSetHash:s[11]||(s[11]=e=>a.mediaHash.push(e))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),pe={name:"ResourceLessonPlanAction"},be=Object.assign(pe,{setup(S){const _=F();return(d,k)=>{const v=l("PageHeaderAction"),o=l("PageHeader"),V=l("ParentTransition");return h(),L(y,null,[i(o,{title:d.$trans(n(_).meta.trans,{attribute:d.$trans(n(_).meta.label)}),navs:[{label:d.$trans("resource.resource"),path:"Resource"},{label:d.$trans("resource.lesson_plan.lesson_plan"),path:"ResourceLessonPlanList"}]},{default:p(()=>[i(v,{name:"ResourceLessonPlan",title:d.$trans("resource.lesson_plan.lesson_plan"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),i(V,{appear:"",visibility:!0},{default:p(()=>[i(ue)]),_:1})],64)}}});export{be as default};
