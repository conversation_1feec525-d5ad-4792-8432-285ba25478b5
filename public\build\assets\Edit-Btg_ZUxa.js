import{u as P,h as j,I as O,l as B,n as E,r as d,q as b,o as f,w as p,d as l,e as u,f as s,H as F,b as T,K as H,j as M,m as K,z as R,a as C,A,s as L,t as x,F as N}from"./app-DvIo72ZO.js";import{_ as q,a as z}from"./Og-x8msI_kL.js";const G={class:"grid w-full grid-cols-3 gap-y-2"},J={class:"col-span-3 sm:col-span-1"},Q={class:"mt-4 grid w-full grid-cols-1 gap-y-2"},W={class:"col-span-1"},X={class:"col-span-1"},Y={class:"col-span-1"},Z={name:"SitePageEditContent"},h=Object.assign(Z,{props:{page:{type:Object,default(){return{}}}},emits:["refresh"],setup(U,{emit:k}){P();const w=j(),_=k,m=U,c={name:"",title:"",subTitle:"",content:""},g="site/page/",e=O(g),t=B({...c}),$=()=>{_("refresh")};return E(()=>{Object.assign(c,{name:m.page.name,title:m.page.title,subTitle:m.page.subTitle,content:m.page.content}),Object.assign(t,c)}),(a,n)=>{const V=d("BaseInput"),v=d("MdEditor"),o=d("FormAction");return f(),b(o,{"no-card":"","no-data-fetch":"","button-padding":"",action:"update","keep-adding":!1,"stay-on":!0,"init-url":g,"init-form":c,form:t,"after-submit":$,onCancelled:n[8]||(n[8]=r=>s(w).push({name:"SitePageShow",params:{uuid:U.page.uuid}}))},{default:p(()=>[l("div",G,[l("div",J,[u(V,{type:"text",modelValue:t.name,"onUpdate:modelValue":n[0]||(n[0]=r=>t.name=r),name:"name",label:a.$trans("site.page.props.name"),error:s(e).name,"onUpdate:error":n[1]||(n[1]=r=>s(e).name=r)},null,8,["modelValue","label","error"])])]),l("div",Q,[l("div",W,[u(V,{type:"text",modelValue:t.title,"onUpdate:modelValue":n[2]||(n[2]=r=>t.title=r),name:"title",label:a.$trans("site.page.props.title"),error:s(e).title,"onUpdate:error":n[3]||(n[3]=r=>s(e).title=r)},null,8,["modelValue","label","error"])]),l("div",X,[u(V,{type:"text",modelValue:t.subTitle,"onUpdate:modelValue":n[4]||(n[4]=r=>t.subTitle=r),name:"subTitle",label:a.$trans("site.page.props.sub_title"),error:s(e).subTitle,"onUpdate:error":n[5]||(n[5]=r=>s(e).subTitle=r)},null,8,["modelValue","label","error"])]),l("div",Y,[u(v,{modelValue:t.content,"onUpdate:modelValue":n[6]||(n[6]=r=>t.content=r),error:s(e).content,"onUpdate:error":n[7]||(n[7]=r=>s(e).content=r)},null,8,["modelValue","error"])])])]),_:1},8,["form"])}}}),ee={class:"grid w-full grid-cols-1 gap-y-4"},te={class:"col-span-1"},oe={class:"col-span-1"},se={class:"col-span-1"},ae={class:"col-span-1"},ne={class:"col-span-1"},re={name:"SitePageEditMeta"},ie=Object.assign(re,{props:{page:{type:Object,default(){return{}}}},emits:["refresh"],setup(U,{emit:k}){const w=P();j();const _=k,m=U,c={seo:{robots:"",metaTitle:"",metaDescription:"",metaKeywords:""},media:[],mediaUpdated:!1,mediaToken:F(),mediaHash:[]},g="site/page/",e=O(g),t=B({...c}),$=B({isLoaded:!w.params.uuid}),a=()=>{_("refresh")},n=()=>{t.mediaToken=F(),t.mediaHash=[]},V=()=>{Object.assign(c,{seo:{robots:m.page.seo.robots,metaTitle:m.page.seo.metaTitle,metaDescription:m.page.seo.metaDescription,metaKeywords:m.page.seo.metaKeywords},media:m.page.media,mediaToken:m.page.mediaToken}),Object.assign(t,H(c)),$.isLoaded=!0};return E(()=>{V()}),(v,o)=>{const r=d("BaseSwitch"),D=d("BaseInput"),S=d("BaseTextarea"),I=d("MediaUpload"),y=d("FormAction");return f(),b(y,{"no-card":"","no-data-fetch":"",action:"updateMeta","keep-adding":!1,"stay-on":!0,"init-url":g,"init-form":c,form:t,"after-submit":a,onResetMediaFiles:n},{default:p(()=>[l("div",ee,[l("div",te,[u(r,{modelValue:t.seo.robots,"onUpdate:modelValue":o[0]||(o[0]=i=>t.seo.robots=i),name:"robots",label:v.$trans("site.seo.robots"),error:s(e)["seo.robots"],"onUpdate:error":o[1]||(o[1]=i=>s(e)["seo.robots"]=i)},null,8,["modelValue","label","error"])]),l("div",oe,[u(D,{type:"text",modelValue:t.seo.metaTitle,"onUpdate:modelValue":o[2]||(o[2]=i=>t.seo.metaTitle=i),name:"seoMetaTitle",label:v.$trans("site.seo.meta_title"),error:s(e)["seo.metaTitle"],"onUpdate:error":o[3]||(o[3]=i=>s(e)["seo.metaTitle"]=i)},null,8,["modelValue","label","error"])]),l("div",se,[u(S,{rows:1,modelValue:t.seo.metaDescription,"onUpdate:modelValue":o[4]||(o[4]=i=>t.seo.metaDescription=i),name:"seoMetaDescription",label:v.$trans("site.seo.meta_description"),error:s(e)["seo.metaDescription"],"onUpdate:error":o[5]||(o[5]=i=>s(e)["seo.metaDescription"]=i)},null,8,["modelValue","label","error"])]),l("div",ae,[u(S,{rows:1,modelValue:t.seo.metaKeywords,"onUpdate:modelValue":o[6]||(o[6]=i=>t.seo.metaKeywords=i),name:"seoMetaKeywords",label:v.$trans("site.seo.meta_keywords"),error:s(e)["seo.metaKeywords"],"onUpdate:error":o[7]||(o[7]=i=>s(e)["seo.metaKeywords"]=i)},null,8,["modelValue","label","error"])]),l("div",ne,[$.isLoaded?(f(),b(I,{key:0,multiple:"",label:v.$trans("general.file"),module:"site_page",media:t.media,"media-token":t.mediaToken,onIsUpdated:o[8]||(o[8]=i=>t.mediaUpdated=!0),onSetHash:o[9]||(o[9]=i=>t.mediaHash.push(i))},null,8,["label","media","media-token"])):T("",!0)])])]),_:1},8,["form"])}}}),le={class:"space-y-2"},de={class:"px-4 py-2"},ue={name:"SitePageEdit"},ge=Object.assign(ue,{setup(U){const k=P(),w=j(),_=M("$trans");M("emitter");const m={uuid:""},c="site/page/",g=K(!1),e=B({...m}),t=$=>{Object.assign(e,$)};return($,a)=>{const n=d("BaseButton"),V=d("PageHeaderAction"),v=d("PageHeader"),o=d("BaseCard"),r=d("DetailLayoutVertical"),D=d("ShowItem"),S=d("ParentTransition"),I=R("tooltip");return f(),C(N,null,[e.uuid?(f(),b(v,{key:0,title:e.title,navs:[{label:s(_)("site.site"),path:"Site"},{label:s(_)("site.page.page"),path:"SitePageList"}]},{default:p(()=>[u(V,{name:"SitePage",title:s(_)("site.page.page"),actions:["list"]},{default:p(()=>[A((f(),b(n,{design:"white",onClick:a[0]||(a[0]=y=>s(w).push({name:"SitePageShow",params:{uuid:e.uuid}}))},{default:p(()=>a[7]||(a[7]=[l("i",{class:"fas fa-eye"},null,-1)])),_:1})),[[I,s(_)("general.preview")]])]),_:1},8,["title"])]),_:1},8,["title","navs"])):T("",!0),u(S,{appear:"",visibility:!0},{default:p(()=>[u(D,{"init-url":c,uuid:s(k).params.uuid,onSetItem:t,onRedirectTo:a[5]||(a[5]=y=>s(w).push({name:"SitePage"})),refresh:g.value,onRefreshed:a[6]||(a[6]=y=>g.value=!1)},{default:p(()=>[e.uuid?(f(),b(r,{key:0},{detail:p(()=>[l("div",le,[u(o,null,{default:p(()=>[e.uuid?(f(),b(ie,{key:0,page:e,onRefreshItem:a[1]||(a[1]=y=>g.value=!0)},null,8,["page"])):T("",!0)]),_:1}),u(o,{"no-padding":""},{title:p(()=>[L(x(s(_)("site.assets.custom_og")),1)]),default:p(()=>[e.uuid?(f(),b(z,{key:0,page:e,onRefreshItem:a[2]||(a[2]=y=>g.value=!0)},null,8,["page"])):T("",!0)]),_:1})])]),default:p(()=>[l("div",null,[u(o,{"no-padding":"","no-content-padding":""},{default:p(()=>[e.uuid?(f(),b(q,{key:0,page:e,onRefreshItem:a[3]||(a[3]=y=>g.value=!0)},null,8,["page"])):T("",!0),l("div",de,[e.uuid?(f(),b(h,{key:0,page:e,onRefreshItem:a[4]||(a[4]=y=>g.value=!0)},null,8,["page"])):T("",!0)])]),_:1})])]),_:1})):T("",!0)]),_:1},8,["uuid","refresh"])]),_:1})],64)}}});export{ge as default};
