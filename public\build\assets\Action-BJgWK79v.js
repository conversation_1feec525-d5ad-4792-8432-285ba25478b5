import{I as g,l as T,r as s,q as $,o as d,w as m,d as p,e as o,f as a,u as v,a as B,F as V}from"./app-DvIo72ZO.js";const C={class:"grid grid-cols-3 gap-6"},F={class:"col-span-3 sm:col-span-1"},P={class:"col-span-3"},A={name:"TransportCircleForm"},U=Object.assign(A,{setup(u){const l={name:"",description:""},t="transport/circle/",i=g(t),r=T({...l});return(c,e)=>{const _=s("BaseInput"),f=s("BaseTextarea"),b=s("FormAction");return d(),$(b,{"init-url":t,"init-form":l,form:r,redirect:"TransportCircle"},{default:m(()=>[p("div",C,[p("div",F,[o(_,{type:"text",modelValue:r.name,"onUpdate:modelValue":e[0]||(e[0]=n=>r.name=n),name:"name",label:c.$trans("transport.circle.props.name"),error:a(i).name,"onUpdate:error":e[1]||(e[1]=n=>a(i).name=n),autofocus:""},null,8,["modelValue","label","error"])]),p("div",P,[o(f,{modelValue:r.description,"onUpdate:modelValue":e[2]||(e[2]=n=>r.description=n),name:"description",label:c.$trans("transport.circle.props.description"),error:a(i).description,"onUpdate:error":e[3]||(e[3]=n=>a(i).description=n)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),H={name:"TransportCircleAction"},E=Object.assign(H,{setup(u){const l=v();return(t,i)=>{const r=s("PageHeaderAction"),c=s("PageHeader"),e=s("ParentTransition");return d(),B(V,null,[o(c,{title:t.$trans(a(l).meta.trans,{attribute:t.$trans(a(l).meta.label)}),navs:[{label:t.$trans("transport.transport"),path:"Transport"},{label:t.$trans("transport.circle.circle"),path:"TransportCircleList"}]},{default:m(()=>[o(r,{name:"TransportCircle",title:t.$trans("transport.circle.circle"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),o(e,{appear:"",visibility:!0},{default:m(()=>[o(U)]),_:1})],64)}}});export{E as default};
