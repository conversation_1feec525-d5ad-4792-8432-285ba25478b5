import{u as E,l as C,n as O,r as c,q as f,o as p,w as t,d as _,e as o,b,h as z,j as G,y as q,m as J,f as y,a as I,F as K,v as Q,s as i,t as r}from"./app-DvIo72ZO.js";const W={class:"grid grid-cols-3 gap-6"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={class:"col-span-3 sm:col-span-1"},x={class:"col-span-3 sm:col-span-1"},ee={class:"col-span-3 sm:col-span-1"},te={class:"col-span-3 sm:col-span-1"},ne={class:"col-span-3 sm:col-span-1"},ae={class:"col-span-3 sm:col-span-1"},se={class:"col-span-3 sm:col-span-1"},oe={class:"col-span-3 sm:col-span-1"},le={class:"col-span-3 sm:col-span-1"},re={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(B,{emit:k}){const d=E(),L=k,V=B,D={codeNumber:"",types:[],paymentMethods:[],onlinePaymentMethods:[],period:"",ledgers:[],secondaryLedgers:[],startDate:"",endDate:"",status:"",head:"",pgAccount:""},n=C({...D}),g=C({isLoaded:!(d.query.types||d.query.paymentMethods||d.query.onlinePaymentMethods||d.query.ledgers||d.query.secondaryLedgers)}),$=C({periods:V.preRequisites.periods,types:V.preRequisites.types,heads:V.preRequisites.heads,paymentMethods:V.preRequisites.paymentMethods,onlinePaymentMethods:V.preRequisites.onlinePaymentMethods});return O(async()=>{g.types=d.query.types?d.query.types.split(","):[],g.paymentMethods=d.query.paymentMethods?d.query.paymentMethods.split(","):[],g.onlinePaymentMethods=d.query.onlinePaymentMethods?d.query.onlinePaymentMethods.split(","):[],g.ledgers=d.query.ledgers?d.query.ledgers.split(","):[],g.secondaryLedgers=d.query.secondaryLedgers?d.query.secondaryLedgers.split(","):[],g.isLoaded=!0}),(m,a)=>{const u=c("BaseInput"),l=c("BaseSelect"),F=c("BaseSelectSearch"),T=c("DatePicker"),N=c("FilterForm");return p(),f(N,{"init-form":D,form:n,multiple:["ledgers","types","paymentMethods","onlinePaymentMethods","secondaryLedgers"],onHide:a[12]||(a[12]=s=>L("hide"))},{default:t(()=>[_("div",W,[_("div",X,[o(u,{type:"text",modelValue:n.codeNumber,"onUpdate:modelValue":a[0]||(a[0]=s=>n.codeNumber=s),name:"codeNumber",label:m.$trans("finance.transaction.props.code_number")},null,8,["modelValue","label"])]),_("div",Y,[o(l,{multiple:"",modelValue:n.types,"onUpdate:modelValue":a[1]||(a[1]=s=>n.types=s),name:"types",label:m.$trans("finance.transaction.props.type"),options:$.types},null,8,["modelValue","label","options"])]),_("div",Z,[o(l,{multiple:"",modelValue:n.paymentMethods,"onUpdate:modelValue":a[2]||(a[2]=s=>n.paymentMethods=s),name:"paymentMethods",label:m.$trans("finance.payment_method.payment_method"),"label-prop":"name","value-prop":"uuid",options:$.paymentMethods},null,8,["modelValue","label","options"])]),_("div",x,[o(l,{multiple:"",modelValue:n.onlinePaymentMethods,"onUpdate:modelValue":a[3]||(a[3]=s=>n.onlinePaymentMethods=s),name:"onlinePaymentMethods",label:m.$trans("finance.payment_method.online_payment_method"),"label-prop":"name","value-prop":"uuid",options:$.onlinePaymentMethods},null,8,["modelValue","label","options"])]),_("div",ee,[o(l,{name:"period",label:m.$trans("global.select",{attribute:m.$trans("academic.period.period")}),modelValue:n.period,"onUpdate:modelValue":a[4]||(a[4]=s=>n.period=s),"label-prop":"name","value-prop":"uuid",options:$.periods},null,8,["label","modelValue","options"])]),_("div",te,[g.isLoaded?(p(),f(F,{key:0,multiple:"",name:"ledgers",label:m.$trans("global.select",{attribute:m.$trans("finance.ledger.ledger")}),modelValue:n.ledgers,"onUpdate:modelValue":a[5]||(a[5]=s=>n.ledgers=s),"label-props":"name","value-prop":"uuid","init-search":g.ledgers,"search-action":"finance/ledger/list"},null,8,["label","modelValue","init-search"])):b("",!0)]),_("div",ne,[g.isLoaded?(p(),f(F,{key:0,multiple:"",name:"secondaryLedgers",label:m.$trans("global.select",{attribute:m.$trans("finance.ledger.secondary_ledger")}),modelValue:n.secondaryLedgers,"onUpdate:modelValue":a[6]||(a[6]=s=>n.secondaryLedgers=s),"label-props":"name","value-prop":"uuid","init-search":g.secondaryLedgers,"search-action":"finance/ledger/list"},null,8,["label","modelValue","init-search"])):b("",!0)]),_("div",ae,[o(T,{start:n.startDate,"onUpdate:start":a[7]||(a[7]=s=>n.startDate=s),end:n.endDate,"onUpdate:end":a[8]||(a[8]=s=>n.endDate=s),name:"dateBetween",as:"range",label:m.$trans("general.date_between")},null,8,["start","end","label"])]),_("div",se,[o(l,{name:"status",label:m.$trans("global.select",{attribute:m.$trans("finance.transaction.props.status")}),modelValue:n.status,"onUpdate:modelValue":a[9]||(a[9]=s=>n.status=s),options:B.preRequisites.statuses},null,8,["label","modelValue","options"])]),_("div",oe,[o(l,{name:"head",label:m.$trans("global.select",{attribute:m.$trans("finance.transaction.props.head")}),modelValue:n.head,"onUpdate:modelValue":a[10]||(a[10]=s=>n.head=s),options:B.preRequisites.heads},null,8,["label","modelValue","options"])]),_("div",le,[o(u,{type:"text",modelValue:n.pgAccount,"onUpdate:modelValue":a[11]||(a[11]=s=>n.pgAccount=s),name:"pgAccount",label:m.$trans("finance.config.props.pg_account")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},ie={key:0},de={key:1},ue={name:"FinanceTransactionList"},me=Object.assign(ue,{setup(B){const k=z(),d=G("emitter");let L=["filter"];q("transaction:create")&&L.unshift("create");let V=[];q("transaction:export")&&(V=["print","pdf","excel"]);const D="finance/transaction/",n=C({types:[],paymentMethods:[]}),g=J(!1),$=C({}),m=u=>{Object.assign($,u)},a=u=>{Object.assign(n,u)};return(u,l)=>{const F=c("BaseButton"),T=c("PageHeaderAction"),N=c("PageHeader"),s=c("ParentTransition"),R=c("TextMuted"),U=c("BaseBadge"),v=c("DataCell"),w=c("FloatingMenuItem"),S=c("FloatingMenu"),A=c("DataRow"),j=c("DataTable"),H=c("ListItem");return p(),f(H,{"init-url":D,"pre-requisites":!0,onSetPreRequisites:a,onSetItems:m},{header:t(()=>[o(N,{title:u.$trans("finance.transaction.transaction"),navs:[{label:u.$trans("finance.finance"),path:"Finance"}]},{default:t(()=>[o(T,{url:"finance/transactions/",name:"FinanceTransaction",title:u.$trans("finance.transaction.transaction"),actions:y(L),"dropdown-actions":y(V),onToggleFilter:l[1]||(l[1]=e=>g.value=!g.value)},{after:t(()=>[y(q)("finance:config")?(p(),f(F,{key:0,design:"white",onClick:l[0]||(l[0]=e=>y(k).push({name:"FinanceConfig"}))},{default:t(()=>l[6]||(l[6]=[_("i",{class:"fas fa-cog"},null,-1)])),_:1})):b("",!0)]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:t(()=>[o(s,{appear:"",visibility:g.value},{default:t(()=>[o(re,{onRefresh:l[2]||(l[2]=e=>y(d).emit("listItems")),"pre-requisites":n,onHide:l[3]||(l[3]=e=>g.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:t(()=>[o(s,{appear:"",visibility:!0},{default:t(()=>[o(j,{header:$.headers,footer:$.footers,meta:$.meta,module:"finance.transaction",onRefresh:l[5]||(l[5]=e=>y(d).emit("listItems"))},{actionButton:t(()=>[y(q)("transaction:create")?(p(),f(F,{key:0,onClick:l[4]||(l[4]=e=>y(k).push({name:"FinanceTransactionCreate"}))},{default:t(()=>[i(r(u.$trans("global.add",{attribute:u.$trans("finance.transaction.transaction")})),1)]),_:1})):b("",!0)]),default:t(()=>[(p(!0),I(K,null,Q($.data,e=>(p(),f(A,{key:e.uuid,onDoubleClick:h=>y(k).push({name:"FinanceTransactionShow",params:{uuid:e.uuid}})},{default:t(()=>[o(v,{name:"codeNumber"},{default:t(()=>[i(r(e.codeNumber)+" ",1),e.referenceNumber?(p(),f(R,{key:0,block:""},{default:t(()=>[i(r(e.referenceNumber),1)]),_:2},1024)):b("",!0),e.isRejected?(p(),f(U,{key:1,design:"warning",size:"sm"},{default:t(()=>[i(r(u.$trans("general.rejected")),1)]),_:1})):b("",!0),e.isCancelled?(p(),f(U,{key:2,design:"danger",size:"sm"},{default:t(()=>[i(r(u.$trans("general.cancelled")),1)]),_:1})):b("",!0),e.isOnline&&!e.isCompleted?(p(),f(U,{key:3,design:"danger",size:"sm"},{default:t(()=>[i(r(u.$trans("general.failed")),1)]),_:1})):b("",!0)]),_:2},1024),o(v,{name:"primaryLedger"},{default:t(()=>{var h,M;return[i(r(((M=(h=e.payment)==null?void 0:h.ledger)==null?void 0:M.name)||"-")+" ",1),e.payment?(p(),f(R,{key:0,block:""},{default:t(()=>{var P;return[i(r((P=e.payment)==null?void 0:P.methodName),1)]}),_:2},1024)):b("",!0),e.head?(p(),f(R,{key:1,block:""},{default:t(()=>[i(r(e.head),1)]),_:2},1024)):b("",!0)]}),_:2},1024),o(v,{name:"type"},{default:t(()=>[i(r(e.type.label),1)]),_:2},1024),o(v,{name:"date"},{default:t(()=>[i(r(e.date.formatted),1)]),_:2},1024),o(v,{name:"amount"},{default:t(()=>[i(r(e.amount.formatted),1)]),_:2},1024),o(v,{name:"secondaryLedger"},{default:t(()=>{var h,M,P;return[e.record?(p(),I("span",ie,r((M=(h=e.record)==null?void 0:h.ledger)==null?void 0:M.name),1)):b("",!0),(P=e.transactionable)!=null&&P.name?(p(),I("span",de,[i(r(e.transactionable.name)+" ",1),o(R,{block:""},{default:t(()=>[i(r(e.transactionable.contact),1)]),_:2},1024)])):b("",!0)]}),_:2},1024),o(v,{name:"user"},{default:t(()=>{var h,M;return[i(r(((M=(h=e.user)==null?void 0:h.profile)==null?void 0:M.name)||"-"),1)]}),_:2},1024),o(v,{name:"createdAt"},{default:t(()=>[i(r(e.createdAt.formatted),1)]),_:2},1024),o(v,{name:"action"},{default:t(()=>[o(S,null,{default:t(()=>[o(w,{icon:"fas fa-arrow-circle-right",onClick:h=>y(k).push({name:"FinanceTransactionShow",params:{uuid:e.uuid}})},{default:t(()=>[i(r(u.$trans("general.show")),1)]),_:2},1032,["onClick"]),y(q)("transaction:edit")&&e.isEditable?(p(),f(w,{key:0,icon:"fas fa-edit",onClick:h=>y(k).push({name:"FinanceTransactionEdit",params:{uuid:e.uuid}})},{default:t(()=>[i(r(u.$trans("general.edit")),1)]),_:2},1032,["onClick"])):b("",!0),y(q)("transaction:create")&&e.isEditable?(p(),f(w,{key:1,icon:"fas fa-copy",onClick:h=>y(k).push({name:"FinanceTransactionDuplicate",params:{uuid:e.uuid}})},{default:t(()=>[i(r(u.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):b("",!0),y(q)("transaction:delete")&&e.isEditable?(p(),f(w,{key:2,icon:"fas fa-trash",onClick:h=>y(d).emit("deleteItem",{uuid:e.uuid})},{default:t(()=>[i(r(u.$trans("general.delete")),1)]),_:2},1032,["onClick"])):b("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","footer","meta"])]),_:1})]),_:1})}}});export{me as default};
