import{u as P,l as F,n as O,r as o,q as u,o as m,w as e,d as D,e as i,h as U,j as E,y as f,m as z,f as n,a as G,F as J,v as K,s,t as l,b as _}from"./app-DvIo72ZO.js";const Q={class:"grid grid-cols-3 gap-6"},W={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(I,{emit:d}){P();const $=d,v={name:""},p=F({...v}),h=F({isLoaded:!0});return O(async()=>{h.isLoaded=!0}),(k,c)=>{const C=o("BaseInput"),w=o("BaseSelect"),A=o("FilterForm");return m(),u(A,{"init-form":v,form:p,multiple:[],onHide:c[2]||(c[2]=g=>$("hide"))},{default:e(()=>[D("div",Q,[D("div",W,[i(C,{type:"text",modelValue:p.name,"onUpdate:modelValue":c[0]||(c[0]=g=>p.name=g),name:"name",label:k.$trans("academic.certificate.template.props.name")},null,8,["modelValue","label"])]),D("div",X,[i(w,{modelValue:p.for,"onUpdate:modelValue":c[1]||(c[1]=g=>p.for=g),name:"for",label:k.$trans("academic.certificate.template.props.for"),options:I.preRequisites.for},null,8,["modelValue","label","options"])])])]),_:1},8,["form"])}}},Z={name:"AcademicCertificateTemplateList"},ee=Object.assign(Z,{setup(I){P();const d=U(),$=E("emitter");let v=["filter"];f("certificate-template:create")&&v.unshift("create");let p=[];f("certificate-template:export")&&(p=["print","pdf","excel"]);const h="academic/certificateTemplate/",k=F({for:[]}),c=z(!1),C=F({}),w=a=>{Object.assign(C,a)},A=a=>{Object.assign(k,a)},g=a=>{window.open(`/app/academic/certificate-templates/${a.uuid}/export?action=print`)};return(a,r)=>{const R=o("BaseButton"),M=o("PageHeaderAction"),S=o("PageHeader"),V=o("ParentTransition"),q=o("TextMuted"),B=o("DataCell"),T=o("BaseBadge"),b=o("FloatingMenuItem"),j=o("FloatingMenu"),H=o("DataRow"),L=o("DataTable"),N=o("ListItem");return m(),u(N,{"init-url":h,"pre-requisites":!0,onSetPreRequisites:A,onSetItems:w},{header:e(()=>[i(S,{title:a.$trans("academic.certificate.template.template"),navs:[{label:a.$trans("academic.academic"),path:"Academic"}]},{default:e(()=>[i(M,{url:"academic/certificate-templates/",name:"AcademicCertificateTemplate",title:a.$trans("academic.certificate.template.template"),actions:n(v),"dropdown-actions":n(p),onToggleFilter:r[1]||(r[1]=t=>c.value=!c.value)},{default:e(()=>[n(f)("certificate:read")?(m(),u(R,{key:0,design:"white",onClick:r[0]||(r[0]=t=>n(d).push({name:"AcademicCertificate"}))},{default:e(()=>[s(l(a.$trans("academic.certificate.certificate")),1)]),_:1})):_("",!0)]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[i(V,{appear:"",visibility:c.value},{default:e(()=>[i(Y,{onRefresh:r[2]||(r[2]=t=>n($).emit("listItems")),"pre-requisites":k,onHide:r[3]||(r[3]=t=>c.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:e(()=>[i(V,{appear:"",visibility:!0},{default:e(()=>[i(L,{header:C.headers,meta:C.meta,module:"academic.certificate.template",onRefresh:r[5]||(r[5]=t=>n($).emit("listItems"))},{actionButton:e(()=>[n(f)("certificate-template:create")?(m(),u(R,{key:0,onClick:r[4]||(r[4]=t=>n(d).push({name:"AcademicCertificateTemplateCreate"}))},{default:e(()=>[s(l(a.$trans("global.add",{attribute:a.$trans("academic.certificate.template.template")})),1)]),_:1})):_("",!0)]),default:e(()=>[(m(!0),G(J,null,K(C.data,t=>(m(),u(H,{key:t.uuid,onDoubleClick:y=>n(d).push({name:"AcademicCertificateTemplateShow",params:{uuid:t.uuid}})},{default:e(()=>[i(B,{name:"name"},{default:e(()=>[s(l(t.name)+" ",1),i(q,{block:""},{default:e(()=>[s(l(t.type.label),1)]),_:2},1024)]),_:2},1024),i(B,{name:"for"},{default:e(()=>[s(l(t.for.label)+" ",1),t.hasCustomTemplateFile?(m(),u(T,{key:0,design:"info"},{default:e(()=>[s(l(a.$trans("academic.certificate.template.props.custom_template_file")),1)]),_:1})):_("",!0),t.hasCustomTemplateFile?(m(),u(q,{key:1,block:""},{default:e(()=>[s(l(t.customTemplateFileName),1)]),_:2},1024)):_("",!0)]),_:2},1024),i(B,{name:"createdAt"},{default:e(()=>[s(l(t.createdAt.formatted),1)]),_:2},1024),i(B,{name:"action"},{default:e(()=>[i(j,null,{default:e(()=>[i(b,{icon:"fas fa-arrow-circle-right",onClick:y=>n(d).push({name:"AcademicCertificateTemplateShow",params:{uuid:t.uuid}})},{default:e(()=>[s(l(a.$trans("general.show")),1)]),_:2},1032,["onClick"]),i(b,{icon:"fas fa-print",onClick:y=>g(t)},{default:e(()=>[s(l(a.$trans("general.print")),1)]),_:2},1032,["onClick"]),n(f)("certificate-template:edit")?(m(),u(b,{key:0,icon:"fas fa-edit",onClick:y=>n(d).push({name:"AcademicCertificateTemplateEdit",params:{uuid:t.uuid}})},{default:e(()=>[s(l(a.$trans("general.edit")),1)]),_:2},1032,["onClick"])):_("",!0),n(f)("certificate-template:create")?(m(),u(b,{key:1,icon:"fas fa-copy",onClick:y=>n(d).push({name:"AcademicCertificateTemplateDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[s(l(a.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):_("",!0),n(f)("certificate-template:delete")?(m(),u(b,{key:2,icon:"fas fa-trash",onClick:y=>n($).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[s(l(a.$trans("general.delete")),1)]),_:2},1032,["onClick"])):_("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{ee as default};
