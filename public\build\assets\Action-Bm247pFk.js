import{u as A,l as $,I as q,r as i,q as B,o as b,w as f,d as m,a as y,b as D,f as r,s as U,t as g,e as n,F as k,v as N,K as H}from"./app-DvIo72ZO.js";const E={class:"grid grid-cols-3 gap-6"},R={class:"col-span-3 sm:col-span-1"},C={class:"col-span-3 sm:col-span-1"},I={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3"},W={class:"col-span-4 flex items-end sm:col-span-1"},z={class:"col-span-4 flex items-end space-x-4 sm:col-span-1"},G={key:0,class:"col-span-4 sm:col-span-1"},J={name:"AcademicTimetableForm"},M=Object.assign(J,{setup(S){const v=A(),l={batch:"",effectiveDate:"",room:"",records:[],description:""},T="academic/timetable/",d=$({days:[],classTimings:[]}),s=q(T),o=$({...l}),h=$({batch:"",isLoaded:!v.params.uuid}),F=t=>{Object.assign(d,t),d.days.forEach(a=>{l.records.push({day:a.value,label:a.label,isHoliday:!1,classTiming:""})}),Object.assign(o,H(l))},O=t=>{var V;let a=t.records.map(p=>{var _;return{day:p.day,label:p.label,isHoliday:p.isHoliday,classTiming:((_=p.classTiming)==null?void 0:_.uuid)||""}});Object.assign(l,{batch:t.batch.uuid,effectiveDate:t.effectiveDate.value,room:((V=t.room)==null?void 0:V.uuid)||"",records:a,description:t.description}),Object.assign(o,H(l)),h.batch=t.batch.uuid,h.isLoaded=!0};return(t,a)=>{const V=i("BaseSelectSearch"),p=i("DatePicker"),_=i("BaseSelect"),P=i("BaseTextarea"),L=i("BaseLabel"),j=i("BaseSwitch"),w=i("FormAction");return b(),B(w,{"pre-requisites":!0,onSetPreRequisites:F,"init-url":T,"init-form":l,form:o,"set-form":O,redirect:"AcademicTimetable"},{default:f(()=>[m("div",E,[m("div",R,[h.isLoaded?(b(),B(V,{key:0,name:"batch",label:t.$trans("academic.batch.batch"),modelValue:o.batch,"onUpdate:modelValue":a[0]||(a[0]=e=>o.batch=e),error:r(s).batch,"onUpdate:error":a[1]||(a[1]=e=>r(s).batch=e),"value-prop":"uuid","init-search":h.batch,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:f(e=>[U(g(e.value.course.name)+" - "+g(e.value.name),1)]),listOption:f(e=>[U(g(e.option.course.nameWithTerm)+" - "+g(e.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):D("",!0)]),m("div",C,[n(p,{modelValue:o.effectiveDate,"onUpdate:modelValue":a[2]||(a[2]=e=>o.effectiveDate=e),name:"effectiveDate",label:t.$trans("academic.timetable.props.effective_date"),"no-clear":"",error:r(s).effectiveDate,"onUpdate:error":a[3]||(a[3]=e=>r(s).effectiveDate=e)},null,8,["modelValue","label","error"])]),m("div",I,[n(_,{name:"room",label:t.$trans("asset.building.room.room"),modelValue:o.room,"onUpdate:modelValue":a[4]||(a[4]=e=>o.room=e),error:r(s).room,"onUpdate:error":a[5]||(a[5]=e=>r(s).room=e),"label-prop":"fullName","value-prop":"uuid",options:d.rooms},null,8,["label","modelValue","error","options"])]),m("div",K,[n(P,{rows:1,modelValue:o.description,"onUpdate:modelValue":a[6]||(a[6]=e=>o.description=e),name:"description",label:t.$trans("academic.timetable.props.description"),error:r(s).description,"onUpdate:error":a[7]||(a[7]=e=>r(s).description=e)},null,8,["modelValue","label","error"])])]),(b(!0),y(k,null,N(o.records,(e,u)=>(b(),y("div",{class:"mt-4 grid grid-cols-4 gap-3",key:e.day},[m("div",W,[n(L,{class:"mt-4"},{default:f(()=>[U(g(e.label),1)]),_:2},1024)]),m("div",z,[n(j,{reverse:"",modelValue:e.isHoliday,"onUpdate:modelValue":c=>e.isHoliday=c,name:`records.${u}.isHoliday`,label:t.$trans("employee.attendance.work_shift.props.is_holiday"),error:r(s)[`records.${u}.isHoliday`],"onUpdate:error":c=>r(s)[`records.${u}.isHoliday`]=c},null,8,["modelValue","onUpdate:modelValue","name","label","error","onUpdate:error"])]),e.isHoliday?D("",!0):(b(),y("div",G,[n(_,{name:`records.${u}.classTiming`,placeholder:t.$trans("academic.class_timing.class_timing"),modelValue:e.classTiming,"onUpdate:modelValue":c=>e.classTiming=c,options:d.classTimings,error:r(s)[`records.${u}.classTiming`],"onUpdate:error":c=>r(s)[`records.${u}.classTiming`]=c,"label-prop":"name","value-prop":"uuid"},null,8,["name","placeholder","modelValue","onUpdate:modelValue","options","error","onUpdate:error"])]))]))),128))]),_:1},8,["form"])}}}),Q={name:"AcademicTimetableAction"},Y=Object.assign(Q,{setup(S){const v=A();return(l,T)=>{const d=i("PageHeaderAction"),s=i("PageHeader"),o=i("ParentTransition");return b(),y(k,null,[n(s,{title:l.$trans(r(v).meta.trans,{attribute:l.$trans(r(v).meta.label)}),navs:[{label:l.$trans("academic.academic"),path:"Academic"},{label:l.$trans("academic.timetable.timetable"),path:"AcademicTimetableList"}]},{default:f(()=>[n(d,{name:"AcademicTimetable",title:l.$trans("academic.timetable.timetable"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(o,{appear:"",visibility:!0},{default:f(()=>[n(M)]),_:1})],64)}}});export{Y as default};
