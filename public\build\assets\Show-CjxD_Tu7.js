import{i as P,u as N,h as A,l as H,r as l,a as T,o as p,e as n,w as e,f as c,q as _,b,d as f,s,t as i,F as j}from"./app-DvIo72ZO.js";const E={class:"space-y-2"},R={class:"flex justify-center gap-2"},F={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},M={name:"DisciplineIncidentShow"},U=Object.assign(M,{setup(O){P();const u=N(),g=A(),y={},B="discipline/incident/",t=H({...y}),h=a=>{Object.assign(t,a)};return(a,r)=>{const v=l("PageHeaderAction"),V=l("PageHeader"),o=l("ListItemView"),w=l("ListContainerVertical"),$=l("BaseCard"),m=l("BaseDataView"),D=l("ListMedia"),I=l("BaseButton"),L=l("ShowButton"),C=l("DetailLayoutVertical"),S=l("ShowItem"),k=l("ParentTransition");return p(),T(j,null,[n(V,{title:a.$trans(c(u).meta.trans,{attribute:a.$trans(c(u).meta.label)}),navs:[{label:a.$trans("discipline.discipline"),path:"Discipline"},{label:a.$trans("discipline.incident.incident"),path:"DisciplineIncidentList"}]},{default:e(()=>[n(v,{name:"DisciplineIncident",title:a.$trans("discipline.incident.incident"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(k,{appear:"",visibility:!0},{default:e(()=>[n(S,{"init-url":B,uuid:c(u).params.uuid,onSetItem:h,onRedirectTo:r[1]||(r[1]=d=>c(g).push({name:"DisciplineIncident"}))},{default:e(()=>[t.uuid?(p(),_(C,{key:0},{detail:e(()=>[f("div",E,[n($,{"no-padding":"","no-content-padding":""},{title:e(()=>[s(i(t.title),1)]),action:e(()=>r[2]||(r[2]=[])),default:e(()=>[n(w,null,{default:e(()=>[n(o,{label:a.$trans("discipline.incident.props.category")},{default:e(()=>{var d;return[s(i(((d=t.category)==null?void 0:d.name)||"-"),1)]}),_:1},8,["label"]),n(o,{label:a.$trans("discipline.incident.props.nature")},{default:e(()=>[s(i(t.nature.label),1)]),_:1},8,["label"]),n(o,{label:a.$trans("discipline.incident.props.severtiy")},{default:e(()=>{var d;return[s(i(((d=t.severtiy)==null?void 0:d.label)||"-"),1)]}),_:1},8,["label"]),n(o,{label:a.$trans("student.props.name")},{default:e(()=>[s(i(t.name),1)]),_:1},8,["label"]),n(o,{label:a.$trans("contact.props.contact_number")},{default:e(()=>[s(i(t.contactNumber),1)]),_:1},8,["label"]),n(o,{label:a.$trans("discipline.incident.props.date")},{default:e(()=>[s(i(t.date.formatted),1)]),_:1},8,["label"]),n(o,{label:a.$trans("discipline.incident.props.reported_by")},{default:e(()=>[s(i(t.reportedBy),1)]),_:1},8,["label"]),n(o,{label:a.$trans("general.created_at")},{default:e(()=>[s(i(t.createdAt.formatted),1)]),_:1},8,["label"]),n(o,{label:a.$trans("general.updated_at")},{default:e(()=>[s(i(t.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})])]),default:e(()=>[t.uuid?(p(),_($,{key:0},{title:e(()=>[f("div",R,i(t.title),1)]),footer:e(()=>[n(L,null,{default:e(()=>[n(I,{design:"primary",onClick:r[0]||(r[0]=d=>c(g).push({name:"DisciplineIncidentEdit",params:{uuid:t.uuid}}))},{default:e(()=>[s(i(a.$trans("general.edit")),1)]),_:1})]),_:1})]),default:e(()=>[f("dl",F,[n(m,{label:a.$trans("discipline.incident.props.description"),class:"col-span-1 sm:col-span-2",html:""},{default:e(()=>[s(i(t.description),1)]),_:1},8,["label"]),n(m,{label:a.$trans("discipline.incident.props.action"),class:"col-span-1 sm:col-span-2",html:""},{default:e(()=>[s(i(t.action),1)]),_:1},8,["label"]),t.media.length>0?(p(),_(m,{key:0,class:"col-span-1 sm:col-span-2"},{default:e(()=>[n(D,{media:t.media,url:`/app/discipline/incidents/${t.uuid}/`},null,8,["media","url"])]),_:1})):b("",!0)])]),_:1})):b("",!0)]),_:1})):b("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{U as default};
