import{c as U,a_ as W,u as J,j as Y,l as I,I as Z,n as x,r as _,q as T,o as d,w as l,d as o,b as k,f as m,s as h,t as n,e as u,h as se,i as ue,m as X,a as v,F as C,v as q,H as ie,K as de}from"./app-DvIo72ZO.js";function ee(y=""){var E;let r=U(()=>W.getters["config/config"]("system.currencies"));y||(y=(E=W.getters["config/config"]("system.currency"))==null?void 0:E.name);let i=r.value.find(S=>S.name==y);return i===void 0?{decimal:2,position:"prefix"}:i}function F(y=0,r=""){let i=ee(r);return me(y,i.decimal||2)}function N(y=0,r=""){let i=ee(r);return y=F(y,r),i.position==="prefix"?i.symbol+""+y:y+" "+i.symbol}function me(y,r){r=Math.abs(parseInt(r))||0;var i=Math.pow(10,r);return Math.round(y*i)/i}const pe={class:"grid grid-cols-3 gap-6"},ce={class:"col-span-3 sm:col-span-1"},ye={class:"col-span-3 sm:col-span-1"},fe={class:"mt-4 grid grid-cols-3 gap-6"},_e={class:"col-span-3 sm:col-span-1"},ge={__name:"FilterGenerate",props:{initUrl:{type:String,default:""}},emits:["hide"],setup(y,{emit:r}){const i=J();Y("moment");const E=r,S=y,D={employee:"",startDate:"",endDate:"",ignoreAttendance:!1},f=I({...D}),c=Z(S.initUrl),w=I({employee:"",isLoaded:!i.query.employee});return x(async()=>{w.employee=i.query.employee,w.isLoaded=!0}),(j,p)=>{const R=_("BaseSelectSearch"),M=_("DatePicker"),G=_("BaseSwitch"),K=_("FilterForm");return d(),T(K,{"init-form":D,form:f,onHide:p[7]||(p[7]=s=>E("hide"))},{default:l(()=>[o("div",pe,[o("div",ce,[w.isLoaded?(d(),T(R,{key:0,name:"employee",label:j.$trans("global.select",{attribute:j.$trans("employee.employee")}),modelValue:f.employee,"onUpdate:modelValue":p[0]||(p[0]=s=>f.employee=s),"value-prop":"uuid","init-search":w.employee,"additional-search-query":{self:0},"search-action":"employee/list",error:m(c).employee,"onUpdate:error":p[1]||(p[1]=s=>m(c).employee=s)},{selectedOption:l(s=>[h(n(s.value.name)+" ("+n(s.value.codeNumber)+") ",1)]),listOption:l(s=>[h(n(s.option.name)+" ("+n(s.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search","error"])):k("",!0)]),o("div",ye,[u(M,{start:f.startDate,"onUpdate:start":p[2]||(p[2]=s=>f.startDate=s),end:f.endDate,"onUpdate:end":p[3]||(p[3]=s=>f.endDate=s),name:"dateBetween",as:"range",label:j.$trans("employee.payroll.props.period"),error:m(c).startDate,"onUpdate:error":p[4]||(p[4]=s=>m(c).startDate=s)},null,8,["start","end","label","error"])])]),o("div",fe,[o("div",_e,[u(G,{vertical:"",modelValue:f.ignoreAttendance,"onUpdate:modelValue":p[5]||(p[5]=s=>f.ignoreAttendance=s),name:"ignoreAttendance",label:j.$trans("payroll.props.ignore_attendance"),error:m(c).ignoreAttendance,"onUpdate:error":p[6]||(p[6]=s=>m(c).ignoreAttendance=s)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}},be={class:"font-semibold"},ve={class:"space-y-4"},$e={class:"grid grid-cols-2 gap-4"},He={class:"col-span-2 space-y-4 sm:col-span-1"},he={key:0},Ve={class:"col-span-2 space-y-4 sm:col-span-1"},De={key:0},Ue={key:0},ke={class:"col-span-2 sm:col-span-1"},Ee={class:"text-success flex justify-between text-lg font-semibold"},Se={class:"col-span-2 sm:col-span-1"},Be={class:"text-danger flex justify-between text-lg font-semibold"},Ce={class:"col-span-2 sm:col-span-1"},Fe={class:"text-success flex justify-between text-xl font-semibold"},Te={class:"mt-4 grid grid-cols-2 gap-4"},we={class:"col-span-2 space-y-4 sm:col-span-1"},je={key:0},qe={class:"text-success flex justify-between text-lg font-semibold"},Ae={class:"mt-4 grid grid-cols-2 gap-3"},Le={class:"col-span-3"},Pe={name:"EmployeePayrollForm"},Oe=Object.assign(Pe,{setup(y){const r=J(),i=se(),E=ue(),S=Y("emitter"),D={records:[],remarks:""},f="employee/payroll/",c=Z(f),w=U(()=>$.records.filter(e=>e.payHead.category==="earning")),j=U(()=>$.records.filter(e=>e.payHead.category==="deduction")),p=U(()=>$.records.filter(e=>e.payHead.category==="employee_contribution"));U(()=>$.records.filter(e=>e.payHead.category==="employer_contribution"));const R=U(()=>{let e=0;return $.records.filter(a=>a.payHead.category=="earning").filter(a=>!a.payHead.asTotal).forEach(a=>{e+=F(a.amount)}),N(e)}),M=U(()=>{let e=0;return $.records.filter(a=>a.payHead.category=="deduction").filter(a=>!a.payHead.asTotal).forEach(a=>{e+=F(a.amount)}),$.records.filter(a=>a.payHead.category=="employee_contribution").forEach(a=>{e+=F(a.amount)}),N(e)}),G=U(()=>{let e=0;return $.records.filter(a=>!a.payHead.asTotal).forEach(a=>{a.payHead.category=="earning"?e+=F(a.amount):(a.payHead.category=="deduction"||a.payHead.category=="employee_contribution")&&(e-=F(a.amount))}),N(e)}),K=U(()=>{let e=0;return $.records.filter(a=>a.payHead.category=="employer_contribution").forEach(a=>{e+=F(a.amount)}),N(e)}),s=X(!1),B=X(!1),V=I({}),$=I({...D}),Q=async()=>{B.value=!1,s.value=!0,await E.dispatch(f+"fetchDetail",{params:r.query}).then(e=>{Object.assign(V,{attendanceSummary:e.attendanceSummary,netEarning:e.netEarning.value,netDeduction:e.netDeduction.value,netEmployeeContribution:e.employeeContribution.value,netEmployerContribution:e.employerContribution.value,netSalary:e.netSalary.value}),Object.assign($,{records:e.records}),s.value=!1,B.value=!0}).catch(e=>{s.value=!1})},ae=e=>{var A;Object.assign(V,{codeNumber:e.codeNumber,employee:e.employee,period:e.period,attendanceSummary:e.attendanceSummary,netEarning:e.netEarning.value,netDeduction:e.netDeduction.value,netSalary:e.netSalary.value});let a=e.records.map(H=>{let L={uuid:H.payHead.uuid||ie(),name:H.payHead.name,category:H.payHead.category.value,code:H.payHead.code,asTotal:H.payHead.asTotal};return{uuid:H.uuid,payHead:L,amount:H.amount.value}});Object.assign(D,{records:a,remarks:e.remarks}),Object.assign($,de(D)),i.push({name:r.name,query:{employee:(A=e.employee)==null?void 0:A.uuid,startDate:e.startDate.value,endDate:e.endDate.value}})},te=()=>{B.value=!1},oe=()=>{B.value=!1,S.emit("resetFilter")};return x(async()=>{if(r.params.uuid){B.value=!0;return}r.query.employee&&r.query.startDate&&r.query.endDate&&await Q()}),(e,a)=>{const A=_("ParentTransition"),H=_("ListItemView"),L=_("ListContainerVertical"),z=_("BaseCard"),P=_("BaseLabel"),O=_("BaseInput"),ne=_("BaseTextarea"),le=_("FormAction"),re=_("DetailLayoutVertical");return d(),v(C,null,[u(A,{appear:"",visibility:!0},{default:l(()=>[m(r).params.uuid?k("",!0):(d(),T(ge,{key:0,onAfterFilter:Q,"init-url":f}))]),_:1}),B.value?(d(),T(re,{key:0},{detail:l(()=>[m(r).params.uuid&&V.codeNumber?(d(),T(z,{key:0,"no-padding":"","no-content-padding":""},{title:l(()=>[h(n(e.$trans("employee.payroll.props.code_number"))+" "+n(V.codeNumber),1)]),default:l(()=>[u(L,null,{default:l(()=>[u(H,{label:e.$trans("employee.employee")},{default:l(()=>[h(n(V.employee.name),1)]),_:1},8,["label"]),u(H,{label:e.$trans("employee.department.department")},{default:l(()=>[h(n(V.employee.department),1)]),_:1},8,["label"]),u(H,{label:e.$trans("employee.designation.designation")},{default:l(()=>[h(n(V.employee.designation),1)]),_:1},8,["label"]),u(H,{label:e.$trans("employee.employment_status.employment_status")},{default:l(()=>[h(n(V.employee.employmentStatus),1)]),_:1},8,["label"]),u(H,{label:e.$trans("employee.payroll.props.period")},{default:l(()=>[o("span",be,n(V.period),1)]),_:1},8,["label"])]),_:1})]),_:1})):k("",!0),u(z,{"no-padding":"","no-content-padding":""},{title:l(()=>[h(n(e.$trans("global.summary",{attribute:e.$trans("attendance.attendance")})),1)]),default:l(()=>[u(L,null,{default:l(()=>[(d(!0),v(C,null,q(V.attendanceSummary,t=>(d(),T(H,{align:"right",label:t.name+" ("+t.code+")"},{default:l(()=>[h(n(t.count)+" "+n(e.$trans("list.durations."+t.unit)),1)]),_:2},1032,["label"]))),256))]),_:1})]),_:1})]),default:l(()=>[o("div",ve,[B.value?(d(),T(z,{key:0,"is-loading":s.value},{default:l(()=>[u(le,{"no-card":"","init-url":f,action:m(r).params.uuid?"edit":"generate","init-form":D,form:$,"set-form":ae,"after-reset":te,redirect:"EmployeePayroll",onCompleted:oe},{default:l(()=>[o("div",$e,[o("div",He,[(d(!0),v(C,null,q(w.value,(t,g)=>(d(),v("div",null,[u(P,{class:"text-success"},{default:l(()=>[h(n(t.payHead.name)+" ("+n(t.payHead.code)+") ",1),t.payHead.asTotal?(d(),v("span",he,a[2]||(a[2]=[o("i",{class:"fas fa-not-equal"},null,-1)]))):k("",!0)]),_:2},1024),u(O,{currency:"",modelValue:t.amount,"onUpdate:modelValue":b=>t.amount=b,name:`records.${g}.amount`,placeholder:t.payHead.name,error:m(c)[`records.${g}.amount`],"onUpdate:error":b=>m(c)[`records.${g}.amount`]=b},null,8,["modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"])]))),256))]),o("div",Ve,[(d(!0),v(C,null,q(j.value,(t,g)=>(d(),v("div",null,[u(P,{class:"text-danger"},{default:l(()=>[h(n(t.payHead.name)+" ("+n(t.payHead.code)+") ",1),t.payHead.asTotal?(d(),v("span",De,a[3]||(a[3]=[o("i",{class:"fas fa-not-equal"},null,-1)]))):k("",!0)]),_:2},1024),u(O,{currency:"",modelValue:t.amount,"onUpdate:modelValue":b=>t.amount=b,name:`records.${g}.amount`,placeholder:t.payHead.name,error:m(c)[`records.${g}.amount`],"onUpdate:error":b=>m(c)[`records.${g}.amount`]=b},null,8,["modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"])]))),256)),(d(!0),v(C,null,q(p.value,(t,g)=>(d(),v("div",null,[u(P,{class:"text-danger"},{default:l(()=>[h(n(t.payHead.name)+" ("+n(t.payHead.code)+") ",1),t.payHead.asTotal?(d(),v("span",Ue,a[4]||(a[4]=[o("i",{class:"fas fa-not-equal"},null,-1)]))):k("",!0)]),_:2},1024),u(O,{currency:"",modelValue:t.amount,"onUpdate:modelValue":b=>t.amount=b,name:`records.${g}.amount`,placeholder:t.payHead.name,error:m(c)[`records.${g}.amount`],"onUpdate:error":b=>m(c)[`records.${g}.amount`]=b},null,8,["modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"])]))),256))]),o("div",ke,[o("div",Ee,[o("span",null,n(e.$trans("employee.payroll.salary_structure.props.net_earning")),1),o("span",null,n(R.value),1)])]),o("div",Se,[o("div",Be,[o("span",null,n(e.$trans("employee.payroll.salary_structure.props.net_deduction")),1),o("span",null,n(M.value),1)])]),o("div",Ce,[o("div",Fe,[o("span",null,n(e.$trans("employee.payroll.salary_structure.props.net_salary")),1),o("span",null,n(G.value),1)])])]),a[6]||(a[6]=o("hr",{class:"my-4"},null,-1)),o("div",Te,[o("div",we,[(d(!0),v(C,null,q(p.value,(t,g)=>(d(),v("div",null,[u(P,{class:"text-success"},{default:l(()=>[h(n(t.payHead.name)+" ("+n(t.payHead.code)+") ",1),t.payHead.asTotal?(d(),v("span",je,a[5]||(a[5]=[o("i",{class:"fas fa-not-equal"},null,-1)]))):k("",!0)]),_:2},1024),u(O,{currency:"",modelValue:t.amount,"onUpdate:modelValue":b=>t.amount=b,name:`records.${g}.amount`,placeholder:t.payHead.name,error:m(c)[`records.${g}.amount`],"onUpdate:error":b=>m(c)[`records.${g}.amount`]=b},null,8,["modelValue","onUpdate:modelValue","name","placeholder","error","onUpdate:error"])]))),256)),o("div",qe,[o("span",null,n(e.$trans("employee.payroll.salary_structure.props.net_employer_contribution")),1),o("span",null,n(K.value),1)])])]),o("div",Ae,[o("div",Le,[u(ne,{modelValue:$.remarks,"onUpdate:modelValue":a[0]||(a[0]=t=>$.remarks=t),name:"remarks",label:e.$trans("employee.payroll.props.remarks"),error:m(c).remarks,"onUpdate:error":a[1]||(a[1]=t=>m(c).remarks=t)},null,8,["modelValue","label","error"])])])]),_:1},8,["action","form"])]),_:1},8,["is-loading"])):k("",!0)])]),_:1})):k("",!0)],64)}}}),Ne={name:"EmployeePayrollAction"},Re=Object.assign(Ne,{setup(y){const r=J();return(i,E)=>{const S=_("PageHeaderAction"),D=_("PageHeader"),f=_("ParentTransition");return d(),v(C,null,[u(D,{title:i.$trans(m(r).meta.trans,{attribute:i.$trans(m(r).meta.label)}),navs:[{label:i.$trans("employee.employee"),path:"Employee"},{label:i.$trans("employee.payroll.payroll"),path:"EmployeePayroll"}]},{default:l(()=>[u(S,{name:"EmployeePayroll",title:i.$trans("employee.payroll.payroll"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),u(f,{appear:"",visibility:!0},{default:l(()=>[u(Oe)]),_:1})],64)}}});export{Re as default};
