import{u as _,h as C,H as v,I as H,l as T,r as l,q as V,o as U,w as g,d,b as P,e as r,f as i,J as q,K as R,a as I,F as O}from"./app-DvIo72ZO.js";const x={class:"grid grid-cols-4 gap-6"},L={class:"col-span-4"},M={class:"col-span-4 sm:col-span-2"},N={class:"col-span-4 sm:col-span-2"},w={class:"mt-4 grid grid-cols-1 gap-6"},D={class:"col-span-4"},S={class:"col"},J={name:"CommunicationEmailForm"},K=Object.assign(J,{setup(E){const m=_();C();const a={subject:"",studentAudienceType:"",employeeAudienceType:"",studentAudiences:[],employeeAudiences:[],inclusion:"",exclusion:"",content:"",media:[],mediaUpdated:!1,mediaToken:v(),mediaHash:[]},f="communication/email/",o=H(f),p=T({studentAudienceTypes:[],employeeAudienceTypes:[]}),t=T({...a}),A=T({studentAudiences:[],employeeAudiences:[],isLoaded:!m.params.uuid}),$=s=>{Object.assign(p,s)},j=()=>{t.mediaToken=v(),t.mediaHash=[]},F=s=>{var c,b;let e=s.audiences.filter(u=>u.type=="student").map(u=>u.uuid),y=s.audiences.filter(u=>u.type=="employee").map(u=>u.uuid);Object.assign(a,{...s,studentAudienceType:((c=s.studentAudienceType)==null?void 0:c.value)||"",employeeAudienceType:((b=s.employeeAudienceType)==null?void 0:b.value)||"",studentAudiences:e,employeeAudiences:y}),Object.assign(t,R(a)),A.studentAudiences=e,A.employeeAudiences=y,A.isLoaded=!0};return(s,e)=>{const y=l("BaseInput"),c=l("BaseTextarea"),b=l("AudienceInput"),u=l("BaseEditor"),B=l("MediaUpload"),k=l("FormAction");return U(),V(k,{"pre-requisites":!0,onSetPreRequisites:$,"init-url":f,"init-form":a,form:t,setForm:F,redirect:"CommunicationEmail",onResetMediaFiles:j},{default:g(()=>[d("div",x,[d("div",L,[r(y,{type:"text",modelValue:t.subject,"onUpdate:modelValue":e[0]||(e[0]=n=>t.subject=n),name:"subject",label:s.$trans("communication.email.props.subject"),error:i(o).subject,"onUpdate:error":e[1]||(e[1]=n=>i(o).subject=n),autofocus:""},null,8,["modelValue","label","error"])]),d("div",M,[r(c,{modelValue:t.inclusion,"onUpdate:modelValue":e[2]||(e[2]=n=>t.inclusion=n),name:"inclusion",label:s.$trans("communication.email.props.inclusion"),error:i(o).inclusion,"onUpdate:error":e[3]||(e[3]=n=>i(o).inclusion=n)},null,8,["modelValue","label","error"])]),d("div",N,[r(c,{modelValue:t.exclusion,"onUpdate:modelValue":e[4]||(e[4]=n=>t.exclusion=n),name:"exclusion",label:s.$trans("communication.email.props.exclusion"),error:i(o).exclusion,"onUpdate:error":e[5]||(e[5]=n=>i(o).exclusion=n)},null,8,["modelValue","label","error"])])]),A.isLoaded?(U(),V(b,{key:0,"pre-requisites":p,studentAudienceType:t.studentAudienceType,"onUpdate:studentAudienceType":e[6]||(e[6]=n=>t.studentAudienceType=n),employeeAudienceType:t.employeeAudienceType,"onUpdate:employeeAudienceType":e[7]||(e[7]=n=>t.employeeAudienceType=n),studentAudiences:t.studentAudiences,"onUpdate:studentAudiences":e[8]||(e[8]=n=>t.studentAudiences=n),employeeAudiences:t.employeeAudiences,"onUpdate:employeeAudiences":e[9]||(e[9]=n=>t.employeeAudiences=n),formErrors:i(o),"onUpdate:formErrors":e[10]||(e[10]=n=>q(o)?o.value=n:null)},null,8,["pre-requisites","studentAudienceType","employeeAudienceType","studentAudiences","employeeAudiences","formErrors"])):P("",!0),d("div",w,[d("div",D,[r(u,{modelValue:t.content,"onUpdate:modelValue":e[11]||(e[11]=n=>t.content=n),name:"content",edit:!!i(m).params.uuid,label:s.$trans("communication.email.props.content"),error:i(o).content,"onUpdate:error":e[12]||(e[12]=n=>i(o).content=n)},null,8,["modelValue","edit","label","error"])]),d("div",S,[r(B,{multiple:"",label:s.$trans("general.file"),module:"communication",media:t.media,"media-token":t.mediaToken,onIsUpdated:e[13]||(e[13]=n=>t.mediaUpdated=!0),onSetHash:e[14]||(e[14]=n=>t.mediaHash.push(n))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),z={name:"CommunicationEmailAction"},Q=Object.assign(z,{setup(E){const m=_();return(a,f)=>{const o=l("PageHeaderAction"),p=l("PageHeader"),t=l("ParentTransition");return U(),I(O,null,[r(p,{title:a.$trans(i(m).meta.trans,{attribute:a.$trans(i(m).meta.label)}),navs:[{label:a.$trans("communication.communication"),path:"Communication"},{label:a.$trans("communication.email.email"),path:"CommunicationEmailList"}]},{default:g(()=>[r(o,{name:"CommunicationEmail",title:a.$trans("communication.email.email"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),r(t,{appear:"",visibility:!0},{default:g(()=>[r(K)]),_:1})],64)}}});export{Q as default};
