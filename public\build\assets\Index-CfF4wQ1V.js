import{u as H,l as B,n as R,r,q as v,o as y,w as e,d as C,b as w,s,t as o,e as a,h as T,j as M,y as b,m as O,f as m,a as j,F as q,v as U}from"./app-DvIo72ZO.js";import{_ as z}from"./ModuleDropdown-Cjwc1GLq.js";const G={class:"grid grid-cols-3 gap-6"},J={class:"col-span-3 sm:col-span-1"},K={class:"col-span-3 sm:col-span-1"},Q={__name:"Filter",emits:["hide"],setup(E,{emit:g}){const _=H(),k=g,$={employees:[],startDate:"",endDate:""},u=B({...$}),p=B({employees:[],isLoaded:!_.query.employees});return R(async()=>{p.employees=_.query.employees?_.query.employees.split(","):[],p.isLoaded=!0}),(c,d)=>{const F=r("BaseSelectSearch"),n=r("DatePicker"),i=r("FilterForm");return y(),v(i,{"init-form":$,form:u,multiple:["employees"],onHide:d[3]||(d[3]=l=>k("hide"))},{default:e(()=>[C("div",G,[C("div",J,[p.isLoaded?(y(),v(F,{key:0,multiple:"",name:"employees",label:c.$trans("global.select",{attribute:c.$trans("employee.employee")}),modelValue:u.employees,"onUpdate:modelValue":d[0]||(d[0]=l=>u.employees=l),"value-prop":"uuid","init-search":p.employees,"search-key":"name","search-action":"employee/list"},{selectedOption:e(l=>[s(o(l.value.name)+" ("+o(l.value.codeNumber)+") ",1)]),listOption:e(l=>[s(o(l.option.name)+" ("+o(l.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):w("",!0)]),C("div",K,[a(n,{start:u.startDate,"onUpdate:start":d[1]||(d[1]=l=>u.startDate=l),end:u.endDate,"onUpdate:end":d[2]||(d[2]=l=>u.endDate=l),name:"dateBetween",as:"range",label:c.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},W={class:"text-success font-semibold"},X={name:"EmployeePayrollList"},x=Object.assign(X,{setup(E){const g=T(),_=M("emitter");let k=["filter"];b("payroll:create")&&k.unshift("create");let $=[];b("payroll:export")&&($=["print","pdf","excel"]);const u="employee/payroll/",p=O(!1),c=B({}),d=n=>{Object.assign(c,n)},F=n=>{window.open(`/app/employee/payrolls/${n.uuid}/export?action=print`)};return(n,i)=>{const l=r("PageHeaderAction"),I=r("PageHeader"),P=r("ParentTransition"),f=r("DataCell"),D=r("FloatingMenuItem"),N=r("FloatingMenu"),S=r("DataRow"),L=r("BaseButton"),V=r("DataTable"),A=r("ListItem");return y(),v(A,{"init-url":u,onSetItems:d},{header:e(()=>[a(I,{title:n.$trans("employee.payroll.payroll"),navs:[{label:n.$trans("employee.employee"),path:"Employee"}]},{default:e(()=>[a(l,{url:"employee/payrolls/",name:"EmployeePayroll",title:n.$trans("employee.payroll.payroll"),actions:m(k),"dropdown-actions":m($),onToggleFilter:i[0]||(i[0]=t=>p.value=!p.value)},{moduleOption:e(()=>[a(z)]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[a(P,{appear:"",visibility:p.value},{default:e(()=>[a(Q,{onRefresh:i[1]||(i[1]=t=>m(_).emit("listItems")),onHide:i[2]||(i[2]=t=>p.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[a(P,{appear:"",visibility:!0},{default:e(()=>[a(V,{header:c.headers,meta:c.meta,module:"employee.payroll",onRefresh:i[4]||(i[4]=t=>m(_).emit("listItems"))},{actionButton:e(()=>[m(b)("payroll:create")?(y(),v(L,{key:0,onClick:i[3]||(i[3]=t=>m(g).push({name:"EmployeePayrollCreate"}))},{default:e(()=>[s(o(n.$trans("global.add",{attribute:n.$trans("employee.payroll.payroll")})),1)]),_:1})):w("",!0)]),default:e(()=>[(y(!0),j(q,null,U(c.data,t=>(y(),v(S,{key:t.uuid,onDoubleClick:h=>m(g).push({name:"EmployeePayrollShow",params:{uuid:t.uuid}})},{default:e(()=>[a(f,{name:"codeNumber"},{default:e(()=>[s(o(t.codeNumber),1)]),_:2},1024),a(f,{name:"employee"},{default:e(()=>[s(o(t.employee.name)+" ("+o(t.employee.codeNumber)+") ",1)]),_:2},1024),a(f,{name:"designation"},{default:e(()=>[s(o(t.employee.designation),1)]),_:2},1024),a(f,{name:"startDate"},{default:e(()=>[s(o(t.startDate.formatted),1)]),_:2},1024),a(f,{name:"endDate"},{default:e(()=>[s(o(t.endDate.formatted),1)]),_:2},1024),a(f,{name:"total"},{default:e(()=>[C("span",W,o(t.total.formatted),1)]),_:2},1024),a(f,{name:"createdAt"},{default:e(()=>[s(o(t.createdAt.formatted),1)]),_:2},1024),a(f,{name:"action"},{default:e(()=>[a(N,null,{default:e(()=>[a(D,{icon:"fas fa-print",onClick:h=>F(t)},{default:e(()=>[s(o(n.$trans("general.print")),1)]),_:2},1032,["onClick"]),a(D,{icon:"fas fa-arrow-circle-right",onClick:h=>m(g).push({name:"EmployeePayrollShow",params:{uuid:t.uuid}})},{default:e(()=>[s(o(n.$trans("general.show")),1)]),_:2},1032,["onClick"]),m(b)("payroll:edit")?(y(),v(D,{key:0,icon:"fas fa-edit",onClick:h=>m(g).push({name:"EmployeePayrollEdit",params:{uuid:t.uuid}})},{default:e(()=>[s(o(n.$trans("general.edit")),1)]),_:2},1032,["onClick"])):w("",!0),m(b)("payroll:delete")?(y(),v(D,{key:1,icon:"fas fa-trash",onClick:h=>m(_).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[s(o(n.$trans("general.delete")),1)]),_:2},1032,["onClick"])):w("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{x as default};
