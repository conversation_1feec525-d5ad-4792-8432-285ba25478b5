import{u as g,H as q,I as P,l as b,r as u,q as H,o as $,w as V,d as r,e as n,f as o,K as B,a as j,F as y}from"./app-DvIo72ZO.js";const O={class:"grid grid-cols-3 gap-6"},A={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},I={class:"col-span-3 sm:col-span-1"},M={class:"col-span-2 sm:col-span-1"},w={class:"col-span-2 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},L={class:"grid grid-cols-1"},C={class:"col"},N={name:"StudentQualificationForm"},K=Object.assign(N,{setup(p){const d=g(),i={level:"",course:"",institute:"",affiliatedTo:"",startDate:"",endDate:"",result:"",media:[],mediaUpdated:!1,mediaToken:q(),mediaHash:[]},v="student/qualification/",s=P(v),c=b({levels:[]}),a=b({...i}),D=b({level:"",isLoaded:!d.params.muuid}),_=l=>{Object.assign(c,l)},S=()=>{a.mediaToken=q(),a.mediaHash=[]},T=l=>{var e,f,m;Object.assign(i,{...l,level:(e=l.level)==null?void 0:e.uuid,startDate:((f=l.startDate)==null?void 0:f.value)||"",endDate:((m=l.endDate)==null?void 0:m.value)||""}),Object.assign(a,B(i)),D.level=l.level.name,D.isLoaded=!0};return(l,e)=>{const f=u("BaseSelect"),m=u("BaseInput"),U=u("DatePicker"),k=u("MediaUpload"),F=u("FormAction");return $(),H(F,{"no-data-fetch":"","pre-requisites":!0,onSetPreRequisites:_,"init-url":v,uuid:o(d).params.uuid,"module-uuid":o(d).params.muuid,"init-form":i,form:a,"set-form":T,redirect:{name:"StudentQualification",params:{uuid:o(d).params.uuid}},onResetMediaFiles:S},{default:V(()=>[r("div",O,[r("div",A,[n(f,{modelValue:a.level,"onUpdate:modelValue":e[0]||(e[0]=t=>a.level=t),name:"level",label:l.$trans("student.qualification_level.qualification_level"),"label-prop":"name","value-prop":"uuid",options:c.levels,error:o(s).level,"onUpdate:error":e[1]||(e[1]=t=>o(s).level=t)},null,8,["modelValue","label","options","error"])]),r("div",Q,[n(m,{type:"text",modelValue:a.course,"onUpdate:modelValue":e[2]||(e[2]=t=>a.course=t),name:"course",label:l.$trans("student.qualification.props.course"),error:o(s).course,"onUpdate:error":e[3]||(e[3]=t=>o(s).course=t),autofocus:""},null,8,["modelValue","label","error"])]),r("div",R,[n(m,{type:"text",modelValue:a.institute,"onUpdate:modelValue":e[4]||(e[4]=t=>a.institute=t),name:"institute",label:l.$trans("student.qualification.props.institute"),error:o(s).institute,"onUpdate:error":e[5]||(e[5]=t=>o(s).institute=t),autofocus:""},null,8,["modelValue","label","error"])]),r("div",I,[n(m,{type:"text",modelValue:a.affiliatedTo,"onUpdate:modelValue":e[6]||(e[6]=t=>a.affiliatedTo=t),name:"affiliatedTo",label:l.$trans("student.qualification.props.affiliated_to"),error:o(s).affiliatedTo,"onUpdate:error":e[7]||(e[7]=t=>o(s).affiliatedTo=t),autofocus:""},null,8,["modelValue","label","error"])]),r("div",M,[n(U,{modelValue:a.startDate,"onUpdate:modelValue":e[8]||(e[8]=t=>a.startDate=t),name:"startDate",label:l.$trans("student.qualification.props.start_date"),error:o(s).startDate,"onUpdate:error":e[9]||(e[9]=t=>o(s).startDate=t)},null,8,["modelValue","label","error"])]),r("div",w,[n(U,{modelValue:a.endDate,"onUpdate:modelValue":e[10]||(e[10]=t=>a.endDate=t),name:"endDate",label:l.$trans("student.qualification.props.end_date"),error:o(s).endDate,"onUpdate:error":e[11]||(e[11]=t=>o(s).endDate=t)},null,8,["modelValue","label","error"])]),r("div",E,[n(m,{type:"text",modelValue:a.result,"onUpdate:modelValue":e[12]||(e[12]=t=>a.result=t),name:"result",label:l.$trans("student.qualification.props.result"),error:o(s).result,"onUpdate:error":e[13]||(e[13]=t=>o(s).result=t),autofocus:""},null,8,["modelValue","label","error"])])]),r("div",L,[r("div",C,[n(k,{multiple:"",label:l.$trans("general.file"),module:"qualification",media:a.media,"media-token":a.mediaToken,onIsUpdated:e[14]||(e[14]=t=>a.mediaUpdated=!0),onSetHash:e[15]||(e[15]=t=>a.mediaHash.push(t))},null,8,["label","media","media-token"])])])]),_:1},8,["uuid","module-uuid","form","redirect"])}}}),z={name:"StudentQualificationAction"},J=Object.assign(z,{props:{student:{type:Object,default(){return{}}}},setup(p){const d=g();return(i,v)=>{const s=u("PageHeaderAction"),c=u("PageHeader"),a=u("ParentTransition");return $(),j(y,null,[n(c,{title:i.$trans(o(d).meta.trans,{attribute:i.$trans(o(d).meta.label)}),navs:[{label:i.$trans("student.student"),path:"StudentList"},{label:p.student.contact.name,path:{name:"StudentShow",params:{uuid:p.student.uuid}}},{label:i.$trans("student.qualification.qualification"),path:{name:"StudentQualification",params:{uuid:p.student.uuid}}}]},{default:V(()=>[n(s,{name:"StudentQualification",title:i.$trans("student.qualification.qualification"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(a,{appear:"",visibility:!0},{default:V(()=>[n(K)]),_:1})],64)}}});export{J as default};
