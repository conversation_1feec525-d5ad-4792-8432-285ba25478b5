import{u as P,l as A,n as W,r as u,q as f,o as m,w as e,d as q,e as o,s as i,a as B,b as v,t as n,h as z,i as G,j as J,y as K,m as T,f as x,F as Q,v as X}from"./app-DvIo72ZO.js";import"./lodash-BPUmB9Gy.js";const Y={class:"grid grid-cols-3 gap-6"},Z={class:"col-span-3 sm:col-span-1"},ee={key:0},te={key:0},ae={class:"col-span-3 sm:col-span-1"},se={class:"col-span-3 sm:col-span-1"},ne={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(R,{emit:S}){const b=P(),g=S,w={batches:[],exams:[],attempt:""},d=A({...w}),c=A({batches:[],exams:[],isLoaded:!(b.query.batches||b.query.exams)});return W(async()=>{c.batches=b.query.batches?b.query.batches.split(","):[],c.exams=b.query.exams?b.query.exams.split(","):[],c.isLoaded=!0}),(p,r)=>{const h=u("BaseSelect"),k=u("BaseSelectSearch"),V=u("FilterForm");return m(),f(V,{"init-form":w,form:d,multiple:["exams","batches"],onHide:r[3]||(r[3]=s=>g("hide"))},{default:e(()=>[q("div",Y,[q("div",Z,[o(h,{multiple:"",modelValue:d.exams,"onUpdate:modelValue":r[0]||(r[0]=s=>d.exams=s),name:"exams",label:p.$trans("global.select",{attribute:p.$trans("exam.exam")}),"track-by":["name"],"value-prop":"uuid",options:R.preRequisites.exams},{selectedOption:e(s=>{var _,$;return[i(n(s.value.name)+" ",1),s.value.term?(m(),B("span",ee,"("+n((($=(_=s.value.term)==null?void 0:_.division)==null?void 0:$.name)||p.$trans("general.all"))+")",1)):v("",!0)]}),listOption:e(s=>{var _,$;return[i(n(s.option.name)+" ",1),s.option.term?(m(),B("span",te,"("+n((($=(_=s.option.term)==null?void 0:_.division)==null?void 0:$.name)||p.$trans("general.all"))+")",1)):v("",!0)]}),_:1},8,["modelValue","label","options"])]),q("div",ae,[c.isLoaded?(m(),f(h,{key:0,modelValue:d.attempt,"onUpdate:modelValue":r[1]||(r[1]=s=>d.attempt=s),name:"attempt",label:p.$trans("exam.schedule.props.attempt"),options:R.preRequisites.attempts},null,8,["modelValue","label","options"])):v("",!0)]),q("div",se,[c.isLoaded?(m(),f(k,{key:0,multiple:"",name:"batches",label:p.$trans("global.select",{attribute:p.$trans("academic.batch.batch")}),modelValue:d.batches,"onUpdate:modelValue":r[2]||(r[2]=s=>d.batches=s),"value-prop":"uuid","init-search":c.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:e(s=>[i(n(s.value.course.name)+" "+n(s.value.name),1)]),listOption:e(s=>[i(n(s.option.course.nameWithTerm)+" "+n(s.option.name),1)]),_:1},8,["label","modelValue","init-search"])):v("",!0)])])]),_:1},8,["form"])}}},oe={name:"ExamFormList"},re=Object.assign(oe,{setup(R){const S=z(),b=G(),g=J("emitter");let w=["filter"],d=[];K("form:export")&&(d=["print","pdf","excel"]);const c="exam/form/",p=A({attempts:[],exams:[]}),r=T(!1),h=T(!1),k=A({}),V=a=>{Object.assign(k,a)},s=a=>{Object.assign(p,a)},_=async a=>{r.value=!0,await b.dispatch(c+"printExamForm",{uuid:a}).then(l=>{r.value=!1,window.open("/print").document.write(l)}).catch(l=>{r.value=!1})},$=async a=>{r.value=!0,await b.dispatch(c+"printAdmitCard",{uuid:a}).then(l=>{r.value=!1,window.open("/print").document.write(l)}).catch(l=>{r.value=!1})},D=async(a,l)=>{g.emit("actionItem",{uuid:a.uuid,action:"updateStatus",data:{status:l},confirmation:!0})};return(a,l)=>{const O=u("PageHeaderAction"),E=u("PageHeader"),I=u("ParentTransition"),M=u("BaseBadge"),L=u("TextMuted"),y=u("DataCell"),C=u("FloatingMenuItem"),N=u("FloatingMenu"),j=u("DataRow"),H=u("DataTable"),U=u("ListItem");return m(),f(U,{"init-url":c,"pre-requisites":!0,onSetPreRequisites:s,onSetItems:V},{header:e(()=>[o(E,{title:a.$trans("exam.form.form"),navs:[{label:a.$trans("exam.exam"),path:"Exam"}]},{default:e(()=>[o(O,{url:"exam/forms/",name:"ExamForm",title:a.$trans("exam.form.form"),actions:x(w),"dropdown-actions":x(d),onToggleFilter:l[0]||(l[0]=t=>h.value=!h.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[o(I,{appear:"",visibility:h.value},{default:e(()=>[o(ne,{onRefresh:l[1]||(l[1]=t=>x(g).emit("listItems")),"pre-requisites":p,onHide:l[2]||(l[2]=t=>h.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:e(()=>[o(I,{appear:"",visibility:!0},{default:e(()=>[o(H,{header:k.headers,meta:k.meta,module:"exam.form",onRefresh:l[3]||(l[3]=t=>x(g).emit("listItems"))},{default:e(()=>[(m(!0),B(Q,null,X(k.data,t=>(m(),f(j,{key:t.uuid,onDoubleClick:F=>x(S).push({name:"ExamFormShow",params:{uuid:t.uuid}})},{default:e(()=>[o(y,{name:"schedule"},{default:e(()=>[i(n(t.schedule.exam.name)+" ",1),t.schedule.isReassessment?(m(),f(M,{key:0},{default:e(()=>[i(n(a.$trans("exam.schedule.reassessment")+" ("+t.schedule.attempt.label+")"),1)]),_:2},1024)):v("",!0),t.schedule.exam.term?(m(),f(L,{key:1,block:""},{default:e(()=>[i(n(t.schedule.exam.term.name),1)]),_:2},1024)):v("",!0)]),_:2},1024),o(y,{name:"student"},{default:e(()=>[i(n(t.student.name)+" ",1),o(L,{block:""},{default:e(()=>[i(n(t.student.codeNumber),1)]),_:2},1024)]),_:2},1024),o(y,{name:"batch"},{default:e(()=>[i(n(t.student.courseName+" "+t.student.batchName),1)]),_:2},1024),o(y,{name:"submittedAt"},{default:e(()=>[i(n(t.submittedAt.formatted||"-"),1)]),_:2},1024),o(y,{name:"approvedAt"},{default:e(()=>[i(n(t.approvedAt.formatted||"-"),1)]),_:2},1024),o(y,{name:"action"},{default:e(()=>[o(N,null,{default:e(()=>[o(C,{icon:"fas fa-print",onClick:F=>_(t.uuid)},{default:e(()=>[i(n(a.$trans("global.print",{attribute:a.$trans("exam.schedule.form")})),1)]),_:2},1032,["onClick"]),t.approvedAt.value?(m(),f(C,{key:0,icon:"fas fa-id-card",onClick:F=>$(t.uuid)},{default:e(()=>[i(n(a.$trans("global.print",{attribute:a.$trans("exam.admit_card.admit_card")})),1)]),_:2},1032,["onClick"])):v("",!0),t.approvedAt.value?(m(),f(C,{key:1,icon:"fas fa-times-circle",onClick:F=>D(t,"disapprove")},{default:e(()=>[i(n(a.$trans("global.disapprove",{attribute:a.$trans("exam.form.form")})),1)]),_:2},1032,["onClick"])):v("",!0),t.approvedAt.value?v("",!0):(m(),f(C,{key:2,icon:"fas fa-check-circle",onClick:F=>D(t,"approve")},{default:e(()=>[i(n(a.$trans("global.approve",{attribute:a.$trans("exam.form.form")})),1)]),_:2},1032,["onClick"])),o(C,{icon:"fas fa-trash",onClick:F=>x(g).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[i(n(a.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{re as default};
