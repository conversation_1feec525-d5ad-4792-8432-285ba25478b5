import{u as v,h,r as f,a as T,o as t,q as l,b as c,f as e,y as i,w as u,s as d,t as p,F as k}from"./app-DvIo72ZO.js";const V={name:"TransportVehicleModuleDropdown"},C=Object.assign(V,{setup($){const o=v(),n=h();return(s,r)=>{const a=f("DropdownItem");return t(),T(k,null,[e(o).name!="TransportVehicleList"&&e(i)("vehicle:read")?(t(),l(a,{key:0,onClick:r[0]||(r[0]=m=>e(n).push({name:"TransportVehicle"}))},{default:u(()=>[d(p(s.$trans("transport.vehicle.vehicle")),1)]),_:1})):c("",!0),e(o).name!="TransportVehicleDocumentList"&&e(i)("vehicle-document:read")?(t(),l(a,{key:1,onClick:r[1]||(r[1]=m=>e(n).push({name:"TransportVehicleDocument"}))},{default:u(()=>[d(p(s.$trans("transport.vehicle.document.document")),1)]),_:1})):c("",!0),e(o).name!="TransportVehicleTravelRecordList"&&e(i)("vehicle-travel-record:read")?(t(),l(a,{key:2,onClick:r[2]||(r[2]=m=>e(n).push({name:"TransportVehicleTravelRecord"}))},{default:u(()=>[d(p(s.$trans("transport.vehicle.travel_record.travel_record")),1)]),_:1})):c("",!0),e(o).name!="TransportVehicleFuelRecordList"&&e(i)("vehicle-fuel-record:read")?(t(),l(a,{key:3,onClick:r[3]||(r[3]=m=>e(n).push({name:"TransportVehicleFuelRecord"}))},{default:u(()=>[d(p(s.$trans("transport.vehicle.fuel_record.fuel_record")),1)]),_:1})):c("",!0),e(o).name!="TransportVehicleServiceRecordList"&&e(i)("vehicle-service-record:read")?(t(),l(a,{key:4,onClick:r[4]||(r[4]=m=>e(n).push({name:"TransportVehicleServiceRecord"}))},{default:u(()=>[d(p(s.$trans("transport.vehicle.service_record.service_record")),1)]),_:1})):c("",!0)],64)}}});export{C as _};
