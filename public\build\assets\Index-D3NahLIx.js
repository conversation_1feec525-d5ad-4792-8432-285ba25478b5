import{l as D,r as n,q as d,o as i,w as e,d as F,u as R,h as j,j as B,m as C,e as s,f,a as k,F as E,v as L,s as g,t as u,b}from"./app-DvIo72ZO.js";const N={__name:"Filter",emits:["hide"],setup(r,{emit:y}){const p=y,c={},_=D({...c});return(m,o)=>{const v=n("FilterForm");return i(),d(v,{"init-form":c,form:_,onHide:o[0]||(o[0]=l=>p("hide"))},{default:e(()=>o[1]||(o[1]=[F("div",{class:"grid grid-cols-3 gap-6"},[F("div",{class:"col-span-3 sm:col-span-1"})],-1)])),_:1},8,["form"])}}},V={class:"text-xs"},A={key:0,class:"text-xs"},O={name:"EmployeeInchargeList"},M=Object.assign(O,{props:{employee:{type:Object,default(){return{}}}},setup(r){const y=R();j();const p=B("emitter");let c=[];const _="employee/incharge/",m=C(!1),o=D({}),v=l=>{Object.assign(o,l)};return(l,a)=>{const I=n("PageHeaderAction"),T=n("PageHeader"),$=n("ParentTransition"),x=n("DataCell"),h=n("TextMuted"),w=n("DataRow"),H=n("DataTable"),P=n("ListItem");return i(),d(P,{"init-url":_,uuid:f(y).params.uuid,onSetItems:v},{header:e(()=>[r.employee.uuid?(i(),d(T,{key:0,title:l.$trans("employee.incharge.incharge"),navs:[{label:l.$trans("employee.employee"),path:"Employee"},{label:r.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:r.employee.uuid}}}]},{default:e(()=>[s(I,{url:`employees/${r.employee.uuid}/incharges/`,name:"EmployeeIncharge",title:l.$trans("employee.incharge.incharge"),actions:f(c),"dropdown-actions":["print","pdf","excel"],onToggleFilter:a[0]||(a[0]=t=>m.value=!m.value)},null,8,["url","title","actions"])]),_:1},8,["title","navs"])):b("",!0)]),filter:e(()=>[s($,{appear:"",visibility:m.value},{default:e(()=>[s(N,{onRefresh:a[1]||(a[1]=t=>f(p).emit("listItems")),onHide:a[2]||(a[2]=t=>m.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[s($,{appear:"",visibility:!0},{default:e(()=>[s(H,{header:o.headers,meta:o.meta,module:"employee.incharge",onRefresh:a[3]||(a[3]=t=>f(p).emit("listItems"))},{actionButton:e(()=>a[4]||(a[4]=[])),default:e(()=>[(i(!0),k(E,null,L(o.data,t=>(i(),d(w,{key:t.uuid},{default:e(()=>[s(x,{name:"startDate"},{default:e(()=>[g(u(t.period)+" ",1),F("div",V,u(t.duration),1)]),_:2},1024),s(x,{name:"detail"},{default:e(()=>[g(u(t.name)+" ",1),t.detail?(i(),k("span",A,u(t.detail),1)):b("",!0),t.type?(i(),d(h,{key:1,block:""},{default:e(()=>[g(u(t.type),1)]),_:2},1024)):b("",!0)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1},8,["uuid"])}}});export{M as default};
