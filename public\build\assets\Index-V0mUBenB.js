import{u as N,l as w,n as H,r as i,q as g,o as _,w as e,d as I,b as v,s as l,t as o,e as n,h as O,j as P,y as h,m as R,f as r,a as U,F as j,v as W}from"./app-DvIo72ZO.js";const E={class:"grid grid-cols-3 gap-6"},z={class:"col-span-3 sm:col-span-1"},G={class:"col-span-3 sm:col-span-1"},J={class:"col-span-3 sm:col-span-1"},K={__name:"Filter",emits:["hide"],setup(B,{emit:y}){const p=N(),k=y,b={courses:[],employees:[],startDate:"",endDate:""},m=w({...b}),c=w({courses:[],employees:[],isLoaded:!(p.query.courses||p.query.employees)});return H(async()=>{c.courses=p.query.courses?p.query.courses.split(","):[],c.employees=p.query.employees?p.query.employees.split(","):[],c.isLoaded=!0}),(f,u)=>{const s=i("BaseSelectSearch"),d=i("DatePicker"),A=i("FilterForm");return _(),g(A,{"init-form":b,form:m,multiple:["courses","employees"],onHide:u[4]||(u[4]=t=>k("hide"))},{default:e(()=>[I("div",E,[I("div",z,[c.isLoaded?(_(),g(s,{key:0,multiple:"",name:"courses",label:f.$trans("global.select",{attribute:f.$trans("academic.course.course")}),modelValue:m.courses,"onUpdate:modelValue":u[0]||(u[0]=t=>m.courses=t),"value-prop":"uuid","init-search":c.courses,"search-key":"course_course","search-action":"academic/course/list"},{selectedOption:e(t=>[l(o(t.value.nameWithTerm),1)]),listOption:e(t=>[l(o(t.option.nameWithTerm),1)]),_:1},8,["label","modelValue","init-search"])):v("",!0)]),I("div",G,[c.isLoaded?(_(),g(s,{key:0,multiple:"",name:"employees",label:f.$trans("global.select",{attribute:f.$trans("employee.employee")}),modelValue:m.employees,"onUpdate:modelValue":u[1]||(u[1]=t=>m.employees=t),"value-prop":"uuid","init-search":c.employees,"search-key":"name","search-action":"employee/list"},{selectedOption:e(t=>[l(o(t.value.name)+" ("+o(t.value.codeNumber)+") ",1)]),listOption:e(t=>[l(o(t.option.name)+" ("+o(t.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):v("",!0)]),I("div",J,[n(d,{start:m.startDate,"onUpdate:start":u[2]||(u[2]=t=>m.startDate=t),end:m.endDate,"onUpdate:end":u[3]||(u[3]=t=>m.endDate=t),name:"dateBetween",as:"range",label:f.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},Q={name:"AcademicCourseInchargeList"},Y=Object.assign(Q,{setup(B){const y=O(),p=P("emitter");let k=["filter"];h("course-incharge:create")&&k.unshift("create");let b=[];h("course-incharge:export")&&(b=["print","pdf","excel"]);const m="academic/courseIncharge/",c=R(!1),f=w({}),u=s=>{Object.assign(f,s)};return(s,d)=>{const A=i("PageHeaderAction"),t=i("PageHeader"),F=i("ParentTransition"),$=i("DataCell"),T=i("TextMuted"),C=i("FloatingMenuItem"),V=i("FloatingMenu"),L=i("DataRow"),S=i("BaseButton"),q=i("DataTable"),M=i("ListItem");return _(),g(M,{"init-url":m,onSetItems:u},{header:e(()=>[n(t,{title:s.$trans("academic.course_incharge.course_incharge"),navs:[{label:s.$trans("academic.academic"),path:"Academic"},{label:s.$trans("academic.course.course"),path:"AcademicCourse"}]},{default:e(()=>[n(A,{url:"academic/course-incharges/",name:"AcademicCourseIncharge",title:s.$trans("academic.course_incharge.course_incharge"),actions:r(k),"dropdown-actions":r(b),onToggleFilter:d[0]||(d[0]=a=>c.value=!c.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[n(F,{appear:"",visibility:c.value},{default:e(()=>[n(K,{onRefresh:d[1]||(d[1]=a=>r(p).emit("listItems")),onHide:d[2]||(d[2]=a=>c.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[n(F,{appear:"",visibility:!0},{default:e(()=>[n(q,{header:f.headers,meta:f.meta,module:"academic.course_incharge",onRefresh:d[4]||(d[4]=a=>r(p).emit("listItems"))},{actionButton:e(()=>[r(h)("course-incharge:create")?(_(),g(S,{key:0,onClick:d[3]||(d[3]=a=>r(y).push({name:"AcademicCourseInchargeCreate"}))},{default:e(()=>[l(o(s.$trans("global.add",{attribute:s.$trans("academic.course_incharge.course_incharge")})),1)]),_:1})):v("",!0)]),default:e(()=>[(_(!0),U(j,null,W(f.data,a=>(_(),g(L,{key:a.uuid,onDoubleClick:D=>r(y).push({name:"AcademicCourseInchargeShow",params:{uuid:a.uuid}})},{default:e(()=>[n($,{name:"course"},{default:e(()=>[l(o(a.course.nameWithTerm),1)]),_:2},1024),n($,{name:"employee"},{default:e(()=>[l(o(a.employee.name)+" ",1),n(T,{block:""},{default:e(()=>[l(o(a.employee.codeNumber),1)]),_:2},1024)]),_:2},1024),n($,{name:"period"},{default:e(()=>[l(o(a.period),1)]),_:2},1024),n($,{name:"createdAt"},{default:e(()=>[l(o(a.createdAt.formatted),1)]),_:2},1024),n($,{name:"action"},{default:e(()=>[n(V,null,{default:e(()=>[n(C,{icon:"fas fa-arrow-circle-right",onClick:D=>r(y).push({name:"AcademicCourseInchargeShow",params:{uuid:a.uuid}})},{default:e(()=>[l(o(s.$trans("general.show")),1)]),_:2},1032,["onClick"]),r(h)("course-incharge:edit")?(_(),g(C,{key:0,icon:"fas fa-edit",onClick:D=>r(y).push({name:"AcademicCourseInchargeEdit",params:{uuid:a.uuid}})},{default:e(()=>[l(o(s.$trans("general.edit")),1)]),_:2},1032,["onClick"])):v("",!0),r(h)("course-incharge:create")?(_(),g(C,{key:1,icon:"fas fa-copy",onClick:D=>r(y).push({name:"AcademicCourseInchargeDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[l(o(s.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):v("",!0),r(h)("course-incharge:delete")?(_(),g(C,{key:2,icon:"fas fa-trash",onClick:D=>r(p).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[l(o(s.$trans("general.delete")),1)]),_:2},1032,["onClick"])):v("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Y as default};
