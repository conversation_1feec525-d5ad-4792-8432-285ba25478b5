import{u as Q,h as ee,j as te,H as J,I as se,g as P,l as T,n as re,K as oe,r as u,q as N,o as p,w as i,e as n,d as l,s as b,t as c,f as o,a as z,b as g,J as K,F as ae}from"./app-DvIo72ZO.js";const ne={class:"mt-4 grid grid-cols-3 gap-6"},le={class:"col-span-3 sm:col-span-1"},de={class:"col-span-3 sm:col-span-1"},ie={class:"col-span-3 sm:col-span-1"},ue={class:"col-span-3 sm:col-span-1"},me={class:"col-span-3 sm:col-span-1"},pe={class:"col-span-3 sm:col-span-1"},be={class:"col-span-3 sm:col-span-1"},ce={class:"col-span-3 sm:col-span-1"},ye={class:"mt-4 grid grid-cols-3 gap-6"},ge={class:"col-span-3 sm:col-span-1"},Ae={class:"col-span-3 sm:col-span-1"},Ne={class:"col-span-3 sm:col-span-1"},fe={class:"col-span-3 sm:col-span-1"},Ue={class:"col-span-3 sm:col-span-1"},Ve={class:"col-span-3 sm:col-span-1"},qe={class:"col-span-3 sm:col-span-1"},Le={class:"col-span-3 sm:col-span-1"},ve={class:"col-span-3 sm:col-span-1"},Ie={key:0,class:"col-span-3 sm:col-span-1"},$e={key:1,class:"col-span-3 sm:col-span-1"},Pe={class:"grid grid-cols-3 gap-6"},Ee={class:"grid grid-cols-3 gap-6"},Se={class:"col-span-3 sm:col-span-1"},ke={class:"mt-4 grid grid-cols-3 gap-6"},Ce={class:"grid grid-cols-1"},Be={class:"col"},Re={name:"EmployeeProfileEditRequest"},Te=Object.assign(Re,{props:{employee:{type:Object,default(){return{}}}},setup(m){const L=Q();ee(),te("emitter");const A=m,v={contactNumber:"",alternateContactNumber:"",email:"",fatherName:"",motherName:"",uniqueIdNumber1:"",uniqueIdNumber2:"",uniqueIdNumber3:"",birthPlace:"",nationality:"",motherTongue:"",bloodGroup:"",maritalStatus:"",religion:"",category:"",caste:"",presentAddress:{},permanentAddress:{sameAsPresentAddress:!1},media:[],mediaUpdated:!1,mediaToken:J(),mediaHash:[]},E="employee/profileEditRequest/",R="employee/",a=se(E),W=P("contact.uniqueIdNumber1Label"),X=P("contact.uniqueIdNumber2Label"),Y=P("contact.uniqueIdNumber3Label"),Z=P("contact.enableCategoryField"),x=P("contact.enableCasteField"),f=T({genders:[],bloodGroups:[],religions:[],categories:[],castes:[],maritalStatuses:[]}),s=T({...v}),U=T({religion:"",category:"",caste:"",isLoaded:!L.params.uuid}),_=r=>{Object.assign(f,r)},h=()=>{s.mediaToken=J(),s.mediaHash=[]};return re(async()=>{var e,S,V,q,d,y,I,$,k,C,B,t,F,G,j,H,O,w,M,D;let r=(e=A.employee)==null?void 0:e.contact;Object.assign(v,{contactNumber:r.contactNumber,alternateContactNumber:(S=r.alternateRecords)==null?void 0:S.contactNumber,email:r.email,fatherName:r.fatherName,motherName:r.motherName,alternateEmail:(V=r.alternateRecords)==null?void 0:V.email,uniqueIdNumber1:r.uniqueIdNumber1,uniqueIdNumber2:r.uniqueIdNumber2,uniqueIdNumber3:r.uniqueIdNumber3,birthPlace:r.birthPlace,bloodGroup:((q=r.bloodGroup)==null?void 0:q.value)||"",maritalStatus:((d=r.maritalStatus)==null?void 0:d.value)||"",nationality:r.nationality,motherTongue:r.motherTongue,religion:((y=r.religion)==null?void 0:y.uuid)||"",category:((I=r.category)==null?void 0:I.uuid)||"",caste:(($=r.caste)==null?void 0:$.uuid)||"",presentAddress:{addressLine1:(k=r.presentAddress)==null?void 0:k.addressLine1,addressLine2:(C=r.presentAddress)==null?void 0:C.addressLine2,city:(B=r.presentAddress)==null?void 0:B.city,state:(t=r.presentAddress)==null?void 0:t.state,zipcode:(F=r.presentAddress)==null?void 0:F.zipcode,country:(G=r.presentAddress)==null?void 0:G.country},permanentAddress:{sameAsPresentAddress:r.sameAsPresentAddress,addressLine1:(j=r.permanentAddress)==null?void 0:j.addressLine1,addressLine2:(H=r.permanentAddress)==null?void 0:H.addressLine2,city:(O=r.permanentAddress)==null?void 0:O.city,state:(w=r.permanentAddress)==null?void 0:w.state,zipcode:(M=r.permanentAddress)==null?void 0:M.zipcode,country:(D=r.permanentAddress)==null?void 0:D.country}}),Object.assign(s,oe(v)),U.isLoaded=!0}),(r,e)=>{const S=u("BaseAlert"),V=u("BaseLabel"),q=u("TextContent"),d=u("BaseInput"),y=u("BaseSelect"),I=u("AddressInput"),$=u("BaseFieldset"),k=u("BaseSwitch"),C=u("MediaUpload"),B=u("FormAction");return p(),N(B,{"no-data-fetch":"","pre-requisites":!0,onSetPreRequisites:_,"pre-requisite-url":R,"init-url":E,uuid:o(L).params.uuid,"init-form":v,form:s,"keep-adding":!1,redirect:{name:"EmployeeProfileEditRequest",params:{uuid:m.employee.uuid}},onResetMediaFiles:h},{default:i(()=>[n(S,{design:"info",size:"xs"},{default:i(()=>[b(c(r.$trans("employee.edit_request.upload_document_info")),1)]),_:1}),l("div",ne,[l("div",le,[n(V,null,{default:i(()=>[b(c(r.$trans("contact.props.name")),1)]),_:1}),n(q,{class:"mt-1"},{default:i(()=>[b(c(m.employee.name),1)]),_:1})]),l("div",de,[n(V,null,{default:i(()=>[b(c(r.$trans("contact.props.gender")),1)]),_:1}),n(q,{class:"mt-1"},{default:i(()=>[b(c(m.employee.contact.gender.label),1)]),_:1})]),l("div",ie,[n(V,null,{default:i(()=>[b(c(r.$trans("contact.props.birth_date")),1)]),_:1}),n(q,{class:"mt-1"},{default:i(()=>[b(c(m.employee.contact.birthDate.formatted),1)]),_:1})]),l("div",ue,[n(d,{type:"text",modelValue:s.contactNumber,"onUpdate:modelValue":e[0]||(e[0]=t=>s.contactNumber=t),name:"contactNumber",label:r.$trans("contact.props.primary_contact_number"),error:o(a).contactNumber,"onUpdate:error":e[1]||(e[1]=t=>o(a).contactNumber=t)},null,8,["modelValue","label","error"])]),l("div",me,[n(d,{type:"text",modelValue:s.alternateContactNumber,"onUpdate:modelValue":e[2]||(e[2]=t=>s.alternateContactNumber=t),name:"alternateContactNumber",label:r.$trans("contact.props.alternate_contact_number"),error:o(a).alternateContactNumber,"onUpdate:error":e[3]||(e[3]=t=>o(a).alternateContactNumber=t)},null,8,["modelValue","label","error"])]),l("div",pe,[n(d,{type:"text",modelValue:s.email,"onUpdate:modelValue":e[4]||(e[4]=t=>s.email=t),name:"email",label:r.$trans("contact.props.email"),error:o(a).email,"onUpdate:error":e[5]||(e[5]=t=>o(a).email=t)},null,8,["modelValue","label","error"])]),l("div",be,[n(d,{type:"text",modelValue:s.fatherName,"onUpdate:modelValue":e[6]||(e[6]=t=>s.fatherName=t),name:"fatherName",label:r.$trans("contact.props.father_name"),error:o(a).fatherName,"onUpdate:error":e[7]||(e[7]=t=>o(a).fatherName=t)},null,8,["modelValue","label","error"])]),l("div",ce,[n(d,{type:"text",modelValue:s.motherName,"onUpdate:modelValue":e[8]||(e[8]=t=>s.motherName=t),name:"motherName",label:r.$trans("contact.props.mother_name"),error:o(a).motherName,"onUpdate:error":e[9]||(e[9]=t=>o(a).motherName=t)},null,8,["modelValue","label","error"])])]),l("div",ye,[l("div",ge,[n(d,{type:"text",modelValue:s.uniqueIdNumber1,"onUpdate:modelValue":e[10]||(e[10]=t=>s.uniqueIdNumber1=t),name:"uniqueIdNumber1",label:o(W),error:o(a).uniqueIdNumber1,"onUpdate:error":e[11]||(e[11]=t=>o(a).uniqueIdNumber1=t)},null,8,["modelValue","label","error"])]),l("div",Ae,[n(d,{type:"text",modelValue:s.uniqueIdNumber2,"onUpdate:modelValue":e[12]||(e[12]=t=>s.uniqueIdNumber2=t),name:"uniqueIdNumber2",label:o(X),error:o(a).uniqueIdNumber2,"onUpdate:error":e[13]||(e[13]=t=>o(a).uniqueIdNumber2=t)},null,8,["modelValue","label","error"])]),l("div",Ne,[n(d,{type:"text",modelValue:s.uniqueIdNumber3,"onUpdate:modelValue":e[14]||(e[14]=t=>s.uniqueIdNumber3=t),name:"uniqueIdNumber3",label:o(Y),error:o(a).uniqueIdNumber3,"onUpdate:error":e[15]||(e[15]=t=>o(a).uniqueIdNumber3=t)},null,8,["modelValue","label","error"])]),l("div",fe,[n(d,{type:"text",modelValue:s.birthPlace,"onUpdate:modelValue":e[16]||(e[16]=t=>s.birthPlace=t),name:"birthPlace",label:r.$trans("contact.props.birth_place"),error:o(a).birthPlace,"onUpdate:error":e[17]||(e[17]=t=>o(a).birthPlace=t)},null,8,["modelValue","label","error"])]),l("div",Ue,[n(d,{type:"text",modelValue:s.nationality,"onUpdate:modelValue":e[18]||(e[18]=t=>s.nationality=t),name:"nationality",label:r.$trans("contact.props.nationality"),error:o(a).nationality,"onUpdate:error":e[19]||(e[19]=t=>o(a).nationality=t)},null,8,["modelValue","label","error"])]),l("div",Ve,[n(d,{type:"text",modelValue:s.motherTongue,"onUpdate:modelValue":e[20]||(e[20]=t=>s.motherTongue=t),name:"motherTongue",label:r.$trans("contact.props.mother_tongue"),error:o(a).motherTongue,"onUpdate:error":e[21]||(e[21]=t=>o(a).motherTongue=t)},null,8,["modelValue","label","error"])]),l("div",qe,[U.isLoaded?(p(),N(y,{key:0,modelValue:s.bloodGroup,"onUpdate:modelValue":e[22]||(e[22]=t=>s.bloodGroup=t),name:"bloodGroup",label:r.$trans("contact.props.blood_group"),options:f.bloodGroups,error:o(a).bloodGroup,"onUpdate:error":e[23]||(e[23]=t=>o(a).bloodGroup=t)},null,8,["modelValue","label","options","error"])):g("",!0)]),l("div",Le,[U.isLoaded?(p(),N(y,{key:0,modelValue:s.maritalStatus,"onUpdate:modelValue":e[24]||(e[24]=t=>s.maritalStatus=t),name:"maritalStatus",label:r.$trans("contact.props.marital_status"),options:f.maritalStatuses,error:o(a).maritalStatus,"onUpdate:error":e[25]||(e[25]=t=>o(a).maritalStatus=t)},null,8,["modelValue","label","options","error"])):g("",!0)]),l("div",ve,[U.isLoaded?(p(),N(y,{key:0,name:"religion",label:r.$trans("global.select",{attribute:r.$trans("contact.religion.religion")}),modelValue:s.religion,"onUpdate:modelValue":e[26]||(e[26]=t=>s.religion=t),error:o(a).religion,"onUpdate:error":e[27]||(e[27]=t=>o(a).religion=t),options:f.religions,"label-prop":"name","value-prop":"uuid"},null,8,["label","modelValue","error","options"])):g("",!0)]),o(Z)?(p(),z("div",Ie,[U.isLoaded?(p(),N(y,{key:0,name:"category",label:r.$trans("global.select",{attribute:r.$trans("contact.category.category")}),modelValue:s.category,"onUpdate:modelValue":e[28]||(e[28]=t=>s.category=t),error:o(a).category,"onUpdate:error":e[29]||(e[29]=t=>o(a).category=t),options:f.categories,"label-prop":"name","value-prop":"uuid"},null,8,["label","modelValue","error","options"])):g("",!0)])):g("",!0),o(x)?(p(),z("div",$e,[U.isLoaded?(p(),N(y,{key:0,name:"caste",label:r.$trans("global.select",{attribute:r.$trans("contact.caste.caste")}),modelValue:s.caste,"onUpdate:modelValue":e[30]||(e[30]=t=>s.caste=t),error:o(a).caste,"onUpdate:error":e[31]||(e[31]=t=>o(a).caste=t),options:f.castes,"label-prop":"name","value-prop":"uuid"},null,8,["label","modelValue","error","options"])):g("",!0)])):g("",!0)]),n($,{class:"mt-4"},{legend:i(()=>[b(c(r.$trans("contact.props.present_address")),1)]),default:i(()=>[l("div",Pe,[n(I,{prefix:"presentAddress",addressLine1:s.presentAddress.addressLine1,"onUpdate:addressLine1":e[32]||(e[32]=t=>s.presentAddress.addressLine1=t),addressLine2:s.presentAddress.addressLine2,"onUpdate:addressLine2":e[33]||(e[33]=t=>s.presentAddress.addressLine2=t),city:s.presentAddress.city,"onUpdate:city":e[34]||(e[34]=t=>s.presentAddress.city=t),state:s.presentAddress.state,"onUpdate:state":e[35]||(e[35]=t=>s.presentAddress.state=t),zipcode:s.presentAddress.zipcode,"onUpdate:zipcode":e[36]||(e[36]=t=>s.presentAddress.zipcode=t),country:s.presentAddress.country,"onUpdate:country":e[37]||(e[37]=t=>s.presentAddress.country=t),formErrors:o(a),"onUpdate:formErrors":e[38]||(e[38]=t=>K(a)?a.value=t:null)},null,8,["addressLine1","addressLine2","city","state","zipcode","country","formErrors"])])]),_:1}),n($,{class:"mt-4"},{legend:i(()=>[b(c(r.$trans("contact.props.permanent_address")),1)]),default:i(()=>[l("div",Ee,[l("div",Se,[n(k,{modelValue:s.permanentAddress.sameAsPresentAddress,"onUpdate:modelValue":e[39]||(e[39]=t=>s.permanentAddress.sameAsPresentAddress=t),name:"sameAsPresentAddress",label:r.$trans("contact.props.same_as_present_address"),error:o(a).sameAsPresentAddress,"onUpdate:error":e[40]||(e[40]=t=>o(a).sameAsPresentAddress=t)},null,8,["modelValue","label","error"])])]),l("div",ke,[s.permanentAddress.sameAsPresentAddress?g("",!0):(p(),N(I,{key:0,prefix:"permanentAddress",addressLine1:s.permanentAddress.addressLine1,"onUpdate:addressLine1":e[41]||(e[41]=t=>s.permanentAddress.addressLine1=t),addressLine2:s.permanentAddress.addressLine2,"onUpdate:addressLine2":e[42]||(e[42]=t=>s.permanentAddress.addressLine2=t),city:s.permanentAddress.city,"onUpdate:city":e[43]||(e[43]=t=>s.permanentAddress.city=t),state:s.permanentAddress.state,"onUpdate:state":e[44]||(e[44]=t=>s.permanentAddress.state=t),zipcode:s.permanentAddress.zipcode,"onUpdate:zipcode":e[45]||(e[45]=t=>s.permanentAddress.zipcode=t),country:s.permanentAddress.country,"onUpdate:country":e[46]||(e[46]=t=>s.permanentAddress.country=t),formErrors:o(a),"onUpdate:formErrors":e[47]||(e[47]=t=>K(a)?a.value=t:null)},null,8,["addressLine1","addressLine2","city","state","zipcode","country","formErrors"]))])]),_:1}),l("div",Ce,[l("div",Be,[n(C,{multiple:"",label:r.$trans("general.file"),module:"contact_edit_request",media:s.media,"media-token":s.mediaToken,onIsUpdated:e[48]||(e[48]=t=>s.mediaUpdated=!0),onSetHash:e[49]||(e[49]=t=>s.mediaHash.push(t))},null,8,["label","media","media-token"])])])]),_:1},8,["uuid","form","redirect"])}}}),ze={name:"EmployeeProfileEditRequestAction"},Ge=Object.assign(ze,{props:{employee:{type:Object,default(){return{}}}},setup(m){const L=Q();return(A,v)=>{const E=u("PageHeaderAction"),R=u("PageHeader"),a=u("ParentTransition");return p(),z(ae,null,[n(R,{title:A.$trans(o(L).meta.trans,{attribute:A.$trans(o(L).meta.label)}),navs:[{label:A.$trans("employee.employee"),path:"EmployeeList"},{label:m.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:m.employee.uuid}}},{label:A.$trans("employee.edit_request.edit_request"),path:{name:"EmployeeProfileEditRequest",params:{uuid:m.employee.uuid}}}]},{default:i(()=>[n(E,{name:"EmployeeProfileEditRequest",title:A.$trans("employee.edit_request.edit_request"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(a,{appear:"",visibility:!0},{default:i(()=>[n(Te,{employee:m.employee},null,8,["employee"])]),_:1})],64)}}});export{Ge as default};
