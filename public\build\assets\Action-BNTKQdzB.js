import{u as D,I as $,l as y,r as i,q as S,o as _,w as p,d as u,b as E,f as a,s as b,t as k,e as m,K as U,a as B,F}from"./app-DvIo72ZO.js";const O={class:"grid grid-cols-3 gap-6"},P={class:"col-span-3 sm:col-span-1"},j={class:"col-span-3 sm:col-span-1"},A={class:"col-span-3 sm:col-span-1"},T={class:"mt-4 grid grid-cols-1"},W={class:"col"},H={name:"EmployeeWorkShiftForm"},L=Object.assign(H,{setup(c){const l=D(),s={workShift:"",startDate:"",endDate:"",remarks:""},h="employee/workShift/",n=$(h),o=y({...s}),d=y({workShift:"",isLoaded:!l.params.muuid}),g=r=>{var t,f;Object.assign(s,{startDate:r.startDate.value,endDate:r.endDate.value,workShift:(t=r.workShift)==null?void 0:t.uuid,remarks:r.remarks}),Object.assign(o,U(s)),d.workShift=(f=r.workShift)==null?void 0:f.name,d.isLoaded=!0};return(r,t)=>{const f=i("BaseSelectSearch"),w=i("DatePicker"),V=i("BaseTextarea"),v=i("FormAction");return _(),S(v,{"no-data-fetch":"","init-url":h,uuid:a(l).params.uuid,"module-uuid":a(l).params.muuid,"init-form":s,form:o,"set-form":g,redirect:{name:"EmployeeWorkShift",params:{uuid:a(l).params.uuid}}},{default:p(()=>[u("div",O,[u("div",P,[d.isLoaded?(_(),S(f,{key:0,name:"workShift",label:r.$trans("global.select",{attribute:r.$trans("employee.attendance.work_shift.work_shift")}),modelValue:o.workShift,"onUpdate:modelValue":t[0]||(t[0]=e=>o.workShift=e),error:a(n).workShift,"onUpdate:error":t[1]||(t[1]=e=>a(n).workShift=e),"init-search":d.workShift,"init-search-key":"name","search-action":"employee/attendance/workShift/list"},{selectedOption:p(e=>[b(k(e.value.name)+" ("+k(e.value.code)+") ",1)]),listOption:p(e=>[b(k(e.option.name)+" ("+k(e.option.code)+") ",1)]),_:1},8,["label","modelValue","error","init-search"])):E("",!0)]),u("div",j,[m(w,{modelValue:o.startDate,"onUpdate:modelValue":t[2]||(t[2]=e=>o.startDate=e),name:"startDate",label:r.$trans("employee.attendance.work_shift.props.start_date"),error:a(n).startDate,"onUpdate:error":t[3]||(t[3]=e=>a(n).startDate=e)},null,8,["modelValue","label","error"])]),u("div",A,[m(w,{modelValue:o.endDate,"onUpdate:modelValue":t[4]||(t[4]=e=>o.endDate=e),name:"endDate",label:r.$trans("employee.attendance.work_shift.props.end_date"),error:a(n).endDate,"onUpdate:error":t[5]||(t[5]=e=>a(n).endDate=e)},null,8,["modelValue","label","error"])])]),u("div",T,[u("div",W,[m(V,{modelValue:o.remarks,"onUpdate:modelValue":t[6]||(t[6]=e=>o.remarks=e),name:"remarks",label:r.$trans("employee.attendance.work_shift.props.remarks"),error:a(n).remarks,"onUpdate:error":t[7]||(t[7]=e=>a(n).remarks=e)},null,8,["modelValue","label","error"])])])]),_:1},8,["uuid","module-uuid","form","redirect"])}}}),N={name:"EmployeeWorkShiftAction"},q=Object.assign(N,{props:{employee:{type:Object,default(){return{}}}},setup(c){const l=D();return(s,h)=>{const n=i("PageHeaderAction"),o=i("PageHeader"),d=i("ParentTransition");return _(),B(F,null,[m(o,{title:s.$trans(a(l).meta.trans,{attribute:s.$trans(a(l).meta.label)}),navs:[{label:s.$trans("employee.employee"),path:"EmployeeList"},{label:c.employee.contact.name,path:{name:"EmployeeShow",params:{uuid:c.employee.uuid}}},{label:s.$trans("employee.attendance.work_shift.work_shift"),path:{name:"EmployeeWorkShift",params:{uuid:c.employee.uuid}}}]},{default:p(()=>[m(n,{name:"EmployeeWorkShift",title:s.$trans("employee.attendance.work_shift.work_shift"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),m(d,{appear:"",visibility:!0},{default:p(()=>[m(L)]),_:1})],64)}}});export{q as default};
