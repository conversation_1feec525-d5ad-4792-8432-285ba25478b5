import{l as D,r as u,q as f,o as c,w as e,d as $,b as _,s,t as n,e as o,u as q,h as E,j as W,y as g,m as w,f as p,a as F,F as z,v as G}from"./app-DvIo72ZO.js";const J={class:"grid grid-cols-3 gap-6"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},X={class:"col-span-3 sm:col-span-1"},Y={class:"col-span-3 sm:col-span-1"},Z={name:"StudentAttendanceTimesheetFilter"},x=Object.assign(Z,{emits:["hide"],setup(I,{emit:k}){const y=k,T={students:[],courses:[],batches:[],startDate:"",endDate:""},l=D({...T}),b=D({students:[],courses:[],batches:[],isLoaded:!0});return(m,i)=>{const h=u("BaseSelectSearch"),B=u("DatePicker"),A=u("FilterForm");return c(),f(A,{"init-form":T,form:l,multiple:["students","courses","batches"],onHide:i[5]||(i[5]=t=>y("hide"))},{default:e(()=>[$("div",J,[$("div",K,[b.isLoaded?(c(),f(h,{key:0,multiple:"",name:"students",label:m.$trans("global.select",{attribute:m.$trans("student.student")}),modelValue:l.students,"onUpdate:modelValue":i[0]||(i[0]=t=>l.students=t),"value-prop":"uuid","init-search":b.students,"search-key":"name","search-action":"student/list"},{selectedOption:e(t=>[s(n(t.value.name)+" ("+n(t.value.codeNumber)+") ",1)]),listOption:e(t=>[s(n(t.option.name)+" ("+n(t.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):_("",!0)]),$("div",Q,[b.isLoaded?(c(),f(h,{key:0,multiple:"",name:"courses",label:m.$trans("global.select",{attribute:m.$trans("academic.course.course")}),modelValue:l.courses,"onUpdate:modelValue":i[1]||(i[1]=t=>l.courses=t),"value-prop":"uuid","init-search":b.courses,"search-key":"name_with_term","search-action":"academic/course/list"},{selectedOption:e(t=>[s(n(t.value.nameWithTerm),1)]),listOption:e(t=>[s(n(t.option.nameWithTerm),1)]),_:1},8,["label","modelValue","init-search"])):_("",!0)]),$("div",X,[b.isLoaded?(c(),f(h,{key:0,multiple:"",name:"batches",label:m.$trans("global.select",{attribute:m.$trans("academic.batch.batch")}),modelValue:l.batches,"onUpdate:modelValue":i[2]||(i[2]=t=>l.batches=t),"value-prop":"uuid","init-search":b.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:e(t=>[s(n(t.value.course.name)+" "+n(t.value.name),1)]),listOption:e(t=>[s(n(t.option.course.nameWithTerm)+" "+n(t.option.name),1)]),_:1},8,["label","modelValue","init-search"])):_("",!0)]),$("div",Y,[o(B,{start:l.startDate,"onUpdate:start":i[3]||(i[3]=t=>l.startDate=t),end:l.endDate,"onUpdate:end":i[4]||(i[4]=t=>l.endDate=t),name:"dateBetween",as:"range",label:m.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}}),ee={key:1,class:"text-danger text-xs"},te={key:0},ae={name:"StudentAttendanceTimesheetList"},se=Object.assign(ae,{setup(I){q();const k=E(),y=W("emitter");let T=["filter"];g("student-timesheet:create")&&T.unshift("create");let l=[];g("student-timesheet:export")&&(l=["print","pdf","excel"]);const b="student/attendance/timesheet/",m=w(!1),i=w(!1),h=D({}),B=D({}),A=d=>{Object.assign(h,d)},t=d=>{Object.assign(B,d)};return(d,r)=>{const N=u("PageHeaderAction"),O=u("PageHeader"),C=u("ParentTransition"),v=u("DataCell"),R=u("BaseBadge"),L=u("TextMuted"),S=u("FloatingMenuItem"),M=u("FloatingMenu"),j=u("DataRow"),H=u("BaseButton"),P=u("DataTable"),U=u("ListItem");return c(),f(U,{"init-url":b,"pre-requisites":!0,onSetItems:A,onSetPreRequisites:t},{header:e(()=>[o(O,{title:d.$trans("student.attendance.timesheet.timesheet"),navs:[{label:d.$trans("student.student"),path:"Student"},{label:d.$trans("student.attendance.attendance"),path:"StudentAttendance"}]},{default:e(()=>[o(N,{url:"student/attendance/timesheet/",name:"StudentAttendanceTimesheet",title:d.$trans("student.attendance.timesheet.timesheet"),actions:p(T),"dropdown-actions":p(l),onToggleFilter:r[0]||(r[0]=a=>m.value=!m.value),onToggleImport:r[1]||(r[1]=a=>i.value=!i.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[o(C,{appear:"",visibility:m.value},{default:e(()=>[o(x,{onRefresh:r[2]||(r[2]=a=>p(y).emit("listItems")),onHide:r[3]||(r[3]=a=>m.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[o(C,{appear:"",visibility:!0},{default:e(()=>[o(P,{header:h.headers,meta:h.meta,module:"student.attendance.timesheet",onRefresh:r[5]||(r[5]=a=>p(y).emit("listItems"))},{actionButton:e(()=>[p(g)("student-timesheet:create")?(c(),f(H,{key:0,onClick:r[4]||(r[4]=a=>p(k).push({name:"StudentAttendanceTimesheetCreate"}))},{default:e(()=>[s(n(d.$trans("global.add",{attribute:d.$trans("student.attendance.timesheet.timesheet")})),1)]),_:1})):_("",!0)]),default:e(()=>[(c(!0),F(z,null,G(h.data,a=>(c(),f(j,{key:a.uuid},{default:e(()=>[o(v,{name:"student"},{default:e(()=>[s(n(a.student.name)+" ("+n(a.student.codeNumber)+") ",1)]),_:2},1024),o(v,{name:"courseName"},{default:e(()=>[s(n(a.student.courseName),1)]),_:2},1024),o(v,{name:"batch"},{default:e(()=>[s(n(a.student.batchName),1)]),_:2},1024),o(v,{name:"date"},{default:e(()=>[s(n(a.date.formatted)+" ",1),a.isManual?(c(),f(R,{key:0,type:"info"},{default:e(()=>[s(n(d.$trans("student.attendance.timesheet.props.manual")),1)]),_:1})):_("",!0),a.status&&a.status!="ok"?(c(),F("div",ee,n(a.status.label),1)):_("",!0),o(L,{block:""},{default:e(()=>[s(n(a.day),1)]),_:2},1024)]),_:2},1024),o(v,{name:"inAt"},{default:e(()=>[s(n(a.inAtTime.formatted),1)]),_:2},1024),o(v,{name:"outAt"},{default:e(()=>[s(n(a.outAtTime.formatted),1)]),_:2},1024),o(v,{name:"duration"},{default:e(()=>[s(n(a.duration)+" ",1),a.status=="ok"?(c(),F("span",te,r[6]||(r[6]=[$("i",{class:"fas fa-check-circle text-success"},null,-1)]))):_("",!0)]),_:2},1024),o(v,{name:"action"},{default:e(()=>[o(M,null,{default:e(()=>[p(g)("student-timesheet:edit")&&a.isEditable?(c(),f(S,{key:0,icon:"fas fa-edit",onClick:V=>p(k).push({name:"StudentAttendanceTimesheetEdit",params:{uuid:a.uuid}})},{default:e(()=>[s(n(d.$trans("general.edit")),1)]),_:2},1032,["onClick"])):_("",!0),p(g)("student-timesheet:create")?(c(),f(S,{key:1,icon:"fas fa-copy",onClick:V=>p(k).push({name:"StudentAttendanceTimesheetDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[s(n(d.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):_("",!0),p(g)("student-timesheet:delete")&&a.isDeletable?(c(),f(S,{key:2,icon:"fas fa-trash",onClick:V=>p(y).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[s(n(d.$trans("general.delete")),1)]),_:2},1032,["onClick"])):_("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{se as default};
