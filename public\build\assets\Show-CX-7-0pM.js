import{i as v,u as k,h as C,l as P,r as o,a as V,o as u,e as n,w as t,f as r,q as c,b as g,d as A,s,t as l,y as H,F as I}from"./app-DvIo72ZO.js";const N={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},T={name:"EmployeeDesignationShow"},F=Object.assign(T,{setup(R){v();const p=k(),m=C(),_={},f="employee/designation/",a=P({..._}),y=e=>{Object.assign(a,e)};return(e,d)=>{const b=o("PageHeaderAction"),B=o("PageHeader"),i=o("BaseDataView"),$=o("BaseButton"),h=o("ShowButton"),w=o("BaseCard"),D=o("ShowItem"),E=o("ParentTransition");return u(),V(I,null,[n(B,{title:e.$trans(r(p).meta.trans,{attribute:e.$trans(r(p).meta.label)}),navs:[{label:e.$trans("employee.employee"),path:"Employee"},{label:e.$trans("employee.designation.designation"),path:"EmployeeDesignationList"}]},{default:t(()=>[n(b,{name:"EmployeeDesignation",title:e.$trans("employee.designation.designation"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(E,{appear:"",visibility:!0},{default:t(()=>[n(D,{"init-url":f,uuid:r(p).params.uuid,onSetItem:y,onRedirectTo:d[1]||(d[1]=S=>r(m).push({name:"EmployeeDesignation"}))},{default:t(()=>[a.uuid?(u(),c(w,{key:0},{title:t(()=>[s(l(a.name),1)]),footer:t(()=>[n(h,null,{default:t(()=>[r(H)("designation:edit")?(u(),c($,{key:0,design:"primary",onClick:d[0]||(d[0]=S=>r(m).push({name:"EmployeeDesignationEdit",params:{uuid:a.uuid}}))},{default:t(()=>[s(l(e.$trans("general.edit")),1)]),_:1})):g("",!0)]),_:1})]),default:t(()=>[A("dl",N,[n(i,{label:e.$trans("employee.designation.props.name")},{default:t(()=>[s(l(a.name),1)]),_:1},8,["label"]),n(i,{label:e.$trans("employee.designation.props.alias")},{default:t(()=>[s(l(a.alias),1)]),_:1},8,["label"]),n(i,{label:e.$trans("employee.designation.props.parent")},{default:t(()=>[s(l(a.parent?a.parent.name:"-"),1)]),_:1},8,["label"]),n(i,{class:"col-span-1 sm:col-span-2",label:e.$trans("employee.designation.props.description")},{default:t(()=>[s(l(a.description),1)]),_:1},8,["label"]),n(i,{label:e.$trans("general.created_at")},{default:t(()=>[s(l(a.createdAt.formatted),1)]),_:1},8,["label"]),n(i,{label:e.$trans("general.updated_at")},{default:t(()=>[s(l(a.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):g("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{F as default};
