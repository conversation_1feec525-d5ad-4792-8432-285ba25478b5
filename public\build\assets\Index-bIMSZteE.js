import{l as A,r as s,q as C,o as y,w as e,d as b,e as t,h as P,j as R,m as j,f as d,a as F,F as L,v as M,s as m,t as i,b as N}from"./app-DvIo72ZO.js";import{_ as S}from"./ModuleDropdown-DaP494Fu.js";const U={class:"grid grid-cols-3 gap-6"},O={class:"col-span-3 sm:col-span-1"},T={class:"col-span-3 sm:col-span-1"},q={class:"col-span-3 sm:col-span-1"},z={__name:"Filter",emits:["hide"],setup(V,{emit:p}){const f=p,v={name:"",alias:""},r=A({...v});return(u,l)=>{const _=s("BaseInput"),o=s("FilterForm");return y(),C(o,{"init-form":v,form:r,onHide:l[3]||(l[3]=n=>f("hide"))},{default:e(()=>[b("div",U,[b("div",O,[t(_,{type:"text",modelValue:r.name,"onUpdate:modelValue":l[0]||(l[0]=n=>r.name=n),name:"name",label:u.$trans("employee.attendance.type.props.name")},null,8,["modelValue","label"])]),b("div",T,[t(_,{type:"text",modelValue:r.code,"onUpdate:modelValue":l[1]||(l[1]=n=>r.code=n),name:"code",label:u.$trans("employee.attendance.type.props.code")},null,8,["modelValue","label"])]),b("div",q,[t(_,{type:"text",modelValue:r.alias,"onUpdate:modelValue":l[2]||(l[2]=n=>r.alias=n),name:"alias",label:u.$trans("employee.attendance.type.props.alias")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},G={key:0},J={name:"EmployeeAttendanceTypeList"},W=Object.assign(J,{setup(V){const p=P(),f=R("emitter");let v=["create","filter"];const r="employee/attendance/type/",u=j(!1),l=A({}),_=o=>{Object.assign(l,o)};return(o,n)=>{const h=s("PageHeaderAction"),B=s("PageHeader"),k=s("ParentTransition"),c=s("DataCell"),$=s("FloatingMenuItem"),E=s("FloatingMenu"),I=s("DataRow"),w=s("BaseButton"),D=s("DataTable"),H=s("ListItem");return y(),C(H,{"init-url":r,onSetItems:_},{header:e(()=>[t(B,{title:o.$trans("employee.attendance.type.type"),navs:[{label:o.$trans("employee.employee"),path:"Employee"},{label:o.$trans("employee.attendance.attendance"),path:"EmployeeAttendance"}]},{default:e(()=>[t(h,{url:"employee/attendance/types/",name:"EmployeeAttendanceType",title:o.$trans("employee.attendance.type.type"),actions:d(v),"dropdown-actions":["print","pdf","excel"],onToggleFilter:n[0]||(n[0]=a=>u.value=!u.value)},{moduleOption:e(()=>[t(S)]),_:1},8,["title","actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(k,{appear:"",visibility:u.value},{default:e(()=>[t(z,{onRefresh:n[1]||(n[1]=a=>d(f).emit("listItems")),onHide:n[2]||(n[2]=a=>u.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(k,{appear:"",visibility:!0},{default:e(()=>[t(D,{header:l.headers,meta:l.meta,module:"employee.attendance.type",onRefresh:n[4]||(n[4]=a=>d(f).emit("listItems"))},{actionButton:e(()=>[t(w,{onClick:n[3]||(n[3]=a=>d(p).push({name:"EmployeeAttendanceTypeCreate"}))},{default:e(()=>[m(i(o.$trans("global.add",{attribute:o.$trans("employee.attendance.type.type")})),1)]),_:1})]),default:e(()=>[(y(!0),F(L,null,M(l.data,a=>(y(),C(I,{key:a.uuid,onDoubleClick:g=>d(p).push({name:"EmployeeAttendanceTypeShow",params:{uuid:a.uuid}})},{default:e(()=>[t(c,{name:"name"},{default:e(()=>[m(i(a.name),1)]),_:2},1024),t(c,{name:"code"},{default:e(()=>[m(i(a.code),1)]),_:2},1024),t(c,{name:"category"},{default:e(()=>[m(i(a.category.label)+" ",1),a.unit.value?(y(),F("span",G,"("+i(a.unit.label)+")",1)):N("",!0)]),_:2},1024),t(c,{name:"alias"},{default:e(()=>[m(i(a.alias),1)]),_:2},1024),t(c,{name:"createdAt"},{default:e(()=>[m(i(a.createdAt.formatted),1)]),_:2},1024),t(c,{name:"action"},{default:e(()=>[t(E,null,{default:e(()=>[t($,{icon:"fas fa-arrow-circle-right",onClick:g=>d(p).push({name:"EmployeeAttendanceTypeShow",params:{uuid:a.uuid}})},{default:e(()=>[m(i(o.$trans("general.show")),1)]),_:2},1032,["onClick"]),t($,{icon:"fas fa-edit",onClick:g=>d(p).push({name:"EmployeeAttendanceTypeEdit",params:{uuid:a.uuid}})},{default:e(()=>[m(i(o.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t($,{icon:"fas fa-copy",onClick:g=>d(p).push({name:"EmployeeAttendanceTypeDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[m(i(o.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t($,{icon:"fas fa-trash",onClick:g=>d(f).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[m(i(o.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{W as default};
