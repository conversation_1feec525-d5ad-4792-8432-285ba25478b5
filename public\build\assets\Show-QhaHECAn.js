import{i as v,u as C,h as P,l as V,r as o,a as k,o as c,e as a,w as e,f as p,q as y,b as A,d as H,s as n,t as r,F as I}from"./app-DvIo72ZO.js";const N={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},D={name:"TransportStoppageShow"},E=Object.assign(D,{setup(R){v();const u=C(),d=P(),m={},g="transport/stoppage/",s=V({...m}),_=t=>{Object.assign(s,t)};return(t,l)=>{const f=o("PageHeaderAction"),b=o("PageHeader"),i=o("BaseDataView"),B=o("BaseButton"),S=o("ShowButton"),$=o("BaseCard"),T=o("ShowItem"),h=o("ParentTransition");return c(),k(I,null,[a(b,{title:t.$trans(p(u).meta.trans,{attribute:t.$trans(p(u).meta.label)}),navs:[{label:t.$trans("transport.transport"),path:"Transport"},{label:t.$trans("transport.stoppage.stoppage"),path:"TransportStoppageList"}]},{default:e(()=>[a(f,{name:"TransportStoppage",title:t.$trans("transport.stoppage.stoppage"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(h,{appear:"",visibility:!0},{default:e(()=>[a(T,{"init-url":g,uuid:p(u).params.uuid,onSetItem:_,onRedirectTo:l[1]||(l[1]=w=>p(d).push({name:"TransportStoppage"}))},{default:e(()=>[s.uuid?(c(),y($,{key:0},{title:e(()=>[n(r(s.name),1)]),footer:e(()=>[a(S,null,{default:e(()=>[a(B,{design:"primary",onClick:l[0]||(l[0]=w=>p(d).push({name:"TransportStoppageEdit",params:{uuid:s.uuid}}))},{default:e(()=>[n(r(t.$trans("general.edit")),1)]),_:1})]),_:1})]),default:e(()=>[H("dl",N,[a(i,{label:t.$trans("transport.stoppage.props.name")},{default:e(()=>[n(r(s.name),1)]),_:1},8,["label"]),a(i,{class:"col-span-1 sm:col-span-2",label:t.$trans("transport.stoppage.props.description")},{default:e(()=>[n(r(s.description),1)]),_:1},8,["label"]),a(i,{label:t.$trans("general.created_at")},{default:e(()=>[n(r(s.createdAt.formatted),1)]),_:1},8,["label"]),a(i,{label:t.$trans("general.updated_at")},{default:e(()=>[n(r(s.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):A("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{E as default};
