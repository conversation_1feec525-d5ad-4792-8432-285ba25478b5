import{I as v,l as b,r as l,q as $,o as f,w as m,d,e as i,f as r,s as g,t as h,K as F,u as P,a as x,F as O}from"./app-DvIo72ZO.js";const U={class:"grid grid-cols-3 gap-6"},j={class:"col-span-3 sm:col-span-1"},q={class:"col-span-3 sm:col-span-1"},T={class:"col-span-3 sm:col-span-1"},y={name:"AcademicBatchForm"},H=Object.assign(y,{setup(A){const n={name:"",course:"",maxStrength:"",rollNumberPrefix:"",position:"",pgAccount:"",description:""},o="academic/batch/",c=v(o),u=b({courses:[]}),a=b({...n}),p=s=>{Object.assign(u,s)},B=s=>{Object.assign(n,{...s,course:s.course.uuid}),Object.assign(a,F(n))};return(s,t)=>{const _=l("BaseInput"),S=l("BaseSelect"),V=l("FormAction");return f(),$(V,{"has-setup-wizard":!0,"pre-requisites":!0,onSetPreRequisites:p,"init-url":o,"init-form":n,form:a,setForm:B,redirect:"AcademicBatch"},{default:m(()=>[d("div",U,[d("div",j,[i(_,{type:"text",modelValue:a.name,"onUpdate:modelValue":t[0]||(t[0]=e=>a.name=e),name:"name",label:s.$trans("academic.batch.props.name"),error:r(c).name,"onUpdate:error":t[1]||(t[1]=e=>r(c).name=e),placeholder:"JSS 1A",autofocus:""},null,8,["modelValue","label","error"])]),d("div",q,[i(S,{modelValue:a.course,"onUpdate:modelValue":t[2]||(t[2]=e=>a.course=e),name:"course",label:s.$trans("academic.course.course"),"value-prop":"uuid",options:u.courses,error:r(c).course,"onUpdate:error":t[3]||(t[3]=e=>r(c).course=e)},{selectedOption:m(e=>[g(h(e.value.nameWithTerm),1)]),listOption:m(e=>[g(h(e.option.nameWithTerm),1)]),_:1},8,["modelValue","label","options","error"])]),d("div",T,[i(_,{type:"text",modelValue:a.maxStrength,"onUpdate:modelValue":t[4]||(t[4]=e=>a.maxStrength=e),name:"maxStrength",label:s.$trans("academic.batch.props.max_strength"),error:r(c).maxStrength,"onUpdate:error":t[5]||(t[5]=e=>r(c).maxStrength=e),placeholder:"50"},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),N={name:"AcademicBatchAction"},k=Object.assign(N,{setup(A){const n=P();return(o,c)=>{const u=l("PageHeaderAction"),a=l("PageHeader"),p=l("ParentTransition");return f(),x(O,null,[i(a,{title:o.$trans(r(n).meta.trans,{attribute:o.$trans(r(n).meta.label)}),navs:[{label:o.$trans("academic.academic"),path:"Academic"},{label:o.$trans("academic.batch.batch"),path:"AcademicBatchList"}]},{default:m(()=>[i(u,{name:"AcademicBatch",title:o.$trans("academic.batch.batch"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),i(p,{appear:"",visibility:!0},{default:m(()=>[i(H)]),_:1})],64)}}});export{k as default};
