import{l as w,r as n,q as u,o as p,w as e,d as F,e as l,h as M,j as R,y as _,m as j,f as o,a as L,F as N,v as O,s,b as g,t as i}from"./app-DvIo72ZO.js";import{_ as U}from"./ModuleDropdown-Cjwc1GLq.js";const q={class:"grid grid-cols-3 gap-6"},z={class:"col-span-3 sm:col-span-1"},G={__name:"Filter",emits:["hide"],setup(S,{emit:d}){const f=d,v={name:""},y=w({...v});return(b,m)=>{const c=n("BaseInput"),h=n("FilterForm");return p(),u(h,{"init-form":v,form:y,onHide:m[1]||(m[1]=t=>f("hide"))},{default:e(()=>[F("div",q,[F("div",z,[l(c,{type:"text",modelValue:y.name,"onUpdate:modelValue":m[0]||(m[0]=t=>y.name=t),name:"name",label:b.$trans("employee.payroll.salary_template.props.name")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},J={name:"EmployeePayrollSalaryTemplateList"},W=Object.assign(J,{setup(S){const d=M(),f=R("emitter");let v=["filter"];_("salary-template:create")&&v.unshift("create");let y=[];_("salary-template:export")&&(y=["print","pdf","excel"]);const b="employee/payroll/salaryTemplate/",m=j(!1),c=w({}),h=t=>{Object.assign(c,t)};return(t,r)=>{const B=n("PageHeaderAction"),E=n("PageHeader"),P=n("ParentTransition"),I=n("TextMuted"),$=n("DataCell"),k=n("FloatingMenuItem"),D=n("FloatingMenu"),H=n("DataRow"),T=n("BaseButton"),V=n("DataTable"),A=n("ListItem");return p(),u(A,{"init-url":b,onSetItems:h},{header:e(()=>[l(E,{title:t.$trans("employee.payroll.salary_template.salary_template"),navs:[{label:t.$trans("employee.employee"),path:"Employee"},{label:t.$trans("employee.payroll.payroll"),path:"EmployeePayroll"}]},{default:e(()=>[l(B,{url:"employee/payroll/salary-templates/",name:"EmployeePayrollSalaryTemplate",title:t.$trans("employee.payroll.salary_template.salary_template"),actions:o(v),"dropdown-actions":o(y),onToggleFilter:r[0]||(r[0]=a=>m.value=!m.value)},{moduleOption:e(()=>[l(U)]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[l(P,{appear:"",visibility:m.value},{default:e(()=>[l(G,{onRefresh:r[1]||(r[1]=a=>o(f).emit("listItems")),onHide:r[2]||(r[2]=a=>m.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[l(P,{appear:"",visibility:!0},{default:e(()=>[l(V,{header:c.headers,meta:c.meta,module:"employee.payroll.salary_template",onRefresh:r[4]||(r[4]=a=>o(f).emit("listItems"))},{actionButton:e(()=>[o(_)("salary-template:create")?(p(),u(T,{key:0,onClick:r[3]||(r[3]=a=>o(d).push({name:"EmployeePayrollSalaryTemplateCreate"}))},{default:e(()=>[s(i(t.$trans("global.add",{attribute:t.$trans("employee.payroll.salary_template.salary_template")})),1)]),_:1})):g("",!0)]),default:e(()=>[(p(!0),L(N,null,O(c.data,a=>(p(),u(H,{key:a.uuid,onDoubleClick:C=>o(d).push({name:"EmployeePayrollSalaryTemplateShow",params:{uuid:a.uuid}})},{default:e(()=>[l($,{name:"name"},{default:e(()=>[s(i(a.name)+" ",1),a.hasHourlyPayroll?(p(),u(I,{key:0,block:""},{default:e(()=>[s(i(t.$trans("employee.payroll.salary_template.props.hourly_payroll")),1)]),_:1})):g("",!0)]),_:2},1024),l($,{name:"alias"},{default:e(()=>[s(i(a.alias),1)]),_:2},1024),l($,{name:"salaryStructure"},{default:e(()=>[s(i(a.structuresCount),1)]),_:2},1024),l($,{name:"createdAt"},{default:e(()=>[s(i(a.createdAt.formatted),1)]),_:2},1024),l($,{name:"action"},{default:e(()=>[l(D,null,{default:e(()=>[l(k,{icon:"fas fa-arrow-circle-right",onClick:C=>o(d).push({name:"EmployeePayrollSalaryTemplateShow",params:{uuid:a.uuid}})},{default:e(()=>[s(i(t.$trans("general.show")),1)]),_:2},1032,["onClick"]),o(_)("salary-template:edit")?(p(),u(k,{key:0,icon:"fas fa-edit",onClick:C=>o(d).push({name:"EmployeePayrollSalaryTemplateEdit",params:{uuid:a.uuid}})},{default:e(()=>[s(i(t.$trans("general.edit")),1)]),_:2},1032,["onClick"])):g("",!0),o(_)("salary-template:create")?(p(),u(k,{key:1,icon:"fas fa-copy",onClick:C=>o(d).push({name:"EmployeePayrollSalaryTemplateDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[s(i(t.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):g("",!0),o(_)("salary-template:delete")?(p(),u(k,{key:2,icon:"fas fa-trash",onClick:C=>o(f).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[s(i(t.$trans("general.delete")),1)]),_:2},1032,["onClick"])):g("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{W as default};
