import{i as oe,l as M,n as ye,r as f,q as $,o as c,w as i,d as o,e as l,b as h,s as u,t as e,j as Y,m as I,L as X,a as v,f as n,Y as ae,F as G,v as W,O as fe,y as Q,c as be,N as ve}from"./app-DvIo72ZO.js";import{i as ge,t as he,a as ke}from"./table-FwhM-Z75.js";const $e={class:"grid grid-cols-3 gap-6"},Re={class:"col-span-3 sm:col-span-1"},Ve={class:"col-span-3 sm:col-span-1"},Ee={class:"col-span-3 sm:col-span-1"},we={class:"col-span-3 sm:col-span-1"},Te={class:"col-span-3 sm:col-span-1"},Ae={class:"col-span-3 sm:col-span-1"},Be={name:"RestrictedUserFilter"},Se=Object.assign(Be,{emits:["hide"],setup(b,{emit:D}){const t=D,T=oe(),E={name:"",codeNumber:"",userType:"",batches:[],students:[],restrictionUuid:""},x=M({...E}),g=M({isLoaded:!1,batches:[],students:[]}),m=M({paymentRestrictions:[],userTypes:[{value:"student",label:"Student"},{value:"guardian",label:"Guardian"}]}),d=async()=>{try{const _=await T.dispatch("finance/restrictedUser/preRequisite",{data:null});Object.assign(m,{paymentRestrictions:_.paymentRestrictions||[]})}catch(_){console.error("Failed to load prerequisites:",_)}};return ye(async()=>{await d(),g.isLoaded=!0}),(_,s)=>{const A=f("BaseInput"),q=f("BaseSelect"),R=f("BaseSelectSearch"),w=f("FilterForm");return c(),$(w,{"init-form":E,form:x,multiple:["batches","students"],onHide:s[6]||(s[6]=a=>t("hide"))},{default:i(()=>[o("div",$e,[o("div",Re,[l(A,{type:"text",modelValue:x.name,"onUpdate:modelValue":s[0]||(s[0]=a=>x.name=a),name:"name",label:_.$trans("contact.props.name")},null,8,["modelValue","label"])]),o("div",Ve,[l(A,{type:"text",modelValue:x.codeNumber,"onUpdate:modelValue":s[1]||(s[1]=a=>x.codeNumber=a),name:"codeNumber",label:_.$trans("student.admission.props.code_number")},null,8,["modelValue","label"])]),o("div",Ee,[l(q,{modelValue:x.userType,"onUpdate:modelValue":s[2]||(s[2]=a=>x.userType=a),name:"userType",label:_.$trans("general.user_type"),options:m.userTypes,"value-prop":"value","label-prop":"label",placeholder:_.$trans("general.all")},null,8,["modelValue","label","options","placeholder"])]),o("div",we,[g.isLoaded?(c(),$(R,{key:0,multiple:"",name:"batches",label:_.$trans("global.select",{attribute:_.$trans("academic.batch.batch")}),modelValue:x.batches,"onUpdate:modelValue":s[3]||(s[3]=a=>x.batches=a),"value-prop":"uuid","init-search":g.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:i(a=>[u(e(a.value.course.name)+" "+e(a.value.name),1)]),listOption:i(a=>[u(e(a.option.course.name)+" "+e(a.option.name),1)]),_:1},8,["label","modelValue","init-search"])):h("",!0)]),o("div",Te,[g.isLoaded?(c(),$(R,{key:0,multiple:"",name:"students",label:_.$trans("global.select",{attribute:_.$trans("student.student")}),modelValue:x.students,"onUpdate:modelValue":s[4]||(s[4]=a=>x.students=a),"value-prop":"uuid","init-search":g.students,"search-key":"name","search-action":"student/summary"},{selectedOption:i(a=>[u(e(a.value.name)+" ("+e(a.value.codeNumber)+") ",1)]),listOption:i(a=>[u(e(a.option.name)+" ("+e(a.option.codeNumber)+") ",1)]),_:1},8,["label","modelValue","init-search"])):h("",!0)]),o("div",Ae,[l(q,{modelValue:x.restrictionUuid,"onUpdate:modelValue":s[5]||(s[5]=a=>x.restrictionUuid=a),name:"restrictionUuid",label:_.$trans("finance.payment_restriction.payment_restriction"),options:m.paymentRestrictions,"value-prop":"uuid","label-prop":"name",placeholder:_.$trans("general.all")},null,8,["modelValue","label","options","placeholder"])])])]),_:1},8,["form"])}}}),De={class:"space-y-6"},qe={class:"bg-gray-50 p-4 rounded-lg"},Fe={class:"font-medium text-gray-900 mb-2"},Ie={class:"grid grid-cols-2 gap-4 text-sm"},Ce={class:"font-medium"},Ne={class:"font-medium"},Me={class:"font-medium"},Oe={class:"font-medium"},je={key:0,class:"bg-yellow-50 border border-yellow-200 p-4 rounded-lg"},Pe={class:"font-medium text-yellow-800 mb-2"},Le={class:"grid grid-cols-2 gap-4 text-sm text-yellow-700"},Ue={class:"font-medium"},He={class:"font-medium"},ze={key:0},Ge={class:"font-medium"},Ye={key:1},Je={class:"font-medium"},Ke={key:1,class:"space-y-4"},Qe={class:"flex justify-end space-x-3 pt-4 border-t"},We={name:"ExemptionForm"},Xe=Object.assign(We,{props:{userRestriction:{type:Object,required:!0},preRequisites:{type:Object,required:!0}},emits:["submit","cancel"],setup(b,{emit:D}){const t=Y("$trans"),T=b,E=D,x=I(!1),g=I({}),m=M({exemptionType:"",exemptionDuration:"",exemptionReason:"",exemptionExpiresAt:""});X(()=>m.exemptionDuration,_=>{_!=="temporary"&&(m.exemptionExpiresAt="")});const d=async()=>{if(!x.value){x.value=!0,g.value={};try{if(T.userRestriction.isExempted)E("submit",{});else{const _={exemption_type:m.exemptionType,exemption_duration:m.exemptionDuration,exemption_reason:m.exemptionReason,exemption_expires_at:m.exemptionExpiresAt||null};E("submit",_)}}catch{}finally{x.value=!1}}};return(_,s)=>{var a,S,P,L,U;const A=f("BaseSelect"),q=f("DatePicker"),R=f("BaseTextarea"),w=f("BaseButton");return c(),v("form",{onSubmit:ae(d,["prevent"])},[o("div",De,[o("div",qe,[o("h4",Fe,e(n(t)("finance.payment_restriction.user_information")),1),o("div",Ie,[o("div",null,[o("span",Ce,e(n(t)("general.name"))+":",1),u(" "+e(b.userRestriction.displayName),1)]),o("div",null,[o("span",Ne,e(n(t)("general.type"))+":",1),u(" "+e(b.userRestriction.userType),1)]),o("div",null,[o("span",Me,e(n(t)("finance.payment_restriction.payment_restriction"))+":",1),u(" "+e((a=b.userRestriction.paymentRestriction)==null?void 0:a.name),1)]),o("div",null,[o("span",Oe,e(n(t)("finance.fee.outstanding_amount"))+":",1),u(" "+e((S=b.userRestriction.outstandingAmount)==null?void 0:S.formatted),1)])])]),b.userRestriction.isExempted?(c(),v("div",je,[o("h4",Pe,e(n(t)("finance.payment_restriction.exemption.current_exemption")),1),o("div",Le,[o("div",null,[o("span",Ue,e(n(t)("finance.payment_restriction.exemption.type"))+":",1),u(" "+e((P=b.userRestriction.exemptionType)==null?void 0:P.label),1)]),o("div",null,[o("span",He,e(n(t)("finance.payment_restriction.exemption.duration"))+":",1),u(" "+e((L=b.userRestriction.exemptionDuration)==null?void 0:L.label),1)]),b.userRestriction.exemptionReason?(c(),v("div",ze,[o("span",Ge,e(n(t)("finance.payment_restriction.exemption.reason"))+":",1),u(" "+e(b.userRestriction.exemptionReason),1)])):h("",!0),b.userRestriction.exemptionExpiresAt?(c(),v("div",Ye,[o("span",Je,e(n(t)("finance.payment_restriction.exemption.expires_at"))+":",1),u(" "+e((U=b.userRestriction.exemptionExpiresAt)==null?void 0:U.formatted),1)])):h("",!0)])])):h("",!0),b.userRestriction.isExempted?h("",!0):(c(),v("div",Ke,[l(A,{modelValue:m.exemptionType,"onUpdate:modelValue":s[0]||(s[0]=F=>m.exemptionType=F),name:"exemptionType",label:n(t)("global.select",{attribute:n(t)("finance.payment_restriction.exemption.type")}),options:b.preRequisites.exemptionTypes,"value-prop":"value","label-prop":"label",error:g.value.exemption_type,required:""},null,8,["modelValue","label","options","error"]),l(A,{modelValue:m.exemptionDuration,"onUpdate:modelValue":s[1]||(s[1]=F=>m.exemptionDuration=F),name:"exemptionDuration",label:n(t)("global.select",{attribute:n(t)("finance.payment_restriction.exemption.duration")}),options:b.preRequisites.exemptionDurations,"value-prop":"value","label-prop":"label",error:g.value.exemption_duration,required:""},null,8,["modelValue","label","options","error"]),m.exemptionDuration==="temporary"?(c(),$(q,{key:0,modelValue:m.exemptionExpiresAt,"onUpdate:modelValue":s[2]||(s[2]=F=>m.exemptionExpiresAt=F),name:"exemptionExpiresAt",as:"datetime",label:n(t)("finance.payment_restriction.exemption.expires_at"),error:g.value.exemption_expires_at,"no-clear":"",required:""},null,8,["modelValue","label","error"])):h("",!0),l(R,{modelValue:m.exemptionReason,"onUpdate:modelValue":s[3]||(s[3]=F=>m.exemptionReason=F),name:"exemptionReason",label:n(t)("finance.payment_restriction.exemption.reason"),placeholder:n(t)("finance.payment_restriction.exemption.reason_placeholder"),error:g.value.exemption_reason,rows:3},null,8,["modelValue","label","placeholder","error"])])),o("div",Qe,[l(w,{type:"button",design:"secondary",onClick:s[4]||(s[4]=F=>_.$emit("cancel"))},{default:i(()=>[u(e(n(t)("global.cancel",{attribute:n(t)("finance.payment_restriction.exemption.exemption")})),1)]),_:1}),b.userRestriction.isExempted?(c(),$(w,{key:0,type:"submit",design:"danger",disabled:x.value},{default:i(()=>[u(e(n(t)("finance.payment_restriction.exemption.remove_exemption")),1)]),_:1},8,["disabled"])):(c(),$(w,{key:1,type:"submit",design:"primary",disabled:x.value},{default:i(()=>[u(e(n(t)("finance.payment_restriction.exemption.exempt_user")),1)]),_:1},8,["disabled"]))])])],32)}}}),Ze={class:"space-y-6"},et={class:"bg-gray-50 p-4 rounded-lg"},tt={class:"font-medium text-gray-900 mb-2"},nt={class:"max-h-32 overflow-y-auto space-y-1"},ot={class:"space-y-4"},at={class:"font-medium"},it={class:"text-xs text-gray-500"},st={class:"font-medium"},lt={class:"text-xs text-gray-500"},rt={key:0,class:"bg-yellow-50 border border-yellow-200 p-4 rounded-lg"},mt={class:"flex"},ut={class:"ml-3"},ct={class:"text-sm font-medium text-yellow-800"},pt={class:"mt-2 text-sm text-yellow-700"},dt={class:"flex justify-end space-x-3 pt-4 border-t"},_t={name:"BulkExemptionForm"},xt=Object.assign(_t,{props:{selectedUsers:{type:Array,required:!0},preRequisites:{type:Object,required:!0}},emits:["submit","cancel"],setup(b,{emit:D}){const t=Y("$trans"),T=b,E=D,x=I(!1),g=I({}),m=M({exemptionType:"",exemptionDuration:"",exemptionReason:"",exemptionExpiresAt:""});X(()=>m.exemptionDuration,_=>{_!=="temporary"&&(m.exemptionExpiresAt="")});const d=async()=>{if(!x.value){x.value=!0,g.value={};try{const _={restriction_uuids:T.selectedUsers.map(s=>s.uuid),exemption_type:m.exemptionType,exemption_duration:m.exemptionDuration,exemption_reason:m.exemptionReason,exemption_expires_at:m.exemptionExpiresAt||null};E("submit",_)}catch{}finally{x.value=!1}}};return(_,s)=>{const A=f("BaseSelect"),q=f("DatePicker"),R=f("BaseTextarea"),w=f("BaseButton");return c(),v("form",{onSubmit:ae(d,["prevent"])},[o("div",Ze,[o("div",et,[o("h4",tt,e(n(t)("finance.payment_restriction.exemption.selected_users"))+" ("+e(b.selectedUsers.length)+") ",1),o("div",nt,[(c(!0),v(G,null,W(b.selectedUsers,a=>(c(),v("div",{key:a.uuid,class:"text-sm text-gray-600"},e(a.displayName)+" ("+e(a.userType)+") ",1))),128))])]),o("div",ot,[l(A,{modelValue:m.exemptionType,"onUpdate:modelValue":s[0]||(s[0]=a=>m.exemptionType=a),name:"exemptionType",label:n(t)("global.select",{attribute:n(t)("finance.payment_restriction.exemption.type")}),options:b.preRequisites.exemptionTypes,error:g.value.exemption_type,required:""},{option:i(({option:a})=>[o("div",null,[o("div",at,e(a.label),1),o("div",it,e(a.description),1)])]),_:1},8,["modelValue","label","options","error"]),l(A,{modelValue:m.exemptionDuration,"onUpdate:modelValue":s[1]||(s[1]=a=>m.exemptionDuration=a),name:"exemptionDuration",label:n(t)("global.select",{attribute:n(t)("finance.payment_restriction.exemption.duration")}),options:b.preRequisites.exemptionDurations,error:g.value.exemption_duration,required:""},{option:i(({option:a})=>[o("div",null,[o("div",st,e(a.label),1),o("div",lt,e(a.description),1)])]),_:1},8,["modelValue","label","options","error"]),m.exemptionDuration==="temporary"?(c(),$(q,{key:0,modelValue:m.exemptionExpiresAt,"onUpdate:modelValue":s[2]||(s[2]=a=>m.exemptionExpiresAt=a),name:"exemptionExpiresAt",as:"datetime",label:n(t)("finance.payment_restriction.exemption.expires_at"),error:g.value.exemption_expires_at,"label-hint":n(t)("finance.payment_restriction.exemption.expires_at_help"),"no-clear":"",required:""},null,8,["modelValue","label","error","label-hint"])):h("",!0),l(R,{modelValue:m.exemptionReason,"onUpdate:modelValue":s[3]||(s[3]=a=>m.exemptionReason=a),name:"exemptionReason",label:n(t)("finance.payment_restriction.exemption.reason"),placeholder:n(t)("finance.payment_restriction.exemption.bulk_reason_placeholder"),error:g.value.exemption_reason,rows:4},null,8,["modelValue","label","placeholder","error"])]),m.exemptionType==="global"?(c(),v("div",rt,[o("div",mt,[s[5]||(s[5]=o("div",{class:"flex-shrink-0"},[o("i",{class:"fas fa-exclamation-triangle text-yellow-400"})],-1)),o("div",ut,[o("h3",ct,e(n(t)("finance.payment_restriction.exemption.global_exemption_warning")),1),o("div",pt,e(n(t)("finance.payment_restriction.exemption.global_exemption_description")),1)])])])):h("",!0),o("div",dt,[l(w,{type:"button",design:"secondary",onClick:s[4]||(s[4]=a=>_.$emit("cancel"))},{default:i(()=>[u(e(n(t)("global.cancel",{attribute:n(t)("finance.payment_restriction.exemption.exemption")})),1)]),_:1}),l(w,{type:"submit",design:"primary",disabled:x.value},{default:i(()=>[u(e(n(t)("finance.payment_restriction.exemption.exempt_selected_users")),1)]),_:1},8,["disabled"])])])],32)}}}),yt={key:0,class:"space-y-1"},ft={class:"flex gap-1"},bt={class:"text-xs text-gray-600"},vt={key:0},gt={key:1,class:"space-y-1"},ht={class:"text-xs text-gray-600"},kt={key:0},$t={key:2},Rt={key:3},Vt={key:0},Et={key:1},wt={name:"FinanceRestrictedUserList"},Bt=Object.assign(wt,{setup(b){const D=Y("emitter"),t=Y("$trans"),T=fe(),E=oe();let x=["filter"],g=[];Q("payment-restriction:export")&&(g=["print","pdf","excel"]);let m=[];Q("payment-restriction:exempt")&&(m=[{name:"bulk_exempt",label:t("finance.payment_restriction.exemption.bulk_exempt_users"),icon:"fas fa-user-shield"}]);const d=M({...ge}),_="finance/restrictedUser/",s=I(!1),A=I(!1),q=I(!1),R=I(null),w=I([]),a=I({}),S=M({}),P=k=>{Object.assign(S,k),d.items=[],d.pageItems=S.data.map(y=>y.uuid)},L=k=>k==="Student"?"primary":k==="Guardian"?"success":"secondary",U=be(()=>R.value?R.value.isExempted?t("finance.payment_restriction.exemption.remove_exemption"):t("finance.payment_restriction.exemption.exempt_user"):""),F=async k=>{R.value=k,await Z(),A.value=!0},Z=async()=>{try{const k=await E.dispatch("finance/restrictedUser/preRequisite",{data:""});a.value=k}catch{T.error(t("global.something_went_wrong"))}},ie=async k=>{var V,C;if(await ve(t("finance.payment_restriction.exemption.confirm_remove_exemption")))try{await E.dispatch("finance/restrictedUser/removeExemption",{uuid:k.uuid}),D.emit("listItems")}catch(H){T.error(((C=(V=H.response)==null?void 0:V.data)==null?void 0:C.message)||t("global.something_went_wrong"))}},se=async k=>{var y,V;try{R.value.isExempted?await E.dispatch("finance/restrictedUser/removeExemption",{uuid:R.value.uuid}):await E.dispatch("finance/restrictedUser/exemptUser",{uuid:R.value.uuid,form:k}),J(),D.emit("listItems")}catch(C){T.error(((V=(y=C.response)==null?void 0:y.data)==null?void 0:V.message)||t("global.something_went_wrong"))}},J=()=>{A.value=!1,R.value=null},le=async k=>{var y,V;try{await E.dispatch("finance/restrictedUser/bulkExemptUsers",{form:k}),K(),D.emit("listItems")}catch(C){T.error(((V=(y=C.response)==null?void 0:y.data)==null?void 0:V.message)||t("global.something_went_wrong"))}},K=()=>{q.value=!1,w.value=[]},re=()=>{d.items=[],d.global=!d.global},me=async k=>{if(k==="bulk_exempt"){if(d.items.length===0&&!d.global){T.error(t("finance.payment_restriction.exemption.select_at_least_one_user"));return}let y=[];if(d.global?y=S.data.filter(V=>!V.isExempted):y=S.data.filter(V=>d.items.includes(V.uuid)&&!V.isExempted),y.length===0){T.error(t("finance.payment_restriction.exemption.no_users_to_exempt"));return}w.value=y,await Z(),q.value=!0}};return X(()=>[d.items,d.pageItems],()=>{d.all=ke(d)}),(k,y)=>{const V=f("PageHeaderAction"),C=f("PageHeader"),H=f("ParentTransition"),ue=f("BaseArrayCheckbox"),ce=f("BaseCheckbox"),B=f("DataCell"),z=f("TextMuted"),O=f("BaseBadge"),ee=f("FloatingMenuItem"),pe=f("FloatingMenu"),de=f("DataRow"),_e=f("DataTable"),xe=f("ListItem"),te=f("BaseModal");return c(),v(G,null,[l(xe,{"init-url":_,onSetItems:P},{header:i(()=>[l(C,{title:n(t)("finance.payment_restriction.restricted_users"),navs:[{label:n(t)("finance.finance"),path:"Finance"}]},{default:i(()=>[l(V,{url:"finance/restricted-users/",name:"FinanceRestrictedUser",title:n(t)("finance.payment_restriction.restricted_user"),actions:n(x),"dropdown-actions":n(g),"bulk-actions":n(m),"show-bulk-action":d.items.length>0||d.global,headers:S.headers,onToggleFilter:y[0]||(y[0]=r=>s.value=!s.value),onOnBulkAction:me},null,8,["title","actions","dropdown-actions","bulk-actions","show-bulk-action","headers"])]),_:1},8,["title","navs"])]),filter:i(()=>[l(H,{appear:"",visibility:s.value},{default:i(()=>[l(Se,{onHide:y[1]||(y[1]=r=>s.value=!1)})]),_:1},8,["visibility"])]),default:i(()=>[l(H,{appear:"",visibility:!0},{default:i(()=>[l(_e,{onToggleSelectAll:y[4]||(y[4]=r=>d.items=n(he)(r,d)),onToggleGlobalSelect:re,selected:d,header:S.headers,meta:S.meta,module:"finance.restricted_user",onRefresh:y[5]||(y[5]=r=>n(D).emit("listItems"))},{default:i(()=>[(c(!0),v(G,null,W(S.data,r=>(c(),$(de,{key:r.id},{default:i(()=>[l(B,{name:"selectAll"},{default:i(()=>[d.global?h("",!0):(c(),$(ue,{key:0,items:d.items,"onUpdate:items":y[2]||(y[2]=p=>d.items=p),value:r.uuid},null,8,["items","value"])),d.global?(c(),$(ce,{key:1,modelValue:d.global,"onUpdate:modelValue":y[3]||(y[3]=p=>d.global=p)},null,8,["modelValue"])):h("",!0)]),_:2},1024),l(B,{name:"userName"},{default:i(()=>{var p;return[u(e(r.displayName)+" ",1),(p=r.user)!=null&&p.student?(c(),$(z,{key:0,block:""},{default:i(()=>[u(e(r.user.student.codeNumber),1)]),_:2},1024)):h("",!0)]}),_:2},1024),l(B,{name:"userType"},{default:i(()=>[l(O,{design:L(r.userType)},{default:i(()=>[u(e(r.userType),1)]),_:2},1032,["design"])]),_:2},1024),l(B,{name:"restrictionName"},{default:i(()=>{var p;return[u(e((p=r.paymentRestriction)==null?void 0:p.name)+" ",1),l(z,{block:""},{default:i(()=>{var N,j;return[u(e((j=(N=r.paymentRestriction)==null?void 0:N.type)==null?void 0:j.label),1)]}),_:2},1024)]}),_:2},1024),l(B,{name:"totalFees"},{default:i(()=>{var p;return[u(e((p=r.totalFees)==null?void 0:p.formatted),1)]}),_:2},1024),l(B,{name:"paidAmount"},{default:i(()=>{var p;return[u(e((p=r.paidAmount)==null?void 0:p.formatted),1)]}),_:2},1024),l(B,{name:"outstandingAmount"},{default:i(()=>{var p;return[u(e((p=r.outstandingAmount)==null?void 0:p.formatted),1)]}),_:2},1024),l(B,{name:"requiredAmount"},{default:i(()=>{var p;return[u(e((p=r.requiredAmount)==null?void 0:p.formatted)+" ",1),r.paymentPercentage?(c(),$(z,{key:0,block:""},{default:i(()=>[u(e(r.paymentPercentage)+"% paid ",1)]),_:2},1024)):h("",!0)]}),_:2},1024),l(B,{name:"restrictedAt"},{default:i(()=>{var p;return[u(e((p=r.restrictedAt)==null?void 0:p.formatted),1)]}),_:2},1024),l(B,{name:"exemptionStatus"},{default:i(()=>{var p,N,j,ne;return[r.isExempted&&r.isEffectivelyRestricted?(c(),v("div",yt,[o("div",ft,[l(O,{design:"warning",size:"sm"},{default:i(()=>[u(e(n(t)("finance.payment_restriction.exemption.exempted")),1)]),_:1}),l(O,{design:"danger",size:"sm"},{default:i(()=>[u(e(n(t)("finance.payment_restriction.exemption.expired")),1)]),_:1})]),o("div",bt,[o("div",null,e((p=r.exemptionType)==null?void 0:p.label),1),r.exemptionExpiresAt?(c(),v("div",vt,e(n(t)("finance.payment_restriction.exemption.expired_on"))+": "+e((N=r.exemptionExpiresAt)==null?void 0:N.formatted),1)):h("",!0)])])):r.isExempted?(c(),v("div",gt,[l(O,{design:"success",size:"sm"},{default:i(()=>[u(e(n(t)("finance.payment_restriction.exemption.exempted")),1)]),_:1}),o("div",ht,[o("div",null,e((j=r.exemptionType)==null?void 0:j.label),1),r.exemptionExpiresAt?(c(),v("div",kt,e(n(t)("finance.payment_restriction.exemption.expires"))+": "+e((ne=r.exemptionExpiresAt)==null?void 0:ne.formatted),1)):h("",!0)])])):r.isRestricted?(c(),v("div",$t,[l(O,{design:"danger",size:"sm"},{default:i(()=>[u(e(n(t)("finance.payment_restriction.restricted")),1)]),_:1})])):(c(),v("div",Rt,[l(O,{design:"secondary",size:"sm"},{default:i(()=>[u(e(n(t)("finance.payment_restriction.resolved")),1)]),_:1})]))]}),_:2},1024),l(B,{name:"batchInfo"},{default:i(()=>[r.userType==="Student"&&r.batchInfo&&!Array.isArray(r.batchInfo)?(c(),v("div",Vt,e(r.batchInfo.display),1)):r.userType==="Guardian"&&Array.isArray(r.batchInfo)?(c(),v("div",Et,[(c(!0),v(G,null,W(r.batchInfo,(p,N)=>(c(),v("div",{key:N,class:"mb-1"},[u(e(p.display)+" ",1),l(z,{block:"",class:"text-xs"},{default:i(()=>[u(e(p.student_name),1)]),_:2},1024)]))),128))])):h("",!0)]),_:2},1024),l(B,{name:"action"},{default:i(()=>[n(Q)("payment-restriction:exempt")?(c(),$(pe,{key:0},{default:i(()=>[r.isExempted?(c(),$(ee,{key:0,icon:"fas fa-user-times",onClick:p=>ie(r)},{default:i(()=>[u(e(n(t)("finance.payment_restriction.exemption.remove_exemption")),1)]),_:2},1032,["onClick"])):(c(),$(ee,{key:1,icon:"fas fa-user-shield",onClick:p=>F(r)},{default:i(()=>[u(e(n(t)("finance.payment_restriction.exemption.exempt_user")),1)]),_:2},1032,["onClick"]))]),_:2},1024)):h("",!0)]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["selected","header","meta"])]),_:1})]),_:1}),l(te,{visibility:A.value,onClose:J},{title:i(()=>[u(e(U.value),1)]),default:i(()=>[R.value?(c(),$(Xe,{key:0,"user-restriction":R.value,"pre-requisites":a.value,onSubmit:se,onCancel:J},null,8,["user-restriction","pre-requisites"])):h("",!0)]),_:1},8,["visibility"]),l(te,{visibility:q.value,onClose:K},{title:i(()=>[u(e(n(t)("finance.payment_restriction.exemption.bulk_exempt_users")),1)]),default:i(()=>[w.value.length>0?(c(),$(xt,{key:0,"selected-users":w.value,"pre-requisites":a.value,onSubmit:le,onCancel:K},null,8,["selected-users","pre-requisites"])):h("",!0)]),_:1},8,["visibility"])],64)}}});export{Bt as default};
