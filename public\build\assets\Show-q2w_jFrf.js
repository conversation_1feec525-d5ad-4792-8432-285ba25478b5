import{i as P,u as D,h as H,l as N,r as i,a as r,o,e as n,w as e,f as p,q as b,b as C,d as g,s as u,t as s,F as c,v as f}from"./app-DvIo72ZO.js";const E={class:"space-y-2"},R={class:"flex justify-center gap-2"},F={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},O={name:"CommunicationSMSShow"},z=Object.assign(O,{setup(q){P();const _=D(),V=H(),k={},w="communication/sms/",t=N({...k}),h=a=>{Object.assign(t,a)},S=a=>t.audiences.filter(m=>m.type===a);return(a,m)=>{const B=i("PageHeaderAction"),L=i("PageHeader"),d=i("ListItemView"),$=i("TextMuted"),T=i("ListContainerVertical"),v=i("BaseCard"),y=i("BaseDataView"),A=i("ShowButton"),j=i("DetailLayoutVertical"),I=i("ShowItem"),M=i("ParentTransition");return o(),r(c,null,[n(L,{title:a.$trans(p(_).meta.trans,{attribute:a.$trans(p(_).meta.label)}),navs:[{label:a.$trans("communication.communication"),path:"Communication"},{label:a.$trans("communication.sms.sms"),path:"CommunicationSMS"}]},{default:e(()=>[n(B,{name:"CommunicationSMS",title:a.$trans("communication.sms.sms"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(M,{appear:"",visibility:!0},{default:e(()=>[n(I,{"init-url":w,uuid:p(_).params.uuid,"module-uuid":p(_).params.muuid,onSetItem:h,onRedirectTo:m[0]||(m[0]=l=>p(V).push({name:"CommunicationSMS",params:{uuid:t.uuid}}))},{default:e(()=>[t.uuid?(o(),b(j,{key:0},{detail:e(()=>[g("div",E,[n(v,{"no-padding":"","no-content-padding":""},{title:e(()=>[u(" #"+s(t.subjectExcerpt),1)]),action:e(()=>m[1]||(m[1]=[])),default:e(()=>[n(T,null,{default:e(()=>[n(d,{label:a.$trans("communication.sms.props.subject")},{default:e(()=>[u(s(t.subject),1)]),_:1},8,["label"]),n(d,{label:a.$trans("communication.sms.props.audience")},{default:e(()=>[u(s(t.studentAudienceType.label)+" ",1),(o(!0),r(c,null,f(S("student"),l=>(o(),b($,{block:""},{default:e(()=>[u(s(l.name),1)]),_:2},1024))),256))]),_:1},8,["label"]),n(d,{label:a.$trans("communication.sms.props.audience")},{default:e(()=>[t.employeeAudienceType.value?(o(),r(c,{key:0},[u(s(t.employeeAudienceType.label)+" ",1),(o(!0),r(c,null,f(S("employee"),l=>(o(),b($,{block:""},{default:e(()=>[u(s(l.name),1)]),_:2},1024))),256))],64)):(o(),r(c,{key:1},[u("-")],64))]),_:1},8,["label"]),n(d,{label:a.$trans("general.created_at")},{default:e(()=>[u(s(t.createdAt.formatted),1)]),_:1},8,["label"]),n(d,{label:a.$trans("general.updated_at")},{default:e(()=>[u(s(t.updatedAt.formatted),1)]),_:1},8,["label"])]),_:1})]),_:1})])]),default:e(()=>[t.uuid?(o(),b(v,{key:0},{title:e(()=>[g("div",R,s(t.subject),1)]),footer:e(()=>[n(A)]),default:e(()=>[g("dl",F,[n(y,null,{default:e(()=>[u(s(t.content),1)]),_:1}),n(y,{label:a.$trans("communication.sms.props.inclusion")},{default:e(()=>[(o(!0),r(c,null,f(t.inclusionList,l=>(o(),r("div",{key:l},s(l),1))),128))]),_:1},8,["label"]),n(y,{label:a.$trans("communication.sms.props.exclusion")},{default:e(()=>[(o(!0),r(c,null,f(t.exclusionList,l=>(o(),r("div",{key:l},s(l),1))),128))]),_:1},8,["label"])])]),_:1})):C("",!0)]),_:1})):C("",!0)]),_:1},8,["uuid","module-uuid"])]),_:1})],64)}}});export{z as default};
