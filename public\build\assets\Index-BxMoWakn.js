import{u as re,l as E,n as ue,r as f,q as m,o as n,w as e,d as C,e as u,s as i,a as _,b as d,t as s,h as me,i as de,j as K,y as k,B as R,m as Q,z as ce,f as t,F as I,v as V,A as H,N as P}from"./app-DvIo72ZO.js";const fe={class:"grid grid-cols-3 gap-6"},pe={class:"col-span-3 sm:col-span-1"},_e={key:0},be={key:0},he={class:"col-span-3 sm:col-span-1"},ke={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(N,{emit:F}){const x=re(),o=F,v={batches:[],exams:[]},$=E({...v}),B=E({batches:[],exams:[],isLoaded:!(x.query.batches||x.query.exams)});return ue(async()=>{B.batches=x.query.batches?x.query.batches.split(","):[],B.exams=x.query.exams?x.query.exams.split(","):[],B.isLoaded=!0}),(b,S)=>{const L=f("BaseSelect"),h=f("BaseSelectSearch"),A=f("FilterForm");return n(),m(A,{"init-form":v,form:$,multiple:["exams","batches"],onHide:S[2]||(S[2]=r=>o("hide"))},{default:e(()=>[C("div",fe,[C("div",pe,[u(L,{multiple:"",modelValue:$.exams,"onUpdate:modelValue":S[0]||(S[0]=r=>$.exams=r),name:"exams",label:b.$trans("global.select",{attribute:b.$trans("exam.exam")}),"track-by":["name"],"value-prop":"uuid",options:N.preRequisites.exams},{selectedOption:e(r=>{var T,j;return[i(s(r.value.name)+" ",1),r.value.term?(n(),_("span",_e,"("+s(((j=(T=r.value.term)==null?void 0:T.division)==null?void 0:j.name)||b.$trans("general.all"))+")",1)):d("",!0)]}),listOption:e(r=>{var T,j;return[i(s(r.option.name)+" ",1),r.option.term?(n(),_("span",be,"("+s(((j=(T=r.option.term)==null?void 0:T.division)==null?void 0:j.name)||b.$trans("general.all"))+")",1)):d("",!0)]}),_:1},8,["modelValue","label","options"])]),C("div",he,[B.isLoaded?(n(),m(h,{key:0,multiple:"",name:"batches",label:b.$trans("global.select",{attribute:b.$trans("academic.batch.batch")}),modelValue:$.batches,"onUpdate:modelValue":S[1]||(S[1]=r=>$.batches=r),"value-prop":"uuid","init-search":B.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:e(r=>[i(s(r.value.course.name)+" "+s(r.value.name),1)]),listOption:e(r=>[i(s(r.option.course.nameWithTerm)+" "+s(r.option.name),1)]),_:1},8,["label","modelValue","init-search"])):d("",!0)])])]),_:1},8,["form"])}}},xe={class:"grid grid-cols-1 gap-4 px-4 pt-4 lg:grid-cols-2"},ve={class:"py-2 text-center"},ye=["onClick"],ge={key:0},Ce={key:0},$e={class:"text-xl font-semibold"},Se={class:"ml-1 space-x-1"},we={key:0,class:"fas fa-file-lines"},Fe={key:1,class:"fas fa-id-card"},Be={name:"ExamScheduleList"},Te=Object.assign(Be,{setup(N){const F=me(),x=de(),o=K("$trans"),v=K("emitter");let $=[];k("exam-schedule:create")&&$.unshift("create"),R(["student","guardian"],"any")||$.unshift("filter");let B=[];k("exam-schedule:export")&&(B=["print","pdf","excel"]);const b="exam/schedule/",S=E({exams:[]}),L=[{key:"subject",label:o("academic.subject.subject"),visibility:!0},{key:"date",label:o("exam.schedule.props.date"),visibility:!0},{key:"assessment",label:o("exam.assessment.assessment"),visibility:!0}],h=Q(!1),A=Q(!1),r=E({}),T=p=>{Object.assign(r,p)},j=p=>{Object.assign(S,p)},X=p=>p.filter(c=>c.date.value),Y=async p=>{await P()&&(h.value=!0,await x.dispatch(b+"copyToCourse",{uuid:p.uuid}).then(c=>{h.value=!1,v.emit("listItems")}).catch(c=>{h.value=!1}))},U=async(p,c)=>{await P()&&(h.value=!0,await x.dispatch(b+"updateForm",{uuid:p.uuid,value:c}).then(D=>{h.value=!1,v.emit("listItems")}).catch(D=>{h.value=!1}))},W=async(p,c)=>{await P()&&(h.value=!0,await x.dispatch(b+"togglePublishAdmitCard",{uuid:p.uuid}).then(D=>{h.value=!1,v.emit("listItems")}).catch(D=>{h.value=!1}))};return(p,c)=>{const D=f("PageHeaderAction"),Z=f("PageHeader"),M=f("ParentTransition"),y=f("TextMuted"),z=f("BaseBadge"),g=f("DataCell"),G=f("DataRow"),ee=f("SimpleTable"),te=f("CardView"),ae=f("Pagination"),se=f("CardList"),w=f("FloatingMenuItem"),ne=f("FloatingMenu"),ie=f("BaseButton"),oe=f("DataTable"),le=f("ListItem"),O=ce("tooltip");return n(),m(le,{"init-url":b,"pre-requisites":!t(R)(["student","guardian"],"any"),onSetPreRequisites:j,onSetItems:T},{header:e(()=>[u(Z,{title:t(o)("exam.schedule.schedule"),navs:[{label:t(o)("exam.exam"),path:"Exam"}]},{default:e(()=>[u(D,{url:"exam/schedules/",name:"ExamSchedule",title:t(o)("exam.schedule.schedule"),actions:t($),"dropdown-actions":t(B),onToggleFilter:c[0]||(c[0]=a=>A.value=!A.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[u(M,{appear:"",visibility:A.value},{default:e(()=>[u(ke,{onRefresh:c[1]||(c[1]=a=>t(v).emit("listItems")),"pre-requisites":S,onHide:c[2]||(c[2]=a=>A.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:e(()=>[t(R)(["student","guardian"],"any")?(n(),m(M,{key:0,appear:"",visibility:!0},{default:e(()=>[u(se,{header:r.headers,meta:r.meta},{content:e(()=>[C("div",$e,s(t(o)("dashboard.nothing_to_show")),1)]),default:e(()=>[C("div",xe,[(n(!0),_(I,null,V(r.data,a=>(n(),m(te,{key:a.uuid,"no-padding":""},{default:e(()=>[C("div",ve,[i(s(a.exam.name)+" ",1),t(R)("student")&&a.hasForm?H((n(),_("span",{key:0,class:"cursor-pointer",onClick:l=>t(F).push({name:"ExamScheduleFormSubmission",params:{uuid:a.uuid}})},c[6]||(c[6]=[C("i",{class:"fas fa-arrow-up-right-from-square"},null,-1)]),8,ye)),[[O,t(o)("exam.schedule.form")]]):d("",!0),u(y,{block:""},{default:e(()=>[i(s(a.batch.course.name)+" "+s(a.batch.name),1)]),_:2},1024),a.isReassessment?(n(),m(z,{key:1},{default:e(()=>[i(s(t(o)("exam.schedule.reassessment")+" ("+a.attempt.label+")"),1)]),_:2},1024)):d("",!0)]),a.records.length>0?(n(),m(ee,{key:0,header:L},{default:e(()=>[(n(!0),_(I,null,V(X(a.records),l=>(n(),m(G,{key:l.uuid},{default:e(()=>[u(g,{name:"subject"},{default:e(()=>[i(s(l.subject.name)+" ",1),l.subject.code?(n(),_("span",ge,"("+s(l.subject.code)+")",1)):d("",!0),l.hasGrading?(n(),m(y,{key:1,block:""},{default:e(()=>[i("("+s(t(o)("exam.schedule.props.grading"))+")",1)]),_:1})):d("",!0)]),_:2},1024),u(g,{name:"date"},{default:e(()=>[i(s(l.date.formatted)+" ",1),l.startTime.value?(n(),m(y,{key:0,block:""},{default:e(()=>[i(s(l.startTime.formatted)+" ",1),l.endTime.value?(n(),_("span",Ce,"- "+s(l.endTime.formatted),1)):d("",!0)]),_:2},1024)):d("",!0)]),_:2},1024),u(g,{name:"assessment"},{default:e(()=>[(n(!0),_(I,null,V(l.assessments,q=>(n(),_("div",null,[i(s(q.name)+" ",1),u(y,null,{default:e(()=>[i(s(q.maxMark),1)]),_:2},1024)]))),256))]),_:2},1024)]),_:2},1024))),128))]),_:2},1024)):d("",!0)]),_:2},1024))),128))]),C("div",null,[u(ae,{"card-view":"",meta:r.meta,onRefresh:c[3]||(c[3]=a=>t(v).emit("listItems"))},null,8,["meta"])])]),_:1},8,["header","meta"])]),_:1})):(n(),m(M,{key:1,appear:"",visibility:!0},{default:e(()=>[u(oe,{header:r.headers,meta:r.meta,module:"exam.schedule",onRefresh:c[5]||(c[5]=a=>t(v).emit("listItems"))},{actionButton:e(()=>[t(k)("exam-schedule:create")?(n(),m(ie,{key:0,onClick:c[4]||(c[4]=a=>t(F).push({name:"ExamScheduleCreate"}))},{default:e(()=>[i(s(t(o)("global.add",{attribute:t(o)("exam.schedule.schedule")})),1)]),_:1})):d("",!0)]),default:e(()=>[(n(!0),_(I,null,V(r.data,a=>(n(),m(G,{key:a.uuid,onDoubleClick:l=>t(F).push({name:"ExamScheduleShow",params:{uuid:a.uuid}})},{default:e(()=>[u(g,{name:"exam"},{default:e(()=>{var l;return[C("span",null,s(a.exam.name),1),C("span",Se,[a.hasForm&&t(k)("exam-schedule:edit")?H((n(),_("i",we,null,512)),[[O,t(o)("exam.form.form")]]):d("",!0),a.publishAdmitCard?H((n(),_("i",Fe,null,512)),[[O,t(o)("exam.admit_card.admit_card")]]):d("",!0)]),u(y,{block:""},{default:e(()=>{var q,J;return[i(s((J=(q=a.exam.term)==null?void 0:q.division)==null?void 0:J.name),1)]}),_:2},1024),a.isReassessment?(n(),m(z,{key:0},{default:e(()=>[i(s(t(o)("exam.schedule.reassessment")+" ("+a.attempt.label+")"),1)]),_:2},1024)):d("",!0),(l=a.examConfig)!=null&&l.publishMarksheet?(n(),m(y,{key:1,block:"",class:"text-success"},{default:e(()=>[i(s(t(o)("exam.marksheet.published")),1)]),_:1})):d("",!0)]}),_:2},1024),u(g,{name:"batch"},{default:e(()=>[i(s(a.batch.course.name)+" ",1),u(y,{block:""},{default:e(()=>[i(s(a.batch.name),1)]),_:2},1024)]),_:2},1024),u(g,{name:"assessment"},{default:e(()=>{var l;return[i(s(a.assessment.name)+" ",1),u(y,{block:""},{default:e(()=>{var q;return[i(s((q=a.observation)==null?void 0:q.name),1)]}),_:2},1024),((l=a.marksheetStatus)==null?void 0:l.value)=="processed"?(n(),m(y,{key:0,block:"",class:"text-info"},{default:e(()=>[i(s(t(o)("exam.marksheet.processed")),1)]),_:1})):d("",!0)]}),_:2},1024),u(g,{name:"grade"},{default:e(()=>[i(s(a.grade.name),1)]),_:2},1024),u(g,{name:"period"},{default:e(()=>[i(s(a.startDate.formatted)+" ",1),u(y,{block:""},{default:e(()=>[i(s(a.endDate.formatted),1)]),_:2},1024)]),_:2},1024),u(g,{name:"createdAt"},{default:e(()=>[i(s(a.createdAt.formatted),1)]),_:2},1024),u(g,{name:"action"},{default:e(()=>[u(ne,null,{default:e(()=>[t(R)("student")&&a.hasForm?(n(),m(w,{key:0,icon:"fas fa-file-lines",onClick:l=>t(F).push({name:"ExamScheduleFormSubmission",params:{uuid:a.uuid}})},{default:e(()=>[i(s(t(o)("exam.schedule.form")),1)]),_:2},1032,["onClick"])):d("",!0),u(w,{icon:"fas fa-arrow-circle-right",onClick:l=>t(F).push({name:"ExamScheduleShow",params:{uuid:a.uuid}})},{default:e(()=>[i(s(t(o)("general.show")),1)]),_:2},1032,["onClick"]),t(k)("exam-schedule:edit")?(n(),m(w,{key:1,icon:"fas fa-edit",onClick:l=>t(F).push({name:"ExamScheduleEdit",params:{uuid:a.uuid}})},{default:e(()=>[i(s(t(o)("general.edit")),1)]),_:2},1032,["onClick"])):d("",!0),t(k)("exam-schedule:create")&&a.batch.course.batchWithSameSubject?(n(),m(w,{key:2,icon:"fas fa-copy",onClick:l=>Y(a)},{default:e(()=>[i(s(t(o)("exam.schedule.copy_to_course")),1)]),_:2},1032,["onClick"])):d("",!0),t(k)("exam-schedule:edit")&&!a.hasForm?(n(),m(w,{key:3,icon:"fas fa-file",onClick:l=>U(a,!0)},{default:e(()=>[i(s(t(o)("global.enable",{attribute:t(o)("exam.form.form")})),1)]),_:2},1032,["onClick"])):d("",!0),t(k)("exam-schedule:edit")&&a.hasForm?(n(),m(w,{key:4,icon:"fas fa-file",onClick:l=>U(a,!1)},{default:e(()=>[i(s(t(o)("global.disable",{attribute:t(o)("exam.form.form")})),1)]),_:2},1032,["onClick"])):d("",!0),t(k)("exam-schedule:edit")&&!a.publishAdmitCard?(n(),m(w,{key:5,icon:"fas fa-id-card",onClick:l=>W(a)},{default:e(()=>[i(s(t(o)("global.publish",{attribute:t(o)("exam.admit_card.admit_card")})),1)]),_:2},1032,["onClick"])):d("",!0),t(k)("exam-schedule:edit")&&a.publishAdmitCard?(n(),m(w,{key:6,icon:"fas fa-id-card",onClick:l=>W(a)},{default:e(()=>[i(s(t(o)("global.unpublish",{attribute:t(o)("exam.admit_card.admit_card")})),1)]),_:2},1032,["onClick"])):d("",!0),t(k)("exam-schedule:delete")?(n(),m(w,{key:7,icon:"fas fa-trash",onClick:l=>t(v).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[i(s(t(o)("general.delete")),1)]),_:2},1032,["onClick"])):d("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1}))]),_:1},8,["pre-requisites"])}}});export{Te as default};
