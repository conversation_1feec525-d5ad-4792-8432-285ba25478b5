import{u as J,h as x,j as ee,H as D,I as te,g as C,l as T,n as se,K as re,r as p,q as U,o as b,w as l,e as o,d,s as u,t as m,f as n,a as z,b as N,J as h,F as ne}from"./app-DvIo72ZO.js";const ae={class:"mt-4 grid grid-cols-3 gap-6"},oe={class:"col-span-3 sm:col-span-1"},de={class:"col-span-3 sm:col-span-1"},le={class:"col-span-3 sm:col-span-1"},ie={class:"col-span-3 sm:col-span-1"},ue={class:"col-span-3 sm:col-span-1"},me={class:"col-span-3 sm:col-span-1"},pe={class:"grid grid-cols-3 gap-6"},ce={class:"col-span-3 sm:col-span-1"},be={class:"col-span-3 sm:col-span-1"},ge={class:"col-span-3 sm:col-span-1"},fe={class:"mt-4 grid grid-cols-3 gap-6"},ye={class:"col-span-3 sm:col-span-1"},Ae={class:"col-span-3 sm:col-span-1"},Ne={class:"col-span-3 sm:col-span-1"},Ve={class:"mt-4 grid grid-cols-3 gap-6"},Ue={class:"col-span-3 sm:col-span-1"},qe={class:"col-span-3 sm:col-span-1"},ve={class:"col-span-3 sm:col-span-1"},Le={class:"col-span-3 sm:col-span-1"},Ie={class:"col-span-3 sm:col-span-1"},$e={class:"col-span-3 sm:col-span-1"},Ce={class:"col-span-3 sm:col-span-1"},Ee={class:"col-span-3 sm:col-span-1"},Pe={key:0,class:"col-span-3 sm:col-span-1"},ke={key:1,class:"col-span-3 sm:col-span-1"},Be={class:"grid grid-cols-3 gap-6"},Re={class:"grid grid-cols-3 gap-6"},Se={class:"col-span-3 sm:col-span-1"},Te={class:"mt-4 grid grid-cols-3 gap-6"},ze={class:"grid grid-cols-1"},Fe={class:"col"},Ge={name:"StudentProfileEditRequest"},je=Object.assign(Ge,{props:{student:{type:Object,default(){return{}}}},setup(c){const q=J();x(),ee("emitter");const y=c,v={contactNumber:"",alternateContactNumber:"",email:"",fatherContactNumber:"",fatherEmail:"",motherContactNumber:"",motherEmail:"",uniqueIdNumber1:"",uniqueIdNumber2:"",uniqueIdNumber3:"",birthPlace:"",nationality:"",motherTongue:"",bloodGroup:"",religion:"",category:"",caste:"",presentAddress:{},permanentAddress:{sameAsPresentAddress:!1},media:[],mediaUpdated:!1,mediaToken:D(),mediaHash:[]},E="student/profileEditRequest/",S="student/",a=te(E),K=C("contact.uniqueIdNumber1Label"),Q=C("contact.uniqueIdNumber2Label"),W=C("contact.uniqueIdNumber3Label"),X=C("contact.enableCategoryField"),Y=C("contact.enableCasteField"),L=T({genders:[],bloodGroups:[],religions:[],categories:[],castes:[],maritalStatuses:[]}),s=T({...v}),I=T({religion:"",category:"",caste:"",isLoaded:!q.params.uuid}),Z=r=>{Object.assign(L,r)},_=()=>{s.mediaToken=D(),s.mediaHash=[]};return se(async()=>{var e,P,g,f,i,V,A,$,k,B,R,t,F,G,j,H,O,w,M;let r=(e=y.student)==null?void 0:e.contact;Object.assign(v,{contactNumber:r.contactNumber,alternateContactNumber:(P=r.alternateRecords)==null?void 0:P.contactNumber,email:r.email,alternateEmail:(g=r.alternateRecords)==null?void 0:g.email,fatherContactNumber:r.fatherContactNumber,fatherEmail:r.fatherEmail,motherContactNumber:r.motherContactNumber,motherEmail:r.motherEmail,uniqueIdNumber1:r.uniqueIdNumber1,uniqueIdNumber2:r.uniqueIdNumber2,uniqueIdNumber3:r.uniqueIdNumber3,birthPlace:r.birthPlace,bloodGroup:((f=r.bloodGroup)==null?void 0:f.value)||"",nationality:r.nationality,motherTongue:r.motherTongue,religion:((i=r.religion)==null?void 0:i.uuid)||"",category:((V=r.category)==null?void 0:V.uuid)||"",caste:((A=r.caste)==null?void 0:A.uuid)||"",presentAddress:{addressLine1:($=r.presentAddress)==null?void 0:$.addressLine1,addressLine2:(k=r.presentAddress)==null?void 0:k.addressLine2,city:(B=r.presentAddress)==null?void 0:B.city,state:(R=r.presentAddress)==null?void 0:R.state,zipcode:(t=r.presentAddress)==null?void 0:t.zipcode,country:(F=r.presentAddress)==null?void 0:F.country},permanentAddress:{sameAsPresentAddress:r.sameAsPresentAddress,addressLine1:(G=r.permanentAddress)==null?void 0:G.addressLine1,addressLine2:(j=r.permanentAddress)==null?void 0:j.addressLine2,city:(H=r.permanentAddress)==null?void 0:H.city,state:(O=r.permanentAddress)==null?void 0:O.state,zipcode:(w=r.permanentAddress)==null?void 0:w.zipcode,country:(M=r.permanentAddress)==null?void 0:M.country}}),Object.assign(s,re(v)),I.isLoaded=!0}),(r,e)=>{const P=p("BaseAlert"),g=p("BaseLabel"),f=p("TextContent"),i=p("BaseInput"),V=p("BaseFieldset"),A=p("BaseSelect"),$=p("AddressInput"),k=p("BaseSwitch"),B=p("MediaUpload"),R=p("FormAction");return b(),U(R,{"no-data-fetch":"","pre-requisites":!0,onSetPreRequisites:Z,"pre-requisite-url":S,"init-url":E,uuid:n(q).params.uuid,"init-form":v,form:s,"keep-adding":!1,redirect:{name:"StudentProfileEditRequest",params:{uuid:c.student.uuid}},onResetMediaFiles:_},{default:l(()=>[o(P,{design:"info",size:"xs"},{default:l(()=>[u(m(r.$trans("student.edit_request.upload_document_info")),1)]),_:1}),d("div",ae,[d("div",oe,[o(g,null,{default:l(()=>[u(m(r.$trans("contact.props.name")),1)]),_:1}),o(f,{class:"mt-1"},{default:l(()=>[u(m(c.student.name),1)]),_:1})]),d("div",de,[o(g,null,{default:l(()=>[u(m(r.$trans("contact.props.gender")),1)]),_:1}),o(f,{class:"mt-1"},{default:l(()=>[u(m(c.student.contact.gender.label),1)]),_:1})]),d("div",le,[o(g,null,{default:l(()=>[u(m(r.$trans("contact.props.birth_date")),1)]),_:1}),o(f,{class:"mt-1"},{default:l(()=>[u(m(c.student.contact.birthDate.formatted),1)]),_:1})]),d("div",ie,[o(i,{type:"text",modelValue:s.contactNumber,"onUpdate:modelValue":e[0]||(e[0]=t=>s.contactNumber=t),name:"contactNumber",label:r.$trans("contact.props.primary_contact_number"),error:n(a).contactNumber,"onUpdate:error":e[1]||(e[1]=t=>n(a).contactNumber=t)},null,8,["modelValue","label","error"])]),d("div",ue,[o(i,{type:"text",modelValue:s.alternateContactNumber,"onUpdate:modelValue":e[2]||(e[2]=t=>s.alternateContactNumber=t),name:"alternateContactNumber",label:r.$trans("contact.props.alternate_contact_number"),error:n(a).alternateContactNumber,"onUpdate:error":e[3]||(e[3]=t=>n(a).alternateContactNumber=t)},null,8,["modelValue","label","error"])]),d("div",me,[o(i,{type:"text",modelValue:s.email,"onUpdate:modelValue":e[4]||(e[4]=t=>s.email=t),name:"email",label:r.$trans("contact.props.email"),error:n(a).email,"onUpdate:error":e[5]||(e[5]=t=>n(a).email=t)},null,8,["modelValue","label","error"])])]),o(V,{class:"mt-4"},{legend:l(()=>[u(m(r.$trans("student.props.parent")),1)]),default:l(()=>[d("div",pe,[d("div",ce,[o(g,null,{default:l(()=>[u(m(r.$trans("contact.props.father_name")),1)]),_:1}),o(f,{class:"mt-1"},{default:l(()=>[u(m(c.student.contact.fatherName),1)]),_:1})]),d("div",be,[o(i,{type:"text",modelValue:s.fatherContactNumber,"onUpdate:modelValue":e[6]||(e[6]=t=>s.fatherContactNumber=t),name:"fatherContactNumber",label:r.$trans("contact.props.contact_number"),error:n(a).fatherContactNumber,"onUpdate:error":e[7]||(e[7]=t=>n(a).fatherContactNumber=t)},null,8,["modelValue","label","error"])]),d("div",ge,[o(i,{type:"text",modelValue:s.fatherEmail,"onUpdate:modelValue":e[8]||(e[8]=t=>s.fatherEmail=t),name:"fatherEmail",label:r.$trans("contact.props.email"),error:n(a).fatherEmail,"onUpdate:error":e[9]||(e[9]=t=>n(a).fatherEmail=t)},null,8,["modelValue","label","error"])])]),d("div",fe,[d("div",ye,[o(g,null,{default:l(()=>[u(m(r.$trans("contact.props.mother_name")),1)]),_:1}),o(f,{class:"mt-1"},{default:l(()=>[u(m(c.student.contact.motherName),1)]),_:1})]),d("div",Ae,[o(i,{type:"text",modelValue:s.motherContactNumber,"onUpdate:modelValue":e[10]||(e[10]=t=>s.motherContactNumber=t),name:"motherContactNumber",label:r.$trans("contact.props.contact_number"),error:n(a).motherContactNumber,"onUpdate:error":e[11]||(e[11]=t=>n(a).motherContactNumber=t)},null,8,["modelValue","label","error"])]),d("div",Ne,[o(i,{type:"text",modelValue:s.motherEmail,"onUpdate:modelValue":e[12]||(e[12]=t=>s.motherEmail=t),name:"motherEmail",label:r.$trans("contact.props.email"),error:n(a).motherEmail,"onUpdate:error":e[13]||(e[13]=t=>n(a).motherEmail=t)},null,8,["modelValue","label","error"])])])]),_:1}),d("div",Ve,[d("div",Ue,[o(i,{type:"text",modelValue:s.uniqueIdNumber1,"onUpdate:modelValue":e[14]||(e[14]=t=>s.uniqueIdNumber1=t),name:"uniqueIdNumber1",label:n(K),error:n(a).uniqueIdNumber1,"onUpdate:error":e[15]||(e[15]=t=>n(a).uniqueIdNumber1=t)},null,8,["modelValue","label","error"])]),d("div",qe,[o(i,{type:"text",modelValue:s.uniqueIdNumber2,"onUpdate:modelValue":e[16]||(e[16]=t=>s.uniqueIdNumber2=t),name:"uniqueIdNumber2",label:n(Q),error:n(a).uniqueIdNumber2,"onUpdate:error":e[17]||(e[17]=t=>n(a).uniqueIdNumber2=t)},null,8,["modelValue","label","error"])]),d("div",ve,[o(i,{type:"text",modelValue:s.uniqueIdNumber3,"onUpdate:modelValue":e[18]||(e[18]=t=>s.uniqueIdNumber3=t),name:"uniqueIdNumber3",label:n(W),error:n(a).uniqueIdNumber3,"onUpdate:error":e[19]||(e[19]=t=>n(a).uniqueIdNumber3=t)},null,8,["modelValue","label","error"])]),d("div",Le,[o(i,{type:"text",modelValue:s.birthPlace,"onUpdate:modelValue":e[20]||(e[20]=t=>s.birthPlace=t),name:"birthPlace",label:r.$trans("contact.props.birth_place"),error:n(a).birthPlace,"onUpdate:error":e[21]||(e[21]=t=>n(a).birthPlace=t)},null,8,["modelValue","label","error"])]),d("div",Ie,[o(i,{type:"text",modelValue:s.nationality,"onUpdate:modelValue":e[22]||(e[22]=t=>s.nationality=t),name:"nationality",label:r.$trans("contact.props.nationality"),error:n(a).nationality,"onUpdate:error":e[23]||(e[23]=t=>n(a).nationality=t)},null,8,["modelValue","label","error"])]),d("div",$e,[o(i,{type:"text",modelValue:s.motherTongue,"onUpdate:modelValue":e[24]||(e[24]=t=>s.motherTongue=t),name:"motherTongue",label:r.$trans("contact.props.mother_tongue"),error:n(a).motherTongue,"onUpdate:error":e[25]||(e[25]=t=>n(a).motherTongue=t)},null,8,["modelValue","label","error"])]),d("div",Ce,[I.isLoaded?(b(),U(A,{key:0,modelValue:s.bloodGroup,"onUpdate:modelValue":e[26]||(e[26]=t=>s.bloodGroup=t),name:"bloodGroup",label:r.$trans("contact.props.blood_group"),options:L.bloodGroups,error:n(a).bloodGroup,"onUpdate:error":e[27]||(e[27]=t=>n(a).bloodGroup=t)},null,8,["modelValue","label","options","error"])):N("",!0)]),d("div",Ee,[I.isLoaded?(b(),U(A,{key:0,name:"religion",label:r.$trans("global.select",{attribute:r.$trans("contact.religion.religion")}),modelValue:s.religion,"onUpdate:modelValue":e[28]||(e[28]=t=>s.religion=t),error:n(a).religion,"onUpdate:error":e[29]||(e[29]=t=>n(a).religion=t),options:L.religions,"label-prop":"name","value-prop":"uuid"},null,8,["label","modelValue","error","options"])):N("",!0)]),n(X)?(b(),z("div",Pe,[I.isLoaded?(b(),U(A,{key:0,name:"category",label:r.$trans("global.select",{attribute:r.$trans("contact.category.category")}),modelValue:s.category,"onUpdate:modelValue":e[30]||(e[30]=t=>s.category=t),error:n(a).category,"onUpdate:error":e[31]||(e[31]=t=>n(a).category=t),options:L.categories,"label-prop":"name","value-prop":"uuid"},null,8,["label","modelValue","error","options"])):N("",!0)])):N("",!0),n(Y)?(b(),z("div",ke,[I.isLoaded?(b(),U(A,{key:0,name:"caste",label:r.$trans("global.select",{attribute:r.$trans("contact.caste.caste")}),modelValue:s.caste,"onUpdate:modelValue":e[32]||(e[32]=t=>s.caste=t),error:n(a).caste,"onUpdate:error":e[33]||(e[33]=t=>n(a).caste=t),options:L.castes,"label-prop":"name","value-prop":"uuid"},null,8,["label","modelValue","error","options"])):N("",!0)])):N("",!0)]),o(V,{class:"mt-4"},{legend:l(()=>[u(m(r.$trans("contact.props.present_address")),1)]),default:l(()=>[d("div",Be,[o($,{prefix:"presentAddress",addressLine1:s.presentAddress.addressLine1,"onUpdate:addressLine1":e[34]||(e[34]=t=>s.presentAddress.addressLine1=t),addressLine2:s.presentAddress.addressLine2,"onUpdate:addressLine2":e[35]||(e[35]=t=>s.presentAddress.addressLine2=t),city:s.presentAddress.city,"onUpdate:city":e[36]||(e[36]=t=>s.presentAddress.city=t),state:s.presentAddress.state,"onUpdate:state":e[37]||(e[37]=t=>s.presentAddress.state=t),zipcode:s.presentAddress.zipcode,"onUpdate:zipcode":e[38]||(e[38]=t=>s.presentAddress.zipcode=t),country:s.presentAddress.country,"onUpdate:country":e[39]||(e[39]=t=>s.presentAddress.country=t),formErrors:n(a),"onUpdate:formErrors":e[40]||(e[40]=t=>h(a)?a.value=t:null)},null,8,["addressLine1","addressLine2","city","state","zipcode","country","formErrors"])])]),_:1}),o(V,{class:"mt-4"},{legend:l(()=>[u(m(r.$trans("contact.props.permanent_address")),1)]),default:l(()=>[d("div",Re,[d("div",Se,[o(k,{modelValue:s.permanentAddress.sameAsPresentAddress,"onUpdate:modelValue":e[41]||(e[41]=t=>s.permanentAddress.sameAsPresentAddress=t),name:"sameAsPresentAddress",label:r.$trans("contact.props.same_as_present_address"),error:n(a).sameAsPresentAddress,"onUpdate:error":e[42]||(e[42]=t=>n(a).sameAsPresentAddress=t)},null,8,["modelValue","label","error"])])]),d("div",Te,[s.permanentAddress.sameAsPresentAddress?N("",!0):(b(),U($,{key:0,prefix:"permanentAddress",addressLine1:s.permanentAddress.addressLine1,"onUpdate:addressLine1":e[43]||(e[43]=t=>s.permanentAddress.addressLine1=t),addressLine2:s.permanentAddress.addressLine2,"onUpdate:addressLine2":e[44]||(e[44]=t=>s.permanentAddress.addressLine2=t),city:s.permanentAddress.city,"onUpdate:city":e[45]||(e[45]=t=>s.permanentAddress.city=t),state:s.permanentAddress.state,"onUpdate:state":e[46]||(e[46]=t=>s.permanentAddress.state=t),zipcode:s.permanentAddress.zipcode,"onUpdate:zipcode":e[47]||(e[47]=t=>s.permanentAddress.zipcode=t),country:s.permanentAddress.country,"onUpdate:country":e[48]||(e[48]=t=>s.permanentAddress.country=t),formErrors:n(a),"onUpdate:formErrors":e[49]||(e[49]=t=>h(a)?a.value=t:null)},null,8,["addressLine1","addressLine2","city","state","zipcode","country","formErrors"]))])]),_:1}),d("div",ze,[d("div",Fe,[o(B,{multiple:"",label:r.$trans("general.file"),module:"contact_edit_request",media:s.media,"media-token":s.mediaToken,onIsUpdated:e[50]||(e[50]=t=>s.mediaUpdated=!0),onSetHash:e[51]||(e[51]=t=>s.mediaHash.push(t))},null,8,["label","media","media-token"])])])]),_:1},8,["uuid","form","redirect"])}}}),He={name:"StudentProfileEditRequestAction"},we=Object.assign(He,{props:{student:{type:Object,default(){return{}}}},setup(c){const q=J();return(y,v)=>{const E=p("PageHeaderAction"),S=p("PageHeader"),a=p("ParentTransition");return b(),z(ne,null,[o(S,{title:y.$trans(n(q).meta.trans,{attribute:y.$trans(n(q).meta.label)}),navs:[{label:y.$trans("student.student"),path:"StudentList"},{label:c.student.contact.name,path:{name:"StudentShow",params:{uuid:c.student.uuid}}},{label:y.$trans("student.edit_request.edit_request"),path:{name:"StudentProfileEditRequest",params:{uuid:c.student.uuid}}}]},{default:l(()=>[o(E,{name:"StudentProfileEditRequest",title:y.$trans("student.edit_request.edit_request"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),o(a,{appear:"",visibility:!0},{default:l(()=>[o(je,{student:c.student},null,8,["student"])]),_:1})],64)}}});export{we as default};
