import{i as y,u as N,h as P,l as T,r as n,a as V,o as u,e as a,w as e,f as c,q as p,b as _,d as H,s as o,t as r,y as I,F as D}from"./app-DvIo72ZO.js";const R={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},j={name:"AcademicCertificateShow"},M=Object.assign(j,{setup(E){y();const d=N(),m=P(),b={},g="academic/certificate/",i=T({...b}),B=t=>{Object.assign(i,t)};return(t,l)=>{const $=n("PageHeaderAction"),C=n("PageHeader"),f=n("TextMuted"),s=n("BaseDataView"),A=n("BaseButton"),h=n("ShowButton"),w=n("BaseCard"),k=n("ShowItem"),S=n("ParentTransition");return u(),V(D,null,[a(C,{title:t.$trans(c(d).meta.trans,{attribute:t.$trans(c(d).meta.label)}),navs:[{label:t.$trans("academic.academic"),path:"Academic"},{label:t.$trans("academic.certificate.certificate"),path:"AcademicCertificateList"}]},{default:e(()=>[a($,{name:"AcademicCertificate",title:t.$trans("academic.certificate.certificate"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(S,{appear:"",visibility:!0},{default:e(()=>[a(k,{"init-url":g,uuid:c(d).params.uuid,onSetItem:B,onRedirectTo:l[1]||(l[1]=v=>c(m).push({name:"AcademicCertificate"}))},{default:e(()=>[i.uuid?(u(),p(w,{key:0},{title:e(()=>[o(r(i.codeNumber),1)]),footer:e(()=>[a(h,null,{default:e(()=>[c(I)("certificate:edit")?(u(),p(A,{key:0,design:"primary",onClick:l[0]||(l[0]=v=>c(m).push({name:"AcademicCertificateEdit",params:{uuid:i.uuid}}))},{default:e(()=>[o(r(t.$trans("general.edit")),1)]),_:1})):_("",!0)]),_:1})]),default:e(()=>[H("dl",R,[a(s,{label:t.$trans("academic.certificate.template.template")},{default:e(()=>[o(r(i.template.name)+" ",1),a(f,{block:""},{default:e(()=>[o(r(i.template.for.label),1)]),_:1})]),_:1},8,["label"]),a(s,{label:t.$trans("academic.certificate.props.to")},{default:e(()=>[o(r(i.to.name)+" ",1),a(f,{block:""},{default:e(()=>[o(r(i.to.contactNumber),1)]),_:1})]),_:1},8,["label"]),a(s,{label:t.$trans("academic.certificate.props.date")},{default:e(()=>[o(r(i.date.formatted),1)]),_:1},8,["label"]),a(s,{label:t.$trans("general.created_at")},{default:e(()=>[o(r(i.createdAt.formatted),1)]),_:1},8,["label"]),a(s,{label:t.$trans("general.updated_at")},{default:e(()=>[o(r(i.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):_("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{M as default};
