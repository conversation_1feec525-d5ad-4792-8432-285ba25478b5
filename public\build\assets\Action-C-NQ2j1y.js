import{u as c,I as b,l as V,r as p,q as $,o as y,w as u,d as i,e as s,f as t,a as g,F as E}from"./app-DvIo72ZO.js";const T={class:"grid grid-cols-3 gap-6"},U={class:"col-span-3 sm:col-span-1"},B={class:"col-span-3 sm:col-span-1"},F={class:"col-span-3 sm:col-span-1"},L={class:"col-span-2 sm:col-span-1"},P={name:"EmployeeLeaveTypeForm"},A=Object.assign(P,{setup(v){c();const m={name:"",code:"",alias:"",description:""},l="employee/leave/type/",n=b(l),a=V({...m});return(r,e)=>{const d=p("BaseInput"),_=p("BaseTextarea"),f=p("FormAction");return y(),$(f,{"init-url":l,"init-form":m,form:a,redirect:"EmployeeLeaveType"},{default:u(()=>[i("div",T,[i("div",U,[s(d,{type:"text",modelValue:a.name,"onUpdate:modelValue":e[0]||(e[0]=o=>a.name=o),name:"name",label:r.$trans("employee.leave.type.props.name"),error:t(n).name,"onUpdate:error":e[1]||(e[1]=o=>t(n).name=o),autofocus:""},null,8,["modelValue","label","error"])]),i("div",B,[s(d,{type:"text",modelValue:a.code,"onUpdate:modelValue":e[2]||(e[2]=o=>a.code=o),name:"code",label:r.$trans("employee.leave.type.props.code"),error:t(n).code,"onUpdate:error":e[3]||(e[3]=o=>t(n).code=o),autofocus:""},null,8,["modelValue","label","error"])]),i("div",F,[s(d,{type:"text",modelValue:a.alias,"onUpdate:modelValue":e[4]||(e[4]=o=>a.alias=o),name:"alias",label:r.$trans("employee.leave.type.props.alias"),error:t(n).alias,"onUpdate:error":e[5]||(e[5]=o=>t(n).alias=o)},null,8,["modelValue","label","error"])]),i("div",L,[s(_,{modelValue:a.description,"onUpdate:modelValue":e[6]||(e[6]=o=>a.description=o),name:"description",label:r.$trans("employee.leave.type.props.description"),error:t(n).description,"onUpdate:error":e[7]||(e[7]=o=>t(n).description=o)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),H={name:"EmployeeLeaveTypeAction"},I=Object.assign(H,{setup(v){const m=c();return(l,n)=>{const a=p("PageHeaderAction"),r=p("PageHeader"),e=p("ParentTransition");return y(),g(E,null,[s(r,{title:l.$trans(t(m).meta.trans,{attribute:l.$trans(t(m).meta.label)}),navs:[{label:l.$trans("employee.employee"),path:"Employee"},{label:l.$trans("employee.leave.leave"),path:"EmployeeLeave"},{label:l.$trans("employee.leave.type.type"),path:"EmployeeLeaveTypeList"}]},{default:u(()=>[s(a,{name:"EmployeeLeaveType",title:l.$trans("employee.leave.type.type"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),s(e,{appear:"",visibility:!0},{default:u(()=>[s(A)]),_:1})],64)}}});export{I as default};
