import{l as h,r as o,q as b,o as C,w as e,d as w,e as t,h as V,j as R,m as j,f as r,a as L,F as S,v as q,s as i,t as s}from"./app-DvIo72ZO.js";const N={class:"grid grid-cols-3 gap-6"},E={class:"col-span-3 sm:col-span-1"},O={__name:"Filter",emits:["hide"],setup(B,{emit:m}){const c=m,f={name:""},p=h({...f});return($,d)=>{const u=o("BaseInput"),k=o("FilterForm");return C(),b(k,{"init-form":f,form:p,onHide:d[1]||(d[1]=n=>c("hide"))},{default:e(()=>[w("div",N,[w("div",E,[t(u,{type:"text",modelValue:p.name,"onUpdate:modelValue":d[0]||(d[0]=n=>p.name=n),name:"name",label:$.$trans("academic.class_timing.props.name")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},U={name:"AcademicClassTimingList"},G=Object.assign(U,{setup(B){const m=V(),c=R("emitter");let f=["create","filter"],p=["print","pdf","excel"];const $="academic/classTiming/",d=j(!1),u=h({}),k=n=>{Object.assign(u,n)};return(n,l)=>{const I=o("PageHeaderAction"),D=o("PageHeader"),A=o("ParentTransition"),_=o("DataCell"),F=o("TextMuted"),g=o("FloatingMenuItem"),y=o("FloatingMenu"),H=o("DataRow"),M=o("BaseButton"),P=o("DataTable"),T=o("ListItem");return C(),b(T,{"init-url":$,"additional-query":{details:!0},onSetItems:k},{header:e(()=>[t(D,{title:n.$trans("academic.class_timing.class_timing"),navs:[{label:n.$trans("academic.academic"),path:"Academic"}]},{default:e(()=>[t(I,{url:"academic/class-timings/",name:"AcademicClassTiming",title:n.$trans("academic.class_timing.class_timing"),actions:r(f),"dropdown-actions":r(p),"additional-dropdown-actions-query":{details:!0},onToggleFilter:l[0]||(l[0]=a=>d.value=!d.value)},null,8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(A,{appear:"",visibility:d.value},{default:e(()=>[t(O,{onRefresh:l[1]||(l[1]=a=>r(c).emit("listItems")),onHide:l[2]||(l[2]=a=>d.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[t(A,{appear:"",visibility:!0},{default:e(()=>[t(P,{header:u.headers,meta:u.meta,module:"academic.class_timing",onRefresh:l[4]||(l[4]=a=>r(c).emit("listItems"))},{actionButton:e(()=>[t(M,{onClick:l[3]||(l[3]=a=>r(m).push({name:"AcademicClassTimingCreate"}))},{default:e(()=>[i(s(n.$trans("global.add",{attribute:n.$trans("academic.class_timing.class_timing")})),1)]),_:1})]),default:e(()=>[(C(!0),L(S,null,q(u.data,a=>(C(),b(H,{key:a.uuid,onDoubleClick:v=>r(m).push({name:"AcademicClassTimingShow",params:{uuid:a.uuid}})},{default:e(()=>[t(_,{name:"name"},{default:e(()=>[i(s(a.name),1)]),_:2},1024),t(_,{name:"duration"},{default:e(()=>[i(s(a.duration)+" ",1),t(F,{block:""},{default:e(()=>[i(s(a.period),1)]),_:2},1024)]),_:2},1024),t(_,{name:"session"},{default:e(()=>[i(s(a.sessionCount)+" ",1),t(F,{block:""},{default:e(()=>[i(s(a.breakCount),1)]),_:2},1024)]),_:2},1024),t(_,{name:"createdAt"},{default:e(()=>[i(s(a.createdAt.formatted),1)]),_:2},1024),t(_,{name:"action"},{default:e(()=>[t(y,null,{default:e(()=>[t(g,{icon:"fas fa-arrow-circle-right",onClick:v=>r(m).push({name:"AcademicClassTimingShow",params:{uuid:a.uuid}})},{default:e(()=>[i(s(n.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(g,{icon:"fas fa-edit",onClick:v=>r(m).push({name:"AcademicClassTimingEdit",params:{uuid:a.uuid}})},{default:e(()=>[i(s(n.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t(g,{icon:"fas fa-copy",onClick:v=>r(m).push({name:"AcademicClassTimingDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[i(s(n.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t(g,{icon:"fas fa-trash",onClick:v=>r(c).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[i(s(n.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{G as default};
