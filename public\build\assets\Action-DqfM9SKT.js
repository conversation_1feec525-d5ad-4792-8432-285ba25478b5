import{I as $,l as f,r as p,q as B,o as _,w as u,d as m,e as n,f as t,K as H,u as U,a as P,F as j}from"./app-DvIo72ZO.js";const q={class:"grid grid-cols-3 gap-6"},A={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},R={class:"col-span-4 sm:col-span-1"},T={class:"col-span-3 sm:col-span-1"},h={name:"HostelFloorForm"},y=Object.assign(h,{setup(k){const i={name:"",alias:"",block:"",description:""},r="hostel/floor/",l=$(r),c=f({blocks:[]}),s=f({...i}),d=a=>{Object.assign(c,a)},g=a=>{Object.assign(i,{...a,block:a.blockUuid}),Object.assign(s,H(i))};return(a,e)=>{const b=p("BaseInput"),V=p("BaseSelect"),F=p("BaseTextarea"),v=p("FormAction");return _(),B(v,{"pre-requisites":!0,onSetPreRequisites:d,"init-url":r,"init-form":i,form:s,"set-form":g,redirect:"HostelFloor"},{default:u(()=>[m("div",q,[m("div",A,[n(b,{type:"text",modelValue:s.name,"onUpdate:modelValue":e[0]||(e[0]=o=>s.name=o),name:"name",label:a.$trans("hostel.floor.props.name"),error:t(l).name,"onUpdate:error":e[1]||(e[1]=o=>t(l).name=o),autofocus:""},null,8,["modelValue","label","error"])]),m("div",O,[n(b,{type:"text",modelValue:s.alias,"onUpdate:modelValue":e[2]||(e[2]=o=>s.alias=o),name:"alias",label:a.$trans("hostel.floor.props.alias"),error:t(l).alias,"onUpdate:error":e[3]||(e[3]=o=>t(l).alias=o),autofocus:""},null,8,["modelValue","label","error"])]),m("div",R,[n(V,{modelValue:s.block,"onUpdate:modelValue":e[4]||(e[4]=o=>s.block=o),name:"block",label:a.$trans("hostel.block.block"),options:c.blocks,"label-prop":"name","value-prop":"uuid",error:t(l).block,"onUpdate:error":e[5]||(e[5]=o=>t(l).block=o)},null,8,["modelValue","label","options","error"])]),m("div",T,[n(F,{modelValue:s.description,"onUpdate:modelValue":e[6]||(e[6]=o=>s.description=o),name:"description",label:a.$trans("hostel.floor.props.description"),error:t(l).description,"onUpdate:error":e[7]||(e[7]=o=>t(l).description=o)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),E={name:"HostelFloorAction"},S=Object.assign(E,{setup(k){const i=U();return(r,l)=>{const c=p("PageHeaderAction"),s=p("PageHeader"),d=p("ParentTransition");return _(),P(j,null,[n(s,{title:r.$trans(t(i).meta.trans,{attribute:r.$trans(t(i).meta.label)}),navs:[{label:r.$trans("hostel.hostel"),path:"Hostel"},{label:r.$trans("hostel.floor.floor"),path:"HostelFloorList"}]},{default:u(()=>[n(c,{name:"HostelFloor",title:r.$trans("hostel.floor.floor"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(d,{appear:"",visibility:!0},{default:u(()=>[n(y)]),_:1})],64)}}});export{S as default};
