import{i as E,u as S,h as x,l as y,r as s,a as C,o as c,e as t,w as a,f as i,q as P,b as V,d as k,s as o,t as r,F as N}from"./app-DvIo72ZO.js";const A={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},H={name:"ExamTermShow"},R=Object.assign(H,{setup(I){E();const u=S(),p=x(),_={},f="exam/term/",n=y({..._}),b=e=>{Object.assign(n,e)};return(e,m)=>{const g=s("PageHeaderAction"),B=s("PageHeader"),l=s("BaseDataView"),$=s("BaseButton"),v=s("ShowButton"),T=s("BaseCard"),h=s("ShowItem"),w=s("ParentTransition");return c(),C(N,null,[t(B,{title:e.$trans(i(u).meta.trans,{attribute:e.$trans(i(u).meta.label)}),navs:[{label:e.$trans("exam.exam"),path:"Exam"},{label:e.$trans("exam.term.term"),path:"ExamTermList"}]},{default:a(()=>[t(g,{name:"ExamTerm",title:e.$trans("exam.term.term"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),t(w,{appear:"",visibility:!0},{default:a(()=>[t(h,{"init-url":f,uuid:i(u).params.uuid,onSetItem:b,onRedirectTo:m[1]||(m[1]=d=>i(p).push({name:"ExamTerm"}))},{default:a(()=>[n.uuid?(c(),P(T,{key:0},{title:a(()=>[o(r(n.name),1)]),footer:a(()=>[t(v,null,{default:a(()=>[t($,{design:"primary",onClick:m[0]||(m[0]=d=>i(p).push({name:"ExamTermEdit",params:{uuid:n.uuid}}))},{default:a(()=>[o(r(e.$trans("general.edit")),1)]),_:1})]),_:1})]),default:a(()=>[k("dl",A,[t(l,{label:e.$trans("exam.term.props.name")},{default:a(()=>[o(r(n.name),1)]),_:1},8,["label"]),t(l,{label:e.$trans("academic.division.division")},{default:a(()=>{var d;return[o(r(((d=n.division)==null?void 0:d.name)||""),1)]}),_:1},8,["label"]),t(l,{label:e.$trans("exam.term.props.display_name")},{default:a(()=>[o(r(n.displayName||"-"),1)]),_:1},8,["label"]),t(l,{class:"col-span-1 sm:col-span-2",label:e.$trans("exam.term.props.description")},{default:a(()=>[o(r(n.description),1)]),_:1},8,["label"]),t(l,{label:e.$trans("general.created_at")},{default:a(()=>[o(r(n.createdAt.formatted),1)]),_:1},8,["label"]),t(l,{label:e.$trans("general.updated_at")},{default:a(()=>[o(r(n.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):V("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{R as default};
