import{j as q,u as D,h as E,i as w,H as M,I,m as L,l as U,r as i,q as _,o as y,w as b,d as l,a as R,b as $,e as r,f as a,s as B,t as j,K as N,F as Y}from"./app-DvIo72ZO.js";const C={class:"grid grid-cols-4 gap-6"},K={class:"col-span-4 sm:col-span-3"},W={class:"col-span-4 sm:col-span-1"},z={class:"col-span-4 sm:col-span-1"},G={class:"col-span-4 sm:col-span-1"},J={class:"col-span-4 sm:col-span-1"},Q={class:"col-span-4 sm:col-span-1"},X={class:"col-span-4 sm:col-span-1"},Z={key:0,class:"col-span-4 sm:col-span-1"},x={class:"col-span-4"},ee={class:"mt-4 grid grid-cols-1"},se={class:"col"},te={name:"ResourceAssignmentForm"},ae=Object.assign(te,{setup(S){const g=q("moment"),d=D();E(),w();const u={title:"",type:"",batches:[],subject:"",date:g().format("YYYY-MM-DD"),dueDate:"",enableMarking:!1,maxMark:0,description:"",media:[],mediaUpdated:!1,mediaToken:M(),mediaHash:[]},V="resource/assignment/",o=I(V);L(!1);const m=U({subjects:[],types:[]});U({selectedBatch:null,subjects:[]});const t=U({...u}),k=U({batches:[],subject:"",isLoaded:!d.params.uuid}),F=n=>{Object.assign(m,n)},A=()=>{t.mediaToken=M(),t.mediaHash=[]},h=async n=>{var p,c,v;let s=n.records.map(f=>f.batch.uuid)||[];Object.assign(u,{...n,type:((p=n.type)==null?void 0:p.uuid)||"",date:n.date.value,dueDate:n.dueDate.value,batches:s,subject:((v=(c=n.records[0])==null?void 0:c.subject)==null?void 0:v.uuid)||""}),Object.assign(t,N(u)),k.batches=s,k.isLoaded=!0};return(n,s)=>{const p=i("BaseInput"),c=i("BaseSelect"),v=i("BaseSelectSearch"),f=i("DatePicker"),H=i("BaseSwitch"),P=i("BaseEditor"),O=i("MediaUpload"),T=i("FormAction");return y(),_(T,{"pre-requisites":!0,onSetPreRequisites:F,"init-url":V,"init-form":u,form:t,setForm:h,redirect:"ResourceAssignment",onResetMediaFiles:A},{default:b(()=>[l("div",C,[l("div",K,[r(p,{type:"text",modelValue:t.title,"onUpdate:modelValue":s[0]||(s[0]=e=>t.title=e),name:"title",label:n.$trans("resource.assignment.props.title"),error:a(o).title,"onUpdate:error":s[1]||(s[1]=e=>a(o).title=e),autofocus:""},null,8,["modelValue","label","error"])]),l("div",W,[r(c,{modelValue:t.type,"onUpdate:modelValue":s[2]||(s[2]=e=>t.type=e),name:"type",label:n.$trans("resource.assignment.props.type"),options:m.types,"label-prop":"name","value-prop":"uuid",error:a(o).type,"onUpdate:error":s[3]||(s[3]=e=>a(o).type=e)},null,8,["modelValue","label","options","error"])]),l("div",z,[k.isLoaded?(y(),_(v,{key:0,multiple:"",name:"batches",label:n.$trans("academic.batch.batch"),modelValue:t.batches,"onUpdate:modelValue":s[4]||(s[4]=e=>t.batches=e),error:a(o).batches,"onUpdate:error":s[5]||(s[5]=e=>a(o).batches=e),"value-prop":"uuid","init-search":k.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:b(e=>[B(j(e.value.course.name)+" - "+j(e.value.name),1)]),listOption:b(e=>[B(j(e.option.course.nameWithTerm)+" - "+j(e.option.name),1)]),_:1},8,["label","modelValue","error","init-search"])):$("",!0)]),l("div",G,[r(c,{modelValue:t.subject,"onUpdate:modelValue":s[6]||(s[6]=e=>t.subject=e),name:"subject",label:n.$trans("academic.subject.subject"),"label-prop":"name","value-prop":"uuid",options:m.subjects,error:a(o).subject,"onUpdate:error":s[7]||(s[7]=e=>a(o).subject=e)},null,8,["modelValue","label","options","error"])]),l("div",J,[r(f,{modelValue:t.date,"onUpdate:modelValue":s[8]||(s[8]=e=>t.date=e),name:"date",label:n.$trans("resource.assignment.props.date"),"no-clear":"",error:a(o).date,"onUpdate:error":s[9]||(s[9]=e=>a(o).date=e)},null,8,["modelValue","label","error"])]),l("div",Q,[r(f,{modelValue:t.dueDate,"onUpdate:modelValue":s[10]||(s[10]=e=>t.dueDate=e),name:"dueDate",label:n.$trans("resource.assignment.props.due_date"),"no-clear":"",error:a(o).dueDate,"onUpdate:error":s[11]||(s[11]=e=>a(o).dueDate=e)},null,8,["modelValue","label","error"])]),l("div",X,[r(H,{vertical:"",modelValue:t.enableMarking,"onUpdate:modelValue":s[12]||(s[12]=e=>t.enableMarking=e),name:"enableMarking",label:n.$trans("resource.assignment.props.enable_marking"),error:a(o).enableMarking,"onUpdate:error":s[13]||(s[13]=e=>a(o).enableMarking=e)},null,8,["modelValue","label","error"])]),t.enableMarking?(y(),R("div",Z,[r(p,{type:"number",modelValue:t.maxMark,"onUpdate:modelValue":s[14]||(s[14]=e=>t.maxMark=e),name:"maxMark",label:n.$trans("resource.assignment.props.max_mark"),error:a(o).maxMark,"onUpdate:error":s[15]||(s[15]=e=>a(o).maxMark=e)},null,8,["modelValue","label","error"])])):$("",!0),l("div",x,[r(P,{modelValue:t.description,"onUpdate:modelValue":s[16]||(s[16]=e=>t.description=e),name:"description",edit:!!a(d).params.uuid,label:n.$trans("resource.assignment.props.description"),error:a(o).description,"onUpdate:error":s[17]||(s[17]=e=>a(o).description=e)},null,8,["modelValue","edit","label","error"])])]),l("div",ee,[l("div",se,[r(O,{multiple:"",label:n.$trans("general.file"),module:"assignment",media:t.media,"media-token":t.mediaToken,onIsUpdated:s[18]||(s[18]=e=>t.mediaUpdated=!0),onSetHash:s[19]||(s[19]=e=>t.mediaHash.push(e))},null,8,["label","media","media-token"])])])]),_:1},8,["form"])}}}),oe={name:"ResourceAssignmentAction"},re=Object.assign(oe,{setup(S){const g=D();return(d,u)=>{const V=i("PageHeaderAction"),o=i("PageHeader"),m=i("ParentTransition");return y(),R(Y,null,[r(o,{title:d.$trans(a(g).meta.trans,{attribute:d.$trans(a(g).meta.label)}),navs:[{label:d.$trans("resource.resource"),path:"Resource"},{label:d.$trans("resource.assignment.assignment"),path:"ResourceAssignmentList"}]},{default:b(()=>[r(V,{name:"ResourceAssignment",title:d.$trans("resource.assignment.assignment"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),r(m,{appear:"",visibility:!0},{default:b(()=>[r(ae)]),_:1})],64)}}});export{re as default};
