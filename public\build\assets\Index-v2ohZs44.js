import{l as F,r as l,q as V,o as B,w as e,d as $,e as t,h as j,j as H,m as T,f as i,a as L,F as M,v as N,s as r,t as u}from"./app-DvIo72ZO.js";const O={class:"grid grid-cols-3 gap-6"},U={class:"col-span-3 sm:col-span-1"},E={class:"col-span-3 sm:col-span-1"},z={class:"col-span-3 sm:col-span-1"},G={__name:"Filter",props:{preRequisites:{type:Object,default(){return{}}}},emits:["hide"],setup(h,{emit:d}){const c=d,C=h,b={name:"",alias:"",block:""},m=F({...b}),g=F({blocks:C.preRequisites.blocks});return(p,o)=>{const _=l("BaseInput"),A=l("BaseSelect"),n=l("FilterForm");return B(),V(n,{"init-form":b,form:m,onHide:o[3]||(o[3]=s=>c("hide"))},{default:e(()=>[$("div",O,[$("div",U,[t(_,{type:"text",modelValue:m.name,"onUpdate:modelValue":o[0]||(o[0]=s=>m.name=s),name:"name",label:p.$trans("asset.building.floor.props.name")},null,8,["modelValue","label"])]),$("div",E,[t(_,{type:"text",modelValue:m.alias,"onUpdate:modelValue":o[1]||(o[1]=s=>m.alias=s),name:"alias",label:p.$trans("asset.building.floor.props.alias")},null,8,["modelValue","label"])]),$("div",z,[t(A,{modelValue:m.block,"onUpdate:modelValue":o[2]||(o[2]=s=>m.block=s),name:"block","label-prop":"name","value-prop":"uuid",label:p.$trans("asset.building.block.block"),options:g.blocks},null,8,["modelValue","label","options"])])])]),_:1},8,["form"])}}},J={name:"AssetBuildingFloorList"},Q=Object.assign(J,{setup(h){const d=j(),c=H("emitter");let C=["create","filter"],b=["print","pdf","excel"];const m="asset/building/floor/",g=F({blocks:[]}),p=T(!1),o=F({}),_=n=>{Object.assign(o,n)},A=n=>{Object.assign(g,n)};return(n,s)=>{const w=l("BaseButton"),R=l("PageHeaderAction"),D=l("PageHeader"),I=l("ParentTransition"),f=l("DataCell"),k=l("FloatingMenuItem"),q=l("FloatingMenu"),y=l("DataRow"),P=l("DataTable"),S=l("ListItem");return B(),V(S,{"init-url":m,"pre-requisites":!0,onSetPreRequisites:A,onSetItems:_},{header:e(()=>[t(D,{title:n.$trans("asset.building.floor.floor"),navs:[{label:n.$trans("asset.asset"),path:"Asset"},{label:n.$trans("asset.building.building"),path:"AssetBuilding"}]},{default:e(()=>[t(R,{url:"asset/building/floors/",name:"AssetBuildingFloor",title:n.$trans("asset.building.floor.floor"),actions:i(C),"dropdown-actions":i(b),onToggleFilter:s[2]||(s[2]=a=>p.value=!p.value)},{default:e(()=>[t(w,{design:"white",onClick:s[0]||(s[0]=a=>i(d).push({name:"AssetBuildingBlock"}))},{default:e(()=>[r(u(n.$trans("asset.building.block.block")),1)]),_:1}),t(w,{design:"white",onClick:s[1]||(s[1]=a=>i(d).push({name:"AssetBuildingRoom"}))},{default:e(()=>[r(u(n.$trans("asset.building.room.room")),1)]),_:1})]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[t(I,{appear:"",visibility:p.value},{default:e(()=>[t(G,{onRefresh:s[3]||(s[3]=a=>i(c).emit("listItems")),"pre-requisites":g,onHide:s[4]||(s[4]=a=>p.value=!1)},null,8,["pre-requisites"])]),_:1},8,["visibility"])]),default:e(()=>[t(I,{appear:"",visibility:!0},{default:e(()=>[t(P,{header:o.headers,meta:o.meta,module:"asset.building.floor",onRefresh:s[6]||(s[6]=a=>i(c).emit("listItems"))},{actionButton:e(()=>[t(w,{onClick:s[5]||(s[5]=a=>i(d).push({name:"AssetBuildingFloorCreate"}))},{default:e(()=>[r(u(n.$trans("global.add",{attribute:n.$trans("asset.building.floor.floor")})),1)]),_:1})]),default:e(()=>[(B(!0),L(M,null,N(o.data,a=>(B(),V(y,{key:a.uuid,onDoubleClick:v=>i(d).push({name:"AssetBuildingFloorShow",params:{uuid:a.uuid}})},{default:e(()=>[t(f,{name:"name"},{default:e(()=>[r(u(a.name),1)]),_:2},1024),t(f,{name:"alias"},{default:e(()=>[r(u(a.alias),1)]),_:2},1024),t(f,{name:"block"},{default:e(()=>[r(u(a.blockName),1)]),_:2},1024),t(f,{name:"createdAt"},{default:e(()=>[r(u(a.createdAt.formatted),1)]),_:2},1024),t(f,{name:"action"},{default:e(()=>[t(q,null,{default:e(()=>[t(k,{icon:"fas fa-arrow-circle-right",onClick:v=>i(d).push({name:"AssetBuildingFloorShow",params:{uuid:a.uuid}})},{default:e(()=>[r(u(n.$trans("general.show")),1)]),_:2},1032,["onClick"]),t(k,{icon:"fas fa-edit",onClick:v=>i(d).push({name:"AssetBuildingFloorEdit",params:{uuid:a.uuid}})},{default:e(()=>[r(u(n.$trans("general.edit")),1)]),_:2},1032,["onClick"]),t(k,{icon:"fas fa-copy",onClick:v=>i(d).push({name:"AssetBuildingFloorDuplicate",params:{uuid:a.uuid}})},{default:e(()=>[r(u(n.$trans("general.duplicate")),1)]),_:2},1032,["onClick"]),t(k,{icon:"fas fa-trash",onClick:v=>i(c).emit("deleteItem",{uuid:a.uuid})},{default:e(()=>[r(u(n.$trans("general.delete")),1)]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Q as default};
