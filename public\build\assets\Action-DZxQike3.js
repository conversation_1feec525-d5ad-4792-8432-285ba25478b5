import{u as g,l as _,I as H,r as i,q as U,o as b,w as u,d,e as n,f as t,K as B,a as E,F}from"./app-DvIo72ZO.js";const j={class:"grid grid-cols-3 gap-6"},q={class:"col-span-3 sm:col-span-1"},A={class:"col-span-3 sm:col-span-1"},O={class:"col-span-3 sm:col-span-1"},C={class:"col-span-3 sm:col-span-1"},R={class:"col-span-3 sm:col-span-1"},T={name:"EmployeePayrollPayHeadForm"},k=Object.assign(T,{setup(f){g();const p={category:"",name:"",code:"",alias:"",description:""},s="employee/payroll/payHead/",m=_({payHeadCategories:[]}),l=H(s),o=_({...p}),y=r=>{Object.assign(m,r)},P=r=>{var e;Object.assign(p,{...r,category:(e=r.category)==null?void 0:e.value}),Object.assign(o,B(p))};return(r,e)=>{const V=i("BaseSelect"),c=i("BaseInput"),$=i("BaseTextarea"),v=i("FormAction");return b(),U(v,{"pre-requisites":!0,onSetPreRequisites:y,"init-url":s,"init-form":p,form:o,"set-form":P,redirect:"EmployeePayrollPayHead"},{default:u(()=>[d("div",j,[d("div",q,[n(V,{modelValue:o.category,"onUpdate:modelValue":e[0]||(e[0]=a=>o.category=a),name:"category",label:r.$trans("employee.payroll.pay_head.props.category"),options:m.payHeadCategories,error:t(l).category,"onUpdate:error":e[1]||(e[1]=a=>t(l).category=a)},null,8,["modelValue","label","options","error"])]),d("div",A,[n(c,{type:"text",modelValue:o.name,"onUpdate:modelValue":e[2]||(e[2]=a=>o.name=a),name:"name",label:r.$trans("employee.payroll.pay_head.props.name"),error:t(l).name,"onUpdate:error":e[3]||(e[3]=a=>t(l).name=a),autofocus:""},null,8,["modelValue","label","error"])]),d("div",O,[n(c,{type:"text",modelValue:o.code,"onUpdate:modelValue":e[4]||(e[4]=a=>o.code=a),name:"code",label:r.$trans("employee.payroll.pay_head.props.code"),error:t(l).code,"onUpdate:error":e[5]||(e[5]=a=>t(l).code=a),autofocus:""},null,8,["modelValue","label","error"])]),d("div",C,[n(c,{type:"text",modelValue:o.alias,"onUpdate:modelValue":e[6]||(e[6]=a=>o.alias=a),name:"alias",label:r.$trans("employee.payroll.pay_head.props.alias"),error:t(l).alias,"onUpdate:error":e[7]||(e[7]=a=>t(l).alias=a)},null,8,["modelValue","label","error"])]),d("div",R,[n($,{modelValue:o.description,"onUpdate:modelValue":e[8]||(e[8]=a=>o.description=a),name:"description",label:r.$trans("employee.payroll.pay_head.props.description"),error:t(l).description,"onUpdate:error":e[9]||(e[9]=a=>t(l).description=a)},null,8,["modelValue","label","error"])])])]),_:1},8,["form"])}}}),I={name:"EmployeePayrollPayHeadAction"},w=Object.assign(I,{setup(f){const p=g();return(s,m)=>{const l=i("PageHeaderAction"),o=i("PageHeader"),y=i("ParentTransition");return b(),E(F,null,[n(o,{title:s.$trans(t(p).meta.trans,{attribute:s.$trans(t(p).meta.label)}),navs:[{label:s.$trans("employee.employee"),path:"Employee"},{label:s.$trans("employee.payroll.payroll"),path:"EmployeePayroll"},{label:s.$trans("employee.payroll.pay_head.pay_head"),path:"EmployeePayrollPayHeadList"}]},{default:u(()=>[n(l,{name:"EmployeePayrollPayHead",title:s.$trans("employee.payroll.pay_head.pay_head"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),n(y,{appear:"",visibility:!0},{default:u(()=>[n(k)]),_:1})],64)}}});export{w as default};
