import{u as S,l as D,n as E,r as l,q as f,o as s,w as e,d as y,e as i,h as O,j as U,y as p,m as q,f as a,a as k,F as B,v as j,s as c,t as r,b as _}from"./app-DvIo72ZO.js";const z={class:"grid grid-cols-3 gap-6"},G={class:"col-span-3 sm:col-span-1"},J={__name:"Filter",emits:["hide"],setup(A,{emit:d}){S();const g=d,$={name:""},v=D({...$}),h=D({isLoaded:!0});return E(async()=>{h.isLoaded=!0}),(F,u)=>{const w=l("BaseInput"),o=l("FilterForm");return s(),f(o,{"init-form":$,form:v,multiple:[],onHide:u[1]||(u[1]=n=>g("hide"))},{default:e(()=>[y("div",z,[y("div",G,[i(w,{type:"text",modelValue:v.name,"onUpdate:modelValue":u[0]||(u[0]=n=>v.name=n),name:"name",label:F.$trans("finance.fee_concession.props.name")},null,8,["modelValue","label"])])])]),_:1},8,["form"])}}},K={key:0,class:"flex justify-between mr-4"},Q={key:0,class:"flex justify-between mr-4"},W={name:"FinanceFeeConcessionList"},Y=Object.assign(W,{setup(A){const d=O(),g=U("emitter");let $=["filter"];p("fee-concession:create")&&$.unshift("create");let v=[];p("fee-concession:export")&&(v=["print","pdf","excel"]);const h="finance/feeConcession/",F=q(!1),u=D({}),w=o=>{Object.assign(u,o)};return(o,n)=>{const I=l("BaseButton"),H=l("PageHeaderAction"),L=l("PageHeader"),V=l("ParentTransition"),C=l("DataCell"),b=l("FloatingMenuItem"),P=l("FloatingMenu"),R=l("DataRow"),M=l("DataTable"),N=l("ListItem");return s(),f(N,{"init-url":h,onSetItems:w},{header:e(()=>[i(L,{title:o.$trans("finance.fee_concession.fee_concession"),navs:[{label:o.$trans("finance.finance"),path:"Finance"}]},{default:e(()=>[i(H,{url:"finance/fee-concessions/",name:"FinanceFeeConcession",title:o.$trans("finance.fee_concession.fee_concession"),actions:a($),"dropdown-actions":a(v),onToggleFilter:n[1]||(n[1]=t=>F.value=!F.value)},{after:e(()=>[a(p)("finance:config")?(s(),f(I,{key:0,design:"white",onClick:n[0]||(n[0]=t=>a(d).push({name:"FinanceConfigFeeConcessionType"}))},{default:e(()=>n[6]||(n[6]=[y("i",{class:"fas fa-cog"},null,-1)])),_:1})):_("",!0)]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[i(V,{appear:"",visibility:F.value},{default:e(()=>[i(J,{onRefresh:n[2]||(n[2]=t=>a(g).emit("listItems")),onHide:n[3]||(n[3]=t=>F.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[i(V,{appear:"",visibility:!0},{default:e(()=>[i(M,{header:u.headers,meta:u.meta,module:"finance.fee_concession",onRefresh:n[5]||(n[5]=t=>a(g).emit("listItems"))},{actionButton:e(()=>[a(p)("fee-concession:create")?(s(),f(I,{key:0,onClick:n[4]||(n[4]=t=>a(d).push({name:"FinanceFeeConcessionCreate"}))},{default:e(()=>[c(r(o.$trans("global.add",{attribute:o.$trans("finance.fee_concession.fee_concession")})),1)]),_:1})):_("",!0)]),default:e(()=>[(s(!0),k(B,null,j(u.data,t=>(s(),f(R,{key:t.uuid,onDoubleClick:m=>a(d).push({name:"FinanceFeeConcessionShow",params:{uuid:t.uuid}})},{default:e(()=>[i(C,{name:"name"},{default:e(()=>[c(r(t.name),1)]),_:2},1024),i(C,{name:"head",table:""},{default:e(()=>[(s(!0),k(B,null,j(t.records,m=>{var T;return s(),k(B,null,[m.value.value>0?(s(),k("div",K,[c(r((T=m.head)==null?void 0:T.name)+" ",1),y("div",null,r(m.value.formatted),1)])):_("",!0)],64)}),256)),t.transportValue.value>0?(s(),k("div",Q,[c(r(o.$trans("transport.fee.fee"))+" ",1),y("div",null,r(t.transportValue.formatted),1)])):_("",!0)]),_:2},1024),i(C,{name:"createdAt"},{default:e(()=>[c(r(t.createdAt.formatted),1)]),_:2},1024),i(C,{name:"action"},{default:e(()=>[i(P,null,{default:e(()=>[i(b,{icon:"fas fa-arrow-circle-right",onClick:m=>a(d).push({name:"FinanceFeeConcessionShow",params:{uuid:t.uuid}})},{default:e(()=>[c(r(o.$trans("general.show")),1)]),_:2},1032,["onClick"]),a(p)("fee-concession:edit")?(s(),f(b,{key:0,icon:"fas fa-edit",onClick:m=>a(d).push({name:"FinanceFeeConcessionEdit",params:{uuid:t.uuid}})},{default:e(()=>[c(r(o.$trans("general.edit")),1)]),_:2},1032,["onClick"])):_("",!0),a(p)("fee-concession:create")?(s(),f(b,{key:1,icon:"fas fa-copy",onClick:m=>a(d).push({name:"FinanceFeeConcessionDuplicate",params:{uuid:t.uuid}})},{default:e(()=>[c(r(o.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):_("",!0),a(p)("fee-concession:delete")?(s(),f(b,{key:2,icon:"fas fa-trash",onClick:m=>a(g).emit("deleteItem",{uuid:t.uuid})},{default:e(()=>[c(r(o.$trans("general.delete")),1)]),_:2},1032,["onClick"])):_("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{Y as default};
