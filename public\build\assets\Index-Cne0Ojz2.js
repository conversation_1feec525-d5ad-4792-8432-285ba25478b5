import{u as q,l as A,n as N,r,q as d,o as c,w as e,d as T,b as h,s as l,t as o,e as s,h as j,j as O,y as v,m as U,f as i,a as E,F as W,v as z,B as G}from"./app-DvIo72ZO.js";const J={class:"grid grid-cols-3 gap-6"},K={class:"col-span-3 sm:col-span-1"},Q={class:"col-span-3 sm:col-span-1"},X={__name:"Filter",emits:["hide"],setup(I,{emit:b}){const k=q(),y=b,$={batches:[],startDate:"",endDate:""},m=A({...$}),p=A({batches:[],isLoaded:!k.query.batches});return N(async()=>{p.batches=k.query.batches?k.query.batches.split(","):[],p.isLoaded=!0}),(_,u)=>{const D=r("BaseSelectSearch"),w=r("DatePicker"),a=r("FilterForm");return c(),d(a,{"init-form":$,form:m,multiple:["batches"],onHide:u[3]||(u[3]=t=>y("hide"))},{default:e(()=>[T("div",J,[T("div",K,[p.isLoaded?(c(),d(D,{key:0,multiple:"",name:"batches",label:_.$trans("global.select",{attribute:_.$trans("academic.batch.batch")}),modelValue:m.batches,"onUpdate:modelValue":u[0]||(u[0]=t=>m.batches=t),"value-prop":"uuid","init-search":p.batches,"search-key":"course_batch","search-action":"academic/batch/list"},{selectedOption:e(t=>[l(o(t.value.course.name)+" "+o(t.value.name),1)]),listOption:e(t=>[l(o(t.option.course.nameWithTerm)+" "+o(t.option.name),1)]),_:1},8,["label","modelValue","init-search"])):h("",!0)]),T("div",Q,[s(w,{start:m.startDate,"onUpdate:start":u[1]||(u[1]=t=>m.startDate=t),end:m.endDate,"onUpdate:end":u[2]||(u[2]=t=>m.endDate=t),name:"dateBetween",as:"range",label:_.$trans("general.date_between")},null,8,["start","end","label"])])])]),_:1},8,["form"])}}},Y={name:"AcademicTimetableList"},x=Object.assign(Y,{setup(I){const b=j(),k=O("emitter");let y=["filter"];v("timetable:create")&&y.unshift("create");let $=[];v("timetable:export")&&($=["print","pdf","excel"]);const m="academic/timetable/",p=U(!1),_=A({}),u=a=>{Object.assign(_,a)},D=a=>{window.open(`/app/academic/timetables/${a}/export?action=print`)},w=()=>{window.open("/app/academic/timetables/teacher/export?action=print")};return(a,t)=>{const F=r("BaseButton"),S=r("PageHeaderAction"),L=r("PageHeader"),B=r("ParentTransition"),M=r("TextMuted"),C=r("DataCell"),g=r("FloatingMenuItem"),V=r("FloatingMenu"),H=r("DataRow"),P=r("DataTable"),R=r("ListItem");return c(),d(R,{"init-url":m,onSetItems:u},{header:e(()=>[s(L,{title:a.$trans("academic.timetable.timetable"),navs:[{label:a.$trans("academic.academic"),path:"Academic"}]},{default:e(()=>[s(S,{url:"academic/timetables/",name:"AcademicTimetable",title:a.$trans("academic.timetable.timetable"),actions:i(y),"dropdown-actions":i($),"additional-dropdown-actions-query":{details:!0},onToggleFilter:t[0]||(t[0]=n=>p.value=!p.value)},{default:e(()=>[i(G)(["student","guardian"],"any")?h("",!0):(c(),d(F,{key:0,design:"white",onClick:w},{default:e(()=>[l(o(a.$trans("global.print",{attribute:a.$trans("academic.timetable.teacher_timetable")})),1)]),_:1}))]),_:1},8,["title","actions","dropdown-actions"])]),_:1},8,["title","navs"])]),filter:e(()=>[s(B,{appear:"",visibility:p.value},{default:e(()=>[s(X,{onRefresh:t[1]||(t[1]=n=>i(k).emit("listItems")),onHide:t[2]||(t[2]=n=>p.value=!1)})]),_:1},8,["visibility"])]),default:e(()=>[s(B,{appear:"",visibility:!0},{default:e(()=>[s(P,{header:_.headers,meta:_.meta,module:"academic.timetable",onRefresh:t[4]||(t[4]=n=>i(k).emit("listItems"))},{actionButton:e(()=>[i(v)("timetable:create")?(c(),d(F,{key:0,onClick:t[3]||(t[3]=n=>i(b).push({name:"AcademicTimetableCreate"}))},{default:e(()=>[l(o(a.$trans("global.add",{attribute:a.$trans("academic.timetable.timetable")})),1)]),_:1})):h("",!0)]),default:e(()=>[(c(!0),E(W,null,z(_.data,n=>(c(),d(H,{key:n.uuid,onDoubleClick:f=>i(b).push({name:"AcademicTimetableShow",params:{uuid:n.uuid}})},{default:e(()=>[s(C,{name:"batch"},{default:e(()=>[l(o(n.batch.course.nameWithTerm+" "+n.batch.name)+" ",1),n.room?(c(),d(M,{key:0,block:""},{default:e(()=>{var f;return[l(o(((f=n.room)==null?void 0:f.fullName)||"-"),1)]}),_:2},1024)):h("",!0)]),_:2},1024),s(C,{name:"effectiveDate"},{default:e(()=>[l(o(n.effectiveDate.formatted),1)]),_:2},1024),s(C,{name:"createdAt"},{default:e(()=>[l(o(n.createdAt.formatted),1)]),_:2},1024),s(C,{name:"action"},{default:e(()=>[s(V,null,{default:e(()=>[s(g,{icon:"fas fa-print",onClick:f=>D(n.uuid)},{default:e(()=>[l(o(a.$trans("general.print")),1)]),_:2},1032,["onClick"]),i(v)("timetable:allocate")?(c(),d(g,{key:0,icon:"fas fa-circle-check",onClick:f=>i(b).push({name:"AcademicTimetableAllocation",params:{uuid:n.uuid}})},{default:e(()=>[l(o(a.$trans("academic.timetable.allocation")),1)]),_:2},1032,["onClick"])):h("",!0),s(g,{icon:"fas fa-arrow-circle-right",onClick:f=>i(b).push({name:"AcademicTimetableShow",params:{uuid:n.uuid}})},{default:e(()=>[l(o(a.$trans("general.show")),1)]),_:2},1032,["onClick"]),i(v)("timetable:edit")?(c(),d(g,{key:1,icon:"fas fa-edit",onClick:f=>i(b).push({name:"AcademicTimetableEdit",params:{uuid:n.uuid}})},{default:e(()=>[l(o(a.$trans("general.edit")),1)]),_:2},1032,["onClick"])):h("",!0),i(v)("timetable:create")?(c(),d(g,{key:2,icon:"fas fa-copy",onClick:f=>i(b).push({name:"AcademicTimetableDuplicate",params:{uuid:n.uuid}})},{default:e(()=>[l(o(a.$trans("general.duplicate")),1)]),_:2},1032,["onClick"])):h("",!0),i(v)("timetable:delete")?(c(),d(g,{key:3,icon:"fas fa-trash",onClick:f=>i(k).emit("deleteItem",{uuid:n.uuid})},{default:e(()=>[l(o(a.$trans("general.delete")),1)]),_:2},1032,["onClick"])):h("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1032,["onDoubleClick"]))),128))]),_:1},8,["header","meta"])]),_:1})]),_:1})}}});export{x as default};
