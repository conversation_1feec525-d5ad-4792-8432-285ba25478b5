import{i as F,u as S,h as v,l as C,r as o,a as P,o as c,e as a,w as t,f as i,q as V,b as y,d as N,s as n,t as l,F as H}from"./app-DvIo72ZO.js";const I={class:"grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2"},T={name:"AssetBuildingFloorShow"},j=Object.assign(T,{setup(D){F();const d=S(),p=v(),m={},b="asset/building/floor/",s=C({...m}),g=e=>{Object.assign(s,e)};return(e,u)=>{const f=o("PageHeaderAction"),_=o("PageHeader"),r=o("BaseDataView"),B=o("BaseButton"),$=o("ShowButton"),A=o("BaseCard"),h=o("ShowItem"),w=o("ParentTransition");return c(),P(H,null,[a(_,{title:e.$trans(i(d).meta.trans,{attribute:e.$trans(i(d).meta.label)}),navs:[{label:e.$trans("asset.asset"),path:"Asset"},{label:e.$trans("asset.building.building"),path:"AssetBuilding"},{label:e.$trans("asset.building.floor.floor"),path:"AssetBuildingFloorList"}]},{default:t(()=>[a(f,{name:"AssetBuildingFloor",title:e.$trans("asset.building.floor.floor"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),a(w,{appear:"",visibility:!0},{default:t(()=>[a(h,{"init-url":b,uuid:i(d).params.uuid,onSetItem:g,onRedirectTo:u[1]||(u[1]=k=>i(p).push({name:"AssetBuildingFloor"}))},{default:t(()=>[s.uuid?(c(),V(A,{key:0},{title:t(()=>[n(l(s.name),1)]),footer:t(()=>[a($,null,{default:t(()=>[a(B,{design:"primary",onClick:u[0]||(u[0]=k=>i(p).push({name:"AssetBuildingFloorEdit",params:{uuid:s.uuid}}))},{default:t(()=>[n(l(e.$trans("general.edit")),1)]),_:1})]),_:1})]),default:t(()=>[N("dl",I,[a(r,{label:e.$trans("asset.building.floor.props.name")},{default:t(()=>[n(l(s.name),1)]),_:1},8,["label"]),a(r,{label:e.$trans("asset.building.floor.props.alias")},{default:t(()=>[n(l(s.alias),1)]),_:1},8,["label"]),a(r,{label:e.$trans("asset.building.block.block")},{default:t(()=>[n(l(s.blockName),1)]),_:1},8,["label"]),a(r,{class:"col-span-1 sm:col-span-2",label:e.$trans("asset.building.floor.props.description")},{default:t(()=>[n(l(s.description),1)]),_:1},8,["label"]),a(r,{label:e.$trans("general.created_at")},{default:t(()=>[n(l(s.createdAt.formatted),1)]),_:1},8,["label"]),a(r,{label:e.$trans("general.updated_at")},{default:t(()=>[n(l(s.updatedAt.formatted),1)]),_:1},8,["label"])])]),_:1})):y("",!0)]),_:1},8,["uuid"])]),_:1})],64)}}});export{j as default};
