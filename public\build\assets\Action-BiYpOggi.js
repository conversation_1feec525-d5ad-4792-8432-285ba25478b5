import{l as V,I as P,r as c,q as y,o as f,w as _,d as m,a as b,e as u,f as a,F as U,v as j,s as O,t as q,K as $,u as E}from"./app-DvIo72ZO.js";const R={class:"grid grid-cols-3 gap-6"},k={class:"col-span-3 sm:col-span-1"},H={class:"col-span-3 sm:col-span-2"},L={class:"col-span-4 sm:col-span-1"},w={class:"col-span-4 sm:col-span-1"},I={class:"col-span-4 sm:col-span-1"},N={class:"col-span-4 sm:col-span-1"},C={name:"TransportFeeForm"},D=Object.assign(C,{setup(h){const l={name:"",description:"",records:[]},d="transport/fee/",A=V({circles:[]}),t=P(d),i=V({...l}),v=o=>{Object.assign(A,o),A.circles.forEach(r=>{l.records.push({circle:r,arrivalAmount:"",departureAmount:"",roundtripAmount:""})}),Object.assign(i,$(l))},F=o=>{Object.assign(l,{name:o.name,description:o.description}),l.records.forEach(r=>{let s=o.records.find(g=>g.circle.uuid==r.circle.uuid);o.isAssigned&&s!==void 0?r.isAssigned=!0:r.isAssigned=!1,s!==void 0&&(r.circle=s.circle,r.arrivalAmount=s.arrivalAmount.value,r.departureAmount=s.departureAmount.value,r.roundtripAmount=s.roundtripAmount.value)}),Object.assign(i,$(l))};return(o,r)=>{const s=c("BaseInput"),g=c("BaseTextarea"),T=c("BaseLabel"),B=c("FormAction");return f(),y(B,{"pre-requisites":!0,onSetPreRequisites:v,"init-url":d,"init-form":l,form:i,"set-form":F,redirect:"TransportFee"},{default:_(()=>[m("div",R,[m("div",k,[u(s,{type:"text",modelValue:i.name,"onUpdate:modelValue":r[0]||(r[0]=e=>i.name=e),name:"name",label:o.$trans("transport.fee.props.name"),error:a(t).name,"onUpdate:error":r[1]||(r[1]=e=>a(t).name=e),autofocus:""},null,8,["modelValue","label","error"])]),m("div",H,[u(g,{modelValue:i.description,"onUpdate:modelValue":r[2]||(r[2]=e=>i.description=e),rows:1,name:"description",label:o.$trans("transport.fee.props.description"),error:a(t).description,"onUpdate:error":r[3]||(r[3]=e=>a(t).description=e)},null,8,["modelValue","label","error"])])]),(f(!0),b(U,null,j(i.records,(e,p)=>(f(),b("div",{class:"mt-4 grid grid-cols-4 gap-3",key:e.circle.uuid},[m("div",L,[u(T,{class:"mt-4"},{default:_(()=>[O(q(e.circle.name),1)]),_:2},1024)]),m("div",w,[u(s,{disabled:e.isAssigned,name:`records.${p}.arrivalAmount`,modelValue:e.arrivalAmount,"onUpdate:modelValue":n=>e.arrivalAmount=n,placeholder:o.$trans("transport.fee.props.arrival_amount"),currency:"",error:a(t)[`records.${p}.arrivalAmount`],"onUpdate:error":n=>a(t)[`records.${p}.arrivalAmount`]=n},null,8,["disabled","name","modelValue","onUpdate:modelValue","placeholder","error","onUpdate:error"])]),m("div",I,[u(s,{disabled:e.isAssigned,name:`records.${p}.departureAmount`,modelValue:e.departureAmount,"onUpdate:modelValue":n=>e.departureAmount=n,placeholder:o.$trans("transport.fee.props.departure_amount"),currency:"",error:a(t)[`records.${p}.departureAmount`],"onUpdate:error":n=>a(t)[`records.${p}.departureAmount`]=n},null,8,["disabled","name","modelValue","onUpdate:modelValue","placeholder","error","onUpdate:error"])]),m("div",N,[u(s,{disabled:e.isAssigned,name:`records.${p}.roundtripAmount`,modelValue:e.roundtripAmount,"onUpdate:modelValue":n=>e.roundtripAmount=n,placeholder:o.$trans("transport.fee.props.roundtrip_amount"),currency:"",error:a(t)[`records.${p}.roundtripAmount`],"onUpdate:error":n=>a(t)[`records.${p}.roundtripAmount`]=n},null,8,["disabled","name","modelValue","onUpdate:modelValue","placeholder","error","onUpdate:error"])])]))),128))]),_:1},8,["form"])}}}),S={name:"TransportFeeAction"},z=Object.assign(S,{setup(h){const l=E();return(d,A)=>{const t=c("PageHeaderAction"),i=c("PageHeader"),v=c("ParentTransition");return f(),b(U,null,[u(i,{title:d.$trans(a(l).meta.trans,{attribute:d.$trans(a(l).meta.label)}),navs:[{label:d.$trans("transport.transport"),path:"Transport"},{label:d.$trans("transport.fee.fee"),path:"TransportFeeList"}]},{default:_(()=>[u(t,{name:"TransportFee",title:d.$trans("transport.fee.fee"),actions:["list"]},null,8,["title"])]),_:1},8,["title","navs"]),u(v,{appear:"",visibility:!0},{default:_(()=>[u(D)]),_:1})],64)}}});export{z as default};
